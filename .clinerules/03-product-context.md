# Product Context: wx-next

## Why wx-next Exists

wx-next is The Weather Company's next-generation web monorepo that replaces fragmented legacy systems with a modern architecture and reduces technical debt while enabling consistent, high-quality user experiences.

**Key Problems Solved:**
- **System Fragmentation**: Consolidates legacy content platforms and backend for frontends into unified monorepo
- **Scalability Constraints**: Modern architecture supports faster builds and development experience
- **Content Management Complexity**: PayloadCMS and Next.js supports faster CMS development
- **Development Inefficiency**: Choice of Turborepo as monorepo improves accelerates development
- **Inconsistent User Experience**: Unified design system ensures consistency with tooling like Storybook and Chromatic

## User Experience Goals

wx-next delivers exceptional experiences for different user types:

### End Users
- **Intuitive Access**: Quick access to relevant weather information for any location
- **Performance First**: Fast page loads, launch times, and fast time to interaction (core web vitals) across all devices

### Content Creators
- **Streamlined Workflows**: Efficient content creation, preview, and publishing
- **Flexible Components**: Block-based system with powerful, combinable building blocks
- **Collaboration Tools**: Support for content teams working together

### Developers
- **Clear Patterns**: Well-documented patterns and examples for platform extension
- **Type Safety**: Strong typing throughout the codebase for confidence and guidance
- **Testing Support**: Architecture that facilitates comprehensive testing

## Platform Value

wx-next provides comprehensive capabilities for weather information delivery and content management through core use of Next.js and Payload CMS on the Vercel platform.

### Weather Data Capabilities

- **Current Conditions**: Real-time weather information for user locations driven by our SUN APIs
- **Forecasts**: Daily and extended forecasts with various time horizons by our SUN APIs
- **Location Services**: Location search and location point retrieved by our SUN APIs
- **Weather Visualization**: Visual representations of weather data through charts, maps, and icons.

### Content Management Capabilities

- **Multi-Tenant CMS**: PayloadCMS integration with support for different content spaces.
- **Rich Text Editing**: Lexical-based rich text editing for content creation.
- **Media Management**: Integrated system for managing images and other media.
- **Content Blocks**: Modular content components that can be combined in various ways.
- **Structured Content**: Type-safe content models for consistent data structure.

### Technical Capabilities

- **Server-Side Rendering**: Fast initial page loads with Next.js SSR, SSG, ISR, and PPR.
- **Client-Side Data Fetching**: Dynamic data updates using the SWR pattern.
- **Responsive Design**: Optimized layouts for all device sizes.
- **Type Safety**: Strong typing throughout the codebase with TypeScript.
- **Performance Optimization**: Various techniques to ensure fast user experiences.
- **Cost Savings**: High cachability by avoiding cache breaking functionality and preferring SSG, ISR, PPR approaches vs dynamic function usage.

## Target Audiences

wx-next serves multiple distinct audiences:

### Primary End Users

- **Weather Information Seekers**: Individuals looking for current conditions and forecasts for their location or areas of interest.
- **Planning-Focused Users**: People making decisions based on weather forecasts (travel, outdoor activities, etc.).
- **Weather Enthusiasts**: Users interested in detailed weather data and meteorological information.

### Content Teams

- **Editors**: Content professionals creating and managing weather-related articles and pages.
- **Meteorologists**: Weather experts providing specialized content and insights.
- **Media Managers**: Team members responsible for weather-related images and videos.

### Business Stakeholders

- **Internal Business Units**: Different departments within The Weather Company using the platform for specific needs.
- **External Partners**: Organizations leveraging the platform through multi-tenant capabilities.
- **API Consumers**: Systems and services consuming weather data through the platform's APIs.

### Technical Teams

- **Frontend Developers**: Engineers building and extending the user interface.
- **Backend Developers**: Engineers working on data access and server-side functionality.
- **DevOps Engineers**: Team members responsible for deployment and infrastructure.

## Success Measures

The success of wx-next will be measured across several key dimensions:

### User Engagement
- **Session Duration**: Time users spend engaging with weather content
- **Return Rate**: Frequency of repeat visits to the platform
- **Monthly Active Users (MAUs)**: Growth in organic MAUs
- **Visits per MAU**: Increase in repeat usage measured by visits per MAU
- **Logged-in Usage Ratio**: Proportion of sessions from logged-in users
- **Page Views per Visit (PV/V)**: Measure of content engagement depth
- **Content Sessions**: Number of sessions that include content page views

### Registration and Subscription
- **Registered Users**: Progress toward 2025 goal of 20M registered users
- **Registration Conversion Rate**: Percentage of visitors who register
- **Active Subscribers**: Progress toward 2025 goal of 1.5M active subscribers
- **Subscription Conversion Rate**: Percentage of registered users who subscribe
- **Subscriber Retention**: Duration of active subscriptions

### Content Effectiveness
- **Content Creation Efficiency**: Time required to create and publish new content
- **Content Performance**: Engagement metrics for different content types
- **Editorial Productivity**: Volume and quality of content produced by editorial teams
- **Content Discoverability**: Effectiveness of content organization and search
- **Cohort Engagement**: Engagement metrics for different user cohorts

### Technical Performance
- **Launch Time**: Speed of initial page load and app startup
- **Touch Sensitivity**: Responsiveness of touch interactions
- **Radar Loading**: Performance of weather radar visualization loading
- **Core Web Vitals**: LCP, FID, CLS metrics across devices
- **API Response Times**: Speed of data delivery from backend services
- **Build and Deployment Efficiency**: Time required for builds and deployments

### Business Impact
- **User Growth**: Increase in platform users over time
- **Multi-Tenant Adoption**: Number of tenants successfully using the platform
- **Development Velocity**: Speed of delivering new features and capabilities
- **Operational Efficiency**: Reduction in maintenance overhead compared to previous systems
- **Notification Engagement**: Open rates and actions taken from notifications
