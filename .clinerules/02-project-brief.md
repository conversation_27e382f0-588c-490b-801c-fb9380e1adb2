# Project Brief: wx-next

## Project Overview

wx-next is a modern weather application platform built by The Weather Company. It's a Next.js web application with PayloadCMS integration that provides weather data, forecasts, and content management capabilities.

## Core Requirements

1. **Weather Data Display**: Accurate, real-time weather information and forecasts
2. **Location Services**: IP-based location detection and manual location selection
3. **Content Management**: PayloadCMS integration for editorial content
4. **Multi-Tenant Support**: Separate content spaces for different use cases
5. **Responsive Design**: Optimal experience across all device types
6. **Performance**: Fast page loads, launch times, and radar loading
7. **User Registration**: Effective flows targeting 20M registered users by 2025
8. **Content Discovery**: Cohort-based experiences to reduce clutter and hyperserve users
9. **Premium Subscriptions**: Valuable offering targeting 1.5M active subscribers by 2025

## Project Scope

### In Scope

- Next.js web application with server and client components
- Web app in turborepo monorepo
- PayloadCMS as new CMS to replace legacy CMS, <PERSON><PERSON>ope and DSX
- Github actions to aid in developer experience (DX)
- Weather data integration via DAL (Data Access Layer)
- UPSx SDK to handle user profiles, registration, purchases, and preferences

## Success Criteria

1. Fast, responsive user experience across devices
2. Accurate, timely weather data presentation
3. Flexible content management for editors
4. Maintainable, well-structured codebase
5. Achievement of 2025 goals for registered users (20M) and subscribers (1.5M)
6. Improved user engagement metrics (MAUs, visits, PV/V, content sessions)
7. Effective cohort-based experiences for different user segments
8. High-performing notification system with rich location awareness
9. Clear value differentiation for premium subscription features
