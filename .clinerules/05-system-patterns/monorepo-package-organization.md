# Monorepo Package Organization Pattern

Package-based monorepo structure where discrete logical code is organized into focused packages to avoid cyclical dependencies and promote clear separation of concerns.

## Key Components

1. **Package-First Architecture**
   ```
   wx-next/
   ├── apps/                    # Entry points into the package tree
   │   ├── web/                 # Next.js web application
   │   └── storybook/           # Component documentation
   └── packages/                # Discrete logical packages
       ├── dal/                 # Data Access Layer
       ├── payload/             # PayloadCMS blocks and config
       ├── ui/                  # Shared UI components
       ├── location/            # Location-specific functionality
       ├── user/                # User management
       ├── icons/               # Icon system
       ├── logger/              # Logging utilities
       ├── analytics/           # Analytics functionality
       ├── experiments/         # A/B testing
       └── ...                  # Domain-specific packages
   ```

   Apps serve as entry points that consume packages, while packages contain discrete logical functionality.

2. **Dependency Flow**
   ```typescript
   // Clear dependency hierarchy prevents cycles
   apps/web → packages/ui → packages/logger
   apps/web → packages/dal → packages/utils
   apps/web → packages/payload → packages/ui

   // ✅ Good: Apps depend on packages
   import { Button } from '@repo/ui';
   import { getCurrentObservations } from '@repo/dal/weather';
   import { createLogger } from '@repo/logger';

   // 🚫 Bad: Packages depending on apps would create cycles
   // packages should never import from apps/
   ```

   Unidirectional dependency flow from apps to packages prevents cyclical dependencies.

3. **Package Boundaries**
   ```typescript
   // Each package has clear responsibilities and exports

   // packages/dal/package.json
   {
     "name": "@repo/dal",
     "exports": {
       "./weather": "./src/weather/index.ts",
       "./locations": "./src/locations/index.ts",
       "./content": "./src/content/index.ts"
     }
   }

   // packages/ui/package.json
   {
     "name": "@repo/ui",
     "exports": {
       "./components": "./src/components/index.ts",
       "./tailwind": "./src/tailwind/index.ts"
     }
   }

   // packages/location/package.json
   {
     "name": "@repo/location",
     "exports": {
       "./atoms": "./src/atoms/index.ts",
       "./hooks": "./src/hooks/index.ts",
       "./utils": "./src/utils/index.ts"
     }
   }
   ```

   Each package defines clear exports and maintains focused responsibilities.

## Benefits

- **Cyclical Dependency Prevention**: Clear dependency hierarchy prevents circular imports
- **Focused Responsibility**: Each package has a single, well-defined purpose
- **Reusability**: Packages can be consumed by multiple apps
- **Independent Development**: Teams can work on packages independently
- **Clear Boundaries**: Package exports define public APIs
- **Scalable Architecture**: Easy to add new packages without affecting existing code
- **Testing Isolation**: Packages can be tested independently

## Example Implementation

```typescript
// Package creation guidelines and examples

// 1. Domain-Specific Package (packages/weather/)
// packages/weather/package.json
{
  "name": "@repo/weather",
  "exports": {
    "./components": "./src/components/index.ts",
    "./hooks": "./src/hooks/index.ts",
    "./types": "./src/types/index.ts"
  },
  "dependencies": {
    "@repo/dal": "workspace:*",
    "@repo/ui": "workspace:*",
    "@repo/logger": "workspace:*"
  }
}

// packages/weather/src/components/index.ts
export { CurrentConditions } from './CurrentConditions';
export { DailyForecast } from './DailyForecast';
export { WeatherAlerts } from './WeatherAlerts';

// 2. Utility Package (packages/units/)
// packages/units/package.json
{
  "name": "@repo/units",
  "exports": {
    ".": "./src/index.ts"
  }
}

// packages/units/src/index.ts
export enum Units {
  IMPERIAL = 'e',
  METRIC = 'm'
}

export const convertTemperature = (temp: number, from: Units, to: Units): number => {
  // Conversion logic
};

// 3. App consuming packages
// apps/web/app/weather/page.tsx
import { getCurrentObservations } from '@repo/dal/weather';
import { CurrentConditions } from '@repo/weather/components';
import { Units } from '@repo/units';
import { createLogger } from '@repo/logger';

const logger = createLogger('WeatherPage');

export default async function WeatherPage() {
  const weatherData = await getCurrentObservations({
    geocode: '40.7,-74.0',
    units: Units.IMPERIAL
  });

  logger.info('Weather data loaded', { temperature: weatherData.temperature });

  return <CurrentConditions data={weatherData} />;
}

// 4. Package interdependencies
// packages/weather/src/hooks/useWeatherData.ts
import { useSWR } from 'swr';
import { getCurrentObservations } from '@repo/dal/weather';
import { createLogger } from '@repo/logger';
import { Units } from '@repo/units';

const logger = createLogger('useWeatherData');

export function useWeatherData(geocode: string) {
  return useSWR(
    ['weather', geocode],
    () => getCurrentObservations({ geocode, units: Units.IMPERIAL }),
    {
      onSuccess: (data) => logger.info('Weather data fetched', { geocode }),
      onError: (error) => logger.error('Weather fetch failed', { geocode, error })
    }
  );
}
```

## Package Creation Guidelines

### When to Create a New Package

1. **Domain Boundaries**: Distinct business domains (weather, user, location)
2. **Shared Functionality**: Code used by multiple apps or packages
3. **Independent Lifecycle**: Code that can be developed and tested independently
4. **Clear API Surface**: Functionality with well-defined inputs and outputs
5. **Reusability**: Code that could be useful in future apps or contexts

### Package Naming Conventions

- Use descriptive, domain-focused names
- Avoid technical implementation details in names
- Use singular nouns (e.g., `user` not `users`)
- Keep names concise but clear

### Package Structure

```
packages/[package-name]/
├── package.json           # Package configuration and exports
├── tsconfig.json         # TypeScript configuration
├── eslint.config.mjs     # ESLint configuration
├── vitest.config.ts      # Test configuration
├── README.md             # Package documentation
└── src/
    ├── index.ts          # Main export (if applicable)
    ├── components/       # React components (if applicable)
    ├── hooks/           # Custom hooks (if applicable)
    ├── utils/           # Utility functions
    ├── types/           # TypeScript type definitions
    └── __tests__/       # Test files
```

## Important Notes

- **Apps as Entry Points**: Apps should be the only entry points that consume packages
- **No App Dependencies**: Packages should never depend on apps
- **Minimal Package Dependencies**: Keep package-to-package dependencies minimal
- **Clear Exports**: Use package.json exports to define public APIs
- **Workspace Dependencies**: Use `workspace:*` for internal package dependencies
- **Independent Testing**: Each package should be testable in isolation
- **Documentation**: Each package should have clear README documentation
- **Focused Scope**: Resist the urge to create overly broad packages
- **Gradual Extraction**: Extract packages from apps when patterns emerge, don't over-engineer upfront
