# Storybook Story Consolidation Pattern

Consolidate related component variants into fewer stories to minimize Chromatic screenshots and improve documentation.

## Key Components

1. **Consolidated Story Structure**
   ```typescript
   // Consolidated story showing multiple variants in one view
   export const ButtonVariants: Story = {
     name: 'Button Variants',
     render: () => (
       <div className="space-y-8">
         <h2 className="text-xl font-bold">Style Variants</h2>
         <div className="flex flex-wrap gap-4">
           {['default', 'secondary', 'destructive', 'outline', 'ghost', 'link'].map(variant => (
             <div key={variant} className="flex flex-col items-center">
               <Button variant={variant as any}>{variant}</Button>
               <span className="mt-2 text-sm">{variant}</span>
             </div>
           ))}
         </div>
       </div>
     ),
   };
   ```

   Group variants by style, size, state, or other logical categories in a single story.

2. **Render Functions Over Args**
   ```typescript
   // Instead of multiple stories with args
   export const DefaultButton: Story = { args: { variant: 'default' } };
   export const SecondaryButton: Story = { args: { variant: 'secondary' } };

   // Use render functions to show multiple variants
   export const AllVariants: Story = {
     render: () => (
       <div className="grid grid-cols-3 gap-4">
         <Button variant="default">Default</Button>
         <Button variant="secondary">Secondary</Button>
         <Button variant="destructive">Destructive</Button>
       </div>
     ),
   };
   ```

   Use render functions instead of individual stories with args to reduce screenshot count.

3. **Visual Hierarchy with Labels**
   ```typescript
   export const ComponentShowcase: Story = {
     render: () => (
       <div className="space-y-12">
         <section>
           <h2 className="mb-4 text-lg font-semibold">Size Variants</h2>
           <div className="flex items-center gap-4">
             {['sm', 'md', 'lg'].map(size => (
               <div key={size} className="text-center">
                 <Button size={size}>Size {size}</Button>
                 <p className="mt-2 text-xs text-gray-500">{size}</p>
               </div>
             ))}
           </div>
         </section>

         <section>
           <h2 className="mb-4 text-lg font-semibold">State Variants</h2>
           <div className="flex items-center gap-4">
             <Button>Normal</Button>
             <Button disabled>Disabled</Button>
             <Button loading>Loading</Button>
           </div>
         </section>
       </div>
     ),
   };
   ```

   Create clear visual hierarchy with headings and labels for better documentation.

## Benefits

- **Reduced Screenshot Count**: Fewer screenshots in Chromatic means faster builds and lower costs
- **Simplified Review Process**: Fewer stories to check during manual review
- **Better Comparison**: Easier to compare variants side-by-side
- **Improved Documentation**: More comprehensive view of component capabilities
- **Faster Build Times**: Less processing time for Chromatic visual regression testing
- **Cost Optimization**: Reduced Chromatic usage and associated costs

## Example Implementation

```typescript
import type { Meta, StoryObj } from '@storybook/react';
import { Button } from './Button';

const meta: Meta<typeof Button> = {
  title: 'Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Consolidated story showing all variants
export const AllVariants: Story = {
  name: 'All Button Variants',
  render: () => (
    <div className="space-y-8 p-6">
      {/* Style Variants */}
      <section>
        <h3 className="mb-4 text-lg font-semibold">Style Variants</h3>
        <div className="flex flex-wrap gap-4">
          {['default', 'secondary', 'destructive', 'outline', 'ghost', 'link'].map(variant => (
            <div key={variant} className="flex flex-col items-center">
              <Button variant={variant as any}>{variant}</Button>
              <span className="mt-2 text-sm capitalize">{variant}</span>
            </div>
          ))}
        </div>
      </section>

      {/* Size Variants */}
      <section>
        <h3 className="mb-4 text-lg font-semibold">Size Variants</h3>
        <div className="flex items-center gap-4">
          {['sm', 'default', 'lg', 'icon'].map(size => (
            <div key={size} className="flex flex-col items-center">
              <Button size={size as any}>
                {size === 'icon' ? '🔍' : `Size ${size}`}
              </Button>
              <span className="mt-2 text-sm">{size}</span>
            </div>
          ))}
        </div>
      </section>

      {/* State Variants */}
      <section>
        <h3 className="mb-4 text-lg font-semibold">State Variants</h3>
        <div className="flex items-center gap-4">
          <Button>Normal</Button>
          <Button disabled>Disabled</Button>
          <Button loading>Loading</Button>
        </div>
      </section>
    </div>
  ),
};

// Keep one focused story for interactive testing
export const Interactive: Story = {
  args: {
    children: 'Click me',
    variant: 'default',
  },
};
```

## Important Notes

- **Balance consolidation with usability**: Don't consolidate so much that stories become unwieldy
- **Keep interactive stories**: Maintain at least one interactive story for testing controls
- **Use semantic grouping**: Group variants logically (by style, size, state, etc.)
- **Clear labeling**: Always label variants clearly for easy identification
- **Consider component complexity**: More complex components may need more individual stories
- **Document in comments**: Explain the consolidation strategy in story comments
- **Review with team**: Ensure the consolidation approach works for all stakeholders
- Comprehensive documentation available in `docs/storybook-best-practices.md`
