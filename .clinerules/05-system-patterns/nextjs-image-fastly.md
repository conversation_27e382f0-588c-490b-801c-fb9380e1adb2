# Next.js Image with Fastly Integration Pattern

Next.js Image component optimized with custom Fastly loader for enhanced image performance and delivery.

## Key Components

1. **Fastly Loader Implementation**
   ```typescript
   /**
    * Fastly image loader
    *
    * This function formats image URLs for the Fastly image optimization service.
    * It adds the following parameters:
    * - width: The requested width of the image
    * - format=auto: Automatically selects the best format based on browser support
    * - optimize=medium: Applies medium level optimization
    * - quality: Only added if explicitly provided
    */
   const fastlyLoader = ({
     src,
     width,
     quality,
   }: {
     src: string;
     width: number;
     quality?: number;
   }) => {
     // Start with base URL and width
     let url = `https://s.w-x.co/${src}?width=${width}&format=auto&optimize=medium`;

     // Only add quality if explicitly provided
     if (quality) {
       url += `&quality=${quality}`;
     }

     return url;
   };
   ```

   Custom loader that integrates Next.js Image with Fastly's image optimization service.

2. **Image Component Usage**
   ```typescript
   import Image from 'next/image';

   // Fixed size image
   <Image
     src="/weather/sunny-day.jpg"
     alt="Sunny weather conditions"
     width={400}
     height={300}
     loader={fastlyLoader}
     priority // For LCP images
   />

   // Responsive fill mode
   <div className="relative w-full h-64">
     <Image
       src="/weather/storm-clouds.jpg"
       alt="Storm approaching"
       fill
       loader={fastlyLoader}
       className="object-cover rounded-lg"
     />
   </div>
   ```

   Standard Next.js Image component enhanced with Fastly optimization.

3. **Quality Control**
   ```typescript
   // High quality for hero images
   <Image
     src="/hero/weather-hero.jpg"
     alt="Weather application hero"
     width={1200}
     height={600}
     loader={fastlyLoader}
     quality={90}
     priority
   />

   // Standard quality for content images
   <Image
     src="/content/weather-map.jpg"
     alt="Weather radar map"
     width={800}
     height={400}
     loader={fastlyLoader}
     // Uses default quality when not specified
   />
   ```

   Granular control over image quality based on use case.

## Benefits

- **Optimized Images**: Automatic image optimization through Fastly
- **Responsive Images**: Support for both fixed size and responsive fill mode
- **Lazy Loading**: Built-in lazy loading with optional priority loading for LCP images
- **Format Optimization**: Automatic format selection based on browser support
- **Quality Control**: Granular control over image quality
- **Flexible Styling**: Support for shadow, rounded corners, and object-fit options
- **Improved Performance**: Reduced image size and faster loading times
- **CDN Benefits**: Global content delivery through Fastly's edge network

## Example Implementation

```typescript
// Complete image component with Fastly integration
import Image from 'next/image';
import { cn } from '@/lib/utils';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  fill?: boolean;
  quality?: number;
  priority?: boolean;
  className?: string;
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  rounded?: boolean;
  shadow?: boolean;
}

const fastlyLoader = ({ src, width, quality }: {
  src: string;
  width: number;
  quality?: number;
}) => {
  let url = `https://s.w-x.co/${src}?width=${width}&format=auto&optimize=medium`;

  if (quality) {
    url += `&quality=${quality}`;
  }

  return url;
};

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  fill = false,
  quality,
  priority = false,
  className,
  objectFit = 'cover',
  rounded = false,
  shadow = false,
}) => {
  const imageClasses = cn(
    className,
    {
      'rounded-lg': rounded,
      'shadow-lg': shadow,
      [`object-${objectFit}`]: fill,
    }
  );

  if (fill) {
    return (
      <Image
        src={src}
        alt={alt}
        fill
        loader={fastlyLoader}
        quality={quality}
        priority={priority}
        className={imageClasses}
      />
    );
  }

  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      loader={fastlyLoader}
      quality={quality}
      priority={priority}
      className={imageClasses}
    />
  );
};

// Usage examples
export const ImageExamples: React.FC = () => {
  return (
    <div className="space-y-8">
      {/* Hero image with high quality */}
      <OptimizedImage
        src="/hero/weather-dashboard.jpg"
        alt="Weather dashboard hero"
        width={1200}
        height={600}
        quality={90}
        priority
        rounded
        shadow
      />

      {/* Content image with responsive fill */}
      <div className="relative w-full h-96">
        <OptimizedImage
          src="/content/satellite-view.jpg"
          alt="Satellite weather view"
          fill
          objectFit="cover"
          rounded
        />
      </div>

      {/* Gallery images */}
      <div className="grid grid-cols-3 gap-4">
        {weatherImages.map((image, index) => (
          <OptimizedImage
            key={index}
            src={image.src}
            alt={image.alt}
            width={300}
            height={200}
            rounded
            className="hover:scale-105 transition-transform"
          />
        ))}
      </div>
    </div>
  );
};

// Weather-specific image component
export const WeatherImage: React.FC<{
  condition: string;
  size?: 'sm' | 'md' | 'lg';
  priority?: boolean;
}> = ({ condition, size = 'md', priority = false }) => {
  const dimensions = {
    sm: { width: 200, height: 150 },
    md: { width: 400, height: 300 },
    lg: { width: 800, height: 600 },
  };

  return (
    <OptimizedImage
      src={`/weather/conditions/${condition}.jpg`}
      alt={`${condition} weather conditions`}
      width={dimensions[size].width}
      height={dimensions[size].height}
      priority={priority}
      rounded
      className="weather-condition-image"
    />
  );
};
```

## Important Notes

- **Loader Configuration**: Always use the fastlyLoader for consistent optimization
- **Quality Settings**: Only specify quality when needed; default optimization is usually sufficient
- **Priority Loading**: Use priority prop for above-the-fold images to improve LCP
- **Responsive Design**: Use fill mode with relative positioned containers for responsive images
- **Alt Text**: Always provide descriptive alt text for accessibility
- **Performance**: Fastly automatically handles format selection and optimization
- **Caching**: Images are cached at the edge for faster subsequent loads
- **Error Handling**: Consider fallback images for cases where the primary image fails to load
- **SEO Benefits**: Optimized images improve page load times and Core Web Vitals scores
- Comprehensive documentation available in Next.js Image documentation and Fastly image optimization guides
