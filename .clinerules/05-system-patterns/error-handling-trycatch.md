# Error Handling with tryCatch

Consistent error handling pattern using a type-safe tryCatch utility for all asynchronous operations.

## Key Components

1. **tryCatch Utility**
   ```typescript
   // Types for the result object with discriminated union
   type Success<T> = {
     data: T;
     error: null;
   };

   type Failure<E> = {
     data: null;
     error: E;
   };

   type Result<T, E = Error> = Success<T> | Failure<E>;

   // Main wrapper function
   export async function tryCatch<T, E = Error>(
     promise: Promise<T>,
   ): Promise<Result<T, E>> {
     try {
       const data = await promise;
       return { data, error: null };
     } catch (error) {
       return { data: null, error: error as E };
     }
   }
   ```

   The core utility that wraps promises and returns a discriminated union result.

2. **Type-Safe Error Handling**
   ```typescript
   // Usage with type safety
   const { data: location, error } = await tryCatch(
     getLocationPointByLocIdParam(locIdParam as string)
   );

   // TypeScript knows the types based on the discriminated union
   if (error || !data) {
     // Handle error case
     return notFound();
   }

   // TypeScript knows data is available here
   console.log(location.displayName);
   ```

   TypeScript's discriminated unions provide compile-time safety for error handling.

3. **Consistent Error Response Structure**
   ```typescript
   // Server components
   const { data: observations, error } = await tryCatch(
     getCurrentObservations({
       geocode: location.geocode,
       units: Units.IMPERIAL,
       language: "en-US",
     })
   );

   if (error || !observations) {
     return notFound();
   }

   // Client components
   const { data: weatherData, error: weatherError } = await tryCatch(
     getCurrentObservations({
       geocode: location.geocode,
       units: Units.IMPERIAL,
       language: "en-US",
     })
   );

   if (weatherError || !weatherData) {
     setError(weatherError?.message || "Unable to load weather data");
   } else {
     setWeatherData(weatherData);
   }
   ```

   Consistent pattern across both server and client components.

## Benefits

- **Type Safety**: Uses TypeScript's discriminated unions for compile-time error checking
- **Consistent Error Handling**: Standardizes how errors are handled across the application
- **Improved Readability**: Eliminates nested try/catch blocks and simplifies code
- **Explicit Error States**: Makes error states explicit in the return type
- **No Thrown Exceptions**: Converts exceptions into return values for better control flow
- **Composability**: Easy to chain and compose with other operations

## Example Implementation

```typescript
// Server component usage
export default async function WeatherPage({ params }: WeatherPageProps) {
  const { locId } = await params;

  // Fetch location data with error handling
  const { data: location, error: locationError } = await tryCatch(
    getLocationPointByLocIdParam(locId)
  );

  if (locationError || !location) {
    return notFound();
  }

  // Fetch weather data with error handling
  const { data: weatherData, error: weatherError } = await tryCatch(
    getCurrentObservations({
      geocode: `${location.latitude},${location.longitude}`,
      units: Units.IMPERIAL,
      language: "en-US",
    })
  );

  if (weatherError || !weatherData) {
    return (
      <div className="error-state">
        <h2>Unable to load weather data</h2>
        <p>{weatherError?.message || "Please try again later"}</p>
      </div>
    );
  }

  return (
    <div>
      <h1>Weather for {location.displayName}</h1>
      <WeatherDisplay data={weatherData} />
    </div>
  );
}

// Client component usage
export const WeatherWidget: React.FC<WeatherWidgetProps> = ({ location }) => {
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchWeatherData = async () => {
      setIsLoading(true);
      setError(null);

      const { data, error: fetchError } = await tryCatch(
        getCurrentObservations({
          geocode: location.geocode,
          units: Units.IMPERIAL,
          language: "en-US",
        })
      );

      if (fetchError || !data) {
        setError(fetchError?.message || "Unable to load weather data");
      } else {
        setWeatherData(data);
      }

      setIsLoading(false);
    };

    fetchWeatherData();
  }, [location.geocode]);

  if (isLoading) {
    return <WeatherSkeleton />;
  }

  if (error) {
    return <WeatherError message={error} />;
  }

  return <WeatherDisplay data={weatherData} />;
};

// Multiple operations with error handling
export async function fetchAllWeatherData(geocode: string) {
  const [currentResult, forecastResult, alertsResult] = await Promise.all([
    tryCatch(getCurrentObservations({ geocode })),
    tryCatch(getDailyForecast({ geocode })),
    tryCatch(getWeatherAlerts({ geocode })),
  ]);

  return {
    current: currentResult,
    forecast: forecastResult,
    alerts: alertsResult,
  };
}
```

## Important Notes

- **Use consistently**: Apply tryCatch to all promise-based operations throughout the application
- **Don't nest try/catch**: Replace traditional try/catch blocks with tryCatch for consistency
- **Handle both cases**: Always check for both error and data states
- **Type the error**: Use generic type parameter for specific error types when needed
- **Compose operations**: tryCatch works well with Promise.all and other async patterns
- **Client vs Server**: Pattern works identically in both server and client components
- **Testing**: Mock the tryCatch utility in tests for predictable error scenarios
- This pattern is used consistently throughout the application for all asynchronous operations
