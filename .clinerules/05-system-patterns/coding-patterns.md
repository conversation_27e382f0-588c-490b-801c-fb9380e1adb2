# Coding Patterns

General coding patterns and conventions for consistent, maintainable code across the wx-next project.

## Key Components

1. **Function Naming Conventions**
   ```typescript
   // ✅ Good: Verb-based naming with clear intent
   function validateUserInput(input: string): boolean { }
   function saveUserPreferences(preferences: UserPrefs): Promise<void> { }
   function executeWeatherUpdate(): void { }

   // Boolean functions use is/has/can prefixes
   function isUserLoggedIn(): boolean { }
   function hasValidLocation(location: Location): boolean { }
   function canAccessPremiumFeatures(user: User): boolean { }

   // Action functions use descriptive verbs
   function createWeatherAlert(alert: Alert): Promise<Alert> { }
   function updateLocationSettings(settings: LocationSettings): void { }
   function deleteUserAccount(userId: string): Promise<void> { }
   ```

   Functions are named with verbs that clearly indicate their purpose and return type.

2. **Early Return Pattern**
   ```typescript
   // ✅ Good: Early returns to avoid nesting
   function processWeatherData(data: WeatherData | null): ProcessedData | null {
     if (!data) {
       return null;
     }

     if (!data.temperature) {
       console.warn('Missing temperature data');
       return null;
     }

     if (data.temperature < -100 || data.temperature > 150) {
       console.error('Invalid temperature range');
       return null;
     }

     // Main logic at the end, not nested
     return {
       temperature: data.temperature,
       condition: data.condition,
       timestamp: new Date().toISOString(),
     };
   }

   // 🚫 Bad: Nested blocks
   function processWeatherDataBad(data: WeatherData | null): ProcessedData | null {
     if (data) {
       if (data.temperature) {
         if (data.temperature >= -100 && data.temperature <= 150) {
           return {
             temperature: data.temperature,
             condition: data.condition,
             timestamp: new Date().toISOString(),
           };
         } else {
           console.error('Invalid temperature range');
           return null;
         }
       } else {
         console.warn('Missing temperature data');
         return null;
       }
     } else {
       return null;
     }
   }
   ```

   Use early returns and guard clauses to reduce nesting and improve readability.

3. **Higher-Order Functions**
   ```typescript
   // ✅ Good: Use map, filter, reduce instead of loops
   const processLocations = (locations: Location[]) => {
     return locations
       .filter(location => location.isActive)
       .map(location => ({
         ...location,
         displayName: formatLocationName(location),
       }))
       .sort((a, b) => a.displayName.localeCompare(b.displayName));
   };

   // Arrow functions for simple operations (< 3 instructions)
   const isValidTemperature = (temp: number) => temp >= -100 && temp <= 150;
   const formatTemperature = (temp: number) => `${temp}°F`;

   // Named functions for complex operations
   function calculateWeatherTrend(readings: WeatherReading[]): WeatherTrend {
     const sortedReadings = readings.sort((a, b) =>
       new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
     );

     const temperatureTrend = calculateTemperatureTrend(sortedReadings);
     const pressureTrend = calculatePressureTrend(sortedReadings);

     return {
       temperature: temperatureTrend,
       pressure: pressureTrend,
       confidence: calculateTrendConfidence(sortedReadings),
     };
   }
   ```

   Prefer functional programming patterns and choose arrow vs named functions based on complexity.

4. **Default Parameters**
   ```typescript
   // ✅ Good: Use default parameters
   function fetchWeatherData(
     geocode: string,
     units: Units = Units.IMPERIAL,
     language: string = 'en-US',
     includeAlerts: boolean = true
   ): Promise<WeatherData> {
     return getCurrentObservations({
       geocode,
       units,
       language,
       includeAlerts,
     });
   }

   // ✅ Good: Default object parameters
   function createUserPreferences(
     options: Partial<UserPreferences> = {}
   ): UserPreferences {
     return {
       units: Units.IMPERIAL,
       language: 'en-US',
       notifications: true,
       ...options,
     };
   }

   // 🚫 Bad: Manual null/undefined checks
   function fetchWeatherDataBad(
     geocode: string,
     units?: Units,
     language?: string,
     includeAlerts?: boolean
   ): Promise<WeatherData> {
     const finalUnits = units || Units.IMPERIAL;
     const finalLanguage = language || 'en-US';
     const finalIncludeAlerts = includeAlerts !== undefined ? includeAlerts : true;

     return getCurrentObservations({
       geocode,
       units: finalUnits,
       language: finalLanguage,
       includeAlerts: finalIncludeAlerts,
     });
   }
   ```

   Use default parameters instead of manual null/undefined checks.

5. **Error Handling Strategy**
   ```typescript
   // ✅ Good: Single-level try/catch with context
   async function saveUserLocation(location: Location): Promise<void> {
     try {
       await validateLocation(location);
       await persistLocation(location);
       await updateLocationCache(location);
     } catch (error) {
       console.error('Failed to save user location:', {
         location: location.displayName,
         error: error.message,
       });
       throw new Error(`Unable to save location: ${location.displayName}`);
     }
   }

   // ✅ Good: Use _error for ESLint compliance when not logging
   async function attemptLocationSave(location: Location): Promise<boolean> {
     try {
       await saveUserLocation(location);
       return true;
     } catch (_error) {
       // Error is handled upstream, just return failure status
       return false;
     }
   }

   // ✅ Good: Integration with tryCatch pattern
   async function processLocationData(locationId: string): Promise<ProcessedLocation | null> {
     const { data: location, error } = await tryCatch(
       getLocationById(locationId)
     );

     if (error) {
       console.error('Failed to fetch location:', {
         locationId,
         error: error.message,
       });
       return null;
     }

     return transformLocationData(location);
   }

   // 🚫 Bad: Nested try/catch blocks
   async function saveUserLocationBad(location: Location): Promise<void> {
     try {
       try {
         await validateLocation(location);
       } catch (validationError) {
         console.error('Validation failed:', validationError);
         throw validationError;
       }

       try {
         await persistLocation(location);
       } catch (persistError) {
         console.error('Persistence failed:', persistError);
         throw persistError;
       }
     } catch (error) {
       throw new Error(`Unable to save location: ${location.displayName}`);
     }
   }
   ```

   Use single-level try/catch blocks with proper error context and integrate with the tryCatch pattern.

6. **Type Colocation Pattern**
   ```typescript
   // ✅ Good: Component-specific types colocated with usage
   export interface WeatherDisplayProps {
     temperature: number;
     condition: string;
     location: LocationData; // Shared type from separate file
   }

   export const WeatherDisplay: React.FC<WeatherDisplayProps> = ({
     temperature,
     condition,
     location
   }) => {
     // Component implementation
   };

   // ✅ Good: Function-specific types colocated with function
   interface ValidationResult {
     isValid: boolean;
     error?: string;
   }

   export function validateWeatherData(data: unknown): ValidationResult {
     // Validation logic
   }

   // ✅ Good: Shared domain types in dedicated file
   // apps/web/location/types.ts
   export interface LocationData {
     displayName: string;
     geocode: string;
     // Used across multiple components and domains
   }

   // 🚫 Bad: Single-use types in separate files
   // types/weather-display-types.ts (unnecessary file)
   export interface WeatherDisplayProps { /* only used in one component */ }
   ```

   Colocate types with their primary usage location, extract only when truly shared or to avoid circular dependencies.

7. **Feature Domain Organization**
   ```typescript
   // ✅ Good: Vertical slice organization by feature domain
   apps/web/user/
   ├── README.md                    // Feature documentation
   ├── atoms/                       // State management
   │   ├── auth.ts
   │   ├── preferences/
   │   └── wxu/
   ├── hooks/                       // Custom hooks
   │   ├── useUser.ts
   │   └── useUserPreferences.ts
   ├── swrFallbacks/               // SWR configuration
   ├── tests/                      // Feature tests
   └── utils/                      // Feature utilities

   apps/web/experiments/amplitude/
   ├── AmplitudeExperimentProvider.tsx  // Main component
   └── hooks/                           // Feature-specific hooks

   // Complete vertical slices with all related functionality colocated
   ```

   Organize code by feature domains with complete vertical slices colocated within each feature directory.

## Benefits

- **Consistent Naming**: Clear, verb-based function names improve code readability
- **Reduced Complexity**: Early returns and guard clauses eliminate deep nesting
- **Functional Programming**: Higher-order functions reduce bugs and improve maintainability
- **Type Safety**: Default parameters eliminate null/undefined checks
- **Error Clarity**: Single-level error handling with proper context
- **Type Proximity**: Colocated types improve discoverability and reduce cognitive load
- **Feature Cohesion**: Domain organization keeps related code together
- **Maintainability**: Clear patterns make code easier to understand and modify
- **Scalability**: Consistent patterns support team growth and codebase expansion

## Example Implementation

```typescript
// Complete example following all coding patterns
import { tryCatch } from '@/utils/tryCatch';
import { getCurrentObservations } from '@repo/dal/weather';

// Feature domain: User Location Management
export interface LocationPreferences {
  primaryLocation?: Location;
  savedLocations: Location[];
  units: Units;
  language: string;
}

// Function naming: verb-based with clear intent
export function hasValidPrimaryLocation(preferences: LocationPreferences): boolean {
  return preferences.primaryLocation !== undefined &&
         isValidLocation(preferences.primaryLocation);
}

export function canSaveMoreLocations(preferences: LocationPreferences): boolean {
  return preferences.savedLocations.length < MAX_SAVED_LOCATIONS;
}

// Early return pattern to avoid nesting
export function validateLocationPreferences(
  preferences: Partial<LocationPreferences>
): ValidationResult {
  if (!preferences) {
    return { isValid: false, error: 'Preferences are required' };
  }

  if (!preferences.savedLocations) {
    return { isValid: false, error: 'Saved locations are required' };
  }

  if (preferences.savedLocations.length === 0) {
    return { isValid: false, error: 'At least one location must be saved' };
  }

  if (preferences.savedLocations.length > MAX_SAVED_LOCATIONS) {
    return {
      isValid: false,
      error: `Cannot save more than ${MAX_SAVED_LOCATIONS} locations`
    };
  }

  // Main validation logic at the end
  const invalidLocations = preferences.savedLocations.filter(
    location => !isValidLocation(location)
  );

  if (invalidLocations.length > 0) {
    return {
      isValid: false,
      error: `Invalid locations: ${invalidLocations.map(l => l.displayName).join(', ')}`
    };
  }

  return { isValid: true };
}

// Higher-order functions with appropriate function types
export const processUserLocations = (
  locations: Location[],
  units: Units = Units.IMPERIAL
) => {
  return locations
    .filter(isActiveLocation) // Arrow function for simple check
    .map(location => enhanceLocationData(location, units)) // Named function for complex logic
    .sort((a, b) => a.displayName.localeCompare(b.displayName));
};

// Arrow function for simple operations (< 3 instructions)
const isActiveLocation = (location: Location): boolean =>
  location.isActive && location.latitude !== 0 && location.longitude !== 0;

// Named function for complex operations
function enhanceLocationData(location: Location, units: Units): EnhancedLocation {
  const weatherData = getCachedWeatherData(location.geocode);
  const displayName = formatLocationDisplayName(location);
  const timezone = calculateTimezone(location.latitude, location.longitude);

  return {
    ...location,
    displayName,
    timezone,
    currentWeather: weatherData ? formatWeatherForUnits(weatherData, units) : null,
    lastUpdated: new Date().toISOString(),
  };
}

// Default parameters instead of null checks
export async function fetchLocationWeather(
  geocode: string,
  units: Units = Units.IMPERIAL,
  language: string = 'en-US',
  includeAlerts: boolean = true
): Promise<WeatherData | null> {
  // Integration with tryCatch pattern
  const { data: weatherData, error } = await tryCatch(
    getCurrentObservations({
      geocode,
      units,
      language,
      includeAlerts,
    })
  );

  if (error) {
    console.error('Failed to fetch weather data:', {
      geocode,
      units,
      language,
      error: error.message,
    });
    return null;
  }

  return weatherData;
}

// Single-level error handling with context
export async function saveLocationPreferences(
  userId: string,
  preferences: LocationPreferences
): Promise<void> {
  try {
    const validation = validateLocationPreferences(preferences);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    await persistUserPreferences(userId, preferences);
    await updateLocationCache(preferences.savedLocations);
    await notifyPreferencesUpdated(userId);
  } catch (error) {
    console.error('Failed to save location preferences:', {
      userId,
      locationCount: preferences.savedLocations.length,
      error: error.message,
    });
    throw new Error('Unable to save location preferences');
  }
}

// ESLint-compliant error handling when not logging
export async function attemptLocationSync(userId: string): Promise<boolean> {
  try {
    await syncUserLocations(userId);
    return true;
  } catch (_error) {
    // Error handling is done upstream, just return status
    return false;
  }
}
```

## Important Notes

- **Function Naming**: Always use verb-based names that clearly indicate the function's purpose
- **Boolean Functions**: Use `is`, `has`, `can`, `should` prefixes for boolean return values
- **Early Returns**: Prefer guard clauses and early returns over nested if statements
- **Arrow vs Named**: Use arrow functions for simple operations (< 3 instructions), named functions for complex logic
- **Default Parameters**: Always use default parameters instead of manual null/undefined checks
- **Error Handling**: Never nest try/catch blocks unless specifically required
- **Error Context**: Always add meaningful context when catching and re-throwing errors
- **ESLint Compliance**: Use `_error` when catching errors that aren't logged or used
- **tryCatch Integration**: Prefer the tryCatch pattern for consistent error handling
- **Feature Organization**: Organize by feature domains, not technical layers
- **Vertical Slices**: Keep all related functionality (components, hooks, utils, tests) together
- **Documentation**: Include README.md files for complex feature domains
- **Higher-Order Functions**: Prefer map, filter, reduce over traditional loops
- **Type Safety**: Leverage TypeScript for better code quality and developer experience
- **Type Colocation**: Colocate types with their primary usage location, extract only when truly shared or to avoid circular dependencies
- **Component-Specific Types**: Define types within the same file as their primary consumer component
- **Shared Domain Types**: Extract types to dedicated files only when used across multiple modules or domains
- **Circular Dependencies**: Use separate type files as an exception when colocation would create circular dependencies
