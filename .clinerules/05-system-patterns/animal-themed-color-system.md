# Animal-Themed Color System Pattern

Custom Tailwind color palette with animal-themed naming convention for consistent brand colors and design system implementation.

## Key Components

1. **Animal-Named Color Families**
   ```typescript
   // Color family structure
   const colorFamilies = {
     whale: {
       100: '#f0f9ff',
       200: '#e0f2fe',
       300: '#7dd3fc',
       400: '#0ea5e9'
     },
     flamingo: {
       100: '#fef2f2',
       200: '#fecaca',
       300: '#fca5a5',
       400: '#f87171',
       500: '#ef4444',
       600: '#dc2626',
       700: '#b91c1c',
       800: '#991b1b',
       900: '#7f1d1d'
     },
     lobster: {
       100: '#fff7ed',
       200: '#ffedd5',
       300: '#fed7aa',
       400: '#fdba74',
       500: '#fb923c',
       600: '#f97316',
       700: '#ea580c',
       800: '#c2410c',
       900: '#9a3412'
     }
   };
   ```

   Each color family is named after an animal for memorable and consistent naming.

2. **Consistent Shade Structure**
   ```typescript
   // Brand colors (Whale) - 4 shades
   const brandColors = {
     whale: {
       100: 'hsl(204, 100%, 97%)',
       200: 'hsl(204, 94%, 94%)',
       300: 'hsl(199, 95%, 74%)',
       400: 'hsl(199, 89%, 48%)'
     }
   };

   // Other colors - 9 shades (100-900)
   const accentColors = {
     flamingo: {
       100: 'hsl(0, 86%, 97%)',
       200: 'hsl(0, 93%, 94%)',
       300: 'hsl(0, 96%, 89%)',
       400: 'hsl(0, 91%, 71%)',
       500: 'hsl(0, 84%, 60%)',
       600: 'hsl(0, 72%, 51%)',
       700: 'hsl(0, 74%, 42%)',
       800: 'hsl(0, 70%, 35%)',
       900: 'hsl(0, 63%, 31%)'
     }
   };
   ```

   Brand colors have 4 shades while other colors have the full 9-shade spectrum.

3. **Tailwind Integration**
   ```css
   /* Implementation using CSS custom properties in @theme block */
   @theme {
     --color-whale-100: #f0f9ff;
     --color-whale-200: #e0f2fe;
     --color-whale-300: #7dd3fc;
     --color-whale-400: #0ea5e9;

     --color-flamingo-100: #fef2f2;
     --color-flamingo-200: #fecaca;
     --color-flamingo-300: #fca5a5;
     --color-flamingo-400: #f87171;
     --color-flamingo-500: #ef4444;
     --color-flamingo-600: #dc2626;
     --color-flamingo-700: #b91c1c;
     --color-flamingo-800: #991b1b;
     --color-flamingo-900: #7f1d1d;
   }
   ```

   Colors are mapped to existing Tailwind color families to ensure only approved colors are used.

## Benefits

- **Memorable Naming**: Animal names are easier to remember than abstract color names
- **Brand Consistency**: Whale colors serve as primary brand colors with limited shades
- **Design System Integration**: Seamless integration with Tailwind CSS utilities
- **Scalable Structure**: Clear pattern for adding new color families
- **Developer Experience**: Intuitive naming convention reduces cognitive load
- **Design Constraints**: Limited palette encourages consistent color usage
- **Documentation**: Self-documenting color system with clear naming

## Example Implementation

```typescript
// Tailwind configuration with animal-themed colors
import type { Config } from 'tailwindcss';

const config: Config = {
  theme: {
    extend: {
      colors: {
        // Brand colors (primary)
        whale: {
          100: 'hsl(204, 100%, 97%)',
          200: 'hsl(204, 94%, 94%)',
          300: 'hsl(199, 95%, 74%)',
          400: 'hsl(199, 89%, 48%)'
        },
        // Accent colors
        flamingo: {
          100: 'hsl(0, 86%, 97%)',
          200: 'hsl(0, 93%, 94%)',
          300: 'hsl(0, 96%, 89%)',
          400: 'hsl(0, 91%, 71%)',
          500: 'hsl(0, 84%, 60%)',
          600: 'hsl(0, 72%, 51%)',
          700: 'hsl(0, 74%, 42%)',
          800: 'hsl(0, 70%, 35%)',
          900: 'hsl(0, 63%, 31%)'
        },
        lobster: {
          100: 'hsl(33, 100%, 96%)',
          200: 'hsl(34, 100%, 92%)',
          300: 'hsl(32, 98%, 83%)',
          400: 'hsl(31, 97%, 72%)',
          500: 'hsl(27, 96%, 61%)',
          600: 'hsl(25, 95%, 53%)',
          700: 'hsl(21, 90%, 48%)',
          800: 'hsl(17, 88%, 40%)',
          900: 'hsl(15, 79%, 34%)'
        }
      }
    }
  }
};

export default config;

// Usage in components
export const WeatherCard = () => {
  return (
    <div className="bg-whale-100 border border-whale-200 rounded-lg p-6">
      <h2 className="text-whale-400 font-semibold text-lg">Current Weather</h2>
      <div className="mt-4">
        <span className="text-flamingo-600 text-2xl font-bold">72°F</span>
        <p className="text-lobster-700 mt-2">Partly Cloudy</p>
      </div>
      <button className="mt-4 bg-whale-400 hover:bg-whale-300 text-white px-4 py-2 rounded">
        View Details
      </button>
    </div>
  );
};

// Storybook showcase component
export const ColorShowcase = () => {
  const colorFamilies = [
    { name: 'Whale (Brand)', colors: ['whale-100', 'whale-200', 'whale-300', 'whale-400'] },
    { name: 'Flamingo', colors: ['flamingo-100', 'flamingo-200', 'flamingo-300', 'flamingo-400', 'flamingo-500', 'flamingo-600', 'flamingo-700', 'flamingo-800', 'flamingo-900'] },
    { name: 'Lobster', colors: ['lobster-100', 'lobster-200', 'lobster-300', 'lobster-400', 'lobster-500', 'lobster-600', 'lobster-700', 'lobster-800', 'lobster-900'] }
  ];

  return (
    <div className="space-y-8">
      {colorFamilies.map((family) => (
        <div key={family.name}>
          <h3 className="text-lg font-semibold mb-4">{family.name}</h3>
          <div className="flex flex-wrap gap-2">
            {family.colors.map((color) => (
              <div key={color} className="text-center">
                <div className={`w-16 h-16 rounded-lg bg-${color} border border-gray-200`} />
                <p className="text-sm mt-2">{color}</p>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};
```

## Important Notes

- **Brand Color Limitation**: Whale colors intentionally have only 4 shades to maintain brand consistency
- **Naming Convention**: Use animal names that are easy to remember and pronounce
- **Tailwind Compatibility**: Colors are designed to work seamlessly with Tailwind's utility classes
- **Documentation Location**: Comprehensive documentation available in `packages/ui/src/tailwind/TAILWIND_COLORS.md`
- **Storybook Integration**: Colors are showcased in Storybook for visual reference and design system documentation
- **CSS Custom Properties**: Implementation uses modern CSS custom properties for better browser support
- **Design Constraints**: Limited palette encourages thoughtful color usage and maintains visual consistency
- **Accessibility**: Ensure sufficient contrast ratios when using color combinations
- **Extensibility**: New animal-themed color families can be added following the same pattern
