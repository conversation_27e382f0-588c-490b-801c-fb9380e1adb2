# Next.js Authentication Patterns

Authentication patterns for Next.js App Router with client-side state management and middleware protection.

## Key Components

1. **Client-Side Authentication Hook**
   ```typescript
   // Custom hook for authentication state
   'use client';

   import { useState, useEffect } from 'react';

   export function useAuth() {
     const [user, setUser] = useState(null);
     const [loading, setLoading] = useState(true);

     useEffect(() => {
       // Check authentication status
       const checkAuth = async () => {
         try {
           const response = await fetch('/api/auth/me');
           if (response.ok) {
             const userData = await response.json();
             setUser(userData);
           }
         } catch (error) {
           console.error('Auth check failed:', error);
         } finally {
           setLoading(false);
         }
       };

       checkAuth();
     }, []);

     return { user, loading, setUser };
   }
   ```

   Client-side hook for managing authentication state across components.

2. **Progressive Enhancement Pattern**
   ```typescript
   // Component that shows content immediately while auth loads
   'use client';

   function ProfileButton() {
     const { user, loading } = useAuth();

     if (loading) {
       return <ButtonSkeleton />;
     }

     if (!user) {
       return <LoginButton />;
     }

     return <UserProfileButton user={user} />;
   }
   ```

   Show appropriate UI states while authentication is being determined.

3. **Middleware Route Protection**
   ```typescript
   // middleware.ts
   import { NextRequest, NextResponse } from 'next/server';

   export function middleware(request: NextRequest) {
     const currentUser = request.cookies.get('currentUser');

     if (!currentUser && request.nextUrl.pathname.startsWith('/dashboard')) {
       return NextResponse.redirect(new URL('/login', request.url));
     }

     return NextResponse.next();
   }

   export const config = {
     matcher: [
       /*
        * Match all request paths except for the ones starting with:
        * - api (API routes)
        * - _next/static (static files)
        * - _next/image (image optimization files)
        * - favicon.ico (favicon file)
        */
       '/((?!api|_next/static|_next/image|favicon.ico).*)',
     ],
   };
   ```

   Server-side route protection using Next.js middleware.

## Benefits

- **Progressive Enhancement**: Content shows immediately while auth state loads
- **Client-Side Flexibility**: Dynamic UI based on authentication status
- **Server-Side Protection**: Middleware prevents unauthorized access
- **Performance**: Minimal impact on page load times
- **User Experience**: Smooth transitions between authenticated and unauthenticated states
- **Security**: Server-side validation and client-side convenience

## Example Implementation

```typescript
// Complete authentication system
import { NextRequest, NextResponse } from 'next/server';
import { useState, useEffect, createContext, useContext } from 'react';

// Authentication context
interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component
'use client';

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const response = await fetch('/api/auth/me');
      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
      }
    } catch (error) {
      console.error('Auth check failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const login = async (credentials: LoginCredentials) => {
    setLoading(true);
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(credentials),
      });

      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
      } else {
        throw new Error('Login failed');
      }
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
      setUser(null);
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <AuthContext.Provider value={{ user, loading, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Protected route component
'use client';

interface ProtectedRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function ProtectedRoute({ children, fallback }: ProtectedRouteProps) {
  const { user, loading } = useAuth();

  if (loading) {
    return <div className="flex justify-center p-8">Loading...</div>;
  }

  if (!user) {
    return fallback || <LoginPrompt />;
  }

  return <>{children}</>;
}

// Navigation with auth-aware components
export function Navigation() {
  const { user, loading, logout } = useAuth();

  return (
    <nav className="navigation">
      <div className="nav-brand">
        <Link href="/">Weather App</Link>
      </div>

      <div className="nav-items">
        {loading ? (
          <div className="h-8 w-24 bg-gray-200 animate-pulse rounded" />
        ) : user ? (
          <div className="user-menu">
            <span>Welcome, {user.name}</span>
            <Link href="/dashboard">Dashboard</Link>
            <button onClick={logout}>Logout</button>
          </div>
        ) : (
          <div className="auth-buttons">
            <Link href="/login">Login</Link>
            <Link href="/register">Register</Link>
          </div>
        )}
      </div>
    </nav>
  );
}

// Login component
'use client';

export function LoginForm() {
  const [credentials, setCredentials] = useState({ email: '', password: '' });
  const [error, setError] = useState('');
  const { login, loading } = useAuth();
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    try {
      await login(credentials);
      router.push('/dashboard');
    } catch (err) {
      setError('Invalid credentials');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="login-form">
      <div className="form-group">
        <label htmlFor="email">Email</label>
        <input
          id="email"
          type="email"
          value={credentials.email}
          onChange={(e) => setCredentials({ ...credentials, email: e.target.value })}
          required
        />
      </div>

      <div className="form-group">
        <label htmlFor="password">Password</label>
        <input
          id="password"
          type="password"
          value={credentials.password}
          onChange={(e) => setCredentials({ ...credentials, password: e.target.value })}
          required
        />
      </div>

      {error && <div className="error-message">{error}</div>}

      <button type="submit" disabled={loading}>
        {loading ? 'Logging in...' : 'Login'}
      </button>
    </form>
  );
}

// Middleware for route protection
export function middleware(request: NextRequest) {
  const currentUser = request.cookies.get('currentUser');
  const { pathname } = request.nextUrl;

  // Protected routes
  const protectedPaths = ['/dashboard', '/profile', '/settings'];
  const isProtectedPath = protectedPaths.some(path => pathname.startsWith(path));

  if (isProtectedPath && !currentUser) {
    return NextResponse.redirect(new URL('/login', request.url));
  }

  // Redirect authenticated users away from auth pages
  const authPaths = ['/login', '/register'];
  const isAuthPath = authPaths.some(path => pathname.startsWith(path));

  if (isAuthPath && currentUser) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  return NextResponse.next();
}
```

## Important Notes

- **Client-Side State**: Use React context or state management for auth state
- **Progressive Enhancement**: Show content immediately while determining auth status
- **Middleware Protection**: Use Next.js middleware for server-side route protection
- **Cookie Management**: Store auth tokens in secure, httpOnly cookies
- **Error Handling**: Gracefully handle authentication failures and network errors
- **Loading States**: Always provide loading indicators during auth operations
- **Redirect Logic**: Implement proper redirects for protected and public routes
- **Security**: Never store sensitive data in client-side state or localStorage
- **Performance**: Minimize auth checks and cache results appropriately
- **Accessibility**: Ensure auth UI is accessible with proper ARIA labels and keyboard navigation
