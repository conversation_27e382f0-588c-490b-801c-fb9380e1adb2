# Data Access Layer (DAL) Patterns

Domain-oriented data access patterns that abstract external API providers and provide type-safe interfaces.

## Key Components

1. **Domain-Driven Design (DDD)**
   ```typescript
   // Organization by business domains rather than API providers
   packages/dal/src/
     weather/
       getCurrentObservations.ts
       getDailyForecast.ts
     locations/
       getLocationPoint.ts
       searchLocations.ts
     content/
       getArticles.ts
       getPages.ts
   ```

   Clear boundaries between different domains (weather, content, locations) rather than technical concerns.

2. **Transformer Pattern**
   ```typescript
   // Transformation of API responses into consumer-friendly formats
   export const transformCurrentObservations = (
     apiResponse: CompactWeatherResponse
   ): CurrentObservations => {
     return {
       temperature: apiResponse.temp,
       condition: apiResponse.cond,
       humidity: apiResponse.hum,
       // Transform compact response to readable format
     };
   };
   ```

   Support for compact response transformation with type-safe transformation utilities.

3. **Repository Pattern**
   ```typescript
   // Abstraction of data sources behind domain-specific interfaces
   export interface WeatherRepository {
     getCurrentObservations(geocode: string): Promise<CurrentObservations>;
     getDailyForecast(params: ForecastParams): Promise<DailyForecast>;
   }

   // Implementation abstracts the actual API provider
   export const weatherRepository: WeatherRepository = {
     getCurrentObservations: async (geocode) => {
       const response = await sunApi.getCurrentObservations(geocode);
       return transformCurrentObservations(response);
     },
     // ...
   };
   ```

   Consistent API for data access regardless of source, with separation of data access logic from business logic.

## Benefits

- **Domain Focus**: Organization by business domains rather than technical concerns
- **Provider Abstraction**: Easy to switch between different API providers
- **Type Safety**: Strong typing throughout the data access layer
- **Consistent Interface**: Uniform API regardless of underlying data source
- **Transformation**: Clean separation between external API formats and internal data models
- **Testability**: Easy to mock and test data access patterns

## Example Implementation

```typescript
// Domain-specific DAL function
export async function getCurrentObservations({
  geocode,
  units = Units.IMPERIAL,
  language = "en-US",
}: GetCurrentObservationsParams): Promise<CurrentObservations> {
  // URL configuration colocated with domain implementation
  const url = buildSunApiUrl("/v1/current", {
    geocode,
    units,
    language,
  });

  // Fetch from external API
  const response = await fetch(url);
  const data = await response.json();

  // Transform compact API response to consumer-friendly format
  return transformCurrentObservations(data);
}

// Type-safe interfaces for all data operations
export interface GetCurrentObservationsParams {
  geocode: string;
  units?: Units;
  language?: string;
}

export interface CurrentObservations {
  temperature: number;
  condition: string;
  humidity: number;
  windSpeed: number;
  // Consumer-friendly field names
}
```

## Important Notes

- **Domain Organization**: Always organize by business domains (weather, locations, content) rather than API providers
- **Type Safety**: Ensure all interfaces are strongly typed with TypeScript
- **Transformation**: Always transform external API responses to internal, consumer-friendly formats
- **URL Colocation**: Keep URL configurations close to their domain-specific implementations
- **Error Handling**: Use consistent error handling patterns (like tryCatch) throughout the DAL
- **Provider Independence**: Design interfaces that don't leak provider-specific details
- The DAL serves as the single source of truth for data access patterns across the application
