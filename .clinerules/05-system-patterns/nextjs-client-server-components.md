# Next.js Client vs Server Components Pattern

Decision tree and patterns for choosing between client and server components in Next.js App Router.

## Key Components

1. **Server Component Usage**
   ```typescript
   // Server component for data fetching and initial rendering
   async function ProductPage({ params }: { params: { id: string } }) {
     // Server-side data fetching
     const product = await getProduct(params.id);

     return (
       <div>
         <h1>{product.name}</h1>
         <ProductDetails product={product} />
         {/* Client component for interactivity */}
         <AddToCartButton productId={product.id} />
       </div>
     );
   }
   ```

   Server components handle data fetching and initial rendering on the server.

2. **Client Component Usage**
   ```typescript
   // Client component handles interactivity
   'use client';

   function AddToCartButton({ productId }: { productId: string }) {
     const [isAdding, setIsAdding] = useState(false);

     const handleAddToCart = async () => {
       setIsAdding(true);
       await addTo<PERSON>art(productId);
       setIsAdding(false);
     };

     return (
       <button
         onClick={handleAddToCart}
         disabled={isAdding}
       >
         {isAdding ? 'Adding...' : 'Add to Cart'}
       </button>
     );
   }
   ```

   Client components handle interactivity and browser-specific functionality.

3. **Hybrid Architecture**
   ```typescript
   // Server component passes data to client components
   async function WeatherDashboard({ locationId }: { locationId: string }) {
     // Server-side data fetching
     const weatherData = await getWeatherData(locationId);
     const location = await getLocation(locationId);

     return (
       <div className="weather-dashboard">
         {/* Static content rendered on server */}
         <WeatherHeader location={location} />

         {/* Interactive client components */}
         <WeatherControls initialData={weatherData} />
         <WeatherChart data={weatherData} />

         {/* Static content with dynamic islands */}
         <WeatherSummary data={weatherData}>
           <FavoriteButton locationId={locationId} />
         </WeatherSummary>
       </div>
     );
   }
   ```

   Islands architecture with interactive client components in static server content.

## Benefits

- **Performance Optimization**: Server components reduce client bundle size
- **SEO Benefits**: Server-rendered content is immediately available to crawlers
- **Security**: Sensitive operations stay on the server
- **Flexibility**: Mix server and client components based on specific needs
- **Progressive Enhancement**: Core functionality works without JavaScript
- **Optimal Loading**: Server components can stream content progressively

## Example Implementation

```typescript
// Complete client/server component architecture
import { Suspense } from 'react';
import { WeatherControls } from '@/components/WeatherControls';
import { FavoriteButton } from '@/components/FavoriteButton';

// Server component - handles data fetching
export default async function WeatherPage({
  params
}: {
  params: { locationId: string }
}) {
  // Parallel data fetching on server
  const [weatherData, location, forecast] = await Promise.all([
    getWeatherData(params.locationId),
    getLocation(params.locationId),
    getForecast(params.locationId)
  ]);

  return (
    <div className="weather-page">
      {/* Server-rendered header */}
      <header className="weather-header">
        <h1>{location.name}</h1>
        <p>{location.region}, {location.country}</p>
      </header>

      {/* Current conditions - server rendered */}
      <section className="current-conditions">
        <div className="temperature">
          <span className="temp-value">{weatherData.temperature}°</span>
          <span className="temp-unit">F</span>
        </div>
        <p className="condition">{weatherData.condition}</p>

        {/* Interactive favorite button - client component */}
        <Suspense fallback={<div className="h-8 w-8 bg-gray-200 rounded" />}>
          <FavoriteButton locationId={params.locationId} />
        </Suspense>
      </section>

      {/* Interactive controls - client component */}
      <Suspense fallback={<div className="h-12 bg-gray-100 rounded" />}>
        <WeatherControls
          initialLocation={location}
          initialWeather={weatherData}
        />
      </Suspense>

      {/* Forecast - server rendered with client interactions */}
      <section className="forecast">
        <h2>7-Day Forecast</h2>
        <div className="forecast-grid">
          {forecast.days.map((day) => (
            <div key={day.date} className="forecast-day">
              <span className="day-name">{day.dayName}</span>
              <span className="high-temp">{day.high}°</span>
              <span className="low-temp">{day.low}°</span>

              {/* Interactive details toggle - client component */}
              <ForecastDetails day={day} />
            </div>
          ))}
        </div>
      </section>
    </div>
  );
}

// Client component for interactive controls
'use client';

import { useState } from 'react';

interface WeatherControlsProps {
  initialLocation: Location;
  initialWeather: WeatherData;
}

export function WeatherControls({
  initialLocation,
  initialWeather
}: WeatherControlsProps) {
  const [units, setUnits] = useState<'F' | 'C'>('F');
  const [view, setView] = useState<'current' | 'hourly' | 'daily'>('current');

  return (
    <div className="weather-controls">
      {/* Unit toggle */}
      <div className="unit-toggle">
        <button
          onClick={() => setUnits('F')}
          className={units === 'F' ? 'active' : ''}
        >
          °F
        </button>
        <button
          onClick={() => setUnits('C')}
          className={units === 'C' ? 'active' : ''}
        >
          °C
        </button>
      </div>

      {/* View toggle */}
      <div className="view-toggle">
        <button
          onClick={() => setView('current')}
          className={view === 'current' ? 'active' : ''}
        >
          Current
        </button>
        <button
          onClick={() => setView('hourly')}
          className={view === 'hourly' ? 'active' : ''}
        >
          Hourly
        </button>
        <button
          onClick={() => setView('daily')}
          className={view === 'daily' ? 'active' : ''}
        >
          Daily
        </button>
      </div>
    </div>
  );
}

// Client component for favorite functionality
'use client';

import { useState, useTransition } from 'react';
import { toggleFavorite } from '@/actions/favorites';

export function FavoriteButton({ locationId }: { locationId: string }) {
  const [isFavorite, setIsFavorite] = useState(false);
  const [isPending, startTransition] = useTransition();

  const handleToggle = () => {
    startTransition(async () => {
      const result = await toggleFavorite(locationId);
      setIsFavorite(result.isFavorite);
    });
  };

  return (
    <button
      onClick={handleToggle}
      disabled={isPending}
      className={`favorite-btn ${isFavorite ? 'favorited' : ''}`}
    >
      {isPending ? '...' : isFavorite ? '★' : '☆'}
    </button>
  );
}
```

## Important Notes

### Use Server Components When:
- **Data Fetching**: Fetching data directly from database or API
- **Backend Resources**: Accessing server-only resources
- **Security**: Keeping sensitive information on server
- **Performance**: Large dependencies that should stay server-side
- **SEO**: Content that needs to be immediately available to crawlers

### Use Client Components When:
- **Interactivity**: Adding event listeners and user interactions
- **React Hooks**: Using useState, useEffect, or other hooks
- **Browser APIs**: Accessing browser-only APIs
- **State Management**: Managing component state
- **Real-time Updates**: WebSocket connections or real-time features

### Hybrid Approach Best Practices:
- **Islands Architecture**: Interactive client components in static server content
- **Data Passing**: Pass server data to client components as props
- **Progressive Enhancement**: Ensure core functionality works without JavaScript
- **Suspense Boundaries**: Use Suspense for client component loading states
- **Error Boundaries**: Implement proper error handling for both component types

### Performance Considerations:
- **Bundle Size**: Server components don't increase client bundle size
- **Hydration**: Minimize client components to reduce hydration overhead
- **Streaming**: Server components can stream content progressively
- **Caching**: Server components benefit from server-side caching strategies
