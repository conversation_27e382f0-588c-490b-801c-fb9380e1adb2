# Semantic Typography System Pattern

Structured typography system with semantic naming conventions for consistent text styling across the application.

## Key Components

1. **Semantic Text Categories**
   ```typescript
   // Typography categories with semantic meaning
   const textCategories = {
     display: {
       xl: { fontSize: '4rem', lineHeight: '1.1', letterSpacing: '-0.02em' },
       l: { fontSize: '3rem', lineHeight: '1.2', letterSpacing: '-0.01em' },
       m: { fontSize: '2.5rem', lineHeight: '1.2', letterSpacing: '0' },
       s: { fontSize: '2rem', lineHeight: '1.3', letterSpacing: '0' }
     },
     title: {
       xl: { fontSize: '1.875rem', lineHeight: '1.3', letterSpacing: '0' },
       l: { fontSize: '1.5rem', lineHeight: '1.4', letterSpacing: '0' },
       m: { fontSize: '1.25rem', lineHeight: '1.4', letterSpacing: '0' },
       s: { fontSize: '1.125rem', lineHeight: '1.5', letterSpacing: '0' }
     },
     body: {
       xl: { fontSize: '1.125rem', lineHeight: '1.6', letterSpacing: '0' },
       l: { fontSize: '1rem', lineHeight: '1.6', letterSpacing: '0' },
       m: { fontSize: '0.875rem', lineHeight: '1.5', letterSpacing: '0' },
       s: { fontSize: '0.75rem', lineHeight: '1.5', letterSpacing: '0' }
     },
     caption: {
       xl: { fontSize: '0.875rem', lineHeight: '1.4', letterSpacing: '0.01em' },
       l: { fontSize: '0.75rem', lineHeight: '1.4', letterSpacing: '0.01em' },
       m: { fontSize: '0.6875rem', lineHeight: '1.3', letterSpacing: '0.02em' },
       s: { fontSize: '0.625rem', lineHeight: '1.3', letterSpacing: '0.02em' }
     }
   };
   ```

   Text styles organized into semantic categories with consistent size variants.

2. **CSS Custom Properties Implementation**
   ```css
   /* Typography system using CSS custom properties */
   @theme {
     /* Display variants */
     --font-size-display-xl: 4rem;
     --line-height-display-xl: 1.1;
     --letter-spacing-display-xl: -0.02em;

     --font-size-display-l: 3rem;
     --line-height-display-l: 1.2;
     --letter-spacing-display-l: -0.01em;

     /* Title variants */
     --font-size-title-xl: 1.875rem;
     --line-height-title-xl: 1.3;
     --letter-spacing-title-xl: 0;

     /* Body variants */
     --font-size-body-l: 1rem;
     --line-height-body-l: 1.6;
     --letter-spacing-body-l: 0;

     /* Caption variants */
     --font-size-caption-m: 0.6875rem;
     --line-height-caption-m: 1.3;
     --letter-spacing-caption-m: 0.02em;
   }
   ```

   Implementation using CSS custom properties for consistent scaling.

3. **Text Component with TypeScript Support**
   ```typescript
   // Type-safe Text component
   interface TextProps {
     variant?: 'display' | 'title' | 'body' | 'caption';
     size?: 'xl' | 'l' | 'm' | 's';
     as?: keyof JSX.IntrinsicElements;
     className?: string;
     children: React.ReactNode;
   }

   export const Text: React.FC<TextProps> = ({
     variant = 'body',
     size = 'm',
     as: Component = 'p',
     className,
     children,
     ...props
   }) => {
     const textClass = `text-${variant}-${size}`;

     return (
       <Component
         className={cn(textClass, className)}
         {...props}
       >
         {children}
       </Component>
     );
   };
   ```

   Flexible Text component with TypeScript support for type-safe variant usage.

## Benefits

- **Semantic Clarity**: Text styles have clear semantic meaning (Display, Title, Body, Caption)
- **Consistent Scaling**: Standardized scales for font sizes, line heights, and letter spacing
- **Type Safety**: TypeScript support prevents invalid variant combinations
- **Accessibility**: Default HTML elements mapped to semantic variants
- **Maintainability**: Centralized typography system easy to update and extend
- **Design System Integration**: Seamless integration with Tailwind utility classes
- **Responsive Design**: Consistent typography across all device sizes

## Example Implementation

```typescript
// Complete typography system implementation
import React from 'react';
import { cn } from '@/lib/utils';

// Typography variant types
type TextVariant = 'display' | 'title' | 'body' | 'caption';
type TextSize = 'xl' | 'l' | 'm' | 's';

interface TextProps extends React.HTMLAttributes<HTMLElement> {
  variant?: TextVariant;
  size?: TextSize;
  as?: keyof JSX.IntrinsicElements;
  className?: string;
  children: React.ReactNode;
}

// Default HTML element mapping for semantic variants
const defaultElements: Record<TextVariant, keyof JSX.IntrinsicElements> = {
  display: 'h1',
  title: 'h2',
  body: 'p',
  caption: 'span'
};

export const Text: React.FC<TextProps> = ({
  variant = 'body',
  size = 'm',
  as,
  className,
  children,
  ...props
}) => {
  const Component = as || defaultElements[variant];
  const textClass = `text-${variant}-${size}`;

  return (
    <Component
      className={cn(textClass, className)}
      {...props}
    >
      {children}
    </Component>
  );
};

// Usage examples in components
export const TypographyExamples: React.FC = () => {
  return (
    <div className="space-y-8">
      {/* Display text for hero sections */}
      <Text variant="display" size="xl">
        Weather Dashboard
      </Text>

      {/* Title text for section headers */}
      <Text variant="title" size="l">
        Current Conditions
      </Text>

      {/* Body text for content */}
      <Text variant="body" size="l">
        Stay informed with accurate weather forecasts and real-time conditions
        for your location and favorite places around the world.
      </Text>

      {/* Caption text for metadata */}
      <Text variant="caption" size="m" className="text-gray-600">
        Last updated 5 minutes ago
      </Text>
    </div>
  );
};

// Weather-specific typography components
export const WeatherDisplay: React.FC<{ temperature: number; condition: string }> = ({
  temperature,
  condition
}) => {
  return (
    <div className="weather-display">
      <Text variant="display" size="l" className="text-whale-400">
        {temperature}°
      </Text>
      <Text variant="title" size="m" className="text-gray-700">
        {condition}
      </Text>
    </div>
  );
};

// Responsive typography with breakpoint variants
export const ResponsiveHeading: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <Text
      variant="display"
      size="m"
      className="md:text-display-l lg:text-display-xl"
    >
      {children}
    </Text>
  );
};

// Tailwind configuration for typography system
const typographyConfig = {
  theme: {
    extend: {
      fontSize: {
        // Display variants
        'display-xl': ['4rem', { lineHeight: '1.1', letterSpacing: '-0.02em' }],
        'display-l': ['3rem', { lineHeight: '1.2', letterSpacing: '-0.01em' }],
        'display-m': ['2.5rem', { lineHeight: '1.2', letterSpacing: '0' }],
        'display-s': ['2rem', { lineHeight: '1.3', letterSpacing: '0' }],

        // Title variants
        'title-xl': ['1.875rem', { lineHeight: '1.3', letterSpacing: '0' }],
        'title-l': ['1.5rem', { lineHeight: '1.4', letterSpacing: '0' }],
        'title-m': ['1.25rem', { lineHeight: '1.4', letterSpacing: '0' }],
        'title-s': ['1.125rem', { lineHeight: '1.5', letterSpacing: '0' }],

        // Body variants
        'body-xl': ['1.125rem', { lineHeight: '1.6', letterSpacing: '0' }],
        'body-l': ['1rem', { lineHeight: '1.6', letterSpacing: '0' }],
        'body-m': ['0.875rem', { lineHeight: '1.5', letterSpacing: '0' }],
        'body-s': ['0.75rem', { lineHeight: '1.5', letterSpacing: '0' }],

        // Caption variants
        'caption-xl': ['0.875rem', { lineHeight: '1.4', letterSpacing: '0.01em' }],
        'caption-l': ['0.75rem', { lineHeight: '1.4', letterSpacing: '0.01em' }],
        'caption-m': ['0.6875rem', { lineHeight: '1.3', letterSpacing: '0.02em' }],
        'caption-s': ['0.625rem', { lineHeight: '1.3', letterSpacing: '0.02em' }]
      }
    }
  }
};
```

## Important Notes

- **Semantic HTML**: Default elements are mapped to semantic variants for better accessibility
- **Consistent Scales**: Font sizes, line heights, and letter spacing follow consistent mathematical scales
- **TypeScript Integration**: Strong typing prevents invalid variant combinations
- **Tailwind Utilities**: Typography system integrates seamlessly with Tailwind CSS utilities
- **Documentation Location**: Comprehensive documentation available in `packages/ui/src/tailwind/TAILWIND_TYPOGRAPHY.md`
- **Responsive Design**: Typography scales appropriately across different screen sizes
- **Brand Consistency**: Typography system maintains consistent visual hierarchy
- **Extensibility**: New variants can be added following the established pattern
- **Performance**: CSS custom properties enable efficient styling with minimal bundle impact
- Always use the Text component instead of raw HTML elements for consistent typography
