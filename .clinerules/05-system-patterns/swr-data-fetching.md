# SWR Data Fetching Pattern

Client-side data fetching pattern using SWR for automatic revalidation, caching, and consistent state management.

## Key Components

1. **SWR Hook Usage**
   ```typescript
   // Basic SWR usage with DAL function
   const { data, error, isLoading } = useSWR(
     ["daily-forecast", location.geocode],
     forecastFetcher,
     {
       revalidateOnFocus: false,
       dedupingInterval: 60000, // 1 minute
     }
   );

   // The fetcher function that calls the DAL
   const forecastFetcher = () =>
     getDailyForecast({
       geocode: location.geocode,
       units: Units.IMPERIAL,
       language: "en-US",
       duration: ForecastDuration.THREE_DAY,
     });
   ```

   SWR provides automatic caching, revalidation, and state management for data fetching.

2. **Conditional Data Fetching**
   ```typescript
   // Only fetch when location is available
   const { data: weatherData, error, isLoading } = useSWR(
     effectiveLocation ? ['weather', effectiveLocation.geocode] : null,
     () => getCurrentObservations({
       geocode: effectiveLocation.geocode,
       units: Units.IMPERIAL,
       language: "en-US",
     }),
     {
       revalidateOnFocus: false,
       dedupingInterval: 60000,
     }
   );
   ```

   Use conditional keys to prevent fetching when dependencies are not available.

3. **Multiple Data Sources**
   ```typescript
   // Fetch multiple data sources with separate SWR calls
   const { data: currentData, error: currentError, isLoading: currentLoading } = useSWR(
     location ? ['current', location.geocode] : null,
     () => getCurrentObservations({ geocode: location.geocode })
   );

   const { data: forecastData, error: forecastError, isLoading: forecastLoading } = useSWR(
     location ? ['forecast', location.geocode] : null,
     () => getDailyForecast({ geocode: location.geocode })
   );

   // Combine loading and error states
   const isLoading = currentLoading || forecastLoading;
   const error = currentError || forecastError;
   ```

   Handle multiple data sources with separate SWR calls for better caching granularity.

## Benefits

- **Automatic Revalidation**: Data refreshes automatically based on configured triggers
- **Caching**: Prevents redundant API calls for the same data
- **Loading States**: Built-in isLoading state for UI feedback
- **Error Handling**: Standardized error handling across components
- **Deduplication**: Prevents multiple requests for the same data during rendering
- **Background Updates**: Keeps data fresh with background revalidation
- **Offline Support**: Works with browser cache when offline

## Example Implementation

```typescript
// Weather component with SWR data fetching
export const WeatherDisplay: React.FC<WeatherDisplayProps> = ({ location }) => {
  // Location hook for effective location
  const { effectiveLocation, isLocationLoading } = useLocationSource({
    location,
  });

  // Current conditions with SWR
  const {
    data: currentConditions,
    error: currentError,
    isLoading: currentLoading
  } = useSWR(
    effectiveLocation ? ['current-conditions', effectiveLocation.geocode] : null,
    () => getCurrentObservations({
      geocode: effectiveLocation.geocode,
      units: Units.IMPERIAL,
      language: "en-US",
    }),
    {
      revalidateOnFocus: false,
      dedupingInterval: 300000, // 5 minutes
      errorRetryCount: 3,
      errorRetryInterval: 5000,
    }
  );

  // Daily forecast with SWR
  const {
    data: dailyForecast,
    error: forecastError,
    isLoading: forecastLoading
  } = useSWR(
    effectiveLocation ? ['daily-forecast', effectiveLocation.geocode] : null,
    () => getDailyForecast({
      geocode: effectiveLocation.geocode,
      units: Units.IMPERIAL,
      language: "en-US",
      duration: ForecastDuration.SEVEN_DAY,
    }),
    {
      revalidateOnFocus: false,
      dedupingInterval: 600000, // 10 minutes
      errorRetryCount: 2,
    }
  );

  // Combine loading states
  const isLoading = isLocationLoading || currentLoading || forecastLoading;
  const error = currentError || forecastError;

  // Handle loading state
  if (isLoading) {
    return <WeatherSkeleton />;
  }

  // Handle error state
  if (error || !currentConditions) {
    return (
      <WeatherError
        message={error?.message || "Unable to load weather data"}
        onRetry={() => {
          // SWR mutate to retry
          mutate(['current-conditions', effectiveLocation?.geocode]);
          mutate(['daily-forecast', effectiveLocation?.geocode]);
        }}
      />
    );
  }

  return (
    <div className="weather-display">
      <CurrentConditionsDisplay data={currentConditions} />
      {dailyForecast && (
        <DailyForecastDisplay data={dailyForecast} />
      )}
    </div>
  );
};

// Custom hook for weather data
export function useWeatherData(location?: LocationData) {
  const { effectiveLocation, isLocationLoading } = useLocationSource({
    location,
  });

  const currentConditions = useSWR(
    effectiveLocation ? ['current', effectiveLocation.geocode] : null,
    () => getCurrentObservations({
      geocode: effectiveLocation.geocode,
      units: Units.IMPERIAL,
      language: "en-US",
    }),
    {
      revalidateOnFocus: false,
      dedupingInterval: 300000,
    }
  );

  const dailyForecast = useSWR(
    effectiveLocation ? ['forecast', effectiveLocation.geocode] : null,
    () => getDailyForecast({
      geocode: effectiveLocation.geocode,
      units: Units.IMPERIAL,
      language: "en-US",
    }),
    {
      revalidateOnFocus: false,
      dedupingInterval: 600000,
    }
  );

  return {
    location: effectiveLocation,
    isLocationLoading,
    currentConditions: currentConditions.data,
    dailyForecast: dailyForecast.data,
    isLoading: isLocationLoading || currentConditions.isLoading || dailyForecast.isLoading,
    error: currentConditions.error || dailyForecast.error,
  };
}
```

## Important Notes

- **Never call DAL functions directly in useEffect**: Always use SWR for DAL function calls in client components
- **Use conditional keys**: Prevent unnecessary requests by using conditional SWR keys
- **Configure appropriately**: Set revalidation and caching options based on data freshness requirements
- **Handle loading states**: Always provide loading UI while data is being fetched
- **Error handling**: Implement proper error handling with retry mechanisms
- **Key consistency**: Use consistent key patterns across components for better cache sharing
- **Deduplication intervals**: Set appropriate deduplication intervals to prevent excessive requests
- **Background revalidation**: Leverage SWR's background revalidation for fresh data
- This pattern replaces direct tryCatch usage in client components for data fetching
