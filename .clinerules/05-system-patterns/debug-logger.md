# Debug Logger Pattern

Standardized logging pattern using industry-standard debug-js library through @repo/logger package for consistent, environment-aware logging across the application.

## Key Components

1. **@repo/logger Package**
   ```typescript
   // packages/logger/src/index.ts
   import debug from 'debug';

   export interface Logger {
     (message: string, ...args: any[]): void;
     info: (message: string, ...args: any[]) => void;
     warn: (message: string, ...args: any[]) => void;
     error: (message: string, ...args: any[]) => void;
     lifecycle: (message: string, ...args: any[]) => void;
   }

   export function createLogger(namespace: string): Logger {
     const baseLogger = debug(`wx-next:${namespace}`);
     const infoLogger = debug(`wx-next:${namespace}:info`);
     const warnLogger = debug(`wx-next:${namespace}:warn`);
     const errorLogger = debug(`wx-next:${namespace}:error`);
     const lifecycleLogger = debug(`wx-next:${namespace}:lifecycle`);

     const logger = baseLogger as Logger;
     logger.info = infoLogger;
     logger.warn = warnLogger;
     logger.error = errorLogger;
     logger.lifecycle = lifecycleLogger;

     return logger;
   }
   ```

   Centralized logging package using debug-js with namespace-based organization.

2. **Standard Usage Pattern**
   ```typescript
   // Standard logging pattern using @repo/logger
   import { createLogger } from '@repo/logger';

   const logger = createLogger('ComponentName');

   logger.info('Message', data);
   logger.warn('Warning message', data);
   logger.error('Error occurred', error);
   logger.lifecycle('Component mounted', data);
   ```

   Consistent namespace-based logging pattern across all components.

3. **Environment Control**
   ```bash
   # Enable all logging
   DEBUG=wx-next:* pnpm dev

   # Enable specific log levels
   DEBUG=wx-next:*:error pnpm dev
   DEBUG=wx-next:*:info pnpm dev
   DEBUG=wx-next:*:lifecycle pnpm dev

   # Enable specific components
   DEBUG=wx-next:DataCollector:* pnpm dev
   DEBUG=wx-next:AtomDebugHydrationBoundaries:* pnpm dev

   # Combine patterns
   DEBUG=wx-next:*:error,wx-next:DataCollector:* pnpm dev
   ```

   Flexible environment variable control for different logging scenarios.

## Benefits

- **Industry Standard**: Uses widely-adopted debug-js library with proven performance
- **Environment Aware**: Automatic production filtering and development control
- **Namespace Organization**: Clear component-based namespace hierarchy
- **Performance**: Zero-cost logging in production when DEBUG is not set
- **Flexibility**: Granular control over log levels and components
- **Consistency**: Standardized logging patterns across the entire application
- **Maintainability**: Centralized logging configuration and behavior

## Example Implementation

```typescript
// Complete implementation example
import { createLogger } from '@repo/logger';

// Component-specific logger with clear namespace
const logger = createLogger('WeatherComponent');

export const WeatherComponent: React.FC<WeatherProps> = ({ location }) => {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    logger.lifecycle('Component mounting', { location: location?.displayName });

    const fetchData = async () => {
      logger.info('Fetching weather data', { geocode: location.geocode });

      try {
        const result = await getCurrentObservations({
          geocode: location.geocode,
          units: Units.IMPERIAL,
        });

        logger.info('Weather data received', {
          temperature: result.temperature,
          condition: result.condition,
        });

        setData(result);
      } catch (err) {
        logger.error('Failed to fetch weather data', {
          error: err.message,
          geocode: location.geocode,
        });
        setError(err);
      }
    };

    fetchData();

    return () => {
      logger.lifecycle('Component unmounting', { location: location?.displayName });
    };
  }, [location]);

  if (error) {
    logger.warn('Rendering error state', { error: error.message });
    return <ErrorDisplay error={error} />;
  }

  if (!data) {
    logger.info('Rendering loading state');
    return <LoadingSpinner />;
  }

  logger.info('Rendering weather data', { temperature: data.temperature });
  return <WeatherDisplay data={data} />;
};

// Usage in different environments
// Development: DEBUG=wx-next:WeatherComponent:* pnpm dev
// Production: Logs automatically disabled unless DEBUG is set
// Error-only: DEBUG=wx-next:*:error pnpm dev
```

## Important Notes

- **Namespace Convention**: Use PascalCase component names for consistency
- **Log Level Usage**:
  - `info`: General information and data flow
  - `warn`: Non-critical issues and fallback scenarios
  - `error`: Actual errors and failures
  - `lifecycle`: Component mounting, unmounting, and major state changes
- **Data Logging**: Always include relevant context data with log messages
- **Production Safety**: Debug-js automatically disables logs in production unless DEBUG is explicitly set
- **Performance**: Zero runtime cost when logging is disabled
- **Implementation Strategy**: Use consistent logger creation and call patterns throughout the application
- **Environment Variables**: Use DEBUG environment variable for flexible log control
- **Namespace Hierarchy**: Follow `wx-next:ComponentName:level` pattern for consistency

## Implementation Checklist

For implementing the debug logger pattern:

1. **Replace Import**:
   ```typescript
   // Remove
   import { debugLogger } from '../utils/debugLogger';

   // Add
   import { createLogger } from '@repo/logger';
   ```

2. **Create Logger Instance**:
   ```typescript
   const logger = createLogger('ComponentName');
   ```

3. **Update All Calls**:
   ```typescript
   // OLD: debugLogger.info('ComponentName', 'Message', data);
   // NEW: logger.info('Message', data);
   ```

4. **Test Environment Control**:
   ```bash
   DEBUG=wx-next:ComponentName:* pnpm dev
   ```

5. **Verify Production Behavior**: Ensure no logs appear without DEBUG variable

This pattern provides a systematic approach to implementing industry-standard debug-js logging while maintaining consistency and improving developer experience.
