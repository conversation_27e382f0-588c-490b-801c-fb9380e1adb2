# Icon System Pattern

Two-pronged approach for icons: weather-specific dynamic icons and general UI static icons with direct imports.

## Key Components

1. **Weather-Specific Icons (WxIcon)**
   ```typescript
   // For dynamic weather condition icons
   import { WxIcon } from '@repo/icons/WxIcon';
   import { NoData } from '@repo/icons/Data';

   // Basic usage with icon code
   <WxIcon iconCode={32} size="lg" />

   // With alert extension
   <WxIcon
     iconCode={32}
     iconCodeExtend={alertCode}
     size="md"
     iconTheme="light"
     className="weather-icon"
     title="Sunny weather"
     desc="Clear skies with sunshine"
   />

   // Fallback for missing data
   {iconCode ? (
     <WxIcon iconCode={iconCode} size="lg" />
   ) : (
     <NoData className="h-8 w-8" />
   )}
   ```

   Dynamic weather icons that render based on condition codes and alert states.

2. **General UI Icons (Direct Import)**
   ```typescript
   // Import specific icons directly from categories
   import { UserAvatar } from '@repo/icons/Avatar';
   import { ChevronRight } from '@repo/icons/Navigation';
   import { Settings } from '@repo/icons/Interface';

   // Standard SVGR component usage
   <UserAvatar className="h-8 w-8" />
   <ChevronRight width={16} height={16} title="Navigate forward" />
   <Settings
     className="text-gray-600 hover:text-gray-800"
     desc="Open settings menu"
   />
   ```

   Static UI icons imported directly as React components from organized categories.

3. **Icon Categories and Organization**
   ```typescript
   // Organized by functional categories
   repo/icons/components/
     Avatar/
       UserAvatar.tsx
       GroupAvatar.tsx
     Navigation/
       ChevronRight.tsx
       ChevronLeft.tsx
       ArrowUp.tsx
     Interface/
       Settings.tsx
       Search.tsx
       Close.tsx
     Data/
       NoData.tsx
       Loading.tsx
       Error.tsx
   ```

   Clear categorization makes icons easy to find and import.

## Benefits

- **Clear Separation**: Weather icons are dynamic, UI icons are static
- **Type Safety**: Direct imports provide better TypeScript support
- **Performance**: Only imports the specific icons needed
- **Maintainability**: Organized categories make icons easy to find
- **Flexibility**: Weather icons support themes, sizes, and alert states
- **Accessibility**: Built-in support for titles and descriptions
- **Bundle Optimization**: Tree-shaking eliminates unused icons

## Example Implementation

```typescript
// Weather component using dynamic weather icons
import { WxIcon } from '@repo/icons/WxIcon';
import { NoData } from '@repo/icons/Data';

interface WeatherDisplayProps {
  iconCode?: number;
  alertCode?: number;
  condition: string;
  temperature: number;
}

export const WeatherDisplay: React.FC<WeatherDisplayProps> = ({
  iconCode,
  alertCode,
  condition,
  temperature,
}) => {
  return (
    <div className="weather-display">
      <div className="weather-icon">
        {iconCode ? (
          <WxIcon
            iconCode={iconCode}
            iconCodeExtend={alertCode}
            size="xl"
            iconTheme="auto"
            title={condition}
            desc={`Weather condition: ${condition}`}
          />
        ) : (
          <NoData
            className="h-16 w-16 text-gray-400"
            title="No weather data"
            desc="Weather information unavailable"
          />
        )}
      </div>
      <div className="weather-info">
        <span className="temperature">{temperature}°</span>
        <span className="condition">{condition}</span>
      </div>
    </div>
  );
};

// Navigation component using static UI icons
import { ChevronLeft } from '@repo/icons/Navigation';
import { ChevronRight } from '@repo/icons/Navigation';
import { Settings } from '@repo/icons/Interface';

export const NavigationHeader: React.FC = () => {
  return (
    <header className="navigation-header">
      <button className="nav-button">
        <ChevronLeft className="h-5 w-5" title="Go back" />
      </button>

      <h1 className="page-title">Weather Dashboard</h1>

      <div className="header-actions">
        <button className="nav-button">
          <Settings
            className="h-5 w-5 text-gray-600 hover:text-gray-800"
            title="Open settings"
          />
        </button>
        <button className="nav-button">
          <ChevronRight className="h-5 w-5" title="Next page" />
        </button>
      </div>
    </header>
  );
};

// Icon usage in different contexts
export const IconExamples: React.FC = () => {
  return (
    <div className="icon-examples">
      {/* Weather Icons - Dynamic */}
      <section>
        <h3>Weather Icons</h3>
        <div className="weather-icons">
          <WxIcon iconCode={32} size="sm" /> {/* Sunny */}
          <WxIcon iconCode={26} size="md" /> {/* Cloudy */}
          <WxIcon iconCode={12} size="lg" /> {/* Rainy */}
          <WxIcon iconCode={16} size="xl" /> {/* Snowy */}
        </div>
      </section>

      {/* UI Icons - Static */}
      <section>
        <h3>UI Icons</h3>
        <div className="ui-icons">
          <UserAvatar className="h-6 w-6" />
          <Settings className="h-6 w-6" />
          <ChevronRight className="h-6 w-6" />
          <NoData className="h-6 w-6" />
        </div>
      </section>
    </div>
  );
};

// Custom hook for weather icon logic
export function useWeatherIcon(iconCode?: number, alertCode?: number) {
  const hasIcon = iconCode !== undefined && iconCode !== null;
  const hasAlert = alertCode !== undefined && alertCode !== null;

  return {
    hasIcon,
    hasAlert,
    iconProps: hasIcon ? {
      iconCode,
      iconCodeExtend: hasAlert ? alertCode : undefined,
      size: 'lg' as const,
      iconTheme: 'auto' as const,
    } : null,
  };
}
```

## Important Notes

- **Weather Icons**: Use `<WxIcon />` for all dynamic weather condition icons
- **UI Icons**: Import specific components directly for static interface icons
- **No Dynamic Loading**: Avoid previous dynamic SVG loading utilities
- **Accessibility**: Always provide `title` and `desc` props for screen readers
- **Performance**: Direct imports enable better tree-shaking and bundle optimization
- **Categories**: Icons are organized by function (Avatar, Navigation, Interface, Data)
- **Fallbacks**: Use `<NoData />` component when weather data is unavailable
- **Theme Support**: Weather icons support light/dark/auto themes
- **Size Variants**: Weather icons have predefined size variants (sm, md, lg, xl)
- Comprehensive documentation available in `docs/svg-loading-options.md` and examples in `apps/web/app/(web)/test/icons/page.tsx`
