# Custom Routing Middleware Pattern

Custom routing system to overcome Next.js's 1024 rewrite limit with pre-compiled patterns and optional Bloom filter optimization.

## Key Components

1. **Custom Router Implementation**
   ```typescript
   // Compile rewrite rules once at module load time
   const compiledRewrites = compileRewrites(afterFiles as RewriteRule[]);

   export default async function edgeMiddleware(request: NextRequest) {
     const pathForRouter = `/${code}${originalPathname}`;

     // Find matching rule using compiled patterns
     const customMatch = findMatch(pathForRouter, compiledRewrites, request);

     if (customMatch) {
       const { rule, params } = customMatch;
       const finalParams = { code, ...params };
       const rewrittenPathAndQuery = applyParams(
         rule.destination,
         finalParams,
         originalSearch,
       );
       rewrittenUrl = new URL(rewrittenPathAndQuery, request.url);
     } else {
       // Fallback to default routing
       rewrittenUrl = new URL(
         `/${code}${originalPathname}${originalSearch}`,
         request.url,
       );
     }

     return NextResponse.rewrite(rewrittenUrl, { request });
   }
   ```

   The custom routing middleware replaces Next.js built-in rewrites with a scalable solution.

2. **Rule Compilation System**
   ```typescript
   // Rule Compilation (compileRewrites.ts)
   export function compileRewrites(rules: RewriteRule[]): CompiledRewrite[] {
     return rules.map(rule => ({
       ...rule,
       matcher: pathToRegexp(rule.source, [], {
         sensitive: false,
         strict: false
       }),
       keys: [],
     }));
   }

   // Pattern Matching (findMatch.ts)
   export function findMatch(
     path: string,
     compiledRules: CompiledRewrite[],
     request: NextRequest
   ): MatchResult | null {
     for (const rule of compiledRules) {
       const match = rule.matcher.exec(path);
       if (match && checkConditions(rule, request)) {
         return { rule, params: extractParams(match, rule.keys) };
       }
     }
     return null;
   }
   ```

   Converts rewrite rules into optimized matcher functions with pre-compiled regex patterns.

3. **Parameter Application**
   ```typescript
   // Parameter Application (applyParams.ts)
   export function applyParams(
     destination: string,
     params: Record<string, string>,
     search: string
   ): string {
     let result = destination;

     // Apply parameter substitution
     for (const [key, value] of Object.entries(params)) {
       result = result.replace(`:${key}`, encodeURIComponent(value));
     }

     // Handle query string construction
     if (search) {
       const separator = result.includes('?') ? '&' : '?';
       result += separator + search.slice(1);
     }

     return result;
   }

   // Condition Checking (checkConditions.ts)
   export function checkConditions(
     rule: RewriteRule,
     request: NextRequest
   ): boolean {
     // Validate 'has' and 'missing' conditions
     // Supports header, cookie, query, and host-based conditions
     return validateHasConditions(rule.has, request) &&
            validateMissingConditions(rule.missing, request);
   }
   ```

   Applies extracted parameters to destination templates and validates conditions.

## Benefits

- **Unlimited Rules**: No longer constrained by Next.js's 1024 rewrite limit
- **Better Performance**: Pre-compiled patterns and optional Bloom filter optimization
- **Complex Patterns**: Support for advanced regex patterns and conditional routing
- **Type Safety**: Full TypeScript support with proper type definitions
- **Maintainability**: Clear separation of concerns and comprehensive documentation
- **Testing**: Comprehensive test suite ensures reliability

## Example Implementation

```typescript
// Rewrite Rule Format
interface RewriteRule {
  source: string;
  destination: string;
  has?: Array<{
    type: 'header' | 'cookie' | 'host' | 'query';
    key: string;
    value?: string;
  }>;
  missing?: Array<{
    type: 'header' | 'cookie' | 'host' | 'query';
    key: string;
    value?: string;
  }>;
}

// Pattern Examples
const rewriteRules: RewriteRule[] = [
  // Complex path with multiple parameters
  {
    source: '/:code/:locale([a-z]{2}-[A-Z]{2})/:year(\\d{4})/:month(\\d{2})/:day(\\d{2})/:collectionId*/:slug',
    destination: '/:code/content/payload/byAssetName/:locale/:year/:month/:day/:collectionId*/:slug'
  },

  // Simple parameter extraction
  {
    source: '/:code/weather/today/l/:locId',
    destination: '/:code/weather/today/byLocId/:locId'
  },

  // Conditional routing with headers
  {
    source: '/:code/:locale([a-z]{2}-[A-Z]{2})/pages/:assetName*',
    destination: '/:code/pages/byAssetName/:locale/:assetName',
    has: [{ type: 'header', key: 'x-custom-header', value: 'required-value' }]
  }
];

// Complete middleware implementation
import { NextRequest, NextResponse } from 'next/server';
import { compileRewrites } from './utils/compileRewrites';
import { findMatch } from './utils/findMatch';
import { applyParams } from './utils/applyParams';

// Pre-compile rules at module load time
const compiledRules = compileRewrites(rewriteRules);

export default async function middleware(request: NextRequest) {
  const { pathname, search } = request.nextUrl;

  // Extract code from hostname or path
  const code = extractCode(request);
  const pathForRouter = `/${code}${pathname}`;

  // Find matching rule
  const match = findMatch(pathForRouter, compiledRules, request);

  let rewrittenUrl: URL;

  if (match) {
    const { rule, params } = match;
    const finalParams = { code, ...params };
    const rewrittenPath = applyParams(rule.destination, finalParams, search);
    rewrittenUrl = new URL(rewrittenPath, request.url);
  } else {
    // Fallback to default routing
    rewrittenUrl = new URL(`/${code}${pathname}${search}`, request.url);
  }

  return NextResponse.rewrite(rewrittenUrl, { request });
}

// Performance Optimizations
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
```

## Important Notes

- **Pre-compilation**: Rules are compiled once at module load time for optimal performance
- **Early Exit**: Pattern matching stops at first successful match
- **Bloom Filter**: Optional O(1) filtering for large rule sets (integrated into build process)
- **Build Integration**: Bloom filter generation integrated into build process
- **Complex Patterns**: Support for named parameters, wildcards, and regex constraints
- **Conditional Routing**: Enables routing based on headers, cookies, query params, and host
- **Type Safety**: Full TypeScript support with proper type definitions
- **Testing**: Comprehensive test suite ensures reliability and prevents regressions
- The custom routing middleware is located in `apps/web/middleware/` with utilities in `utils/`, rewrite configurations in `rewrites/`, and build scripts in `scripts/`
