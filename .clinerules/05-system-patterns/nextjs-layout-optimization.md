# Next.js Layout Optimization Pattern

Optimizing layout and page components in Next.js App Router for better performance and user experience.

## Key Components

1. **Lightweight Layout Pattern**
   ```typescript
   // ✅ Good: Lightweight layout with client wrapper
   // apps/web/app/(web)/layout.tsx
   export default function RootLayout({
     children,
   }: {
     children: React.ReactNode;
   }) {
     return (
       <html lang="en">
         <body className={`${inter.className} font-sans`}>
           <AdminHeaderWrapper>
             {children}
           </AdminHeaderWrapper>
           <SpeedInsights />
         </body>
       </html>
     );
   }
   ```

   Root layouts focus on structure, not data fetching for optimal performance.

2. **Client Wrapper Pattern**
   ```typescript
   // Client wrapper handles authentication state
   // apps/web/components/AdminHeaderWrapper.tsx
   'use client';

   export function AdminHeaderWrapper({ children }: AdminHeaderWrapperProps) {
     const { user, loading } = useAuth();

     // Render children immediately while auth state is determined
     if (loading) {
       return <>{children}</>;
     }

     // Conditionally render admin components only when needed
     return (
       <>
         {user && (
           <Suspense fallback={null}>
             <AdminHeader userRoles={user.role || []} />
           </Suspense>
         )}
         {children}
       </>
     );
   }
   ```

   Client wrappers handle conditional rendering based on authentication or other states.

3. **Progressive Page Loading**
   ```typescript
   // ✅ Good: Page with parallel data fetching and suspense
   export default async function DashboardPage() {
     // Fetch data in parallel
     const userDataPromise = fetchUserData();
     const weatherDataPromise = fetchWeatherData();

     return (
       <div>
         <h1>Dashboard</h1>

         {/* Critical UI renders immediately */}
         <NavigationMenu />

         {/* Weather data loads with suspense */}
         <Suspense fallback={<WeatherSkeleton />}>
           <WeatherWidget dataPromise={weatherDataPromise} />
         </Suspense>

         {/* User data loads with suspense */}
         <Suspense fallback={<UserProfileSkeleton />}>
           <UserProfile dataPromise={userDataPromise} />
         </Suspense>
       </div>
     );
   }
   ```

   Progressive loading shows content incrementally as it becomes available.

## Benefits

- **Improved Performance**: Lightweight layouts reduce blocking operations
- **Better User Experience**: Progressive enhancement and immediate content rendering
- **Optimal Loading**: Parallel data fetching and streaming with Suspense
- **Flexible Architecture**: Client wrappers enable conditional rendering without layout complexity
- **React 19 Compatibility**: Leverages direct promise passing capabilities
- **Reduced Blocking**: Moves data fetching toward leaf components

## Example Implementation

```typescript
// Complete layout optimization example
import { Suspense } from 'react';
import { Inter } from 'next/font/google';
import { SpeedInsights } from '@vercel/speed-insights/next';
import { AdminHeaderWrapper } from '@/components/AdminHeaderWrapper';

const inter = Inter({ subsets: ['latin'] });

// Optimized root layout
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={`${inter.className} font-sans antialiased`}>
        {/* Client wrapper handles conditional logic */}
        <AdminHeaderWrapper>
          <main className="min-h-screen">
            {children}
          </main>
        </AdminHeaderWrapper>

        {/* Performance monitoring */}
        <SpeedInsights />
      </body>
    </html>
  );
}

// Client wrapper with progressive enhancement
'use client';

import { Suspense } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { AdminHeader } from '@/components/AdminHeader';

interface AdminHeaderWrapperProps {
  children: React.ReactNode;
}

export function AdminHeaderWrapper({ children }: AdminHeaderWrapperProps) {
  const { user, loading } = useAuth();

  return (
    <>
      {/* Conditionally render admin header */}
      {user && !loading && (
        <Suspense fallback={<div className="h-16 bg-gray-100 animate-pulse" />}>
          <AdminHeader userRoles={user.role || []} />
        </Suspense>
      )}

      {/* Always render children immediately */}
      {children}
    </>
  );
}

// Optimized page component
export default async function WeatherPage({ params }: WeatherPageProps) {
  // Parallel data fetching
  const locationPromise = getLocationData(params.locationId);
  const weatherPromise = getWeatherData(params.locationId);
  const forecastPromise = getForecastData(params.locationId);

  return (
    <div className="weather-page">
      {/* Critical content renders immediately */}
      <PageHeader />

      {/* Progressive loading with Suspense boundaries */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Suspense fallback={<CurrentConditionsSkeleton />}>
          <CurrentConditions
            locationPromise={locationPromise}
            weatherPromise={weatherPromise}
          />
        </Suspense>

        <Suspense fallback={<ForecastSkeleton />}>
          <WeatherForecast
            locationPromise={locationPromise}
            forecastPromise={forecastPromise}
          />
        </Suspense>
      </div>

      {/* Secondary content loads later */}
      <Suspense fallback={<div className="h-32 bg-gray-100 animate-pulse rounded" />}>
        <WeatherAlerts locationPromise={locationPromise} />
      </Suspense>
    </div>
  );
}
```

## Important Notes

- **Keep Layouts Lightweight**: Root layouts should focus on structure, not data fetching
- **Avoid Blocking Calls in Layouts**: Move data fetching to more specific components
- **Use Client Wrappers**: Create client component wrappers for conditional rendering
- **Dynamic Imports**: Use dynamic imports with loading states for heavy components
- **Suspense Boundaries**: Implement Suspense for asynchronous operations
- **Progressive Enhancement**: Show content immediately while auth state is determined
- **Parallel Data Fetching**: Use parallel data fetching when multiple independent data sources are needed
- **Error Boundaries**: Implement error boundaries at appropriate levels for better error handling
