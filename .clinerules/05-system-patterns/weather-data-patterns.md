# Weather Data Patterns

Patterns for handling weather data flow, location management, and client-side data fetching in weather components.

## Key Components

1. **Location Data Flow**
   ```typescript
   // Server component that fetches location data based on URL parameter
   export default async function TodayPage({ searchParams }: TodayPageProps) {
     const locIdParam = await searchParams.locId;

     // Fetch location data using DAL
     const { data: location } = await tryCatch(
       getLocationPointByLocIdParam(locIdParam as string)
     );

     // Create location object to pass to components
     const locationData = {
       displayName: location.location.displayName,
       adminDistrict: location.location.adminDistrict,
       geocode: `${location.location.latitude},${location.location.longitude}`,
     };

     // Pass location data directly to weather components
     return (
       <div>
         <CurrentConditions location={locationData} />
         <DailyForecast location={locationData} />
       </div>
     );
   }
   ```

   Location calls originate from page.tsx components and are passed to weather components as props.

2. **Location Source Hook Pattern**
   ```typescript
   // Custom hook abstracts location handling logic
   export function useLocationSource({
     location,
   }: UseLocationSourceProps): UseLocationSourceResult {
     // State for the effective location
     const [effectiveLocation, setEffectiveLocation] = useState<
       LocationData | undefined
     >(location);

     // Get favorite locations from atom
     const favoriteLocations = useAtomValue(favoritedLocationsAtom);

     // Get geoip geocode from atom
     const geoipGeocodeValue = useAtomValue(geoipGeocode);

     // Determine which location to use based on priority
     useEffect(() => {
       // Priority order:
       // 1. Location from props (highest priority)
       // 2. First favorite location (if available)
       // 3. GeoIP location from geoipGeocode atom (lowest priority)
     }, [/* dependencies */]);

     return {
       effectiveLocation,
       isLocationLoading,
       locationError,
     };
   }
   ```

   Centralizes location priority logic in a single, reusable hook.

3. **Client-Side Data Fetching with SWR**
   ```typescript
   // Weather components fetch data client-side using SWR with the DAL
   const { effectiveLocation, isLocationLoading } = useLocationSource({
     location, // From props
   });

   // Use effectiveLocation for data fetching
   const { data, error, isLoading } = useSWR(
     effectiveLocation ? ["weather-data", effectiveLocation.geocode] : null,
     () => fetchWeatherData(effectiveLocation.geocode),
     {
       revalidateOnFocus: false,
       dedupingInterval: 60000, // 1 minute
     }
   );
   ```

   SWR handles caching, revalidation, and state management for weather data.

## Benefits

- **Clear Separation**: Location handling is separated from weather data rendering
- **Consistent Location Access**: useLocationSource provides uniform location handling
- **Flexible Architecture**: Supports both location-based and location-less pages
- **Efficient Caching**: SWR prevents redundant API calls and provides automatic revalidation
- **Type Safety**: Consistent location object structure across components
- **Error Handling**: Consistent error and loading states for both location and weather data

## Example Implementation

```typescript
// Location-based page (server component)
export default async function TodayPage({ searchParams }: TodayPageProps) {
  const locIdParam = await searchParams.locId;

  const { data: location } = await tryCatch(
    getLocationPointByLocIdParam(locIdParam as string)
  );

  const locationData = {
    displayName: location.location.displayName,
    adminDistrict: location.location.adminDistrict,
    geocode: `${location.location.latitude},${location.location.longitude}`,
  };

  return (
    <div>
      <CurrentConditions location={locationData} />
      <DailyForecast location={locationData} />
    </div>
  );
}

// Location-less component (client component)
export default function LocalsuiteNav() {
  const { effectiveLocation, isLocationLoading } = useLocationSource({});

  if (isLocationLoading || !effectiveLocation) {
    return null;
  }

  const navItems = [
    { name: 'Today', href: `/weather/today/l/${effectiveLocation.placeId}` },
    { name: 'Hourly', href: `/weather/hourly/l/${effectiveLocation.placeId}` },
    { name: '10 Day', href: `/weather/10-day/l/${effectiveLocation.placeId}` },
  ];

  return (
    <div className="navigation">
      {/* Navigation rendering */}
    </div>
  );
}

// Weather component with data fetching
export const WeatherComponent: React.FC<WeatherComponentProps> = ({ location }) => {
  const { effectiveLocation, isLocationLoading } = useLocationSource({
    location,
  });

  const { data: weatherData, error, isLoading } = useSWR(
    effectiveLocation ? ['weather', effectiveLocation.geocode] : null,
    () => getCurrentObservations({
      geocode: effectiveLocation.geocode,
      units: Units.IMPERIAL,
      language: "en-US",
    }),
    {
      revalidateOnFocus: false,
      dedupingInterval: 60000,
    }
  );

  if (isLocationLoading || isLoading) {
    return <WeatherSkeleton />;
  }

  if (error || !weatherData) {
    return <WeatherError error={error} />;
  }

  return <WeatherDisplay data={weatherData} location={effectiveLocation} />;
};
```

## Important Notes

- **Location Priority**: useLocationSource handles location priority (props > favorites > geoIP)
- **SWR Integration**: All DAL function calls in client components must use SWR
- **Consistent Structure**: Location objects follow a consistent structure across components
- **Error Boundaries**: Consider using Suspense boundaries in page components for improved UX
- **Data Transformation**: DAL transforms compact API responses into consumer-friendly formats
- **State Management**: Clear handling of loading, error, and success states for both location and weather data
- Components focus on rendering weather data, not location resolution
