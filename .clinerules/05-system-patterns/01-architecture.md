# System Architecture

## Package-Based Monorepo Structure

### Architecture Overview

- **Package-First Design**: Discrete logical code organized into focused packages
- **Apps as Entry Points**: Applications consume packages, never the reverse
- **Cyclical Dependency Prevention**: Clear dependency hierarchy prevents circular imports
- **Domain-Driven Organization**: Packages organized by business domains and functionality

### Monorepo Structure

```
wx-next/
├── apps/                    # Entry points into the package tree
│   ├── web/                 # Next.js web application
│   └── storybook/           # Component documentation
└── packages/                # Discrete logical packages
    ├── dal/                 # Data Access Layer
    ├── payload/             # PayloadCMS blocks and config
    ├── ui/                  # Shared UI components
    ├── location/            # Location-specific functionality
    ├── user/                # User management
    ├── icons/               # Icon system
    ├── logger/              # Logging utilities
    ├── analytics/           # Analytics functionality
    ├── experiments/         # A/B testing
    ├── navigation/          # Navigation utilities
    ├── units/               # Unit conversion utilities
    └── ...                  # Additional domain-specific packages
```

### Application Architecture

- **Next.js Web Application**: Primary entry point consuming packages
- **PayloadCMS Integration**: Content management through payload package
- **MongoDB Database**: Content and data storage
- **Domain-Oriented DAL**: Data access abstracted by business domains
- **Shared Component Library**: UI package provides reusable components

## Package Relationships

### Dependency Flow

```typescript
// Unidirectional dependency flow prevents cycles
apps/web → packages/ui → packages/logger
apps/web → packages/dal → packages/utils
apps/web → packages/payload → packages/ui
apps/web → packages/location → packages/dal

// Apps consume packages
import { Button } from '@repo/ui';
import { getCurrentObservations } from '@repo/dal/weather';
import { createLogger } from '@repo/logger';
```

### Package Boundaries

- **Clear Exports**: Each package defines public API through package.json exports
- **Focused Responsibility**: Single, well-defined purpose per package
- **Independent Testing**: Packages can be tested in isolation
- **Workspace Dependencies**: Internal dependencies use `workspace:*` pattern

### Data Flow

- **UI Components**: Consume data from DAL packages
- **DAL Abstraction**: Abstracts external API calls by domain
- **Type Safety**: Consistent interfaces across package boundaries
- **State Management**: Location and user packages provide atomic state

### Integration Points

- **PayloadCMS**: Integrated through payload package with blocks and configurations
- **MongoDB**: Database connections managed within appropriate packages
- **External APIs**: Weather and other external integrations through DAL
- **Shared Configuration**: TypeScript, ESLint, and other configs in dedicated packages
