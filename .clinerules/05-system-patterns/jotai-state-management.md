# Jotai State Management Pattern

Atomic state management using <PERSON><PERSON> for React applications with server-side rendering support.

## Key Components

1. **Atom Definition**
   ```typescript
   // Definition of atoms in apps/web/atoms/geolocation.ts
   import { atom } from "jotai";
   import type { LocationPoint } from "@repo/dal/types/weather";

   export const geoIPLocationPointAtom = atom<LocationPoint | null | undefined>(
     null,
   );
   ```

   Atoms are small, independent pieces of state that can be composed together.

2. **Server-to-Client Hydration**
   ```typescript
   // AtomHydrationBoundaries component
   "use client";

   import { useHydrateAtoms } from "jotai/utils";
   import { geoIPLocationPointAtom } from "@repo/location/atoms/geolocation";

   export const AtomHydrationBoundaries = ({
     geoIpLocationPoint,
   }: AtomHydrationBoundariesProps) => {
     useHydrateAtoms([
       [geoIPLocationPointAtom, geoIpLocationPoint],
       // Additional atoms can be hydrated here
     ]);

     return null;
   };
   ```

   Dedicated component for hydrating atoms with server data using useHydrateAtoms.

3. **Reading Atom Values**
   ```typescript
   "use client";

   import { useAtomValue } from "jotai";
   import { geoIPLocationPointAtom } from "@repo/location/atoms/geolocation";

   export const LocationDisplay = () => {
     const location = useAtomValue(geoIPLocationPointAtom);

     // Use location data in component
     if (!location) {
       return <div>No location available</div>;
     }

     return (
       <div>
         <h2>{location.displayName}</h2>
         <p>{location.adminDistrict}</p>
       </div>
     );
   };
   ```

   Components access atom values directly using useAtomValue hook.

## Benefits

- **Simplicity**: Simpler API compared to Redux or Context
- **Performance**: Fine-grained updates prevent unnecessary re-renders
- **Server Integration**: Seamless integration with Next.js server components
- **Composability**: Atoms can be composed to create derived state
- **Bundle Size**: Smaller bundle size compared to other state management libraries
- **No Provider Nesting**: Eliminates deeply nested context providers
- **Code Splitting**: Better support for code splitting and lazy loading

## Example Implementation

```typescript
// Atom definitions
import { atom } from "jotai";
import type { LocationPoint } from "@repo/dal/types/weather";

// Basic atom for location data
export const geoIPLocationPointAtom = atom<LocationPoint | null | undefined>(
  null,
);

// Atom for favorite locations
export const favoritedLocationsAtom = atom<LocationPoint[]>([]);

// Derived atom that computes effective location
export const effectiveLocationAtom = atom((get) => {
  const geoIPLocation = get(geoIPLocationPointAtom);
  const favoriteLocations = get(favoritedLocationsAtom);

  // Return first favorite or geoIP location
  return favoriteLocations[0] || geoIPLocation;
});

// Server component that provides initial data
export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Fetch initial location data server-side
  const { data: geoIpLocationPoint } = await tryCatch(
    getGeoIPLocation()
  );

  return (
    <html lang="en">
      <body>
        <AtomHydrationBoundaries
          geoIpLocationPoint={geoIpLocationPoint}
        >
          {children}
        </AtomHydrationBoundaries>
      </body>
    </html>
  );
}

// Hydration component
"use client";

import { useHydrateAtoms } from "jotai/utils";
import {
  geoIPLocationPointAtom,
  favoritedLocationsAtom
} from "@repo/location/atoms/geolocation";

interface AtomHydrationBoundariesProps {
  geoIpLocationPoint?: LocationPoint | null;
  favoriteLocations?: LocationPoint[];
  children: React.ReactNode;
}

export const AtomHydrationBoundaries = ({
  geoIpLocationPoint,
  favoriteLocations = [],
  children,
}: AtomHydrationBoundariesProps) => {
  useHydrateAtoms([
    [geoIPLocationPointAtom, geoIpLocationPoint],
    [favoritedLocationsAtom, favoriteLocations],
  ]);

  return <>{children}</>;
};

// Component using atoms
"use client";

import { useAtomValue, useSetAtom } from "jotai";
import {
  effectiveLocationAtom,
  favoritedLocationsAtom
} from "@repo/location/atoms/geolocation";

export const LocationWidget = () => {
  const effectiveLocation = useAtomValue(effectiveLocationAtom);
  const setFavoriteLocations = useSetAtom(favoritedLocationsAtom);

  const addToFavorites = (location: LocationPoint) => {
    setFavoriteLocations((prev) => [...prev, location]);
  };

  if (!effectiveLocation) {
    return <div>Loading location...</div>;
  }

  return (
    <div>
      <h2>Current Location</h2>
      <p>{effectiveLocation.displayName}</p>
      <button onClick={() => addToFavorites(effectiveLocation)}>
        Add to Favorites
      </button>
    </div>
  );
};

// Custom hook using atoms
export function useLocationData() {
  const geoIPLocation = useAtomValue(geoIPLocationPointAtom);
  const favoriteLocations = useAtomValue(favoritedLocationsAtom);
  const effectiveLocation = useAtomValue(effectiveLocationAtom);

  return {
    geoIPLocation,
    favoriteLocations,
    effectiveLocation,
    hasLocation: !!effectiveLocation,
  };
}
```

## Important Notes

- **Atom Hydration Flow**: Server components fetch data → AtomHydrationBoundaries hydrates atoms → Client components access data
- **Client Components Only**: Atoms can only be used in client components with "use client" directive
- **Hydration Component**: Always use AtomHydrationBoundaries to initialize atoms with server data
- **Derived Atoms**: Use derived atoms for computed state that depends on multiple atoms
- **Performance**: Components only re-render when their specific atoms change
- **Type Safety**: Always type atoms with TypeScript for better developer experience
- **Server Integration**: Perfect for Next.js server components that need to pass data to client components
- **No Providers**: Unlike Context API, Jotai doesn't require provider components in the component tree
