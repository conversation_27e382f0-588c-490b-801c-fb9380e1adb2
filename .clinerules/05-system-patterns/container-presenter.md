# Container/Presenter Pattern

Separation of data fetching from presentation logic for improved testability and maintainability.

## Key Components

1. **Container Component**
   ```typescript
   // Container component handles data fetching and state
   export const CurrentConditionsContainer: React.FC<CurrentConditionsContainerProps> = ({
     location,
     video,
   }) => {
     // Use hooks for data fetching
     const { effectiveLocation, isLocationLoading } = useLocationSource({
       location,
     });

     const {
       weatherData,
       forecastData,
       IconComponent,
       isLoading: isWeatherLoading,
       error: weatherError,
       formattedTime,
     } = useCurrentConditionsData(effectiveLocation);

     // Combine loading and error states
     const isLoading = isWeatherLoading || isLocationLoading;
     const error = weatherError || locationError;

     // Pass data and state to presenter component
     return (
       <CurrentConditions
         location={effectiveLocation}
         weatherData={weatherData}
         forecastData={forecastData}
         IconComponent={IconComponent}
         isLoading={isLoading}
         error={error}
         formattedTime={formattedTime}
         video={video}
       />
     );
   };
   ```

   Container components handle data fetching and state management.

2. **Presenter Component**
   ```typescript
   // Presenter component renders the appropriate UI based on state
   export const CurrentConditions: React.FC<CurrentConditionsProps> = ({
     location,
     weatherData,
     forecastData,
     IconComponent,
     isLoading,
     error,
     formattedTime,
     video,
   }) => {
     if (!location) {
       return <CurrentConditionsLoading message="Loading location..." />;
     }

     if (isLoading) {
       return <CurrentConditionsLoading location={location} />;
     }

     if (error || !weatherData || !forecastData) {
       return <CurrentConditionsError location={location} error={error} />;
     }

     return (
       <CurrentConditionsContent
         location={location}
         weatherData={weatherData}
         forecastData={forecastData}
         IconComponent={IconComponent}
         formattedTime={formattedTime}
         video={video}
       />
     );
   };
   ```

   Presenter components focus on rendering UI based on props.

3. **State-Specific Components**
   ```typescript
   // Dedicated components for different states
   const CurrentConditionsLoading: React.FC<LoadingProps> = ({ location, message }) => (
     <div className="animate-pulse">
       <div className="h-8 bg-gray-200 rounded mb-4"></div>
       <div className="h-4 bg-gray-200 rounded mb-2"></div>
       <div className="h-4 bg-gray-200 rounded w-3/4"></div>
     </div>
   );

   const CurrentConditionsError: React.FC<ErrorProps> = ({ location, error }) => (
     <div className="text-red-600">
       <h3>Unable to load weather data</h3>
       <p>{error?.message || 'Please try again later'}</p>
     </div>
   );

   const CurrentConditionsContent: React.FC<ContentProps> = ({
     location,
     weatherData,
     forecastData,
     IconComponent,
     formattedTime,
     video
   }) => (
     <div className="weather-content">
       {/* Actual weather content rendering */}
     </div>
   );
   ```

   Dedicated components for different states (loading, error, success).

## Benefits

- **Improved Testability**: Presenter components can be tested in isolation with static props
- **Separation of Concerns**: Data fetching logic is isolated from presentation logic
- **Reusability**: Presenter components can be reused with different data sources
- **Maintainability**: Easier to understand and modify components with single responsibilities
- **State Management**: Clear handling of loading, error, and success states
- **Storybook Integration**: Presenter components work perfectly in Storybook with mock data

## Example Implementation

```typescript
// Container component with data fetching
export const WeatherWidgetContainer: React.FC<WeatherWidgetContainerProps> = ({
  location,
  showForecast = true,
}) => {
  const { data: weatherData, error, isLoading } = useSWR(
    location ? ['weather', location.geocode] : null,
    () => getCurrentObservations({ geocode: location.geocode }),
    {
      revalidateOnFocus: false,
      dedupingInterval: 60000, // 1 minute
    }
  );

  const { data: forecastData, error: forecastError, isLoading: forecastLoading } = useSWR(
    location && showForecast ? ['forecast', location.geocode] : null,
    () => getDailyForecast({ geocode: location.geocode }),
    {
      revalidateOnFocus: false,
      dedupingInterval: 300000, // 5 minutes
    }
  );

  return (
    <WeatherWidget
      location={location}
      weatherData={weatherData}
      forecastData={forecastData}
      isLoading={isLoading || forecastLoading}
      error={error || forecastError}
      showForecast={showForecast}
    />
  );
};

// Presenter component with pure rendering logic
export const WeatherWidget: React.FC<WeatherWidgetProps> = ({
  location,
  weatherData,
  forecastData,
  isLoading,
  error,
  showForecast,
}) => {
  if (!location) {
    return <WeatherWidgetSkeleton />;
  }

  if (isLoading) {
    return <WeatherWidgetLoading location={location} />;
  }

  if (error || !weatherData) {
    return <WeatherWidgetError location={location} error={error} />;
  }

  return (
    <div className="weather-widget">
      <WeatherCurrent data={weatherData} location={location} />
      {showForecast && forecastData && (
        <WeatherForecast data={forecastData} />
      )}
    </div>
  );
};

// Type definitions
interface WeatherWidgetContainerProps {
  location?: LocationData;
  showForecast?: boolean;
}

interface WeatherWidgetProps {
  location?: LocationData;
  weatherData?: WeatherData;
  forecastData?: ForecastData;
  isLoading: boolean;
  error?: Error;
  showForecast: boolean;
}
```

## Important Notes

- **Clear Naming**: Use "Container" suffix for data-fetching components and descriptive names for presenters
- **Props Interface**: Define clear interfaces for data passed between container and presenter
- **Error Boundaries**: Consider wrapping containers in error boundaries for better error handling
- **Loading States**: Always handle loading states gracefully with skeleton components
- **Data Validation**: Validate data in containers before passing to presenters
- **Memoization**: Consider using React.memo for presenter components to prevent unnecessary re-renders
- **Testing Strategy**: Test containers for data flow, test presenters for UI rendering
- This pattern works exceptionally well with SWR for data fetching and Storybook for component development
