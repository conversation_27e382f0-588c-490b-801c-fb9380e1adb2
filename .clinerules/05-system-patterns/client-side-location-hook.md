# Client-Side Location Hook Pattern

Pattern for accessing location data in client components with separation of location acquisition from component rendering.

## Key Components

1. **useLocationSource Hook**
   ```typescript
   // Custom hook abstracts location handling logic
   export function useLocationSource({
     location,
   }: UseLocationSourceProps): UseLocationSourceResult {
     // State for the effective location
     const [effectiveLocation, setEffectiveLocation] = useState<
       LocationData | undefined
     >(location);

     // Get favorite locations from atom
     const favoriteLocations = useAtomValue(favoritedLocationsAtom);

     // Get geoip geocode from atom
     const geoipGeocodeValue = useAtomValue(geoipGeocode);

     // Determine which location to use based on priority
     useEffect(() => {
       // Priority order:
       // 1. Location from props (highest priority)
       // 2. First favorite location (if available)
       // 3. GeoIP location from geoipGeocode atom (lowest priority)
     }, [/* dependencies */]);

     return {
       effectiveLocation,
       isLocationLoading,
       locationError,
     };
   }
   ```

   Centralizes location priority logic in a single, reusable hook.

2. **Location Priority Logic**
   ```typescript
   // Location priority implementation
   useEffect(() => {
     // If location is provided via props, use it (highest priority)
     if (location) {
       setEffectiveLocation(location);
       return;
     }

     // If favorite locations exist, use the first one
     if (favoriteLocations && favoriteLocations.length > 0) {
       setEffectiveLocation(favoriteLocations[0]);
       return;
     }

     // Fall back to GeoIP location (lowest priority)
     if (geoipGeocodeValue) {
       setEffectiveLocation(geoipGeocodeValue);
       return;
     }

     // No location available
     setEffectiveLocation(undefined);
   }, [location, favoriteLocations, geoipGeocodeValue]);
   ```

   Clear priority order for location resolution.

3. **Component Integration**
   ```typescript
   // Weather components use the hook for location handling
   export const WeatherComponent: React.FC<WeatherComponentProps> = ({ location }) => {
     const { effectiveLocation, isLocationLoading } = useLocationSource({
       location, // From props
     });

     // Use effectiveLocation for data fetching
     const { data, error, isLoading } = useSWR(
       effectiveLocation ? ["weather-data", effectiveLocation.geocode] : null,
       () => fetchWeatherData(effectiveLocation.geocode),
       {
         revalidateOnFocus: false,
         dedupingInterval: 60000, // 1 minute
       }
     );

     if (isLocationLoading || isLoading) {
       return <WeatherSkeleton />;
     }

     if (error || !data) {
       return <WeatherError error={error} />;
     }

     return <WeatherDisplay data={data} location={effectiveLocation} />;
   };
   ```

   Components focus on rendering weather data, not location resolution.

## Benefits

- **Consistent Location Access**: Uniform location handling across all components
- **Clear Priority Logic**: Well-defined priority order for location sources
- **Separation of Concerns**: Location handling separated from weather data rendering
- **Flexible Architecture**: Supports both location-based and location-less pages
- **Type Safety**: Consistent location object structure across components
- **Error Handling**: Consistent error and loading states for location data
- **Atom Integration**: Seamless integration with Jotai state management

## Example Implementation

```typescript
// Complete useLocationSource hook implementation
import { useState, useEffect } from 'react';
import { useAtomValue } from 'jotai';
import { favoritedLocationsAtom, geoipGeocode } from '@repo/location/atoms/geolocation';

interface UseLocationSourceProps {
  location?: LocationData;
}

interface UseLocationSourceResult {
  effectiveLocation?: LocationData;
  isLocationLoading: boolean;
  locationError?: Error;
}

export function useLocationSource({
  location,
}: UseLocationSourceProps): UseLocationSourceResult {
  const [effectiveLocation, setEffectiveLocation] = useState<LocationData | undefined>(location);
  const [isLocationLoading, setIsLocationLoading] = useState(false);
  const [locationError, setLocationError] = useState<Error | undefined>();

  // Get data from atoms
  const favoriteLocations = useAtomValue(favoritedLocationsAtom);
  const geoipGeocodeValue = useAtomValue(geoipGeocode);

  useEffect(() => {
    setIsLocationLoading(true);
    setLocationError(undefined);

    try {
      // Priority 1: Location from props (highest priority)
      if (location) {
        setEffectiveLocation(location);
        setIsLocationLoading(false);
        return;
      }

      // Priority 2: First favorite location
      if (favoriteLocations && favoriteLocations.length > 0) {
        setEffectiveLocation(favoriteLocations[0]);
        setIsLocationLoading(false);
        return;
      }

      // Priority 3: GeoIP location (lowest priority)
      if (geoipGeocodeValue) {
        setEffectiveLocation(geoipGeocodeValue);
        setIsLocationLoading(false);
        return;
      }

      // No location available
      setEffectiveLocation(undefined);
      setIsLocationLoading(false);
    } catch (error) {
      setLocationError(error as Error);
      setIsLocationLoading(false);
    }
  }, [location, favoriteLocations, geoipGeocodeValue]);

  return {
    effectiveLocation,
    isLocationLoading,
    locationError,
  };
}

// Usage in location-less component
export default function LocalsuiteNav() {
  const { effectiveLocation, isLocationLoading } = useLocationSource({});

  if (isLocationLoading || !effectiveLocation) {
    return null;
  }

  const navItems = [
    { name: 'Today', href: `/weather/today/l/${effectiveLocation.placeId}` },
    { name: 'Hourly', href: `/weather/hourly/l/${effectiveLocation.placeId}` },
    { name: '10 Day', href: `/weather/10-day/l/${effectiveLocation.placeId}` },
  ];

  return (
    <div className="navigation">
      {navItems.map((item) => (
        <a key={item.name} href={item.href}>
          {item.name}
        </a>
      ))}
    </div>
  );
}

// Usage in weather component with location prop
export const CurrentConditions: React.FC<{ location?: LocationData }> = ({ location }) => {
  const { effectiveLocation, isLocationLoading } = useLocationSource({ location });

  const { data: weatherData, error, isLoading } = useSWR(
    effectiveLocation ? ['current-conditions', effectiveLocation.geocode] : null,
    () => getCurrentObservations({
      geocode: effectiveLocation.geocode,
      units: Units.IMPERIAL,
      language: "en-US",
    }),
    {
      revalidateOnFocus: false,
      dedupingInterval: 300000, // 5 minutes
    }
  );

  if (isLocationLoading || isLoading) {
    return <CurrentConditionsLoading />;
  }

  if (error || !weatherData || !effectiveLocation) {
    return <CurrentConditionsError error={error} />;
  }

  return (
    <CurrentConditionsDisplay
      data={weatherData}
      location={effectiveLocation}
    />
  );
};
```

## Important Notes

- **Location Priority**: useLocationSource handles location priority (props > favorites > geoIP)
- **Consistent Structure**: Location objects follow a consistent structure across components
- **Atom Integration**: Seamlessly integrates with Jotai atoms for state management
- **Error Handling**: Provides consistent error and loading states
- **Flexible Usage**: Works for both location-based and location-less components
- **Performance**: Efficient re-rendering based on location changes
- **Type Safety**: Strong TypeScript support with proper interfaces
- **SWR Integration**: Works seamlessly with SWR for data fetching
- Components focus on rendering weather data, not location resolution
