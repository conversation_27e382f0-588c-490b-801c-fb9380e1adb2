# Inline Hook Pattern

Co-location of custom hooks with their primary consumer components for improved maintainability and AI efficiency.

## Key Components

1. **Inline Hook Definition**
   ```typescript
   // Container file with inline hook
   function useComponentSpecificData(params) {
     // Hook implementation...
     return {
       data,
       isLoading,
       error
     };
   }

   export const ComponentContainer = ({ props }) => {
     // Use the inline hook
     const { data, isLoading, error } = useComponentSpecificData(params);

     // Return the presenter component
     return <ComponentPresenter data={data} isLoading={isLoading} error={error} />;
   };
   ```

   Co-location of custom hooks with their primary consumer components.

2. **Component-Specific Logic**
   ```typescript
   // Weather component with inline hook
   function useWeatherData(location: LocationData) {
     const [weatherData, setWeatherData] = useState(null);
     const [isLoading, setIsLoading] = useState(true);
     const [error, setError] = useState(null);

     useEffect(() => {
       const fetchData = async () => {
         setIsLoading(true);
         const { data, error } = await tryCatch(
           getCurrentObservations({ geocode: location.geocode })
         );

         if (error) {
           setError(error);
         } else {
           setWeatherData(data);
         }
         setIsLoading(false);
       };

       fetchData();
     }, [location.geocode]);

     return { weatherData, isLoading, error };
   }

   export const WeatherComponent = ({ location }) => {
     const { weatherData, isLoading, error } = useWeatherData(location);

     if (isLoading) return <LoadingSpinner />;
     if (error) return <ErrorMessage error={error} />;

     return <WeatherDisplay data={weatherData} />;
   };
   ```

   Hooks that are specific to a single component and not reused elsewhere.

## Benefits

- **Reduced File Count**: Fewer files to navigate and maintain
- **Clear Ownership**: Hook is clearly owned by its consumer component
- **Improved AI Efficiency**: Better token utilization for AI models
- **Maintained Separation**: Logic is still separated from presentation
- **Simplified Imports**: Fewer import statements and simpler dependency graph
- **Easier Refactoring**: Changes to hook logic don't affect multiple files
- **Better Context**: Hook and component are always viewed together

## Example Implementation

```typescript
// Complete example with inline hook
import { useState, useEffect } from 'react';
import { tryCatch } from '@/utils/tryCatch';
import { getCurrentObservations } from '@repo/dal/weather';

// Inline hook for component-specific data fetching
function useCurrentConditionsData(location: LocationData) {
  const [data, setData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!location) return;

    const fetchConditions = async () => {
      setIsLoading(true);
      setError(null);

      const { data: conditions, error: fetchError } = await tryCatch(
        getCurrentObservations({
          geocode: location.geocode,
          units: Units.IMPERIAL,
          language: "en-US",
        })
      );

      if (fetchError) {
        setError(fetchError);
      } else {
        setData(conditions);
      }

      setIsLoading(false);
    };

    fetchConditions();
  }, [location?.geocode]);

  return { data, isLoading, error };
}

// Component using the inline hook
export const CurrentConditionsContainer = ({ location }) => {
  const { data, isLoading, error } = useCurrentConditionsData(location);

  return (
    <CurrentConditionsPresenter
      data={data}
      isLoading={isLoading}
      error={error}
      location={location}
    />
  );
};

// Presenter component (can be in same file or separate)
const CurrentConditionsPresenter = ({ data, isLoading, error, location }) => {
  if (isLoading) {
    return <CurrentConditionsLoading location={location} />;
  }

  if (error || !data) {
    return <CurrentConditionsError error={error} location={location} />;
  }

  return <CurrentConditionsContent data={data} location={location} />;
};
```

## Important Notes

- **Use for component-specific hooks**: Only use this pattern for hooks that are specific to one component
- **Extract when reused**: If the hook becomes useful elsewhere, extract it to a separate file
- **Maintain separation**: Keep the hook logic separate from the component logic even when co-located
- **Clear naming**: Use descriptive names that indicate the hook's purpose and scope
- **Document well**: Include comments explaining the hook's purpose and behavior
- **Consider file size**: If the file becomes too large, consider splitting the hook out
- **Testing strategy**: Test the hook logic separately from the component rendering
- This pattern is particularly effective for container components that need specific data fetching logic
