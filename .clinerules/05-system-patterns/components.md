# Component Patterns

Comprehensive guide to component patterns used throughout the wx-next project, covering architecture, types, and implementation strategies.

## Key Components

1. **Component Types**
   ```typescript
   // Four distinct component types with specific purposes

   // 1. Payload Blocks - Content components for CMS
   export const WeatherBlock = ({ data }: WeatherBlockProps) => {
     return <div className="weather-block">{/* CMS content */}</div>;
   };

   // 2. Payload Admin UI - CMS admin interface components
   export const WeatherBlockAdmin = ({ field }: AdminProps) => {
     return <div className="admin-ui">{/* Admin interface */}</div>;
   };

   // 3. Frontend UI - End user components
   export const WeatherWidget = ({ location }: FrontendProps) => {
     return <div className="weather-widget">{/* User interface */}</div>;
   };

   // 4. Shared UI - Reusable elements
   export const Button = ({ variant, children }: ButtonProps) => {
     return <button className={`btn-${variant}`}>{children}</button>;
   };
   ```

   Four distinct component types serve different purposes in the application architecture.

2. **Component-Based Architecture**
   ```typescript
   // Composition pattern for building complex UIs
   export const WeatherDashboard = () => {
     return (
       <div className="dashboard">
         <WeatherHeader />
         <WeatherContent>
           <CurrentConditions />
           <DailyForecast />
           <WeatherAlerts />
         </WeatherContent>
         <WeatherFooter />
       </div>
     );
   };

   // Clear separation of concerns
   const WeatherContent = ({ children }: { children: React.ReactNode }) => {
     return <main className="weather-content">{children}</main>;
   };
   ```

   Components are composed together to build complex user interfaces with clear separation of concerns.

3. **Container/Presenter Pattern**
   ```typescript
   // Container handles data fetching and state
   export const WeatherContainer = ({ location }: ContainerProps) => {
     const { data, isLoading, error } = useWeatherData(location);

     return (
       <WeatherPresenter
         data={data}
         isLoading={isLoading}
         error={error}
         location={location}
       />
     );
   };

   // Presenter focuses on rendering UI
   export const WeatherPresenter = ({ data, isLoading, error }: PresenterProps) => {
     if (isLoading) return <WeatherSkeleton />;
     if (error) return <WeatherError error={error} />;
     return <WeatherDisplay data={data} />;
   };
   ```

   Separation of data fetching logic from presentation logic for better testability and maintainability.

## Benefits

- **Clear Architecture**: Four distinct component types with specific purposes and locations
- **Reusability**: Shared UI components can be used across different parts of the application
- **Separation of Concerns**: Container/presenter pattern separates data logic from UI logic
- **Composition**: Components can be composed together to build complex interfaces
- **Maintainability**: Clear patterns make components easier to understand and modify
- **Testability**: Presenter components can be tested in isolation with mock data
- **Scalability**: Architecture supports growth and feature expansion

## Example Implementation

```typescript
// Complete component architecture example
import React from 'react';
import { useWeatherData } from '@/hooks/useWeatherData';
import { WeatherData, LocationData } from '@/types';

// 1. Payload Block Component (CMS)
export const CurrentConditionsBlock = ({ data }: { data: any }) => {
  return (
    <div className="payload-block current-conditions">
      <CurrentConditionsContainer location={data.location} />
    </div>
  );
};

// 2. Container Component (Data Logic)
export const CurrentConditionsContainer = ({ location }: { location: LocationData }) => {
  const { data, isLoading, error } = useWeatherData(location);

  return (
    <CurrentConditionsPresenter
      data={data}
      isLoading={isLoading}
      error={error}
      location={location}
    />
  );
};

// 3. Presenter Component (UI Logic)
interface PresenterProps {
  data?: WeatherData;
  isLoading: boolean;
  error?: Error;
  location: LocationData;
}

export const CurrentConditionsPresenter = ({
  data,
  isLoading,
  error,
  location
}: PresenterProps) => {
  if (isLoading) {
    return <CurrentConditionsLoading location={location} />;
  }

  if (error || !data) {
    return <CurrentConditionsError error={error} location={location} />;
  }

  return <CurrentConditionsContent data={data} location={location} />;
};

// 4. Shared UI Components
export const CurrentConditionsContent = ({ data, location }: ContentProps) => {
  return (
    <Card className="current-conditions">
      <CardHeader>
        <Text variant="title" size="l">{location.displayName}</Text>
        <Text variant="caption" size="m">{data.observationTime}</Text>
      </CardHeader>
      <CardContent>
        <div className="temperature-display">
          <Text variant="display" size="xl">{data.temperature}°</Text>
          <WeatherIcon iconCode={data.iconCode} size="lg" />
        </div>
        <Text variant="body" size="l">{data.condition}</Text>
      </CardContent>
    </Card>
  );
};

// 5. State-Specific Components
export const CurrentConditionsLoading = ({ location }: { location: LocationData }) => (
  <Card className="current-conditions animate-pulse">
    <CardHeader>
      <div className="h-6 bg-gray-200 rounded w-48 mb-2"></div>
      <div className="h-4 bg-gray-200 rounded w-32"></div>
    </CardHeader>
    <CardContent>
      <div className="h-16 bg-gray-200 rounded w-24 mb-4"></div>
      <div className="h-4 bg-gray-200 rounded w-40"></div>
    </CardContent>
  </Card>
);

export const CurrentConditionsError = ({ error, location }: ErrorProps) => (
  <Card className="current-conditions border-red-200">
    <CardContent className="text-center py-8">
      <Text variant="title" size="m" className="text-red-600 mb-2">
        Unable to load weather data
      </Text>
      <Text variant="body" size="m" className="text-gray-600 mb-4">
        {error?.message || 'Please try again later'}
      </Text>
      <Button variant="outline" onClick={() => window.location.reload()}>
        Retry
      </Button>
    </CardContent>
  </Card>
);

// 6. Component Composition
export const WeatherDashboard = ({ location }: { location: LocationData }) => {
  return (
    <div className="weather-dashboard space-y-6">
      <DashboardHeader location={location} />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <CurrentConditionsContainer location={location} />
        <DailyForecastContainer location={location} />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <WeatherAlertsContainer location={location} />
        <AirQualityContainer location={location} />
        <RadarContainer location={location} />
      </div>
    </div>
  );
};
```

## Important Notes

- **Component Locations**:
  - Payload Blocks: `apps/web/blocks/`
  - Payload Admin UI: Various admin-specific directories
  - Frontend UI: `apps/web/components/`
  - Shared UI: `packages/ui/src/components/`
- **Container/Presenter**: Always separate data fetching from presentation logic
- **State Management**: Handle loading, error, and success states consistently
- **Composition**: Build complex UIs by composing smaller, focused components
- **Testing Strategy**: Test containers for data flow, test presenters for UI rendering
- **Type Safety**: Use TypeScript interfaces for all component props
- **Reusability**: Shared UI components should be highly reusable and generic
- **Documentation**: Detailed component patterns documented in `apps/web/components/README.md`
