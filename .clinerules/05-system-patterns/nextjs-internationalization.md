# Next.js Internationalization with next-intl Pattern

Comprehensive internationalization (i18n) implementation using next-intl for multi-language support in the wx-next application.

## Key Components

1. **Centralized Locale Management**
   ```typescript
   // packages/locales/src/index.ts
   export const LOCALES = [
     'ar-A<PERSON>', 'ar-BH', 'ar-DJ', 'ar-DZ', 'ar-EG', 'ar-ER', 'ar-IL', 'ar-IQ',
     'ar-JO', 'ar-KM', 'ar-KW', 'ar-LB', 'ar-LY', 'ar-MA', 'ar-MR', 'ar-OM',
     'ar-QA', 'ar-SA', 'ar-SD', 'ar-SO', 'ar-SY', 'ar-TD', 'ar-TN', 'ar-YE',
     'az-AZ', 'bg-BG', 'bn-BD', 'bn-IN', 'bs-BA', 'ca-AD', 'ca-ES', 'cs-CZ',
     'da-DK', 'de-AT', 'de-CH', 'de-DE', 'de-LI', 'el-CY', 'el-GR',
     'en-AG', 'en-AS', 'en-AU', 'en-BB', 'en-BS', 'en-BZ', 'en-CA', 'en-CM',
     'en-DM', 'en-FJ', 'en-FM', 'en-GB', 'en-GD', 'en-GH', 'en-GM', 'en-GY',
     'en-IE', 'en-IN', 'en-JM', 'en-KE', 'en-KI', 'en-KN', 'en-LC', 'en-LR',
     'en-LS', 'en-MH', 'en-MT', 'en-MU', 'en-NA', 'en-NG', 'en-NZ', 'en-PA',
     'en-PH', 'en-PK', 'en-PW', 'en-RW', 'en-SB', 'en-SG', 'en-SL', 'en-SS',
     'en-SZ', 'en-TO', 'en-TT', 'en-TV', 'en-TZ', 'en-UG', 'en-US', 'en-VC',
     'en-VU', 'en-ZA', 'en-ZM', 'en-ZW',
     // ... 200+ more locales including es-*, fr-*, pt-*, zh-*, etc.
   ] as const;

   // apps/web/i18n/config.ts
   import { Locale } from 'next-intl';

   export const defaultLocale: Locale = 'en-US';
   ```

   Centralized locale management in @repo/locales package with 200+ language-country combinations.

2. **Routing Configuration**
   ```typescript
   // apps/web/i18n/routing.ts
   import { defineRouting } from 'next-intl/routing';
   import { LOCALES } from '@repo/locales';

   export const routing = defineRouting({
     locales: LOCALES,
     defaultLocale: 'en-US',
     localePrefix: 'as-needed',
   });
   ```

   Routing configuration using centralized LOCALES from @repo/locales package.

3. **Request Configuration**
   ```typescript
   // apps/web/i18n/request.ts
   import { hasLocale } from 'next-intl';
   import { getRequestConfig } from 'next-intl/server';
   import { defaultLocale } from './config';
   import { LOCALES } from '@repo/locales';

   export default getRequestConfig(async ({ locale }) => {
     const validatedLocale = hasLocale(LOCALES, locale) ? locale : defaultLocale;

     return {
       locale: validatedLocale,
       messages: (await import(`./messages/${validatedLocale}.json`)).default,
     };
   });
   ```

   Server-side request configuration with locale validation using centralized LOCALES.

4. **Navigation Utilities**
   ```typescript
   // apps/web/i18n/navigation.ts
   import { createNavigation } from 'next-intl/navigation';
   import { routing } from './routing';

   // Should only be used on public routes in the `[locale]` segment
   export const { Link, usePathname, useRouter } = createNavigation(routing);
   ```

   Internationalized navigation components for locale-aware routing.

## Benefits

- **Centralized Locale Management**: Single source of truth for locales in @repo/locales package
- **Comprehensive Locale Support**: 200+ language-country combinations with regional data
- **Dynamic Message Loading**: Messages loaded on-demand per locale for optimal performance
- **Locale Validation**: Server-side validation prevents invalid locale access
- **SEO Optimization**: Locale prefix only when needed for better URL structure
- **Type Safety**: Full TypeScript support with locale type checking and const assertions
- **Performance**: Efficient message loading and caching with Next.js dynamic imports
- **Flexible Routing**: Supports both localized and non-localized routes
- **RTL Support**: Built-in RTL language detection for Arabic, Hebrew, Persian, etc.
- **Regional Organization**: Locales organized by geographic regions (Americas, Europe, Asia Pacific, etc.)

## Example Implementation

```typescript
// Next.js configuration with next-intl plugin
// apps/web/next.config.mjs
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./i18n/request.ts');

const nextConfig = {
  // ... other config
};

export default withNextIntl(nextConfig);

// Message files structure
// apps/web/i18n/messages/en-US.json
{
  "CurrentConditions": {
    "asOf": "As of {formattedTime}",
    "loadingLocation": "Loading location...",
    "dayTempLabel": "Day",
    "nightTempLabel": "Night"
  },
  "DailyForecast": {
    "headerTitle": "Today's Forecast for {presentationName}",
    "loadingLocation": "Loading location...",
    "loadingForecastData": "Loading forecast data...",
    "unableToLoadData": "Unable to load forecast data",
    "bottomCtaButton": "Next 48 Hours"
  },
  "Header": {
    "searchPlaceholderText": "Search City or Zip Code",
    "recentLocationsTitle": "Recents",
    "noRecentLocations": "You have no recent locations"
  }
}

// Component usage with translations
import { useTranslations } from 'next-intl';

export const WeatherHeader = ({ location }) => {
  const t = useTranslations('DailyForecast');

  return (
    <h1>{t('headerTitle', { presentationName: location.presentationName })}</h1>
  );
};

// Server component usage
import { getTranslations } from 'next-intl/server';

export default async function WeatherPage({ params }) {
  const t = await getTranslations('CurrentConditions');
  const formattedTime = new Date().toLocaleString();

  return (
    <div>
      <p>{t('asOf', { formattedTime })}</p>
    </div>
  );
}

// Internationalized navigation
import { Link } from '@/i18n/navigation';

export const Navigation = () => {
  return (
    <nav>
      <Link href="/weather">Weather</Link>
      <Link href="/forecast">Forecast</Link>
    </nav>
  );
};

// Route structure with locale segments
// apps/web/app/(web)/[code]/[locale]/layout.tsx
export default function LocaleLayout({
  children,
  params: { locale }
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  return (
    <html lang={locale}>
      <body>
        <NextIntlClientProvider locale={locale}>
          {children}
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
```

## Important Notes

- **Locale Validation**: Always validate locales server-side to prevent invalid access
- **Message Organization**: Organize messages by component/feature for better maintainability
- **Dynamic Loading**: Messages are loaded dynamically per locale for optimal performance
- **Route Structure**: Uses `[locale]` dynamic segment for internationalized routes
- **Navigation**: Use internationalized Link, usePathname, and useRouter from navigation.ts
- **Fallback Strategy**: Invalid locales fall back to default locale (en-US)
- **SEO Friendly**: Locale prefix only added when needed, keeping default locale URLs clean
- **Type Safety**: Leverage TypeScript for locale and message key validation
- **Performance**: Efficient message loading with Next.js dynamic imports
- **Scalability**: Architecture supports easy addition of new locales and messages

## File Organization

```
packages/locales/src/
└── index.ts            # Centralized locale definitions and utilities

apps/web/i18n/
├── config.ts           # Default locale configuration
├── routing.ts          # Next-intl routing configuration
├── request.ts          # Server-side request configuration
├── navigation.ts       # Internationalized navigation utilities
└── messages/           # Translation files
    ├── en-US.json      # English (US) translations
    ├── de-DE.json      # German translations
    └── fr-FR.json      # French translations
```

## Integration Points

- **Next.js Configuration**: Integrated via next-intl plugin in next.config.mjs
- **Centralized Package**: Locales managed in @repo/locales package for consistency
- **App Router**: Uses `[locale]` dynamic segments for internationalized routes
- **Server Components**: Supports both client and server-side translation usage
- **Custom Routing**: Works alongside existing custom routing middleware
- **Type System**: Full TypeScript integration with locale and message validation
- **Regional Data**: Comprehensive locale data organized by geographic regions
- **RTL Support**: Built-in right-to-left language detection and utilities

## Locale Package Features

The @repo/locales package provides additional utilities:

```typescript
// Locale format conversion utilities
import {
  convertLocaleFormat,
  convertLocaleFormatReverse,
  findCountryByLocale,
  isRtlLang,
  LOCALE_DATA
} from '@repo/locales';

// Convert between locale formats
convertLocaleFormat('en-US'); // Returns 'en_US'
convertLocaleFormatReverse('en_US'); // Returns 'en-US'

// Find country information by locale
const countryInfo = findCountryByLocale('en-US');
// Returns: { name: 'United States', locale: 'en-US', language: 'English' }

// Check if language is right-to-left
isRtlLang('ar'); // Returns true for Arabic
isRtlLang('en'); // Returns false for English

// Access structured locale data by region
LOCALE_DATA; // Array of regions with countries and locale information
```
