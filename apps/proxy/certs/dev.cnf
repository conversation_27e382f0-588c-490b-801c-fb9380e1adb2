[req]
distinguished_name = req_distinguished_name
x509_extensions = v3_req
prompt = no
[req_distinguished_name]
C = US
ST = Georgia
L = Atlanta
O = TWC
OU = WXU
CN = local.weather.com
[v3_req]
keyUsage = critical, digitalSignature, keyAgreement
extendedKeyUsage = serverAuth
subjectAltName = @alt_names
[alt_names]
DNS.1 = local.weather.com
DNS.2 = dev.weather.com
DNS.3 = *.dev.weather.com
DNS.4 = *.weather.com
