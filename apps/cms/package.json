{"name": "cms", "version": "0.1.0", "type": "module", "private": true, "scripts": {"predev": "payload generate:importmap", "dev": "NODE_OPTIONS='--inspect=localhost:9231' next dev --turbopack --port $(microfrontends port)", "prebuild": "payload generate:importmap", "build": "next build --debug", "build:analyze": "ANALYZE=true pnpm build", "start": "next start", "lint": "eslint --max-warnings=0 .", "check-types": "tsc --noEmit", "clean": "rimraf .turbo .next", "generate:importmap": "payload generate:importmap", "generate:types": "payload generate:types", "test": "vitest run", "test:watch": "vitest watch", "test:e2e": "playwright test --reporter=blob", "test:e2e:ui": "playwright test --ui", "test:e2e:setup": "playwright install --with-deps", "test:e2e:report": "playwright show-report --port=9324", "payload:migrate:fresh": "pnpx payload migrate:fresh", "vercel:link": "pnpx vercel link", "vercel:env:pull": "pnpx vercel env pull --environment=development"}, "dependencies": {"@payloadcms/db-mongodb": "catalog:payload", "@payloadcms/email-nodemailer": "catalog:payload", "@payloadcms/live-preview-react": "catalog:payload", "@payloadcms/next": "catalog:payload", "@payloadcms/payload-cloud": "catalog:payload", "@payloadcms/plugin-cloud-storage": "catalog:payload", "@payloadcms/plugin-multi-tenant": "catalog:payload", "@payloadcms/plugin-oauth2": "catalog:payload", "@payloadcms/plugin-seo": "catalog:payload", "@payloadcms/richtext-lexical": "catalog:payload", "@payloadcms/storage-s3": "catalog:payload", "@payloadcms/ui": "catalog:payload", "@repo/dal": "workspace:*", "@repo/logger": "workspace:*", "@repo/payload": "workspace:*", "@repo/ui": "workspace:*", "@repo/utils": "workspace:*", "@vercel/microfrontends": "catalog:vercel", "@vercel/toolbar": "catalog:vercel", "graphql": "^16.10.0", "lucide-react": "catalog:web", "next": "catalog:web", "payload": "catalog:payload", "react": "catalog:web", "react-dom": "catalog:web", "server-only": "^0.0.1", "sharp": "^0.34.0"}, "devDependencies": {"@next/bundle-analyzer": "^15.4.6", "@playwright/test": "catalog:dev", "@repo/eslint-config": "workspace:*", "@repo/nextjs-image-loader": "workspace:*", "@repo/playwright-utils": "workspace:*", "@repo/typescript-config": "workspace:*", "@repo/vitest-config": "workspace:*", "@testing-library/dom": "catalog:dev", "@testing-library/react": "catalog:dev", "@turbo/gen": "^2.4.4", "@types/node": "catalog:node", "@types/react": "catalog:web", "@types/react-dom": "catalog:web", "eslint": "catalog:dev", "rimraf": "catalog:dev", "typescript": "catalog:dev", "vitest": "catalog:dev"}}