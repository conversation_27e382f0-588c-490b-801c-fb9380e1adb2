import bundleAnalyzer from '@next/bundle-analyzer';
import { withPayload } from '@payloadcms/next/withPayload';
import withVercelToolbarFunc from '@vercel/toolbar/plugins/next';
import { withMicrofrontends } from '@vercel/microfrontends/next/config';

/** @type {import('next').NextConfig} */
const nextConfig = {
	productionBrowserSourceMaps: process.env.PRODUCTION_SOURCEMAPS === 'true',
	expireTime: 60 * 5,
	// use for testing purposes locally
	// reactStrictMode: false,
	images: {
		loader: 'custom',
		loaderFile: './node_modules/@repo/nextjs-image-loader/src/imageLoader.ts',
		remotePatterns: [
			{
				protocol: 'https',
				hostname: 's.w-x.co',
				port: '',
				pathname: '/**',
				search: '',
			},
			{
				protocol: 'https',
				hostname: 's-dev.w-x.co',
				port: '',
				pathname: '/**',
				search: '',
			},
		],
	},
	logging: {
		fetches: {
			fullUrl: true,
		},
	},
	experimental: {
		useCache: true,
	},
};

const withVercelToolbar = withVercelToolbarFunc();

let config = nextConfig;

if (process.env.ANALYZE === 'true') {
	config = bundleAnalyzer({
		enabled: true,
		openAnalyzer: true,
	})(config);
}

config = withVercelToolbar(config);
config = withPayload(config);
config = withMicrofrontends(config, { appName: 'wx-next-cms' });

export default config;
