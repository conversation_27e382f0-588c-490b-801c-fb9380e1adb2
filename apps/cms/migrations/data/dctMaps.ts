import type { Image } from '@repo/payload/payload-types';

export const dctMap1 = (
	tenantId: string,
): Omit<Image, 'createdAt' | 'id' | 'updatedAt'> => ({
	tenant: tenantId, // Add tenant reference
	seo: {
		altText: 'I am a map',
		caption:
			"Shaded on the map above is the likelihood of severe thunderstorms, according to NOAA's Storm Prediction Center. Note that not all categories apply for the severe weather risk on a particular day.",
	},
	prefix: 'wxnext/img',
	filename: 'DCT_SPECIAL42.jpg',
	mimeType: 'image/jpeg',
	filesize: 38057,
	width: 484,
	height: 272,
	focalX: 50,
	focalY: 50,
	sizes: {
		small: {
			width: null,
			height: null,
			mimeType: null,
			filesize: null,
			filename: null,
			url: 'https://s.w-x.co/wxnext/img/null',
		},
		large: {
			width: null,
			height: null,
			mimeType: null,
			filesize: null,
			filename: null,
			url: 'https://s.w-x.co/wxnext/img/null',
		},
	},
	url: 'https://s.w-x.co/staticmaps/DCT_SPECIAL42_1280x720.jpg',
	thumbnailURL: null,
});

export const dctMap2 = (
	tenantId: string,
): Omit<Image, 'createdAt' | 'id' | 'updatedAt'> => ({
	tenant: tenantId, // Add tenant reference
	seo: {
		altText: 'I am a map',
		caption: 'Map black',
	},
	prefix: 'wxnext/img',
	filename: 'DCT_SPECIAL45.jpg',
	mimeType: 'image/jpeg',
	filesize: 38057,
	width: 484,
	height: 272,
	focalX: 50,
	focalY: 50,
	sizes: {
		small: {
			width: null,
			height: null,
			mimeType: null,
			filesize: null,
			filename: null,
			url: 'https://s.w-x.co/wxnext/img/null',
		},
		large: {
			width: null,
			height: null,
			mimeType: null,
			filesize: null,
			filename: null,
			url: 'https://s.w-x.co/wxnext/img/null',
		},
	},
	url: 'https://s.w-x.co/staticmaps/DCT_SPECIAL45_1280x720.jpg',
	thumbnailURL: null,
});
