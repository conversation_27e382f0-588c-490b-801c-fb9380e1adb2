# Seed Data

## Overview

This directory contains seed data for initializing the application with a consistent set of test data. The seed script creates a complete environment with tenants, users, pages, articles, and other necessary data structures.

## Structure

The seed data is organized into separate files by entity type:

- `tenants.ts` - Defines TWC and Burda tenants
- `users.ts` - Creates admin and editor users with proper tenant associations
- `tags.ts` - Creates category, location, and topic tags
- `pages.ts` - Creates home and content pages for both tenants
- `articles.ts` - Creates sample articles with proper relationships
- `image-1.ts` and `image-2.ts` - Sample images with tenant associations

## Seeding Process

The main `seed.ts` file orchestrates the seeding process in a specific order to ensure proper relationships:

1. **Tenants** - Creates TWC and Burda tenants first
2. **Users** - Creates users with tenant associations
3. **Tags** - Creates category, location, and topic tags
4. **Images** - Creates images with tenant associations
5. **User Updates** - Updates users with profile pictures
6. **Pages** - Creates pages for both tenants
7. **Articles** - Creates articles with proper relationships
8. **Context Parameters** - Triggers context parameter creation for pages

## Running the Seed

To run the seed script:
