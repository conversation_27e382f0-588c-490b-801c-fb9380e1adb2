import type { Image } from '@repo/payload/payload-types';

export const image1 = (
	tenantId: string,
): Omit<Image, 'createdAt' | 'id' | 'updatedAt'> => ({
	tenant: tenantId, // Add tenant reference
	seo: {
		altText: 'Test',
		caption: 'test',
	},
	prefix: 'wxnext/img',
	filename: 'screenshotjpg.jpg',
	mimeType: 'image/jpeg',
	filesize: 69446,
	width: 1200,
	height: 900,
	focalX: 50,
	focalY: 50,
	sizes: {
		small: {
			width: 900,
			height: 300,
			mimeType: 'image/jpeg',
			filesize: 15139,
			filename: 'screenshotjpg-900x300.jpg',
			url: 'https://s.w-x.co/wxnext/img/screenshotjpg-900x300.jpg',
		},
		large: {
			width: 1800,
			height: 600,
			mimeType: 'image/jpeg',
			filesize: 32848,
			filename: 'screenshotjpg-1800x600.jpg',
			url: 'https://s.w-x.co/wxnext/img/screenshotjpg-1800x600.jpg',
		},
	},
	url: 'https://s.w-x.co/wxnext/img/screenshotjpg.jpg',
	thumbnailURL: null,
});
