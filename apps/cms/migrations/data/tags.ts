import type { Tag } from '@repo/payload/payload-types';

// Define base tags (tags without parents) for better organization and clarity
export const baseTags: Array<Omit<Tag, 'createdAt' | 'updatedAt' | 'id'>> = [
	{
		name: 'News',
		slug: 'news',
		type: 'categoryTags',
	},
	{
		name: 'Southeast',
		slug: 'southeast',
		type: 'locationTags',
	},
	// Add ad zones here
	{
		name: 'Homepage',
		slug: 'homepage',
		type: 'adZones',
	},
];

// Define tags with parents - these will be created after the base tags
export const childTags: Array<
	Omit<Tag, 'createdAt' | 'updatedAt' | 'id'> & { parentSlug: string }
> = [
	{
		name: 'Storms',
		slug: 'storms',
		type: 'categoryTags',
		parentSlug: 'news', // This will be resolved to an ID during migration
	},
	// Add ad metrics here
	{
		name: 'Homepage Banner',
		slug: 'homepage-banner',
		type: 'adMetrics',
		parentSlug: 'homepage', // This will be resolved to an ID during migration
	},
];

// For backward compatibility - this will be used if the migration isn't updated
export const tags: Array<Omit<Tag, 'createdAt' | 'updatedAt' | 'id'>> = [
	...baseTags,
	// Convert child tags to the old format
	...childTags.map(({ ...tag }) => ({
		...tag,
	})),
];
