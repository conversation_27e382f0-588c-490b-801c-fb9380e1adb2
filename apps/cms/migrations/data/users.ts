import type { User } from '@repo/payload/payload-types';

// Helper function to create user data with tenant references
export const createUserData = (tenant1Id: string, tenant2Id: string) => {
	// Use a more specific type that ensures required fields are present
	const users: Array<
		Omit<User, 'createdAt' | 'updatedAt' | 'id'> & { password: string }
	> = [
		// Global admin who is also tenant admin for both tenants
		{
			username: '<EMAIL>',
			password: 'admin',
			role: ['admin'],
			tenants: [
				{
					roles: ['tenant-admin'],
					tenant: tenant1Id,
				},
				{
					roles: ['tenant-admin'],
					tenant: tenant2Id,
				},
			],
		},
		// Regular user for TWC tenant - this user will be an author
		{
			password: 'demo',
			role: ['editor'],
			tenants: [
				{
					roles: ['tenant-admin'],
					tenant: tenant1Id,
				},
			],
			username: '<EMAIL>',
			isAuthor: true, // Set this user as an author
			authorData: {
				firstName: 'TWC',
				lastName: 'Author',
				bio: 'This is a TWC author who writes articles.',
				bioUrl: 'https://weather.com/bios/twc-author',
				// We'll set the profilePicture after creating images
			},
		},
		// Regular user for Burda tenant - this user will also be an author
		{
			role: ['editor'],
			password: 'demo',
			tenants: [
				{
					roles: ['tenant-admin'],
					tenant: tenant2Id,
				},
			],
			username: '<EMAIL>',
			isAuthor: true, // Set this user as an author
			authorData: {
				firstName: 'Burda',
				lastName: 'Author',
				bio: 'This is a Burda author who writes articles.',
				bioUrl: 'https://weather.com/de-DE/bios/burda-author',
				// We'll set the profilePicture after creating images
			},
		},
	];

	return users;
};
