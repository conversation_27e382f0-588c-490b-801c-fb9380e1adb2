{"extends": "@repo/typescript-config/nextjs.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./*"]}, "plugins": [{"name": "next"}]}, "include": ["../../packages/helios/src/helios.d.ts", "../../packages/dpr-sdk/index.d.ts", "../../packages/jw-player/src/types/jwplayer.d.ts", "next-env.d.ts", "assets.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["app/(payload)/payload/admin", "node_modules", ".next"]}