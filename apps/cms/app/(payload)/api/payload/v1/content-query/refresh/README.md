# Content Query Refresh API

This API endpoint triggers a job to refresh content queries that haven't been synced recently.

## Overview

The `/api/payload/v1/content-query/refresh` endpoint queues a `refreshContentQueries` job that finds content queries that need refreshing and then queues individual jobs to update each query's content. This is particularly useful for keeping content up-to-date without manual intervention.

## How It Works

1. **Initial Job**: When this endpoint is called, it queues a `refreshContentQueries` job in the `sync` queue.

2. **Query Discovery**: The job scans the database for content queries and identifies those that need refreshing:

   - Queries that have never been synced
   - Queries that haven't been synced in the last 5 minutes
   - Queries that aren't currently locked by another job

3. **Job Delegation**: For each query that needs refreshing, the job queues an appropriate task based on the query type:

   - `getCMAssetByID` for Kalliope content media queries
   - `getContentByTags` for queries based on tags, categories, or topics
   - `getContentByIds` for queries based on specific content IDs

4. **Content Processing**: Each delegated job:
   - Locks the query to prevent concurrent updates
   - Fetches fresh content from the appropriate source
   - Merges the new content with existing content
   - Updates the query with the new content
   - Updates the `lastSynced` timestamp
   - Unlocks the query when complete

## Usage

This endpoint is typically called by a cron job every 5 minutes, but it can also be called manually to force a refresh.
