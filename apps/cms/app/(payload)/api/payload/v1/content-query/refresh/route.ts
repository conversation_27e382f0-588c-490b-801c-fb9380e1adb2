import { NextResponse } from 'next/server';
import { getPayload } from 'payload';
import config from '@repo/payload/payload-config';

export async function GET() {
	let logger;
	try {
		// Initialize payload properly
		const payload = await getPayload({ config });
		logger = payload.logger;

		// Queue the job to refresh queries not synced in the last 5 minutes
		const job = await payload.jobs.queue({
			task: 'refreshContentQueries',
			input: {
				lastSyncedBefore: undefined, // Use default logic in the handler
				limit: 20,
			},
			queue: 'sync',
		});

		logger.info(`Queued content queries refresh job: ${job.id}`);

		return NextResponse.json({
			success: true,
			message: 'Content queries refresh job queued successfully',
			jobId: job.id,
		});
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logger?.error('Error queueing content queries refresh job:', error);

		return NextResponse.json(
			{
				success: false,
				message: 'Failed to queue content queries refresh job',
				error: errorMessage,
			},
			{ status: 500 },
		);
	}
}
