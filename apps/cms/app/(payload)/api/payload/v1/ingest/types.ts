// Shared types for partner content ingestion

export interface BaseIngestRequest {
	partner: string;
	version?: string;
	metadata?: Record<string, unknown>;
}

export interface IngestResponse<T = unknown> {
	success: boolean;
	partner: string;
	processed: number;
	created: number;
	errorCount: number;
	results: T[];
	errors?: IngestError[];
	metadata?: Record<string, unknown>;
}

export interface IngestError {
	index: number;
	item: string;
	error: string;
	code?:
		| 'AUTH_FAILED'
		| 'INVALID_BODY'
		| 'EMPTY_LOCATIONS'
		| 'PAYLOAD_INIT_FAILED'
		| 'PROCESSING_ERROR'
		| 'FIND_ERROR'
		| 'DUPLICATE_LOCATION'
		| 'CREATE_ERROR'
		| 'INTERNAL_ERROR';
}

// Partner-specific data types
export interface SunLocationData {
	language: string;
	displayname: string;
	city: string;
	admindistrictcode: string;
	admindistrict: string;
	country: string;
	countrycode: string;
	latitude: number;
	longitude: number;
	location_data: {
		poitype: string;
		icaoId?: string;
		iataId?: string;
	};
}

export interface SunLocationIngestRequest extends BaseIngestRequest {
	partner: 'sun';
	locations: SunLocationData[];
}

// Future partner types can be added here
// export interface PartnerBArticleData { ... }
// export interface PartnerBArticleIngestRequest extends BaseIngestRequest { ... }

// Response data types
export interface LocationIngestResult {
	index: number;
	id: string;
	displayName: string;
	locationType: string;
}

// Union type for all supported ingest requests
export type IngestRequest = SunLocationIngestRequest;

// Supported partners enum
export enum SupportedPartners {
	SUN = 'sun',
}

// Content types enum
export enum ContentTypes {
	LOCATIONS = 'locations',
	ARTICLES = 'articles',
	MEDIA = 'media',
}
