import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { NextRequest } from 'next/server';
import { POST, GET } from './route';
import { getPayload } from 'payload';
import { getUserFromHeaders } from '@repo/payload/utils/auth/getPayloadUser';
import { transformSunLocation } from '../utils';
import { SupportedPartners, SunLocationIngestRequest } from '../types';

// Mock dependencies
vi.mock('payload');
vi.mock('@repo/payload/utils/auth/getPayloadUser');
vi.mock('../utils');
vi.mock('@repo/logger', () => ({
	createLogger: () => ({
		info: vi.fn(),
		error: vi.fn(),
		warn: vi.fn(),
	}),
}));

const mockGetPayload = vi.mocked(getPayload);
const mockGetUserFromHeaders = vi.mocked(getUserFromHeaders);
const mockTransformSunLocation = vi.mocked(transformSunLocation);

// Test data
const validLocationData = {
	language: 'en',
	displayname: 'Test Airport',
	city: 'Test City',
	admindistrictcode: 'TC',
	admindistrict: 'Test State',
	country: 'Test Country',
	countrycode: 'TC',
	latitude: 40.7128,
	longitude: -74.006,
	location_data: {
		poitype: 'airport',
		icaoId: 'TEST',
		iataId: 'TST',
	},
};

const validRequestBody: SunLocationIngestRequest = {
	partner: SupportedPartners.SUN,
	locations: [validLocationData],
};

const transformedLocation = {
	displayName: 'Test Airport',
	city: 'Test City',
	adminDistrict: 'Test State',
	adminDistrictCode: 'TC',
	country: 'Test Country',
	countryCode: 'TC',
	latitude: 40.7128,
	longitude: -74.006,
	locationPoint: [-74.006, 40.7128] as [number, number],
	language: 'en',
	locationType: 'airport' as const,
	status: 'published',
	locationData: {
		airport: {
			icaoCode: 'TEST',
			iataCode: 'TST',
		},
	},
};

const mockUser = {
	id: 'user123',
	role: ['admin'] as ('admin' | 'web-developer' | 'editor' | 'authenticated')[],
	updatedAt: '2024-01-01T00:00:00.000Z',
	createdAt: '2024-01-01T00:00:00.000Z',
	collection: 'users' as const,
};
const mockPayload = {
	find: vi.fn(),
	create: vi.fn(),
};

describe('/api/payload/v1/ingest/locations', () => {
	beforeEach(() => {
		vi.clearAllMocks();
		mockGetUserFromHeaders.mockResolvedValue(mockUser);
		mockGetPayload.mockResolvedValue(mockPayload as any);
		mockTransformSunLocation.mockReturnValue(transformedLocation);
	});

	afterEach(() => {
		vi.resetAllMocks();
	});

	describe('GET', () => {
		it('should return API documentation', async () => {
			const response = await GET();
			const data = await response.json();

			expect(response.status).toBe(200);
			expect(data).toMatchObject({
				endpoint: '/api/payload/v1/ingest/locations',
				method: 'POST',
				description: 'Ingest locations from partner systems into Payload CMS',
				version: '1.0.0',
				authentication: 'Payload API Key required in Authorization header',
			});
			expect(data.schema).toBeDefined();
			expect(data.example).toBeDefined();
		});
	});

	describe('POST', () => {
		const createMockRequest = (
			body: any,
			headers: Record<string, string> = {},
		) => {
			const request = new NextRequest(
				'http://localhost:3000/api/payload/v1/ingest/locations',
				{
					method: 'POST',
					body: JSON.stringify(body),
					headers: {
						'Content-Type': 'application/json',
						...headers,
					},
				},
			);
			return request;
		};

		describe('Authentication', () => {
			it('should return 401 when user is not authenticated', async () => {
				mockGetUserFromHeaders.mockResolvedValue(null);
				const request = createMockRequest(validRequestBody);

				const response = await POST(request);
				const data = await response.json();

				expect(response.status).toBe(401);
				expect(data).toMatchObject({
					success: false,
					partner: SupportedPartners.SUN,
					processed: 0,
					created: 0,
					errorCount: 1,
					results: [],
					errors: [
						{
							index: 0,
							item: 'authentication',
							error: 'Authentication failed or internal server error',
							code: 'AUTH_FAILED',
						},
					],
				});
			});

			it('should proceed when user is authenticated', async () => {
				mockPayload.find.mockResolvedValue({ docs: [] });
				mockPayload.create.mockResolvedValue({
					id: 'loc123',
					displayName: 'Test Airport',
					locationType: 'airport',
				});

				const request = createMockRequest(validRequestBody);
				const response = await POST(request);

				expect(response.status).toBe(200);
				expect(mockGetUserFromHeaders).toHaveBeenCalledWith(request.headers);
			});
		});

		describe('Request Body Validation', () => {
			it('should return 400 for invalid JSON', async () => {
				const request = new NextRequest(
					'http://localhost:3000/api/payload/v1/ingest/locations',
					{
						method: 'POST',
						body: 'invalid json',
						headers: { 'Content-Type': 'application/json' },
					},
				);

				const response = await POST(request);
				const data = await response.json();

				expect(response.status).toBe(400);
				expect(data).toMatchObject({
					success: false,
					partner: SupportedPartners.SUN,
					processed: 0,
					created: 0,
					errorCount: 1,
					results: [],
					errors: [
						{
							index: 0,
							item: 'request_body',
							error: 'Invalid request body',
							code: 'INVALID_BODY',
						},
					],
				});
			});

			it('should return 400 for empty locations array', async () => {
				const requestBody = { ...validRequestBody, locations: [] };
				const request = createMockRequest(requestBody);

				const response = await POST(request);
				const data = await response.json();

				expect(response.status).toBe(400);
				expect(data).toMatchObject({
					success: false,
					partner: SupportedPartners.SUN,
					processed: 0,
					created: 0,
					errorCount: 1,
					results: [],
					errors: [
						{
							index: 0,
							item: 'locations',
							error: 'Locations array is required and must not be empty',
							code: 'EMPTY_LOCATIONS',
						},
					],
				});
			});

			it('should return 400 for missing locations array', async () => {
				const requestBody = { partner: SupportedPartners.SUN };
				const request = createMockRequest(requestBody);

				const response = await POST(request);
				const data = await response.json();

				expect(response.status).toBe(400);
				expect(data.errors?.[0]?.code).toBe('EMPTY_LOCATIONS');
			});
		});

		describe('Payload Initialization', () => {
			it('should return 500 when Payload initialization fails', async () => {
				mockGetPayload.mockRejectedValue(new Error('Payload init failed'));
				const request = createMockRequest(validRequestBody);

				const response = await POST(request);
				const data = await response.json();

				expect(response.status).toBe(500);
				expect(data).toMatchObject({
					success: false,
					partner: SupportedPartners.SUN,
					processed: 0,
					created: 0,
					errorCount: 1,
					results: [],
					errors: [
						{
							index: 0,
							item: 'payload_init',
							error: 'Failed to initialize Payload CMS',
							code: 'PAYLOAD_INIT_FAILED',
						},
					],
				});
			});
		});

		describe('Location Processing', () => {
			beforeEach(() => {
				mockPayload.find.mockResolvedValue({ docs: [] });
				mockPayload.create.mockResolvedValue({
					id: 'loc123',
					displayName: 'Test Airport',
					locationType: 'airport',
				});
			});

			it('should successfully process valid locations', async () => {
				const request = createMockRequest(validRequestBody);
				const response = await POST(request);
				const data = await response.json();

				expect(response.status).toBe(200);
				expect(data).toMatchObject({
					success: true,
					partner: SupportedPartners.SUN,
					processed: 1,
					created: 1,
					errorCount: 0,
					results: [
						{
							index: 0,
							id: 'loc123',
							displayName: 'Test Airport',
							locationType: 'airport',
						},
					],
				});

				expect(mockTransformSunLocation).toHaveBeenCalledWith(
					validLocationData,
				);
				expect(mockPayload.find).toHaveBeenCalledWith({
					collection: 'locations',
					where: {
						displayName: {
							equals: transformedLocation.displayName,
						},
					},
					req: request,
				});
				expect(mockPayload.create).toHaveBeenCalledWith({
					collection: 'locations',
					data: transformedLocation,
					req: request,
				});
			});

			it('should handle duplicate locations', async () => {
				mockPayload.find.mockResolvedValue({
					docs: [{ id: 'existing123', displayName: 'Test Airport' }],
				});

				const request = createMockRequest(validRequestBody);
				const response = await POST(request);
				const data = await response.json();

				expect(response.status).toBe(200);
				expect(data).toMatchObject({
					success: true,
					partner: SupportedPartners.SUN,
					processed: 1,
					created: 0,
					errorCount: 1,
					results: [],
					errors: [
						{
							index: 0,
							item: validLocationData.displayname,
							error: 'Location already exists with IDs: existing123',
							code: 'DUPLICATE_LOCATION',
						},
					],
				});

				expect(mockPayload.create).not.toHaveBeenCalled();
			});

			it('should handle transformation errors', async () => {
				mockTransformSunLocation.mockImplementation(() => {
					throw new Error('Invalid POI type');
				});

				const request = createMockRequest(validRequestBody);
				const response = await POST(request);
				const data = await response.json();

				expect(response.status).toBe(200);
				expect(data).toMatchObject({
					success: true,
					partner: SupportedPartners.SUN,
					processed: 1,
					created: 0,
					errorCount: 1,
					results: [],
					errors: [
						{
							index: 0,
							item: validLocationData.displayname,
							error: 'Invalid POI type',
							code: 'PROCESSING_ERROR',
						},
					],
				});
			});

			it('should handle find operation errors', async () => {
				mockPayload.find.mockRejectedValue(new Error('Database error'));

				const request = createMockRequest(validRequestBody);
				const response = await POST(request);
				const data = await response.json();

				expect(response.status).toBe(200);
				expect(data.errors?.[0]).toMatchObject({
					index: 0,
					item: validLocationData.displayname,
					error: 'Failed to check existing locations: Database error',
					code: 'FIND_ERROR',
				});
			});

			it('should handle create operation errors', async () => {
				mockPayload.create.mockRejectedValue(new Error('Create failed'));

				const request = createMockRequest(validRequestBody);
				const response = await POST(request);
				const data = await response.json();

				expect(response.status).toBe(200);
				expect(data.errors?.[0]).toMatchObject({
					index: 0,
					item: validLocationData.displayname,
					error: 'Create failed',
					code: 'CREATE_ERROR',
				});
			});

			it('should process multiple locations with mixed results', async () => {
				const multiLocationRequest = {
					...validRequestBody,
					locations: [
						validLocationData,
						{ ...validLocationData, displayname: 'Second Location' },
					],
				};

				// First location succeeds, second fails
				mockPayload.find
					.mockResolvedValueOnce({ docs: [] })
					.mockRejectedValueOnce(new Error('Database error'));

				mockPayload.create.mockResolvedValue({
					id: 'loc123',
					displayName: 'Test Airport',
					locationType: 'airport',
				});

				const request = createMockRequest(multiLocationRequest);
				const response = await POST(request);
				const data = await response.json();

				expect(response.status).toBe(200);
				expect(data).toMatchObject({
					success: true,
					partner: SupportedPartners.SUN,
					processed: 2,
					created: 1,
					errorCount: 1,
				});
				expect(data.results).toHaveLength(1);
				expect(data.errors).toHaveLength(1);
			});

			it('should include metadata in response when provided', async () => {
				const requestWithMetadata = {
					...validRequestBody,
					metadata: { source: 'test', version: '1.0' },
				};

				const request = createMockRequest(requestWithMetadata);
				const response = await POST(request);
				const data = await response.json();

				expect(response.status).toBe(200);
				expect(data.metadata).toEqual({ source: 'test', version: '1.0' });
			});
		});

		describe('Error Handling', () => {
			it('should handle unexpected errors with 500 response', async () => {
				mockGetUserFromHeaders.mockRejectedValue(new Error('Unexpected error'));

				const request = createMockRequest(validRequestBody);
				const response = await POST(request);
				const data = await response.json();

				expect(response.status).toBe(500);
				expect(data).toMatchObject({
					success: false,
					partner: SupportedPartners.SUN,
					processed: 0,
					created: 0,
					errorCount: 1,
					results: [],
					errors: [
						{
							index: 0,
							item: 'request',
							error: 'Unexpected error',
							code: 'INTERNAL_ERROR',
						},
					],
				});
			});
		});
	});
});
