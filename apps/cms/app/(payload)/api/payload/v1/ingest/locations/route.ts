import { NextRequest, NextResponse } from 'next/server';
import { getPayload, type Payload } from 'payload';
import config from '@repo/payload/payload-config';
import { createLogger } from '@repo/logger';
import { tryCatch } from '@repo/utils/tryCatch';
import {
	SunLocationIngestRequest,
	IngestResponse,
	IngestError,
	SupportedPartners,
	LocationIngestResult,
} from '../types';
import { transformSunLocation } from '../utils';
import { hasLength } from '@repo/utils/hasLength';
import { getUserFromHeaders } from '@repo/payload/utils/auth/getPayloadUser';
import { LocationsSelect } from '@repo/payload/payload-types';

const logger = createLogger('payload:LocationIngestAPI');

export async function POST(request: NextRequest) {
	logger.info('Location ingest request received');

	const { data: response, error } = await tryCatch(
		handleLocationIngest(request),
	);

	if (error) {
		logger.error('Location ingest failed', { error: error.message });
		return NextResponse.json(
			{
				success: false,
				partner: SupportedPartners.SUN,
				processed: 0,
				created: 0,
				errorCount: 1,
				results: [],
				errors: [
					{
						index: 0,
						item: 'request',
						error: error.message,
						code: 'INTERNAL_ERROR',
					},
				],
			} satisfies IngestResponse<LocationIngestResult>,
			{ status: 500 },
		);
	}

	return NextResponse.json(response.data, { status: response.status });
}

async function handleLocationIngest(request: NextRequest): Promise<{
	data: IngestResponse<LocationIngestResult>;
	status: number;
}> {
	// Authentication check
	const user = await getUserFromHeaders(request?.headers);
	if (!user) {
		return {
			data: {
				success: false,
				partner: SupportedPartners.SUN,
				processed: 0,
				created: 0,
				errorCount: 1,
				results: [],
				errors: [
					{
						index: 0,
						item: 'authentication',
						error: 'Authentication failed or internal server error',
						code: 'AUTH_FAILED',
					},
				],
			},
			status: 401,
		};
	}

	// Parse and validate request body
	const { data: body, error: parseError } = await tryCatch(
		request.json() as Promise<SunLocationIngestRequest>,
	);

	if (parseError || !body) {
		logger.error('Invalid request body', { error: parseError?.message });
		return {
			data: {
				success: false,
				partner: SupportedPartners.SUN,
				processed: 0,
				created: 0,
				errorCount: 1,
				results: [],
				errors: [
					{
						index: 0,
						item: 'request_body',
						error: 'Invalid request body',
						code: 'INVALID_BODY',
					},
				],
			},
			status: 400,
		};
	}

	// Validate locations array
	if (!hasLength(body.locations)) {
		logger.error('Invalid locations data');
		return {
			data: {
				success: false,
				partner: body.partner || SupportedPartners.SUN,
				processed: 0,
				created: 0,
				errorCount: 1,
				results: [],
				errors: [
					{
						index: 0,
						item: 'locations',
						error: 'Locations array is required and must not be empty',
						code: 'EMPTY_LOCATIONS',
					},
				],
			},
			status: 400,
		};
	}

	logger.info('Processing location ingest', {
		locationCount: body.locations.length,
		partner: body.partner,
	});

	// Get Payload instance
	const { data: payload, error: payloadError } = await tryCatch(
		getPayload({ config }),
	);

	if (payloadError || !payload) {
		logger.error('Failed to initialize Payload', {
			error: payloadError?.message,
		});
		return {
			data: {
				success: false,
				partner: body.partner || SupportedPartners.SUN,
				processed: 0,
				created: 0,
				errorCount: 1,
				results: [],
				errors: [
					{
						index: 0,
						item: 'payload_init',
						error: 'Failed to initialize Payload CMS',
						code: 'PAYLOAD_INIT_FAILED',
					},
				],
			},
			status: 500,
		};
	}

	// Process locations batch
	const { results, errors } = await processLocationBatch(
		body.locations,
		payload,
		request,
	);

	const response: IngestResponse<LocationIngestResult> = {
		success: true,
		partner: body.partner || SupportedPartners.SUN,
		processed: body.locations.length,
		created: results.length,
		errorCount: errors.length,
		results,
		...(errors.length > 0 && { errors }),
		...(body.metadata && { metadata: body.metadata }),
	};

	logger.info('Location ingest completed', {
		partner: body.partner,
		processed: body.locations.length,
		created: results.length,
		errorCount: errors.length,
	});

	return {
		data: response,
		status: 200,
	};
}

async function processLocationBatch(
	locations: SunLocationIngestRequest['locations'],
	payload: Payload,
	request: NextRequest,
): Promise<{ results: LocationIngestResult[]; errors: IngestError[] }> {
	const results: LocationIngestResult[] = [];
	const errors: IngestError[] = [];

	for (const [index, sunLocation] of locations.entries()) {
		const { data: locationResult, error: locationError } = await tryCatch(
			processIndividualLocation(sunLocation, index, payload, request),
		);

		if (locationError) {
			logger.error('Failed to process location', {
				index,
				displayName: sunLocation.displayname,
				error: locationError.message,
			});
			errors.push({
				index,
				item: sunLocation.displayname,
				error: locationError.message,
				code: 'PROCESSING_ERROR',
			});
		} else if (locationResult) {
			if (locationResult.type === 'success') {
				results.push(locationResult.data);
			} else {
				errors.push(locationResult.error);
			}
		}
	}

	return { results, errors };
}

async function processIndividualLocation(
	sunLocation: SunLocationIngestRequest['locations'][0],
	index: number,
	payload: Payload,
	request: NextRequest,
): Promise<
	| { type: 'success'; data: LocationIngestResult }
	| { type: 'error'; error: IngestError }
> {
	// Transform the location data
	const transformedLocation = transformSunLocation(sunLocation);

	// Check for existing locations
	const { data: existingLocations, error: findError } = await tryCatch(
		payload.find({
			collection: 'locations',
			where: {
				displayName: {
					equals: transformedLocation.displayName,
				},
			},
			req: request,
		}),
	);

	if (findError) {
		return {
			type: 'error',
			error: {
				index,
				item: sunLocation.displayname,
				error: `Failed to check existing locations: ${findError.message}`,
				code: 'FIND_ERROR',
			},
		};
	}

	if (hasLength(existingLocations?.docs)) {
		return {
			type: 'error',
			error: {
				index,
				item: sunLocation.displayname,
				error: `Location already exists with IDs: ${existingLocations.docs.map((loc) => loc?.id).join(', ')}`,
				code: 'DUPLICATE_LOCATION',
			},
		};
	}

	logger.info('Creating location', {
		index,
		displayName: transformedLocation.displayName,
		locationType: transformedLocation.locationType,
	});

	// Create location in Payload
	// TODO: Improve this logic to take in languages so we can update them.
	const { data: createdLocation, error: createError } = await tryCatch(
		payload.create<'locations', LocationsSelect>({
			collection: 'locations',
			data: transformedLocation,
			req: request,
		}),
	);

	if (createError) {
		return {
			type: 'error',
			error: {
				index,
				item: sunLocation.displayname,
				error: createError.message,
				code: 'CREATE_ERROR',
			},
		};
	}

	logger.info('Location created successfully', {
		index,
		id: createdLocation.id,
		displayName: createdLocation.displayName,
	});

	return {
		type: 'success',
		data: {
			index,
			id: createdLocation.id,
			displayName: createdLocation.displayName,
			locationType: createdLocation.locationType,
		},
	};
}

// GET method for API documentation/health check
export async function GET() {
	return NextResponse.json({
		endpoint: '/api/payload/v1/ingest/locations',
		method: 'POST',
		description: 'Ingest locations from partner systems into Payload CMS',
		version: '1.0.0',
		authentication: 'Payload API Key required in Authorization header',
		schema: {
			locations: 'array of location objects (required)',
		},
		example: {
			locations: [
				{
					language: 'en',
					displayname: 'Southwest Georgia Regional Airport',
					city: 'Albany',
					admindistrictcode: 'GA',
					admindistrict: 'Georgia',
					country: 'United States',
					countrycode: 'US',
					latitude: 31.532946,
					longitude: -84.196215,
					location_data: {
						poitype: 'airport',
						icaoId: 'KABY',
						iataId: 'ABY',
					},
				},
			],
		},
	});
}
