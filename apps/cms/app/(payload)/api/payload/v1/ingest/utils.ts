import { NextRequest } from 'next/server';
import { SupportedPartners, SunLocationData } from './types';
import { createLogger } from '@repo/logger';
import { Poi } from '@repo/payload/payload-types';

const logger = createLogger('payload:IngestUtils');

// Partner validation utilities
export function validatePartner(
	partner: string,
	_request: NextRequest,
): boolean {
	// For now, simple partner validation - in production, use proper API keys
	const validPartners = Object.values(SupportedPartners);
	return validPartners.includes(partner as SupportedPartners);
}

// Valid POI types from the schema
const VALID_POI_TYPES = [
	'amusement park',
	'beach',
	'campground',
	'fair ground',
	'hiking trail',
	'motorsport racing track',
	'national park',
	'ski',
	'sports venue',
	'stadium',
	'state park',
	'theme park',
	'urban park',
] as const;

type ValidPoiType = NonNullable<Poi['poiType']>;

function isValidPoiType(poiType: string): poiType is ValidPoiType {
	return VALID_POI_TYPES.includes(poiType as ValidPoiType);
}

// Location transformation utilities
export function transformSunLocation(sunLocation: SunLocationData) {
	const isAirport = sunLocation.location_data.poitype === 'airport';

	const baseLocation = {
		displayName: sunLocation.displayname,
		city: sunLocation.city,
		adminDistrict: sunLocation.admindistrict,
		adminDistrictCode: sunLocation.admindistrictcode,
		country: sunLocation.country,
		countryCode: sunLocation.countrycode,
		latitude: sunLocation.latitude,
		longitude: sunLocation.longitude,
		locationPoint: [sunLocation.longitude, sunLocation.latitude] as [
			number,
			number,
		], // GeoJSON format: [lng, lat]
		language: sunLocation.language,
		locationType: isAirport ? ('airport' as const) : ('poi' as const),
		status: 'published',
	};

	// Add location-specific data based on type
	if (isAirport) {
		return {
			...baseLocation,
			locationData: {
				airport: {
					icaoCode: sunLocation.location_data.icaoId,
					iataCode: sunLocation.location_data.iataId,
				},
			},
		};
	} else {
		// Validate POI type
		const poiType = sunLocation.location_data.poitype;
		if (!isValidPoiType(poiType)) {
			logger.warn('Invalid POI type received', {
				poiType,
				validTypes: VALID_POI_TYPES,
				location: sunLocation.displayname,
			});
			throw new Error(
				`Invalid POI type: ${poiType}. Valid types are: ${VALID_POI_TYPES.join(', ')}`,
			);
		}

		return {
			...baseLocation,
			locationData: {
				poi: {
					poiType: poiType,
				},
			},
		};
	}
}
