import { describe, it, expect } from 'vitest';
import { transformSunLocation } from './utils';
import { SunLocationData } from './types';

describe('transformSunLocation', () => {
	describe('Airport locations', () => {
		it('should transform airport location with ICAO and IATA codes', () => {
			const sunLocation: SunLocationData = {
				language: 'en',
				displayname: 'John F. Kennedy International Airport',
				city: 'New York',
				admindistrictcode: 'NY',
				admindistrict: 'New York',
				country: 'United States',
				countrycode: 'US',
				latitude: 40.6413,
				longitude: -73.7781,
				location_data: {
					poitype: 'airport',
					icaoId: 'KJFK',
					iataId: 'JFK',
				},
			};

			const result = transformSunLocation(sunLocation);

			expect(result).toEqual({
				displayName: 'John F. Kennedy International Airport',
				city: 'New York',
				adminDistrict: 'New York',
				adminDistrictCode: 'NY',
				country: 'United States',
				countryCode: 'US',
				latitude: 40.6413,
				longitude: -73.7781,
				locationPoint: [-73.7781, 40.6413],
				language: 'en',
				locationType: 'airport',
				status: 'published',
				locationData: {
					airport: {
						icaoCode: 'KJFK',
						iataCode: 'JFK',
					},
				},
			});
		});

		it('should transform airport location with only ICAO code', () => {
			const sunLocation: SunLocationData = {
				language: 'en',
				displayname: 'Test Airport',
				city: 'Test City',
				admindistrictcode: 'TC',
				admindistrict: 'Test State',
				country: 'Test Country',
				countrycode: 'TC',
				latitude: 40.7128,
				longitude: -74.006,
				location_data: {
					poitype: 'airport',
					icaoId: 'TEST',
				},
			};

			const result = transformSunLocation(sunLocation);

			expect(result.locationData?.airport).toEqual({
				icaoCode: 'TEST',
				iataCode: undefined,
			});
		});

		it('should transform airport location with only IATA code', () => {
			const sunLocation: SunLocationData = {
				language: 'en',
				displayname: 'Test Airport',
				city: 'Test City',
				admindistrictcode: 'TC',
				admindistrict: 'Test State',
				country: 'Test Country',
				countrycode: 'TC',
				latitude: 40.7128,
				longitude: -74.006,
				location_data: {
					poitype: 'airport',
					iataId: 'TST',
				},
			};

			const result = transformSunLocation(sunLocation);

			expect(result.locationData?.airport).toEqual({
				icaoCode: undefined,
				iataCode: 'TST',
			});
		});

		it('should transform airport location without codes', () => {
			const sunLocation: SunLocationData = {
				language: 'en',
				displayname: 'Test Airport',
				city: 'Test City',
				admindistrictcode: 'TC',
				admindistrict: 'Test State',
				country: 'Test Country',
				countrycode: 'TC',
				latitude: 40.7128,
				longitude: -74.006,
				location_data: {
					poitype: 'airport',
				},
			};

			const result = transformSunLocation(sunLocation);

			expect(result.locationData?.airport).toEqual({
				icaoCode: undefined,
				iataCode: undefined,
			});
		});
	});

	describe('POI locations', () => {
		it('should transform POI location with valid POI type', () => {
			const sunLocation: SunLocationData = {
				language: 'en',
				displayname: 'Central Park',
				city: 'New York',
				admindistrictcode: 'NY',
				admindistrict: 'New York',
				country: 'United States',
				countrycode: 'US',
				latitude: 40.7829,
				longitude: -73.9654,
				location_data: {
					poitype: 'urban park',
				},
			};

			const result = transformSunLocation(sunLocation);

			expect(result).toEqual({
				displayName: 'Central Park',
				city: 'New York',
				adminDistrict: 'New York',
				adminDistrictCode: 'NY',
				country: 'United States',
				countryCode: 'US',
				latitude: 40.7829,
				longitude: -73.9654,
				locationPoint: [-73.9654, 40.7829],
				language: 'en',
				locationType: 'poi',
				status: 'published',
				locationData: {
					poi: {
						poiType: 'urban park',
					},
				},
			});
		});

		it('should transform POI location with all supported POI types', () => {
			const supportedPoiTypes = [
				'amusement park',
				'beach',
				'campground',
				'fair ground',
				'hiking trail',
				'motorsport racing track',
				'national park',
				'ski',
				'sports venue',
				'stadium',
				'state park',
				'theme park',
				'urban park',
			];

			supportedPoiTypes.forEach((poiType) => {
				const sunLocation: SunLocationData = {
					language: 'en',
					displayname: `Test ${poiType}`,
					city: 'Test City',
					admindistrictcode: 'TC',
					admindistrict: 'Test State',
					country: 'Test Country',
					countrycode: 'TC',
					latitude: 40.7128,
					longitude: -74.006,
					location_data: {
						poitype: poiType,
					},
				};

				const result = transformSunLocation(sunLocation);

				expect(result.locationType).toBe('poi');
				expect(result.locationData?.poi?.poiType).toBe(poiType);
			});
		});

		it('should throw error for unsupported POI type', () => {
			const sunLocation: SunLocationData = {
				language: 'en',
				displayname: 'Test Location',
				city: 'Test City',
				admindistrictcode: 'TC',
				admindistrict: 'Test State',
				country: 'Test Country',
				countrycode: 'TC',
				latitude: 40.7128,
				longitude: -74.006,
				location_data: {
					poitype: 'unsupported type',
				},
			};

			expect(() => transformSunLocation(sunLocation)).toThrow(
				'Invalid POI type: unsupported type',
			);
		});
	});

	describe('Location point transformation', () => {
		it('should create location point as [longitude, latitude]', () => {
			const sunLocation: SunLocationData = {
				language: 'en',
				displayname: 'Test Location',
				city: 'Test City',
				admindistrictcode: 'TC',
				admindistrict: 'Test State',
				country: 'Test Country',
				countrycode: 'TC',
				latitude: 40.7128,
				longitude: -74.006,
				location_data: {
					poitype: 'airport',
				},
			};

			const result = transformSunLocation(sunLocation);

			// GeoJSON format: [longitude, latitude]
			expect(result.locationPoint).toEqual([-74.006, 40.7128]);
		});

		it('should handle negative coordinates correctly', () => {
			const sunLocation: SunLocationData = {
				language: 'en',
				displayname: 'Test Location',
				city: 'Test City',
				admindistrictcode: 'TC',
				admindistrict: 'Test State',
				country: 'Test Country',
				countrycode: 'TC',
				latitude: -33.8688,
				longitude: 151.2093,
				location_data: {
					poitype: 'airport',
				},
			};

			const result = transformSunLocation(sunLocation);

			expect(result.locationPoint).toEqual([151.2093, -33.8688]);
		});
	});

	describe('Field mapping', () => {
		it('should map all basic fields correctly', () => {
			const sunLocation: SunLocationData = {
				language: 'fr',
				displayname: 'Aéroport de Test',
				city: 'Ville de Test',
				admindistrictcode: 'VT',
				admindistrict: 'État de Test',
				country: 'Pays de Test',
				countrycode: 'PT',
				latitude: 48.8566,
				longitude: 2.3522,
				location_data: {
					poitype: 'airport',
				},
			};

			const result = transformSunLocation(sunLocation);

			expect(result.displayName).toBe('Aéroport de Test');
			expect(result.city).toBe('Ville de Test');
			expect(result.adminDistrict).toBe('État de Test');
			expect(result.adminDistrictCode).toBe('VT');
			expect(result.country).toBe('Pays de Test');
			expect(result.countryCode).toBe('PT');
			expect(result.language).toBe('fr');
			expect(result.latitude).toBe(48.8566);
			expect(result.longitude).toBe(2.3522);
		});

		it('should set status to published by default', () => {
			const sunLocation: SunLocationData = {
				language: 'en',
				displayname: 'Test Location',
				city: 'Test City',
				admindistrictcode: 'TC',
				admindistrict: 'Test State',
				country: 'Test Country',
				countrycode: 'TC',
				latitude: 40.7128,
				longitude: -74.006,
				location_data: {
					poitype: 'airport',
				},
			};

			const result = transformSunLocation(sunLocation);

			expect(result.status).toBe('published');
		});
	});

	describe('Error handling', () => {
		it('should throw error for missing location_data', () => {
			const sunLocation = {
				language: 'en',
				displayname: 'Test Location',
				city: 'Test City',
				admindistrictcode: 'TC',
				admindistrict: 'Test State',
				country: 'Test Country',
				countrycode: 'TC',
				latitude: 40.7128,
				longitude: -74.006,
			} as SunLocationData;

			expect(() => transformSunLocation(sunLocation)).toThrow(
				'Cannot read properties of undefined',
			);
		});

		it('should throw error for missing poitype', () => {
			const sunLocation = {
				language: 'en',
				displayname: 'Test Location',
				city: 'Test City',
				admindistrictcode: 'TC',
				admindistrict: 'Test State',
				country: 'Test Country',
				countrycode: 'TC',
				latitude: 40.7128,
				longitude: -74.006,
				location_data: {},
			} as SunLocationData;

			expect(() => transformSunLocation(sunLocation)).toThrow(
				'Invalid POI type: undefined',
			);
		});

		it('should handle missing optional fields gracefully', () => {
			const sunLocation = {
				language: 'en',
				displayname: 'Test Location',
				latitude: 40.7128,
				longitude: -74.006,
				location_data: {
					poitype: 'airport',
				},
			} as SunLocationData;

			const result = transformSunLocation(sunLocation);

			expect(result.city).toBeUndefined();
			expect(result.adminDistrict).toBeUndefined();
			expect(result.adminDistrictCode).toBeUndefined();
			expect(result.country).toBeUndefined();
			expect(result.countryCode).toBeUndefined();
		});
	});

	describe('Edge cases', () => {
		it('should handle zero coordinates', () => {
			const sunLocation: SunLocationData = {
				language: 'en',
				displayname: 'Null Island',
				city: 'Ocean',
				admindistrictcode: 'OC',
				admindistrict: 'Atlantic',
				country: 'International Waters',
				countrycode: 'IW',
				latitude: 0,
				longitude: 0,
				location_data: {
					poitype: 'airport',
				},
			};

			const result = transformSunLocation(sunLocation);

			expect(result.latitude).toBe(0);
			expect(result.longitude).toBe(0);
			expect(result.locationPoint).toEqual([0, 0]);
		});

		it('should handle extreme coordinates', () => {
			const sunLocation: SunLocationData = {
				language: 'en',
				displayname: 'Extreme Location',
				city: 'Extreme City',
				admindistrictcode: 'EX',
				admindistrict: 'Extreme State',
				country: 'Extreme Country',
				countrycode: 'EX',
				latitude: 89.9999,
				longitude: 179.9999,
				location_data: {
					poitype: 'airport',
				},
			};

			const result = transformSunLocation(sunLocation);

			expect(result.latitude).toBe(89.9999);
			expect(result.longitude).toBe(179.9999);
			expect(result.locationPoint).toEqual([179.9999, 89.9999]);
		});

		it('should handle empty string values', () => {
			const sunLocation: SunLocationData = {
				language: 'en',
				displayname: 'Test Location',
				city: '',
				admindistrictcode: '',
				admindistrict: '',
				country: '',
				countrycode: '',
				latitude: 40.7128,
				longitude: -74.006,
				location_data: {
					poitype: 'airport',
					icaoId: '',
					iataId: '',
				},
			};

			const result = transformSunLocation(sunLocation);

			expect(result.city).toBe('');
			expect(result.adminDistrict).toBe('');
			expect(result.adminDistrictCode).toBe('');
			expect(result.country).toBe('');
			expect(result.countryCode).toBe('');
			expect(result.locationData?.airport?.icaoCode).toBe('');
			expect(result.locationData?.airport?.iataCode).toBe('');
		});
	});
});
