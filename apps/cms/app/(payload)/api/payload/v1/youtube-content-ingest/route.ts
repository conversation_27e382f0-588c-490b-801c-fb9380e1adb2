import { NextResponse } from 'next/server';
import { getPayload } from 'payload';
import config from '@repo/payload/payload-config';

export const maxDuration = 600;

export async function GET() {
	let logger;

	const title = '[YouTube Ingest Payload Job]';
	try {
		const payload = await getPayload({ config });
		logger = payload.logger;

		const input = await payload.findGlobal({
			slug: 'samsung-korea',
		});

		const job = await payload.jobs.queue({
			task: 'youTubeRssIngest',
			input,
			queue: 'samsung-korea',
		});

		if (process.env.VERCEL_TARGET_ENV !== 'production') {
			await payload.jobs.runByID({
				id: job.id,
			});
		}

		logger.debug(`Queued ${title}: ${job.id}`);

		return NextResponse.json({
			success: true,
			message: `${title} queued successfully`,
			jobId: job.id,
		});
	} catch (error) {
		logger?.error(`Error queueing ${title}:`, error);

		return NextResponse.json({
			success: false,
			message: 'Failed to queue YouTube Ingest Payload Job',
			error: (error as Error).message,
		});
	}
}
