import { getPayload } from 'payload';
import config from '@repo/payload/payload-config';
import { headers } from 'next/headers';
import { getQuerySetIds } from '@repo/dal/content/querysets/getQuerySetIds';

export async function GET(request: Request): Promise<Response> {
	const payload = await getPayload({ config });
	const requestHeaders = await headers();
	const url = new URL(request.url);

	// Get pagination parameters from query string
	const page = parseInt(url.searchParams.get('page') || '1', 10);
	const limit = parseInt(url.searchParams.get('limit') || '50', 10);
	const search = url.searchParams.get('search') || '';

	// Get additional search parameters
	const caseInsensitive = url.searchParams.get('caseInsensitive') === 'true';
	const searchById = url.searchParams.get('searchById') === 'true';

	// Authenticate by passing request headers
	const { user } = await payload.auth({ headers: requestHeaders });

	if (!user) {
		return new Response('Action forbidden.', { status: 403 });
	}

	try {
		return Response.json(
			await getQuerySetIds({
				page,
				limit,
				search,
				caseInsensitive,
				searchById,
			}),
		);
	} catch (error: unknown) {
		return new Response(
			`Issue with retrieving asset or transforming data: ${(error as Error).message}`,
			{ status: 404 },
		);
	}
}
