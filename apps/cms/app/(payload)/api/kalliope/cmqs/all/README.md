# Kalliope CMQS API

This API provides access to Content Media Query Sets (CMQS) from the Kalliope system.

## Overview

The Kalliope CMQS API allows authenticated users to retrieve content media query sets from the Kalliope system. This endpoint is specifically designed to support the `KalliopeCMQSSelect` component in the admin UI, providing a searchable, paginated interface for selecting Kalliope CMQS IDs.

## DEPRECATION NOTICE

**IMPORTANT**: This API integrates with the legacy Kalliope system and will be deprecated in the future. This implementation should be revisited as part of the migration away from the legacy system.

## How It Works

### Query Parameters

- `page` (optional): Page number for pagination (default: 1)
- `limit` (optional): Number of items per page (default: 50)
- `search` (optional): Search term to filter results
- `caseInsensitive` (optional): Whether to perform case-insensitive search (default: false)
- `searchById` (optional): Whether to search by ID in addition to title (default: false)

### Authentication

This endpoint requires authentication. The user must be logged in to access this API.

### Processing Flow

1. The API authenticates the user using the request headers
2. If authenticated, it calls the `getQuerySetIds` function from the DAL
3. The function fetches query sets from <PERSON>lliope based on the provided parameters
4. The API returns the query sets as JSON

### Response Format

The API returns a JSON response with the following structure:

## Integration with KalliopeCMQSSelect Component

The `KalliopeCMQSSelect` component is a custom field component for Payload CMS that provides a user-friendly interface for selecting Kalliope CMQS IDs. It features:

1. **Search Functionality**: Users can search for CMQS by title or ID
2. **Pagination**: Results are paginated with first/previous/next/last navigation
3. **Debounced Search**: Search requests are debounced to prevent excessive API calls
4. **Loading States**: Visual feedback during API requests
5. **Error Handling**: Clear error messages when API requests fail

The component transforms the API response into a format suitable for the Payload CMS SelectInput component, displaying each CMQS as an option with its title and ID.

## Example Usage in Code

## Error Handling

- 403: Unauthorized access (user not authenticated)
- 404: Error retrieving assets or transforming data
- 500: Internal server error

## Related Files

- `apps/web/app/api/kalliope/cmqs/all/route.ts`: API route handler
- `packages/dal/src/content/querysets/getQuerySetIds.ts`: DAL function for fetching Kalliope query sets
- `apps/web/collections/ContentQueries/fields/KalliopeCMQSSelect.tsx`: UI component that uses this API
- `apps/web/collections/ContentQueries/fields/queries.ts`: Field configuration that includes the KalliopeCMQSSelect component

## TODO

- Replace this implementation as part of the migration away from the legacy Kalliope system
- In the interim, add caching to improve performance for frequently requested query sets
- Consider adding additional filtering options beyond simple search
- Add support for sorting options (by date, title, etc.)
