import { defineConfig } from '@playwright/test';

export default defineConfig({
	testDir: './',
	testMatch: '{app,components}/**/*.spec.ts',
	use: {
		geolocation: { longitude: 33.915606, latitude: -84.3417324920699 },
		permissions: ['geolocation'],
		locale: 'en-US',
		timezoneId: 'America/New_York',
		baseURL: process.env.VERCEL_PREVIEW_URL || 'https://local.weather.com',
		screenshot: 'only-on-failure',
		trace: 'on-first-retry',
		extraHTTPHeaders: {
			...(process.env.VERCEL_AUTOMATION_BYPASS_SECRET
				? {
						'x-vercel-protection-bypass':
							process.env.VERCEL_AUTOMATION_BYPASS_SECRET,
					}
				: {}),
		},
	},
	reporter: [
		['html', { outputFolder: './playwright-report' }],
		['junit', { outputFile: './playwright-results/junit-results.xml' }],
	],
	outputDir: './playwright-results',
	// Folder for test artifacts such as screenshots, videos, traces, etc.
	preserveOutput: 'failures-only',
	// Run tests in files in parallel
	fullyParallel: true,
	// Fail the build on CI if you accidentally left test.only in the source code
	forbidOnly: !!process.env.CI,
	// Retry on CI only
	retries: process.env.CI ? 2 : 0,
	// Use parallel tests on CI with 100% of available CPU cores
	workers: process.env.CI ? '100%' : undefined,
});
