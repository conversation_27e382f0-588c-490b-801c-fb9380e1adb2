{"$schema": "https://openapi.vercel.sh/vercel.json", "crons": [{"path": "/api/payload/payload-jobs/run?limit=10&queue=content-queries", "schedule": "* * * * *"}, {"path": "/api/payload/payload-jobs/run?limit=30&queue=ad-metrics", "schedule": "* * * * *"}, {"path": "/api/payload/payload-jobs/run?limit=50&queue=sync", "schedule": "* * * * *"}, {"path": "/api/payload/v1/content-query/refresh", "schedule": "*/5 * * * *"}, {"path": "/api/payload/v1/youtube-content-ingest", "schedule": "*/10 * * * *"}, {"path": "/api/payload/payload-jobs/run?limit=1&queue=samsung-korea", "schedule": "* * * * *"}], "functions": {"app/api/payload/payload-jobs/*.ts": {"maxDuration": 600}}}