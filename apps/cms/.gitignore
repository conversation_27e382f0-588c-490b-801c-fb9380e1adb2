# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage
**/blob-report

# next.js
/.next/
/out/

# Database
/db

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# env files (can opt-in for commiting if needed)
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
.env*.local

certificates

# emacs stuff
.projectile*

