import { describe, expect, it, vi, beforeEach } from 'vitest';
import {
	getAuthorString,
	articleToCmsAttributes,
} from './articleToCmsAttributes';
import { parseArrayToString } from '@repo/analytics/utils/parseArrayToString';
import type { Article } from '@repo/payload/payload-types';

// Mock the parseArrayToString utility
vi.mock('@repo/analytics/utils/parseArrayToString', () => ({
	parseArrayToString: vi.fn(),
}));

// Reset mocks before each test to ensure clean state
beforeEach(() => {
	vi.resetAllMocks();
});

describe('getAuthorString', () => {
	it('should format author data correctly', () => {
		// Setup
		const authors = [
			{
				authorData: {
					firstName: 'John',
					lastName: 'Doe',
				},
			},
			{
				authorData: {
					firstName: '<PERSON>',
					lastName: '<PERSON>',
				},
			},
		] as unknown as Article['authors'];

		vi.mocked(parseArrayToString).mockReturnValue("['<PERSON>','<PERSON>']");

		// Execute
		const result = getAuthorString(authors);

		// Verify
		expect(result).toBe("['John Doe','Jane <PERSON>']");
		expect(parseArrayToString).toHaveBeenCalledWith(['John Doe', 'Jane Smith']);
	});

	it('should handle string author names', () => {
		// Setup
		const authors = ['John Doe', 'Jane Smith'] as unknown as Article['authors'];

		vi.mocked(parseArrayToString).mockReturnValue("['John Doe','Jane Smith']");

		// Execute
		const result = getAuthorString(authors);

		// Verify
		expect(result).toBe("['John Doe','Jane Smith']");
		expect(parseArrayToString).toHaveBeenCalledWith(['John Doe', 'Jane Smith']);
	});

	it('should return null for undefined authors', () => {
		// Execute
		const result = getAuthorString(undefined);

		// Verify
		expect(result).toBe(null);
		expect(parseArrayToString).not.toHaveBeenCalled();
	});

	it('should handle authors with missing first or last names', () => {
		// Setup
		const authors = [
			{
				authorData: {
					firstName: 'John',
					lastName: '',
				},
			},
			{
				authorData: {
					firstName: '',
					lastName: 'Smith',
				},
			},
		] as unknown as Article['authors'];

		vi.mocked(parseArrayToString).mockReturnValue("['John ',' Smith']");

		// Execute
		const result = getAuthorString(authors);

		// Verify
		expect(result).toBe("['John ',' Smith']");
		expect(parseArrayToString).toHaveBeenCalledWith(['John ', ' Smith']);
	});

	it('should handle empty authors array', () => {
		// Setup
		const authors = [] as unknown as Article['authors'];

		vi.mocked(parseArrayToString).mockReturnValue('[]');

		// Execute
		const result = getAuthorString(authors);

		// Verify
		expect(result).toBe('[]');
		expect(parseArrayToString).toHaveBeenCalledWith([]);
	});
});

describe('articleToCmsAttributes', () => {
	const mockArticle: Article = {
		id: 'article-123',
		authors: [
			{
				authorData: {
					firstName: 'John',
					lastName: 'Doe',
				},
			},
		] as unknown as Article['authors'],
		category: {
			relationTo: 'tags',
			value: 'weather-news',
		} as Article['category'],
		createdAt: '2023-05-15T10:30:00.000Z',
		publishDate: '2023-05-16T08:00:00.000Z',
		coreMetadata: {
			entitlements: ['platform-all-free', 'platform-all-premium'],
		},
	} as Article;

	it('should transform article to CMS attributes correctly', () => {
		// Setup
		vi.mocked(parseArrayToString)
			.mockReturnValueOnce("['John Doe']") // for getAuthorString
			.mockReturnValueOnce("['platform-all-free','platform-all-premium']"); // for entitlements

		// Execute
		const result = articleToCmsAttributes(mockArticle);

		// Verify
		expect(result).toEqual({
			author: "['John Doe']",
			createdDate: '2023-05-15',
			contentId: 'article-123',
			collection: 'weather-news',
			teaserTitle: '',
			publishDate: '2023-05-16',
			entitlements: "['platform-all-free','platform-all-premium']",
			title: '',
			seoTitle: '',
		});

		expect(parseArrayToString).toHaveBeenCalledTimes(2);
		expect(parseArrayToString).toHaveBeenNthCalledWith(1, ['John Doe']);
		expect(parseArrayToString).toHaveBeenNthCalledWith(2, [
			'platform-all-free',
			'platform-all-premium',
		]);
	});

	it('should handle article with missing optional fields', () => {
		// Setup
		const minimalArticle: Article = {
			id: 'minimal-article',
		} as Article;

		vi.mocked(parseArrayToString).mockReturnValue('[]');

		// Execute
		const result = articleToCmsAttributes(minimalArticle);

		// Verify
		expect(result).toEqual({
			author: null,
			createdDate: undefined,
			contentId: 'minimal-article',
			collection: undefined,
			teaserTitle: '',
			publishDate: undefined,
			entitlements: '[]',
			title: '',
			seoTitle: '',
		});
	});

	it('should handle article with no entitlements', () => {
		// Setup
		const articleWithoutEntitlements: Article = {
			...mockArticle,
			coreMetadata: undefined,
		} as Article;

		vi.mocked(parseArrayToString)
			.mockReturnValueOnce("['John Doe']") // for getAuthorString
			.mockReturnValueOnce('[]'); // for empty entitlements

		// Execute
		const result = articleToCmsAttributes(articleWithoutEntitlements);

		// Verify
		expect(result.entitlements).toBe('[]');
		expect(parseArrayToString).toHaveBeenNthCalledWith(2, []);
	});

	it('should correctly format dates', () => {
		// Setup
		const articleWithDates: Article = {
			...mockArticle,
			createdAt: '2023-10-25T14:30:45.123Z',
			publishDate: '2023-10-26T09:15:30.456Z',
		} as Article;

		vi.mocked(parseArrayToString)
			.mockReturnValueOnce("['John Doe']")
			.mockReturnValueOnce("['platform-all-free','platform-all-premium']");

		// Execute
		const result = articleToCmsAttributes(articleWithDates);

		// Verify
		expect(result.createdDate).toBe('2023-10-25');
		expect(result.publishDate).toBe('2023-10-26');
	});

	it('should handle category as string value', () => {
		// Setup
		const articleWithStringCategory: Article = {
			...mockArticle,
			category: {
				relationTo: 'tags',
				value: 'breaking-news',
			} as Article['category'],
		} as Article;

		vi.mocked(parseArrayToString)
			.mockReturnValueOnce("['John Doe']")
			.mockReturnValueOnce("['platform-all-free','platform-all-premium']");

		// Execute
		const result = articleToCmsAttributes(articleWithStringCategory);

		// Verify
		expect(result.collection).toBe('breaking-news');
	});
});
