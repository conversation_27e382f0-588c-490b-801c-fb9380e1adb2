import { parseArrayToString } from '@repo/analytics/utils/parseArrayToString';
import type { CMSAttributes } from '@repo/analytics/atoms/metricsData';
import type { Article } from '@repo/payload/payload-types';

export const getAuthorString = (authors: Article['authors']) => {
	if (!authors) return null;

	const authorsArray = authors?.map?.((author) => {
		if (typeof author !== 'string') {
			const { firstName = '', lastName = '' } = author?.authorData || {};

			return `${firstName} ${lastName}`;
		}

		return author;
	});
	const authorString = parseArrayToString(authorsArray);

	return authorString;
};

export function articleToCmsAttributes(article: Article): CMSAttributes {
	return {
		author: getAuthorString(article?.authors),
		createdDate: article?.createdAt?.substr?.(0, 10),
		contentId: article?.id,
		collection: article?.category?.value as string,
		teaserTitle: '',
		publishDate: article?.publishDate?.substr?.(0, 10),
		entitlements: parseArrayToString(article?.coreMetadata?.entitlements ?? []),
		title: '',
		seoTitle: '',
	};
}
