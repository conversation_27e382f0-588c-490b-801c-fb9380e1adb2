'use client';

import { pageIdAtom } from '@repo/analytics/atoms/pageId';
import { pageLocaleAtom } from '@repo/analytics/atoms/pageLocale';
import { deviceClassAtom } from '@repo/analytics/atoms/deviceClass';
import {
	type CMSAttributes,
	metricsArticleDataAtom,
} from '@repo/analytics/atoms/metricsData';
import { useRehydrateAtoms } from '@repo/utils/hooks/useRehydrateAtoms';

interface AnalyticsBoundaryProps {
	pageId: string;
	pageLocale: string;
	deviceClass: string;
	metricsArticleData?: CMSAttributes;
}

export const AnalyticsBoundary = (props: AnalyticsBoundaryProps) => {
	useRehydrateAtoms([
		[pageIdAtom, props.pageId],
		[pageLocaleAtom, props.pageLocale],
		[deviceClassAtom, props.deviceClass],
		[metricsArticleDataAtom, props.metricsArticleData],
	]);

	return null;
};
