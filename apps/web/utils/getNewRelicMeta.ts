import { UserSubscriptionTiers } from '@repo/user/utils/consts';
import { precomputeFlags, userTierFlag } from '@repo/flags';
import { getSubscriptionTier } from '@/components/Helios/utils/heliosTransformer';
import { Article } from '@repo/payload/payload-types';
import isAtmosphereArticle from './isAtmosphereArticle';

interface NewRelicMetaParams {
	deviceClass: string;
	code: string;
	locale: string;
	pageKey: string;
	article?: Article | null | undefined;
}

/**
 * Gets metadata for New Relic RUM tracking.
 *
 * This function collects various contextual data points including:
 * - Device class (mobile/desktop/tablet)
 * - User locale
 * - Subscription tier
 * - Whether ads should be shown (based on premium status or article type)
 *
 * @param params - Object containing parameters for New Relic metadata generation
 * @param params.deviceClass - The device class (mobile/desktop/tablet)
 * @param params.code - The precomputed flag code for feature flag evaluation
 * @param params.locale - The user locale
 * @param params.pageKey - The page key for tracking
 * @param params.article - Optional article data
 * @returns An object containing metadata for New Relic tracking
 */
export const getNewRelicMeta = async (
	params: NewRelicMetaParams,
): Promise<{
	deviceClass: string;
	locale: string;
	subsTier: string;
	noAds: boolean;
	page: string;
}> => {
	const { deviceClass, code, locale, pageKey, article } = params;

	const subscriptionTierFlag = code
		? await userTierFlag(code, precomputeFlags)
		: UserSubscriptionTiers.none.toString();

	const subscriptionTier = getSubscriptionTier(subscriptionTierFlag);

	const isUserPremium = [
		UserSubscriptionTiers.premium.toString(),
		UserSubscriptionTiers.adFree.toString(),
		UserSubscriptionTiers.premiumWithAd.toString(),
	].includes(subscriptionTierFlag);

	const noAds = isUserPremium || isAtmosphereArticle(article);

	return {
		deviceClass: deviceClass || '',
		locale: locale || '',
		subsTier: subscriptionTier,
		noAds,
		page: pageKey?.startsWith('/') ? pageKey.slice(1) : pageKey,
	};
};
