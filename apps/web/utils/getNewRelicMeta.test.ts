import { describe, it, expect, beforeEach, vi } from 'vitest';
import { getNewRelicMeta } from './getNewRelicMeta';
import { UserSubscriptionTiers } from '@repo/user/utils/consts';
import { precomputeFlags, userTierFlag } from '@repo/flags';
import { Article } from '@repo/payload/payload-types';

// Only mock the async feature flag
vi.mock('@repo/flags');

describe('getNewRelicMeta', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	it('should return correct metadata for basic parameters', async () => {
		const params = {
			deviceClass: 'desktop',
			code: 'test-code',
			locale: 'en-US',
			pageKey: '/test-page',
		};

		vi.mocked(userTierFlag).mockResolvedValue(
			UserSubscriptionTiers.none.toString(),
		);

		const result = await getNewRelicMeta(params);

		expect(result).toEqual({
			deviceClass: 'desktop',
			locale: 'en-US',
			subsTier: 'none',
			noAds: false,
			page: 'test-page',
		});

		expect(vi.mocked(userTierFlag)).toHaveBeenCalledWith(
			'test-code',
			precomputeFlags,
		);
	});

	it('should handle premium user subscription tier', async () => {
		const params = {
			deviceClass: 'mobile',
			code: 'premium-code',
			locale: 'en-GB',
			pageKey: '/premium-content',
		};

		vi.mocked(userTierFlag).mockResolvedValue(
			UserSubscriptionTiers.premium.toString(),
		);

		const result = await getNewRelicMeta(params);

		expect(result).toEqual({
			deviceClass: 'mobile',
			locale: 'en-GB',
			subsTier: 'premium',
			noAds: true,
			page: 'premium-content',
		});
	});

	it('should handle ad-free user subscription tier', async () => {
		const params = {
			deviceClass: 'tablet',
			code: 'adfree-code',
			locale: 'es-ES',
			pageKey: '/ad-free-content',
		};

		vi.mocked(userTierFlag).mockResolvedValue(
			UserSubscriptionTiers.adFree.toString(),
		);

		const result = await getNewRelicMeta(params);

		expect(result).toEqual({
			deviceClass: 'tablet',
			locale: 'es-ES',
			subsTier: 'adFree',
			noAds: true,
			page: 'ad-free-content',
		});
	});

	it('should handle premium with ad user subscription tier', async () => {
		const params = {
			deviceClass: 'desktop',
			code: 'premium-with-ad-code',
			locale: 'fr-FR',
			pageKey: '/premium-with-ad',
		};

		vi.mocked(userTierFlag).mockResolvedValue(
			UserSubscriptionTiers.premiumWithAd.toString(),
		);

		const result = await getNewRelicMeta(params);

		expect(result).toEqual({
			deviceClass: 'desktop',
			locale: 'fr-FR',
			subsTier: 'premiumWithAd',
			noAds: true,
			page: 'premium-with-ad',
		});
	});

	it('should handle atmosphere article with no ads', async () => {
		const mockArticle = {
			id: '123',
			title: 'Atmosphere Article',
			category: { value: 'atmosphere/reviews' },
		} as Article;
		const params = {
			deviceClass: 'mobile',
			code: 'test-code',
			locale: 'en-US',
			pageKey: '/atmosphere-article',
			article: mockArticle,
		};

		vi.mocked(userTierFlag).mockResolvedValue(
			UserSubscriptionTiers.none.toString(),
		);

		const result = await getNewRelicMeta(params);

		expect(result).toEqual({
			deviceClass: 'mobile',
			locale: 'en-US',
			subsTier: 'none',
			noAds: true,
			page: 'atmosphere-article',
		});
	});

	it('should handle null and undefined values', async () => {
		const params = {
			deviceClass: '',
			code: '',
			locale: '',
			pageKey: '',
			article: null,
		};

		vi.mocked(userTierFlag).mockResolvedValue(
			UserSubscriptionTiers.none.toString(),
		);

		const result = await getNewRelicMeta(params);

		expect(result).toEqual({
			deviceClass: '',
			locale: '',
			subsTier: 'none',
			noAds: false,
			page: '',
		});
	});

	it('should handle pageKey without leading slash', async () => {
		const params = {
			deviceClass: 'desktop',
			code: 'test-code',
			locale: 'en-US',
			pageKey: 'test-page',
		};

		vi.mocked(userTierFlag).mockResolvedValue(
			UserSubscriptionTiers.none.toString(),
		);

		const result = await getNewRelicMeta(params);

		expect(result.page).toBe('test-page');
	});

	it('should handle pageKey with leading slash', async () => {
		const params = {
			deviceClass: 'desktop',
			code: 'test-code',
			locale: 'en-US',
			pageKey: '/test-page',
		};

		vi.mocked(userTierFlag).mockResolvedValue(
			UserSubscriptionTiers.none.toString(),
		);

		const result = await getNewRelicMeta(params);

		expect(result.page).toBe('test-page');
	});

	it('should handle undefined article', async () => {
		const params = {
			deviceClass: 'desktop',
			code: 'test-code',
			locale: 'en-US',
			pageKey: '/test-page',
			article: undefined,
		};

		vi.mocked(userTierFlag).mockResolvedValue(
			UserSubscriptionTiers.none.toString(),
		);

		const result = await getNewRelicMeta(params);

		expect(result.noAds).toBe(false);
	});

	it('should handle non-premium user with atmosphere article', async () => {
		const mockArticle = {
			id: '123',
			title: 'Atmosphere Article',
			category: { value: 'atmosphere/reviews' },
		} as Article;
		const params = {
			deviceClass: 'desktop',
			code: 'test-code',
			locale: 'en-US',
			pageKey: '/test-page',
			article: mockArticle,
		};

		vi.mocked(userTierFlag).mockResolvedValue(
			UserSubscriptionTiers.none.toString(),
		);

		const result = await getNewRelicMeta(params);

		expect(result.noAds).toBe(true);
	});

	it('should handle premium user with non-atmosphere article', async () => {
		const mockArticle = { id: '123', title: 'Regular Article' } as Article;
		const params = {
			deviceClass: 'desktop',
			code: 'premium-code',
			locale: 'en-US',
			pageKey: '/test-page',
			article: mockArticle,
		};

		vi.mocked(userTierFlag).mockResolvedValue(
			UserSubscriptionTiers.premium.toString(),
		);

		const result = await getNewRelicMeta(params);

		expect(result.noAds).toBe(true);
	});

	it('should handle non-premium user with non-atmosphere article', async () => {
		const mockArticle = { id: '123', title: 'Regular Article' } as Article;
		const params = {
			deviceClass: 'desktop',
			code: 'test-code',
			locale: 'en-US',
			pageKey: '/test-page',
			article: mockArticle,
		};

		vi.mocked(userTierFlag).mockResolvedValue(
			UserSubscriptionTiers.none.toString(),
		);

		const result = await getNewRelicMeta(params);

		expect(result.noAds).toBe(false);
	});
});
