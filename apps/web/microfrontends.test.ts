import { describe, it, expect } from 'vitest';
import { validateRouting } from '@vercel/microfrontends/next/testing';

describe('microfrontends', () => {
	it('routing', () => {
		expect(() => {
			validateRouting('./microfrontends.json', {
				'wx-next-cms': ['/api/payload/v1', '/payload/admin'],
				'wx-next-web': [
					'/',
					'/news',
					'/api/v1/revalidate',
					'/de-DE',
					'/weather/today/l/foo',
					'/news/news/2025-03-25-teen-killed-triggered-alaska-avalanche-turnagain-pass',
				],
			});
		}).not.toThrow();
	});
});
