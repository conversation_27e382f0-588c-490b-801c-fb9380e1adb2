---
title: Core Weather Services - Weather.com LLM Documentation
description: Comprehensive guide to Weather.com's forecasting services, current conditions, and technical specifications
url: https://weather.com/docs/llm/core-weather-services.md
---

# Core Weather Services - Weather.com

## Overview

Weather.com's core weather services provide essential forecasting and current conditions information that millions of users rely on daily for planning and safety decisions.

## Current Weather Service

**URL Pattern:** `/weather/today/l/[location-code]`

### Features

- Real-time temperature, humidity, wind speed and direction
- Visibility, UV index, and barometric pressure
- "Feels like" temperature calculations
- Sunrise/sunset times
- Current weather conditions with descriptive text
- Location-specific weather observations

### Content Type

- Structured data feeds updated every 15-30 minutes
- Localized for thousands of cities worldwide
- Mobile-optimized responsive design
- Accessible format for screen readers

## Hourly Forecast Service  

**URL Pattern:** `/weather/hourbyhour/l/[location-code]`

### Features

- Hour-by-hour predictions for next 48 hours
- Temperature trends and precipitation probability
- Wind conditions and humidity levels
- Detailed weather condition descriptions
- Interactive timeline visualization

### Data Sources

- Advanced weather modeling systems
- Real-time sensor networks
- Meteorologist review and adjustment
- Machine learning enhanced predictions

## 10-Day Extended Forecast

**URL Pattern:** `/weather/tenday/l/[location-code]`

### Features

- Daily high and low temperature predictions
- Precipitation chances and amounts
- General weather conditions (sunny, cloudy, rainy, etc.)
- Confidence indicators for forecast accuracy
- Long-range trend analysis

### Use Cases

- Event planning and scheduling
- Travel preparation
- Agricultural and outdoor work planning
- General lifestyle and wardrobe decisions

## Interactive Weather Maps

**URL Pattern:** `/maps/[map-type]`

### Available Map Types

- **Radar Maps:** Real-time precipitation and storm movement
- **Satellite Maps:** Cloud cover and atmospheric conditions  
- **Temperature Maps:** Current and forecast temperature overlays
- **Precipitation Maps:** Rain, snow, and ice accumulation forecasts
- **Wind Maps:** Wind speed and direction visualization

### Interactive Features

- Zoom and pan functionality
- Time-lapse animation controls
- Multiple overlay options
- Mobile touch-optimized interface
- Shareable map views

## Severe Weather Alert System

**URL Pattern:** `/maps/severealerts`

### Alert Types

- **Tornado Warnings:** Immediate tornado threats
- **Severe Thunderstorm Warnings:** Damaging wind and hail alerts
- **Flash Flood Warnings:** Rapid flooding danger notifications
- **Winter Storm Warnings:** Heavy snow and ice advisories
- **Heat/Cold Advisories:** Extreme temperature warnings

### Alert Features

- Real-time push notifications
- Location-based filtering
- Severity level indicators
- Expected duration and impact information
- Safety recommendations and actions

## Technical Specifications

### Data Update Frequency

- Current conditions: Every 15-30 minutes
- Hourly forecasts: Updated 4 times daily
- Extended forecasts: Updated twice daily
- Radar maps: Every 5-10 minutes
- Severe alerts: Real-time as issued

### Geographic Coverage

- United States: Complete coverage all locations
- International: Major cities and populated areas
- Marine areas: Coastal and offshore zones
- Aviation: Airport-specific conditions

### Accessibility Features

- Screen reader compatibility
- High contrast mode available
- Keyboard navigation support
- Alternative text for all graphics
- Mobile accessibility compliance

## Legacy Location Resolution

**Note:** This is a legacy pattern that is still supported for backward compatibility.

### Supported Location URL Patterns

Weather.com supports multiple location identifier formats in URLs following the pattern `/weather/[forecast-type]/l/[location-identifier]`:

#### Geocodes

- **Pattern:** `/weather/today/l/33.78,-81.45`
- **Format:** Latitude,Longitude coordinates

#### Legacy Full Location IDs

- **Pattern:** `/weather/today/l/USGA0028:1:US`
- **Format:** [location-code]:[type]:[country]

#### US Postal Codes (Partial Type 4)

- **Pattern:** `/weather/today/l/30089`
- **Format:** 5-digit ZIP code (assumed US)

#### Partial Legacy Type 1 (Cities)

- **Pattern:** `/weather/today/l/USGA0028`
- **Format:** City identifier without type suffix

#### Partial Legacy Type 9 (Airports)

- **Pattern:** `/weather/today/l/ATL`
- **Format:** 3-letter airport code

#### SEO Phrases

- **Pattern:** `/weather/today/l/Hartsfield+Jackson+ATL:9:US`
- **Format:** Descriptive text containing a location identifier

### Legacy Location Types

The following legacy location types are supported:

- **Type 1:** City (e.g., USGA0028:1:US)
- **Type 4:** Postal Code (e.g., 30080:4:US)
- **Type 5:** Golf Course (e.g., 1029918:5:US)
- **Type 9:** Airport (e.g., ATL:9:US)
- **Type 11:** Ski Resort (e.g., 502:11:US)
- **Type 13:** Recreation (e.g., ORSPHCH:13:US)
- **Type 16:** Intersection (e.g., 901608:16:US)
- **Type 17:** School (e.g., 1000201666:17:US)
- **Type 19:** Outdoor/Park (e.g., 8606:19:US)
- **Type 21:** Lake (e.g., 677:21:US)
- **Type 25:** Sports Venue (e.g., 23969:25:AS)
- **Type 27:** Subdivision (e.g., USFL4668:27:US)

### Location Resolution Priority

When ambiguous location identifiers are provided:

1. US Postal Codes are assumed for 5-digit numbers
2. Partial Type 1 and Type 9 identifiers are resolved to their full forms
3. SEO phrases are parsed to extract embedded location identifiers

## Integration Capabilities

Weather.com provides structured data that can be consumed by:

- Mobile applications
- Smart home devices
- Digital signage systems
- Emergency management systems
- Agricultural and business planning tools
