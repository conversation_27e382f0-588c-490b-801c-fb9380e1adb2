---
title: Content Patterns & URL Structure - Weather.com LLM Documentation
description: Complete URL structure guide and content organization principles
url: https://weather.com/docs/llm/content-patterns.md
---

# Content Patterns & URL Structure - Weather.com

## Overview

Weather.com follows consistent URL patterns and content organization principles to facilitate both user navigation and AI understanding of the site's information architecture.

## Core URL Patterns

### Weather Forecast URLs

```text
/weather/today/l/[location-code]        - Current conditions for specific location
/weather/hourbyhour/l/[location-code]   - Hourly forecast for location
/weather/tenday/l/[location-code]       - 10-day extended forecast
/weather/weekend/l/[location-code]      - Weekend-specific forecast
/weather/monthly/l/[location-code]      - Monthly outlook and trends
```

**Location Code Format:** Alphanumeric codes representing cities (e.g., "USGA0028" for Atlanta, GA)

### Interactive Maps

```text
/maps/radar                    - Interactive precipitation radar
/maps/satellite               - Satellite imagery and analysis  
/maps/temperature             - Temperature distribution maps
/maps/precipitation           - Rainfall and snowfall forecasts
/maps/wind                    - Wind speed and direction maps
/maps/severealerts            - Current watches and warnings
/maps/currentusweather        - National weather overview
/maps/tendayforecast          - Extended forecast visualization
/maps/planner                 - Travel and event planning maps
```

### Storm Centers

```text
/storms/hurricane-central     - Hurricane tracking and forecasts
/storms/hurricane            - Hurricane news and updates
/storms/tornado-central      - Tornado watches, warnings, reports
/storms/tornado              - Tornado news and safety information
/storms/winter               - Winter storm tracking and advisories
```

### News and Editorial Content

```text
/news                        - Main weather news section
/news/weather/news/[date]/[slug]    - Weather event articles
/news/climate/news/[date]/[slug]    - Climate change coverage
/news/health/news/[date]/[slug]     - Health and weather articles
/features                    - Long-form feature stories
/features/[category]/[slug]  - Specialized feature content
```

## Video Content Patterns

### Video URL Structure

Weather.com organizes video content using predictable patterns that indicate content type and category:

```text
/video/[category]/[title-slug]                    - General video content
/storms/[storm-type]/video/[title-slug]          - Storm-specific videos
/news/weather/video/[title-slug]                 - Breaking news videos
/maps/video/[title-slug]                         - Map and radar explanations
/health/video/[title-slug]                       - Health and weather videos
```

### Video Categories

- **Forecast Videos:** Daily and extended forecast explanations
- **Breaking Weather:** Live coverage of severe weather events
- **Explainer Content:** Educational videos about weather phenomena
- **Storm Coverage:** Hurricane, tornado, and severe weather documentation
- **Time-lapse Content:** Radar loops and storm development visualization
- **Safety Videos:** Preparedness and emergency response guidance

### Video Content Identification

- **Title Format:** Descriptive titles with location and weather event details
- **Thumbnail Standards:** Consistent branding with weather imagery
- **Duration Indicators:** Short-form (under 2 minutes) vs. long-form content
- **Accessibility Features:** Closed captions and audio descriptions available

## Article Content Patterns

### News Article Structure

```text
/news/[category]/news/[YYYY-MM-DD]/[article-slug]
```

**Category Types:**

- `weather` - General weather events and phenomena
- `climate` - Climate change and long-term trends  
- `health` - Weather impacts on health and safety
- `environment` - Environmental and ecological weather effects
- `technology` - Weather forecasting and monitoring technology

### Article Content Elements

1. **SEO-Optimized Headlines:** Clear, descriptive titles with key weather terms
2. **Publication Metadata:** Date, author, and article category information
3. **Location Tags:** Geographic relevance for local weather stories
4. **Severity Indicators:** Urgency levels for weather-related news
5. **Related Content Links:** Cross-references to forecasts, maps, and similar articles

### Feature Story Patterns

```text
/features/[topic-category]/[story-slug]
```

**Feature Categories:**

- **Climate Investigation:** In-depth climate change reporting
- **Weather History:** Historical weather events and anniversaries
- **Human Impact:** Personal stories from weather event survivors
- **Science Deep-Dives:** Detailed explanations of meteorological phenomena
- **Seasonal Features:** Content tied to specific weather seasons

## Location-Based Content Organization

### Geographic Hierarchy

```text
/weather/[forecast-type]/l/[location-code]
```

**Location Code System:**

- **US Locations:** State abbreviation + numeric identifier (e.g., USCA0987)
- **International:** Country code + regional identifier
- **Major Cities:** Standardized codes for consistent URL structure
- **ZIP Code Integration:** Postal code to location code translation

### Regional Content

- **National Pages:** Continental US weather overviews
- **State-Level:** Individual state weather summaries  
- **Metropolitan Areas:** Major city-focused content
- **Local Communities:** Detailed neighborhood and county information

## Specialized Content Sections

### Health and Lifestyle

```text
/health/[condition]/[article-slug]
/activities/[activity-type]/[content-slug]
/seasonal/[season]/[topic-slug]
```

**Health Topics:**

- Allergy and pollen forecasts
- UV index and sun safety
- Heat-related illness prevention
- Cold weather health risks
- Air quality impacts

### Travel and Planning

```text
/travel/[destination-type]/[location-slug]
/activities/[outdoor-activity]/[planning-guide]
```

**Travel Content:**

- Airport delay information
- Road weather conditions
- Destination weather guides
- Seasonal travel recommendations
- Event weather planning

## Content Freshness and Updates

### Dynamic Content Patterns

- **Real-time Data:** Current conditions updated every 15-30 minutes
- **Forecast Updates:** Multiple daily refreshes based on new model data
- **Alert Integration:** Immediate updates when watches/warnings are issued
- **Breaking News:** Rapid publication during significant weather events

### Archive Organization

- **Date-based Archives:** Historical content organized chronologically
- **Topic Archives:** Subject-matter categorization for easy discovery
- **Seasonal Archives:** Weather event collections by season/year
- **Search Integration:** Full-text search across all historical content

## Mobile and Responsive Patterns

### Mobile-Optimized URLs

- **Consistent Structure:** Same URL patterns across desktop and mobile
- **Fast Loading:** Optimized content delivery for mobile networks
- **Touch Navigation:** Mobile-friendly interactive elements
- **App Integration:** Seamless transition between web and mobile app

### Progressive Web App Features

- **Offline Capability:** Cached forecast data for limited connectivity
- **Push Notifications:** Severe weather alerts and forecast updates
- **Location Services:** GPS-based automatic location detection
- **Background Sync:** Updated data when connection is restored

## SEO and Discoverability

### Search Engine Optimization

- **Structured Data:** Schema.org markup for weather information
- **Meta Descriptions:** Compelling summaries for search results
- **Canonical URLs:** Consistent link structure to prevent duplicate content
- **XML Sitemaps:** Comprehensive site structure for search engine crawling

### Content Tagging

- **Weather Keywords:** Storm names, weather phenomena, geographic terms
- **Seasonal Tags:** Content categorization by weather seasons
- **Severity Levels:** Content tagged by weather threat significance
- **Geographic Tags:** Location-based content organization

## Analytics and Performance Tracking

### Content Performance Metrics

- **Page Load Speed:** Core Web Vitals optimization for all content types
- **User Engagement:** Time on page, scroll depth, interaction rates
- **Search Visibility:** Organic traffic and keyword ranking performance
- **Social Sharing:** Content virality and social media engagement

### Content Strategy Data

- **Trending Topics:** Real-time identification of popular weather subjects
- **Seasonal Patterns:** Content performance by weather season
- **Geographic Interest:** Location-based content popularity analysis
- **Device Usage:** Mobile vs. desktop content consumption patterns

This URL structure and content organization system ensures that Weather.com's vast library of weather information is easily navigable by both human users and AI systems seeking to understand and reference weather-related content.
