import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { NextRequest, NextResponse } from 'next/server';
import { UserSubscriptionTiers } from '@repo/user/utils/consts';

// Create mock compiled rewrites that will be used at module load time
const mockCompiledRewrites = vi.hoisted(() => [
	{
		source: '/:code',
		destination: '/:code/home/<USER>',
		matcher: vi.fn(),
		destinationCompiler: vi.fn(),
	},
]);

// Mock all external dependencies
vi.mock('flags/next', () => ({
	precompute: vi.fn(),
}));

vi.mock('@repo/flags', () => ({
	precomputeFlags: {},
	partnerParamFlag: vi.fn(),
}));

vi.mock('@repo/privacy/determinePrivacyRegimeCode', () => ({
	determinePrivacyRegimeCode: vi.fn(),
}));

vi.mock('@repo/location/utils/formatGeocode', () => ({
	formatGeocode: vi.fn(),
}));

vi.mock('@repo/user/utils/upsxCookies', () => ({
	getUserData: vi.fn(),
}));

vi.mock('./middleware/utils/userAgent', () => ({
	deriveDeviceClass: vi.fn(),
}));

vi.mock('./middleware/rewrites/index.mjs', () => ({
	afterFiles: [],
}));

vi.mock('./middleware/utils', () => ({
	compileDestination: vi.fn(),
	compileRewrites: vi.fn(() => mockCompiledRewrites),
	findMatch: vi.fn(),
	checkForRedirect: vi.fn(),
	getPartnerCookieMaxAge: vi.fn(),
}));

vi.mock('@repo/logger', () => ({
	createLogger: vi.fn(() => ({
		info: vi.fn(),
		warn: vi.fn(),
		error: vi.fn(),
		debug: vi.fn(),
	})),
}));

// Import the middleware after mocks are set up
import edgeMiddleware, { config } from './middleware';

// Import mocked modules
import { precompute } from 'flags/next';
import { partnerParamFlag } from '@repo/flags';
import { determinePrivacyRegimeCode } from '@repo/privacy/determinePrivacyRegimeCode';
import { formatGeocode } from '@repo/location/utils/formatGeocode';
import { getUserData } from '@repo/user/utils/upsxCookies';
import { deriveDeviceClass } from './middleware/utils/userAgent';
import {
	compileDestination,
	compileRewrites,
	findMatch,
	checkForRedirect,
	getPartnerCookieMaxAge,
} from './middleware/utils';
import type {
	CompiledRewriteRule,
	RouteParams,
} from './middleware/utils/types';

// Helper function to create mock NextRequest
const createMockRequest = (
	pathname: string,
	options: {
		origin?: string;
		search?: string;
		headers?: Record<string, string>;
		cookies?: Record<string, string>;
	} = {},
): NextRequest => {
	const {
		origin = 'https://weather.com',
		search = '',
		headers = {},
		cookies = {},
	} = options;

	const url = new URL(pathname + search, origin);

	return {
		nextUrl: url,
		url: url.toString(),
		headers: {
			get: (key: string) => headers[key] || null,
			set: vi.fn(),
			entries: () => Object.entries(headers),
		},
		cookies: {
			get: (key: string) =>
				cookies[key] ? { value: cookies[key] } : undefined,
			has: (key: string) => key in cookies,
		},
	} as unknown as NextRequest;
};

// Helper function to create mock NextResponse
const createMockResponse = () => {
	const mockCookies = {
		set: vi.fn(),
	};

	const mockResponse = {
		cookies: mockCookies,
	} as unknown as NextResponse;

	return { mockResponse, mockCookies };
};

describe('Middleware', () => {
	beforeEach(() => {
		vi.clearAllMocks();

		// Setup default mocks
		vi.mocked(precompute).mockResolvedValue('test-code');
		vi.mocked(partnerParamFlag).mockResolvedValue('');
		vi.mocked(determinePrivacyRegimeCode).mockReturnValue('US');
		vi.mocked(formatGeocode).mockReturnValue('40.7,-74.0');
		vi.mocked(getUserData).mockResolvedValue({
			userData: { id: 'test-user', status: 'none' },
			userID: 'test-user',
			subscriptionTier: UserSubscriptionTiers.none,
			isUserLoggedIn: false,
			isUserPremium: false,
		});
		vi.mocked(deriveDeviceClass).mockReturnValue('desktop');
		const mockCompiledRewrites = [
			{
				source: '/:code',
				destination: '/:code/home/<USER>',
				matcher: vi.fn(),
				destinationCompiler: vi.fn(),
			},
		];
		vi.mocked(compileRewrites).mockReturnValue(mockCompiledRewrites);
		vi.mocked(checkForRedirect).mockResolvedValue(null);
		vi.mocked(findMatch).mockReturnValue(null);
		vi.mocked(compileDestination).mockReturnValue('/test-destination');
		vi.mocked(getPartnerCookieMaxAge).mockReturnValue(null);

		// Mock NextResponse.rewrite
		const { mockResponse } = createMockResponse();
		vi.spyOn(NextResponse, 'rewrite').mockReturnValue(mockResponse);
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	describe('Config Matcher', () => {
		// Helper function to test if a path matches the middleware matcher
		const shouldRunMiddleware = (path: string): boolean => {
			// The matcher is a negative lookahead pattern that excludes certain paths
			// Convert it to a proper regex pattern for testing
			const pattern = config.matcher;
			// Remove the leading slash and convert to proper regex
			const regexPattern = pattern.replace(/^\//, '');
			const regex = new RegExp(`^${regexPattern}$`);
			return regex.test(path.replace(/^\//, ''));
		};

		it('should exclude API routes', () => {
			const testPaths = [
				'/api/weather',
				'/api/payload/redirects',
				'/api/auth/login',
			];

			testPaths.forEach((path) => {
				expect(shouldRunMiddleware(path)).toBe(false);
			});
		});

		it('should exclude payload admin routes', () => {
			const testPaths = ['/payload/admin', '/payload/admin/collections'];

			testPaths.forEach((path) => {
				expect(shouldRunMiddleware(path)).toBe(false);
			});
		});

		it('should exclude Next.js static files', () => {
			const testPaths = [
				'/_next/static/chunks/main.js',
				'/_next/image?url=test.jpg',
			];

			testPaths.forEach((path) => {
				expect(shouldRunMiddleware(path)).toBe(false);
			});
		});

		it('should exclude well-known files', () => {
			const testPaths = [
				'/.well-known/security.txt',
				'/.well-known/apple-app-site-association',
			];

			testPaths.forEach((path) => {
				expect(shouldRunMiddleware(path)).toBe(false);
			});
		});

		it('should exclude static files', () => {
			const testPaths = [
				'/favicon.ico',
				'/sitemap.xml',
				'/robots.txt',
				'/llms.txt',
			];

			testPaths.forEach((path) => {
				expect(shouldRunMiddleware(path)).toBe(false);
			});
		});

		it('should exclude docs/llm route', () => {
			expect(shouldRunMiddleware('/docs/llm')).toBe(false);
		});

		it('should include regular routes', () => {
			const testPaths = [
				'/',
				'/weather/today',
				'/pages/about',
				'/de-DE/weather/today',
			];

			testPaths.forEach((path) => {
				expect(shouldRunMiddleware(path)).toBe(true);
			});
		});
	});

	describe('Redirect Logic', () => {
		it('should check for redirects and return redirect response if found', async () => {
			const request = createMockRequest('/old-path');
			const mockRedirectResponse = NextResponse.redirect(
				new URL('/new-path', request.url),
			);

			vi.mocked(checkForRedirect).mockResolvedValue(mockRedirectResponse);

			const result = await edgeMiddleware(request);

			expect(checkForRedirect).toHaveBeenCalledWith(request);
			expect(result).toBe(mockRedirectResponse);
		});

		it('should proceed with normal flow when no redirect is found', async () => {
			const request = createMockRequest('/normal-path');

			vi.mocked(checkForRedirect).mockResolvedValue(null);

			await edgeMiddleware(request);

			expect(checkForRedirect).toHaveBeenCalledWith(request);
			expect(NextResponse.rewrite).toHaveBeenCalled();
		});
	});

	describe('Rewrite Logic', () => {
		beforeEach(() => {
			vi.mocked(precompute).mockResolvedValue('abc123');
		});

		it('should rewrite root path to home page', async () => {
			const request = createMockRequest('/');

			const mockMatch = {
				rule: {
					source: '/:code',
					destination: '/:code/home/<USER>',
					matcher: vi.fn(),
					destinationCompiler: vi.fn(),
				} as CompiledRewriteRule,
				params: { code: 'abc123' } as RouteParams,
			};

			vi.mocked(findMatch).mockReturnValue(mockMatch);
			vi.mocked(compileDestination).mockReturnValue('/abc123/home/<USER>');

			await edgeMiddleware(request);

			expect(findMatch).toHaveBeenCalledWith(
				'/abc123/',
				expect.any(Array),
				request,
			);
			expect(compileDestination).toHaveBeenCalledWith(
				'/:code/home/<USER>',
				{ code: 'abc123' },
				'',
			);
			expect(NextResponse.rewrite).toHaveBeenCalledWith(
				new URL('/abc123/home/<USER>', request.url),
				{ request },
			);
		});

		it('should rewrite locale path to home page with locale', async () => {
			const request = createMockRequest('/de-DE');

			const mockMatch = {
				rule: {
					source: '/:code/:locale([a-z]{2}-[A-Z]{2})',
					destination: '/:code/home/<USER>',
					matcher: vi.fn(),
					destinationCompiler: vi.fn(),
				} as CompiledRewriteRule,
				params: { code: 'abc123', locale: 'de-DE' } as RouteParams,
			};

			vi.mocked(findMatch).mockReturnValue(mockMatch);
			vi.mocked(compileDestination).mockReturnValue('/abc123/home/<USER>');

			await edgeMiddleware(request);

			expect(findMatch).toHaveBeenCalledWith(
				'/abc123/de-DE',
				expect.any(Array),
				request,
			);
			expect(compileDestination).toHaveBeenCalledWith(
				'/:code/home/<USER>',
				{ code: 'abc123', locale: 'de-DE' },
				'',
			);
		});

		it('should rewrite weather today path', async () => {
			const request = createMockRequest('/weather/today/l/USGA0028');

			const mockMatch = {
				rule: {
					source: '/:code/weather/today/l/:locId',
					destination: '/:code/weather/today/byLocId/en-US/:locId',
					matcher: vi.fn(),
					destinationCompiler: vi.fn(),
				} as CompiledRewriteRule,
				params: { code: 'abc123', locId: 'USGA0028' } as RouteParams,
			};

			vi.mocked(findMatch).mockReturnValue(mockMatch);
			vi.mocked(compileDestination).mockReturnValue(
				'/abc123/weather/today/byLocId/en-US/USGA0028',
			);

			await edgeMiddleware(request);

			expect(findMatch).toHaveBeenCalledWith(
				'/abc123/weather/today/l/USGA0028',
				expect.any(Array),
				request,
			);
			expect(compileDestination).toHaveBeenCalledWith(
				'/:code/weather/today/byLocId/en-US/:locId',
				{ code: 'abc123', locId: 'USGA0028' },
				'',
			);
		});

		it('should rewrite pages with asset ID', async () => {
			const request = createMockRequest('/pages/507f1f77bcf86cd799439011');

			const mockMatch = {
				rule: {
					source: '/:code/pages/:assetId([0-9a-f]{24})',
					destination: '/:code/pages/byId/en-US/:assetId',
					matcher: vi.fn(),
					destinationCompiler: vi.fn(),
				} as CompiledRewriteRule,
				params: {
					code: 'abc123',
					assetId: '507f1f77bcf86cd799439011',
				} as RouteParams,
			};

			vi.mocked(findMatch).mockReturnValue(mockMatch);
			vi.mocked(compileDestination).mockReturnValue(
				'/abc123/pages/byId/en-US/507f1f77bcf86cd799439011',
			);

			await edgeMiddleware(request);

			expect(compileDestination).toHaveBeenCalledWith(
				'/:code/pages/byId/en-US/:assetId',
				{ code: 'abc123', assetId: '507f1f77bcf86cd799439011' },
				'',
			);
		});

		it('should rewrite localized pages with asset name', async () => {
			const request = createMockRequest('/de-DE/pages/about-us');

			const mockMatch = {
				rule: {
					source: '/:code/:locale([a-z]{2}-[A-Z]{2})/pages/:assetName*',
					destination: '/:code/pages/byAssetName/:locale/:assetName*',
					matcher: vi.fn(),
					destinationCompiler: vi.fn(),
				} as CompiledRewriteRule,
				params: {
					code: 'abc123',
					locale: 'de-DE',
					assetName: ['about-us'],
				} as RouteParams,
			};

			vi.mocked(findMatch).mockReturnValue(mockMatch);
			vi.mocked(compileDestination).mockReturnValue(
				'/abc123/pages/byAssetName/de-DE/about-us',
			);

			await edgeMiddleware(request);

			expect(compileDestination).toHaveBeenCalledWith(
				'/:code/pages/byAssetName/:locale/:assetName*',
				{ code: 'abc123', locale: 'de-DE', assetName: ['about-us'] },
				'',
			);
		});

		it('should use original URL when no rewrite rule matches', async () => {
			const request = createMockRequest('/unknown-path');

			vi.mocked(findMatch).mockReturnValue(null);

			await edgeMiddleware(request);

			expect(NextResponse.rewrite).toHaveBeenCalledWith(new URL(request.url), {
				request,
			});
		});

		it('should preserve query parameters in rewrites', async () => {
			const request = createMockRequest('/weather/today/l/USGA0028', {
				search: '?units=metric&lang=en',
			});

			const mockMatch = {
				rule: {
					source: '/:code/weather/today/l/:locId',
					destination: '/:code/weather/today/byLocId/en-US/:locId',
					matcher: vi.fn(),
					destinationCompiler: vi.fn(),
				} as CompiledRewriteRule,
				params: { code: 'abc123', locId: 'USGA0028' } as RouteParams,
			};

			vi.mocked(findMatch).mockReturnValue(mockMatch);
			vi.mocked(compileDestination).mockReturnValue(
				'/abc123/weather/today/byLocId/en-US/USGA0028?units=metric&lang=en',
			);

			await edgeMiddleware(request);

			expect(compileDestination).toHaveBeenCalledWith(
				'/:code/weather/today/byLocId/en-US/:locId',
				{ code: 'abc123', locId: 'USGA0028' },
				'?units=metric&lang=en',
			);
		});
	});

	describe('Cookie Management', () => {
		let mockCookies: any;

		beforeEach(() => {
			const { mockResponse, mockCookies: cookies } = createMockResponse();
			mockCookies = cookies;
			vi.spyOn(NextResponse, 'rewrite').mockReturnValue(mockResponse);
		});

		describe('Location Cookies', () => {
			it('should set geocode cookie when latitude and longitude are available', async () => {
				const request = createMockRequest('/', {
					headers: {
						'x-vercel-ip-latitude': '40.7128',
						'x-vercel-ip-longitude': '-74.0060',
					},
				});

				vi.mocked(formatGeocode).mockReturnValue('40.71,-74.01');

				await edgeMiddleware(request);

				expect(formatGeocode).toHaveBeenCalledWith('40.7128,-74.0060');
				expect(mockCookies.set).toHaveBeenCalledWith(
					'twc-location-geocode',
					'40.71,-74.01',
					{
						domain: '.weather.com',
						secure: true,
					},
				);
			});

			it('should set default geocode when latitude and longitude are not available', async () => {
				const request = createMockRequest('/');

				await edgeMiddleware(request);

				expect(mockCookies.set).toHaveBeenCalledWith(
					'twc-location-geocode',
					'33.86,-84.34',
					{
						domain: '.weather.com',
						secure: true,
					},
				);
			});

			it('should set country cookie when available', async () => {
				const request = createMockRequest('/', {
					headers: {
						'x-vercel-ip-country': 'US',
					},
				});

				await edgeMiddleware(request);

				expect(mockCookies.set).toHaveBeenCalledWith(
					'twc-location-country',
					'US',
					{
						domain: '.weather.com',
						secure: true,
					},
				);
			});

			it('should set region cookie when available', async () => {
				const request = createMockRequest('/', {
					headers: {
						'x-vercel-ip-country-region': 'NY',
					},
				});

				await edgeMiddleware(request);

				expect(mockCookies.set).toHaveBeenCalledWith(
					'twc-location-region',
					'NY',
					{
						domain: '.weather.com',
						secure: true,
					},
				);
			});
		});

		describe('Privacy Cookie', () => {
			it('should set privacy cookie based on country and region', async () => {
				const request = createMockRequest('/', {
					headers: {
						'x-vercel-ip-country': 'US',
						'x-vercel-ip-country-region': 'CA',
					},
				});

				vi.mocked(determinePrivacyRegimeCode).mockReturnValue('CCPA');

				await edgeMiddleware(request);

				expect(determinePrivacyRegimeCode).toHaveBeenCalledWith('US', 'CA');
				expect(mockCookies.set).toHaveBeenCalledWith('twc-privacy', 'CCPA', {
					domain: '.weather.com',
					secure: true,
				});
			});
		});

		describe('Debug Mode Cookie', () => {
			it('should set debug mode cookie when payload-token exists', async () => {
				const request = createMockRequest('/', {
					cookies: {
						'payload-token': 'some-token',
					},
				});

				await edgeMiddleware(request);

				expect(mockCookies.set).toHaveBeenCalledWith('twc-debug-mode', 'true', {
					domain: '.weather.com',
					secure: true,
				});
			});

			it('should not set debug mode cookie when payload-token does not exist', async () => {
				const request = createMockRequest('/');

				await edgeMiddleware(request);

				expect(mockCookies.set).not.toHaveBeenCalledWith(
					'twc-debug-mode',
					expect.any(String),
					expect.any(Object),
				);
			});
		});

		describe('Anonymous ID Cookies', () => {
			it('should set ANON_S when ANON_C exists but ANON_S does not', async () => {
				const request = createMockRequest('/', {
					cookies: {
						ANON_C: 'client-anon-id',
					},
				});

				await edgeMiddleware(request);

				expect(mockCookies.set).toHaveBeenCalledWith(
					'ANON_S',
					'client-anon-id',
					{
						domain: '.weather.com',
						secure: true,
						httpOnly: true,
						path: '/',
					},
				);
			});

			it('should update ANON_S when values mismatch', async () => {
				const request = createMockRequest('/', {
					cookies: {
						ANON_C: 'new-client-anon-id',
						ANON_S: 'old-server-anon-id',
					},
				});

				await edgeMiddleware(request);

				expect(mockCookies.set).toHaveBeenCalledWith(
					'ANON_S',
					'new-client-anon-id',
					{
						domain: '.weather.com',
						secure: true,
						httpOnly: true,
						path: '/',
					},
				);
			});

			it('should set ANON_C when ANON_S exists but ANON_C does not', async () => {
				const request = createMockRequest('/', {
					cookies: {
						ANON_S: 'server-anon-id',
					},
				});

				await edgeMiddleware(request);

				expect(mockCookies.set).toHaveBeenCalledWith(
					'ANON_C',
					'server-anon-id',
					{
						domain: '.weather.com',
						secure: true,
						httpOnly: false,
						path: '/',
					},
				);
			});

			it('should not modify cookies when ANON_C and ANON_S match', async () => {
				const request = createMockRequest('/', {
					cookies: {
						ANON_C: 'matching-anon-id',
						ANON_S: 'matching-anon-id',
					},
				});

				await edgeMiddleware(request);

				expect(mockCookies.set).not.toHaveBeenCalledWith(
					'ANON_S',
					expect.any(String),
					expect.objectContaining({ httpOnly: true }),
				);
				expect(mockCookies.set).not.toHaveBeenCalledWith(
					'ANON_C',
					expect.any(String),
					expect.objectContaining({ httpOnly: false }),
				);
			});
		});

		describe('User Cookies', () => {
			it('should set user cookie when user is logged in', async () => {
				const request = createMockRequest('/');

				vi.mocked(getUserData).mockResolvedValue({
					userData: { id: 'test-user', status: 'standard' },
					userID: 'test-user',
					subscriptionTier: UserSubscriptionTiers.standard,
					isUserLoggedIn: true,
					isUserPremium: false,
				});

				await edgeMiddleware(request);

				// Check that the user cookie is set with the subscription tier value
				expect(mockCookies.set).toHaveBeenCalledWith(
					'twc-user',
					UserSubscriptionTiers.standard.toString(),
					{
						domain: '.weather.com',
						secure: true,
					},
				);
			});

			it('should set expired user cookie when user is not logged in', async () => {
				const request = createMockRequest('/');

				vi.mocked(getUserData).mockResolvedValue({
					userData: { id: null, status: 'none' },
					userID: null,
					subscriptionTier: UserSubscriptionTiers.none,
					isUserLoggedIn: false,
					isUserPremium: false,
				});

				await edgeMiddleware(request);

				// Check that the user cookie is set with expires: -1 when not logged in
				expect(mockCookies.set).toHaveBeenCalledWith(
					'twc-user',
					UserSubscriptionTiers.none.toString(),
					{
						domain: '.weather.com',
						secure: true,
						expires: -1,
					},
				);
			});
		});
	});

	describe('Header Processing', () => {
		it('should set privacy header', async () => {
			const request = createMockRequest('/', {
				headers: {
					'x-vercel-ip-country': 'DE',
					'x-vercel-ip-country-region': 'BY',
				},
			});

			vi.mocked(determinePrivacyRegimeCode).mockReturnValue('GDPR');

			await edgeMiddleware(request);

			expect(request.headers.set).toHaveBeenCalledWith('x-twc-privacy', 'GDPR');
		});

		it('should set device class header', async () => {
			const request = createMockRequest('/');

			vi.mocked(deriveDeviceClass).mockReturnValue('mobile');

			await edgeMiddleware(request);

			expect(deriveDeviceClass).toHaveBeenCalledWith(request);
			expect(request.headers.set).toHaveBeenCalledWith(
				'x-device-class',
				'mobile',
			);
		});

		it('should set user tier header', async () => {
			const request = createMockRequest('/');

			vi.mocked(getUserData).mockResolvedValue({
				userData: { id: 'test-user', status: 'premium' },
				userID: 'test-user',
				subscriptionTier: UserSubscriptionTiers.premium,
				isUserLoggedIn: true,
				isUserPremium: true,
			});

			await edgeMiddleware(request);

			expect(request.headers.set).toHaveBeenCalledWith(
				'x-user-tier',
				UserSubscriptionTiers.premium.toString(),
			);
		});

		it('should set search params header', async () => {
			const request = createMockRequest('/', {
				search: '?param1=value1&param2=value2',
			});

			await edgeMiddleware(request);

			expect(request.headers.set).toHaveBeenCalledWith(
				'x-search-params',
				'param1=value1&param2=value2',
			);
		});
	});

	describe('Integration Tests', () => {
		it('should handle complete middleware flow with all features', async () => {
			const request = createMockRequest('/weather/today/l/USGA0028', {
				search: '?units=metric',
				headers: {
					'x-vercel-ip-latitude': '40.7128',
					'x-vercel-ip-longitude': '-74.0060',
					'x-vercel-ip-country': 'US',
					'x-vercel-ip-country-region': 'NY',
				},
				cookies: {
					'payload-token': 'admin-token',
					ANON_C: 'client-anon-id',
				},
			});

			const mockMatch = {
				rule: {
					source: '/:code/weather/today/l/:locId',
					destination: '/:code/weather/today/byLocId/en-US/:locId',
					matcher: vi.fn(),
					destinationCompiler: vi.fn(),
				} as CompiledRewriteRule,
				params: { code: 'abc123', locId: 'USGA0028' } as RouteParams,
			};

			vi.mocked(findMatch).mockReturnValue(mockMatch);
			vi.mocked(compileDestination).mockReturnValue(
				'/abc123/weather/today/byLocId/en-US/USGA0028?units=metric',
			);
			vi.mocked(formatGeocode).mockReturnValue('40.71,-74.01');
			vi.mocked(determinePrivacyRegimeCode).mockReturnValue('US');
			vi.mocked(deriveDeviceClass).mockReturnValue('desktop');
			vi.mocked(getUserData).mockResolvedValue({
				userData: { id: 'test-user', status: 'none' },
				userID: 'test-user',
				subscriptionTier: UserSubscriptionTiers.none,
				isUserLoggedIn: false,
				isUserPremium: false,
			});

			const { mockResponse, mockCookies } = createMockResponse();
			vi.spyOn(NextResponse, 'rewrite').mockReturnValue(mockResponse);

			const result = await edgeMiddleware(request);

			// Verify rewrite
			expect(NextResponse.rewrite).toHaveBeenCalledWith(
				new URL(
					'/abc123/weather/today/byLocId/en-US/USGA0028?units=metric',
					request.url,
				),
				{ request },
			);

			// Verify headers
			expect(request.headers.set).toHaveBeenCalledWith('x-twc-privacy', 'US');
			expect(request.headers.set).toHaveBeenCalledWith(
				'x-device-class',
				'desktop',
			);
			expect(request.headers.set).toHaveBeenCalledWith(
				'x-user-tier',
				UserSubscriptionTiers.none.toString(),
			);
			expect(request.headers.set).toHaveBeenCalledWith(
				'x-search-params',
				'units=metric',
			);

			// Verify cookies
			expect(mockCookies.set).toHaveBeenCalledWith(
				'twc-location-geocode',
				'40.71,-74.01',
				expect.any(Object),
			);
			expect(mockCookies.set).toHaveBeenCalledWith(
				'twc-location-country',
				'US',
				expect.any(Object),
			);
			expect(mockCookies.set).toHaveBeenCalledWith(
				'twc-location-region',
				'NY',
				expect.any(Object),
			);
			expect(mockCookies.set).toHaveBeenCalledWith(
				'twc-privacy',
				'US',
				expect.any(Object),
			);
			expect(mockCookies.set).toHaveBeenCalledWith(
				'twc-debug-mode',
				'true',
				expect.any(Object),
			);
			expect(mockCookies.set).toHaveBeenCalledWith(
				'ANON_S',
				'client-anon-id',
				expect.objectContaining({ httpOnly: true }),
			);

			expect(result).toBe(mockResponse);
		});

		it('should handle requests with missing headers gracefully', async () => {
			const request = createMockRequest('/');

			vi.mocked(getUserData).mockResolvedValue({
				userData: { id: null, status: 'none' },
				userID: null,
				subscriptionTier: UserSubscriptionTiers.none,
				isUserLoggedIn: false,
				isUserPremium: false,
			});

			const { mockResponse, mockCookies } = createMockResponse();
			vi.spyOn(NextResponse, 'rewrite').mockReturnValue(mockResponse);

			await edgeMiddleware(request);

			// Should still set default geocode
			expect(mockCookies.set).toHaveBeenCalledWith(
				'twc-location-geocode',
				'33.86,-84.34',
				expect.any(Object),
			);

			// Should not set country/region cookies
			expect(mockCookies.set).not.toHaveBeenCalledWith(
				'twc-location-country',
				expect.any(String),
				expect.any(Object),
			);
			expect(mockCookies.set).not.toHaveBeenCalledWith(
				'twc-location-region',
				expect.any(String),
				expect.any(Object),
			);
		});

		it('should handle errors in external dependencies gracefully', async () => {
			const request = createMockRequest('/');

			// Mock errors in dependencies
			vi.mocked(getUserData).mockRejectedValue(new Error('User service error'));
			vi.mocked(precompute).mockRejectedValue(new Error('Flags service error'));

			// Should not throw and should return a response
			await expect(edgeMiddleware(request)).rejects.toThrow();
		});
	});
});
