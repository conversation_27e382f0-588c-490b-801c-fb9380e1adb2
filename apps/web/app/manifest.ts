import { MetadataRoute } from 'next';
import twc36x36 from '@/assets/favicons/twc_36x36.png';
import twc48x48 from '@/assets/favicons/twc_48x48.png';
import twc72x72 from '@/assets/favicons/twc_72x72.png';
import twc96x96 from '@/assets/favicons/twc_96x96.png';
import twc128x128 from '@/assets/favicons/twc_128x128.png';
import twc144x144 from '@/assets/favicons/twc_144x144.png';
import twc192x192 from '@/assets/favicons/twc_192x192.png';

// Define a custom icon type that includes the density property
interface CustomIcon {
	src: string;
	sizes: string;
	type: string;
	density: number;
}

// Define a custom manifest type that includes our custom icons
interface CustomManifest extends Omit<MetadataRoute.Manifest, 'icons'> {
	icons: CustomIcon[];
	gcm_sender_id?: string;
}

export default function manifest(): MetadataRoute.Manifest {
	// Create a manifest with custom properties
	const customManifest: CustomManifest = {
		name: 'The Weather Channel',
		short_name: 'Weather.com',
		icons: [
			{
				src: twc36x36.src,
				sizes: '36x36',
				type: 'image/png',
				density: 0.75,
			},
			{
				src: twc48x48.src,
				sizes: '48x48',
				type: 'image/png',
				density: 1.0,
			},
			{
				src: twc72x72.src,
				sizes: '72x72',
				type: 'image/png',
				density: 1.5,
			},
			{
				src: twc96x96.src,
				sizes: '96x96',
				type: 'image/png',
				density: 2.0,
			},
			{
				src: twc128x128.src,
				sizes: '128x128',
				type: 'image/png',
				density: 2.5,
			},
			{
				src: twc144x144.src,
				sizes: '144x144',
				type: 'image/png',
				density: 3.0,
			},
			{
				src: twc192x192.src,
				sizes: '192x192',
				type: 'image/png',
				density: 4.0,
			},
		],
		prefer_related_applications: false,
		start_url: '.?from=launch:homescreen',
		theme_color: '#003399',
		background_color: '#2962a9',
		display: 'standalone',
		// gcm_sender_id is needed for push notifications
		gcm_sender_id: '798371954211',
	};

	// Cast to the expected return type
	return customManifest as unknown as MetadataRoute.Manifest;
}
