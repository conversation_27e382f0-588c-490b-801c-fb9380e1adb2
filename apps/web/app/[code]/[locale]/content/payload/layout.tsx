import React, { Fragment } from 'react';
import { draftMode } from 'next/headers';
import RefreshRouteOnSave from '@repo/payload/components/RefreshRoute';
import { AdBlock } from '@repo/payload/blocks';
import AuthAwarePromoDriver from '@/components/AuthAwarePromoDriver/AuthAwarePromoDriver';
import PromoGif from '@/assets/gifs/overlay_ani_v.gif';
import { TaboolaContent } from '@repo/payload/blocks/Taboola';
import { deviceClassFlag, precomputeFlags } from '@repo/flags';

export default async function ContentPayloadLayout({
	children,
	params,
}: Readonly<{ children: React.ReactNode; params: Promise<{ code: string }> }>) {
	const { isEnabled: draft } = await draftMode();

	const { code } = await params;
	const deviceClass = await deviceClassFlag(code, precomputeFlags);
	const isMobile = deviceClass === 'mobile';

	return (
		<Fragment>
			{/* Hidden advertisement */}
			<AdBlock adId="WX_Hidden" />
			{/* Mobile Web Position1 (p1) advertisement */}
			{isMobile && <AdBlock adId="MW_Position1" />}
			{draft && <RefreshRouteOnSave />}
			{/* labBG and wx-hero-content divs required for WX_WindowShade ads */}
			<div id="labBG" className="w-full" />
			<div id="wx-hero-content" />
			<div className="mx-auto max-w-7xl px-4 py-4">
				{/* WindowShade (Top banner) advertisement */}
				{!isMobile && <AdBlock adId="WX_WindowShade" />}
				<div className="flex flex-col gap-4 pt-4 md:flex-row">
					{/* Left content area (flexible width) */}
					<div className="space-y-4 md:flex-1">
						{children}
						{/* Mobile Web Position4 (p4) advertisement */}
						{isMobile && <AdBlock adId="MW_Position4" title="Advertisement" />}
						{/* Taboola Below Content */}
						<TaboolaContent
							taboolaType="generic"
							title="Sponsored Content"
							className="mt-4"
							taboolaId="taboola-below-content-thumbnails-article"
						/>
					</div>

					{/* Right sidebar (fixed 338px width) */}
					<div className="space-y-4 md:w-[338px]">
						{/* Top300 advertisement */}
						{!isMobile && (
							<AdBlock
								adId="WX_Top300Variable"
								variant="sidebar"
								title="Advertisement"
								height="300px"
								className="mb-4"
							/>
						)}

						{/* Auth-aware PromoDriver only on mobile */}
						{isMobile && (
							<AuthAwarePromoDriver
								image={{
									id: 'promo-gif',
									url: PromoGif.src,
									seo: {
										altText: 'Weather Dashboard',
										caption: 'Weather Dashboard',
										credit: 'The Weather Company',
									},
									updatedAt: new Date().toISOString(),
									createdAt: new Date().toISOString(),
								}}
								title=""
								loggedInContent={{
									subtitle: 'Weather across locations',
									bodyText:
										'Customize the chart to compare weather condition trends.',
									ctaText: 'Go to dashboard',
									ctaUrl: '/mydashboard',
								}}
								loggedOutContent={{
									subtitle: 'Compare Weather, anywhere.',
									bodyText:
										'Build a custom dashboard to see the forecast at-a-glance across locations.',
									ctaText: 'Sign up',
									ctaUrl: '/signup',
								}}
							/>
						)}

						{/* Mobile Web Position5 (p5) advertisement */}
						{isMobile && <AdBlock adId="MW_Position5" title="Advertisement" />}
					</div>
				</div>
			</div>
		</Fragment>
	);
}
