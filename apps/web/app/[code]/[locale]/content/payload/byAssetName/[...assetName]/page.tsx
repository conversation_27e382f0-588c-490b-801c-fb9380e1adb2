import React from 'react';
import { unstable_cacheTag as cacheTag } from 'next/cache';
import { notFound } from 'next/navigation';
import { queryArticlesByAssetName } from '../../utils/getPayloadArticles';
import { AnalyticsBoundary } from '@/components/AnalyticsBoundary/AnalyticsBoundary';
import { getLocaleFromPathParams } from '@/utils/getLocaleFromPathParams';
import { DebugCollector } from '@repo/payload/components/FrontendAdminHeader/collectors/DebugCollector';
import { ArticleHeader } from '@/components/ArticleHeader/ArticleHeader';
import { getContextualizedPage } from '@repo/payload/contextParameters/getContextualizedPage';
import {
	buildArticlePageMetadata,
	getArticlePageDebugData,
} from '@repo/payload/collections/Articles/utils/articleData';
import { ArticleJsonLd } from '@/components/JsonLd/JsonLd';
import ArticleBody from '@/components/ArticleBody/ArticleBody';
import Helios from '@/components/Helios';
import { getContextParams } from '@repo/payload/contextParameters/extractParameters';
import { articleToCmsAttributes } from '@/components/AnalyticsBoundary/articleToCmsAttributes';
import { TaboolaBlock } from '@repo/payload/blocks';
import isAtmosphereArticle from '@/utils/isAtmosphereArticle';
import { NewRelic } from '@repo/newrelic/NewRelic';
import { getNewRelicMeta } from '@/utils/getNewRelicMeta';

const pageKey = '/content';

export const dynamic = 'force-static';

interface ArticleByAssetNameParams {
	assetName: string[];
	locale?: string;
	code: string;
}

interface ArticleByAssetNameProps {
	params: Promise<ArticleByAssetNameParams>;
}

export async function generateMetadata({ params }: ArticleByAssetNameProps) {
	const segments = await params;
	const assetName = `/${segments.assetName.join('/')}`;
	const locale = getLocaleFromPathParams(segments.locale);
	const article = await queryArticlesByAssetName({
		assetName,
		locale,
	});

	return buildArticlePageMetadata(article || null);
}

export default async function ArticlePage({ params }: ArticleByAssetNameProps) {
	'use cache';

	const { code, assetName: assetNameParam, locale: localeParam } = await params;

	cacheTag(code);

	const assetName = `/${assetNameParam.join('/')}`;
	const locale = getLocaleFromPathParams(localeParam);
	const contextParams = await getContextParams(code);
	const contextParamsResults = await getContextualizedPage({
		...contextParams,
		locale,
		pageKey,
	});

	const article = await queryArticlesByAssetName({
		assetName,
		locale,
	});

	if (!article) {
		return notFound();
	}

	const pageMetadata = getArticlePageDebugData(article);

	const deviceClass = contextParams?.deviceClass || '';

	const newRelicMeta = await getNewRelicMeta({
		deviceClass,
		code,
		locale,
		pageKey,
		article,
	});

	return (
		<article className="prose w-full max-w-4xl rounded-md bg-white p-5">
			<DebugCollector
				componentName="ContentPage"
				data={{
					pageType: 'content',
					locale,
					context: {
						match: contextParamsResults?.match,
					},
				}}
				page={pageMetadata}
			/>
			<AnalyticsBoundary
				pageId={'article'}
				pageLocale={locale}
				deviceClass={deviceClass}
				metricsArticleData={articleToCmsAttributes(article)}
			/>
			<Helios code={code} article={article} />
			<NewRelic meta={newRelicMeta} />
			<TaboolaBlock
				showTaboola={isAtmosphereArticle(article)}
				placements={[
					{
						mode: 'thumbnails-d',
						container: 'taboola-below-content-thumbnails-article',
						placement: 'Below Content Thumbnails - article',
						target_type: 'mix',
					},
				]}
				pageType="article"
			/>
			<ArticleJsonLd article={article} />
			<ArticleHeader article={article} />
			<ArticleBody article={article} />
		</article>
	);
}
