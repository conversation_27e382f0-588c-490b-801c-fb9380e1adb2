import React from 'react';
import { unstable_cacheTag as cacheTag } from 'next/cache';
import { notFound } from 'next/navigation';
import { queryArticlesByArticleId } from '../../utils/getPayloadArticles';
import { AnalyticsBoundary } from '@/components/AnalyticsBoundary/AnalyticsBoundary';
import { getLocaleFromPathParams } from '@/utils/getLocaleFromPathParams';
import {
	buildArticlePageMetadata,
	getArticlePageDebugData,
} from '@repo/payload/collections/Articles/utils/articleData';
import { ArticleJsonLd } from '@/components/JsonLd/JsonLd';
import { DebugCollector } from '@repo/payload/components/FrontendAdminHeader/collectors/DebugCollector';
import { getContextualizedPage } from '@repo/payload/contextParameters/getContextualizedPage';
import Helios from '@/components/Helios';
import { ArticleHeader } from '@/components/ArticleHeader/ArticleHeader';
import ArticleBody from '@/components/ArticleBody/ArticleBody';
import { getContextParams } from '@repo/payload/contextParameters/extractParameters';
import { articleToCmsAttributes } from '@/components/AnalyticsBoundary/articleToCmsAttributes';
import { NewRelic } from '@repo/newrelic/NewRelic';
import { getNewRelicMeta } from '@/utils/getNewRelicMeta';

const pageKey = '/content';

export const dynamic = 'force-static';

interface ArticleByIDParams {
	articleId: string;
	locale?: string;
	code: string;
}

interface ArticleByIDProps {
	params: Promise<ArticleByIDParams>;
}

export async function generateMetadata({ params }: ArticleByIDProps) {
	const segments = await params;
	const locale = getLocaleFromPathParams(segments.locale);
	const article = await queryArticlesByArticleId({
		articleId: segments.articleId,
		locale,
	});

	return buildArticlePageMetadata(article);
}

export default async function ArticlePage({ params }: ArticleByIDProps) {
	'use cache';

	const { code, articleId, locale: localeParam } = await params;

	cacheTag(code);

	const locale = getLocaleFromPathParams(localeParam);
	const article = await queryArticlesByArticleId({
		articleId: articleId,
		locale,
	});

	if (!article) {
		return notFound();
	}

	const contextParams = await getContextParams(code);
	const contextParamsResults = await getContextualizedPage({
		...contextParams,
		locale,
		pageKey,
	});
	const pageMetadata = getArticlePageDebugData(article);

	const deviceClass = contextParams?.deviceClass || '';

	const newRelicMeta = await getNewRelicMeta({
		deviceClass,
		code,
		locale,
		pageKey,
	});

	return (
		<article className="prose w-full max-w-4xl rounded-md bg-white p-5">
			<DebugCollector
				componentName="ContentPage"
				data={{
					pageType: 'content',
					locale,
					context: {
						match: contextParamsResults?.match,
					},
				}}
				page={pageMetadata}
			/>
			<AnalyticsBoundary
				pageId={'article'}
				pageLocale={locale}
				deviceClass={deviceClass}
				metricsArticleData={articleToCmsAttributes(article)}
			/>
			<Helios code={code} article={article} />
			<NewRelic meta={newRelicMeta} />
			<ArticleJsonLd article={article} />
			<ArticleHeader article={article} />
			<ArticleBody article={article} />
		</article>
	);
}
