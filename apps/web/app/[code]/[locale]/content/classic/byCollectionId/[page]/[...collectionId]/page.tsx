import React from 'react';
import { unstable_cacheTag as cacheTag } from 'next/cache';
import { getLocaleFromPathParams } from '@/utils/getLocaleFromPathParams';
import { DebugCollector } from '@repo/payload/components/FrontendAdminHeader/collectors/DebugCollector';
import { getContextualizedPage } from '@repo/payload/contextParameters/getContextualizedPage';
import { Metadata } from 'next';
import { getCollectionById } from '@repo/dal/content/collections/byCollectionId';
import {
	buildArticleIndexesPageMetadata,
	PageMetadata,
} from '@repo/payload/collections/Articles/utils/articleData';
import DefaultIndex from '@/components/Collections/defaultindex';
import {
	ARTICLES_PER_PAGE,
	HEALTH_INDEX_GROUP,
} from '@/components/Collections/constants';
import HealthIndex from '@/components/Collections/healthindex';
import { notFound } from 'next/navigation';
import { getServerSideURL } from '@repo/payload/utils/getURL';
import { getLexicalDsxArticlesByCollectionId } from '../../../utils/getLexicalDsxArticle';
import { AnalyticsBoundary } from '@/components/AnalyticsBoundary/AnalyticsBoundary';
import Helios from '@/components/Helios';
import { getContextParams } from '@repo/payload/contextParameters/extractParameters';
import { NewRelic } from '@repo/newrelic/NewRelic';
import { getNewRelicMeta } from '@/utils/getNewRelicMeta';

const pageKey = '/content';

export const dynamic = 'force-static';

function getCollectionUrl(
	collectionId: string,
	locale: string,
	page: number,
): string {
	const baseUrl =
		locale === 'en-US' ? getServerSideURL() : `${getServerSideURL()}/${locale}`;

	return `${baseUrl}/${collectionId}?pg=${page}`;
}

interface Params {
	locale?: string;
	page?: string;
	collectionId: string[];
	code: string;
}

interface PageProps {
	params: Promise<Params>;
}

export async function generateMetadata({
	params,
}: PageProps): Promise<Metadata> {
	const segments = await params;
	const id = segments.collectionId.join('/');

	const page = segments.page ? Number(segments.page) : 1;
	const locale = getLocaleFromPathParams(segments.locale);
	const collection = await getCollectionById(id, locale);

	if (!collection) {
		return notFound();
	}

	const url = getCollectionUrl(id, locale, page);

	const meta: PageMetadata = {
		url,
	};

	if (!HEALTH_INDEX_GROUP.includes(collection.id)) {
		const nextPage = await getLexicalDsxArticlesByCollectionId({
			id,
			locale,
			skip: page * ARTICLES_PER_PAGE,
			limit: 1,
		});

		if (nextPage?.length) {
			meta.nextPageUrl = getCollectionUrl(id, locale, page + 1);
		}

		if (page > 1) {
			meta.previousPageUrl = getCollectionUrl(id, locale, page - 1);
		}
	}

	return buildArticleIndexesPageMetadata(collection, meta);
}

export default async function Page({ params }: PageProps) {
	'use cache';

	const {
		code,
		collectionId,
		page: pageParam,
		locale: localeParam,
	} = await params;

	cacheTag(code);

	const id = collectionId.join('/');
	const page = pageParam ? Number(pageParam) : 1;
	const locale = getLocaleFromPathParams(localeParam);

	const collection = await getCollectionById(id, locale);
	const contextParams = await getContextParams(code);
	const contextParamsResults = await getContextualizedPage({
		...contextParams,
		locale,
		pageKey,
	});

	if (!collection) {
		return notFound();
	}

	const deviceClass = contextParams?.deviceClass || '';

	const newRelicMeta = await getNewRelicMeta({
		deviceClass,
		code,
		locale,
		pageKey,
	});

	return (
		<article className="w-full max-w-4xl rounded-md bg-white">
			<DebugCollector
				componentName="ClassicArticleIndexesPage"
				data={{
					pageType: 'content',
					locale,
					context: {
						match: contextParamsResults?.match,
					},
				}}
			/>
			<AnalyticsBoundary
				pageId="index"
				pageLocale={locale}
				deviceClass={deviceClass}
			/>
			<Helios code={code} />
			<NewRelic meta={newRelicMeta} />
			{HEALTH_INDEX_GROUP.includes(collection.id) ? (
				<HealthIndex locale={locale} collection={collection} />
			) : (
				<DefaultIndex locale={locale} collection={collection} page={page} />
			)}
		</article>
	);
}
