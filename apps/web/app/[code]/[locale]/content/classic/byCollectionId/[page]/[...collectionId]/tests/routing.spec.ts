import { expect, test } from '@playwright/test';
import { BaseTest } from '@repo/playwright-utils';

test.describe('Content classic byCollectionId routing tests', () => {
	const baseTest = new BaseTest();
	// Note: This test may need a valid collection ID for the test environment
	const TEST_COLLECTION_ID = 'news';

	test.beforeEach(async ({ page }) => {
		await baseTest.setupTest(page);
	});

	test('should render classic news with 200 status', async ({ page }) => {
		const response = await page.goto(`/${TEST_COLLECTION_ID}`);
		expect(response?.status()).toBe(200);

		await baseTest.waitForDom(page);
		await baseTest.takeScreenshot(
			page,
			'content-classic-bycollectionid-news-render',
		);
	});
});
