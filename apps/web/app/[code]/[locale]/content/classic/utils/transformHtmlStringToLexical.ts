import {
	convertHTMLToLexical,
	editorConfigFactory,
	type TypedEditorState,
} from '@payloadcms/richtext-lexical';
import type { TextFieldSingleValidation } from 'payload';
import {
	BoldFeature,
	HeadingFeature,
	ItalicFeature,
	LinkFeature,
	ParagraphFeature,
	UnderlineFeature,
	type LinkFields,
} from '@payloadcms/richtext-lexical';
import { JSDOM } from 'jsdom';
import { enabledCollections } from '@repo/payload/fields/defaultLexical';
import WxNodeConverterFeature from '@repo/payload/lexical/features/WxNodeConverterFeature';
import payloadConfig from '@repo/payload/payload-config';

/**
 * Converts HTML string to Lexical JSON format
 * @param html The HTML string to convert
 * @returns A Promise that resolves to the Lexical JSON state
 */
export async function transformHtmlStringToLexical(
	html: string,
): Promise<TypedEditorState> {
	const editorConfig = await editorConfigFactory.fromFeatures({
		config: await payloadConfig,
		features: ({ defaultFeatures }) => [
			...defaultFeatures,
			HeadingFeature({
				enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'],
			}),
			ParagraphFeature(),
			UnderlineFeature(),
			BoldFeature(),
			ItalicFeature(),
			LinkFeature({
				enabledCollections,
				fields: ({ defaultFields }) => {
					const defaultFieldsWithoutUrl = defaultFields.filter((field) => {
						if ('name' in field && field.name === 'url') return false;
						return true;
					});

					return [
						...defaultFieldsWithoutUrl,
						{
							name: 'url',
							type: 'text',
							admin: {
								condition: (_data, siblingData) =>
									siblingData?.linkType !== 'internal',
							},
							label: ({ t }) => t('fields:enterURL'),
							required: true,
							validate: ((value, options) => {
								if (
									(options?.siblingData as LinkFields)?.linkType === 'internal'
								) {
									return true; // no validation needed, as no url should exist for internal links
								}
								return value ? true : 'URL is required';
							}) as TextFieldSingleValidation,
						},
					];
				},
			}),
			WxNodeConverterFeature(),
		],
	});

	const lexicalEditorStateJson = convertHTMLToLexical({
		editorConfig,
		html,
		JSDOM,
	});

	return lexicalEditorStateJson;
}
