import { wxNodesToBlockTypes } from '@repo/payload/lexical/features/WxNodeConverterFeature/transformWxNodesToBlockConfigs';
import type {
	SerializedEditorState,
	SerializedLexicalNode,
} from '@payloadcms/richtext-lexical/lexical';
import type { SerializedBlockNode } from '@payloadcms/richtext-lexical';

/**
 * Removes unsupported wxnode blocks from Lexical content and
 * this is only done on root children level and not deeply nested nodes.
 * Only filters in production environment; preserves all nodes in development/preview
 * @param content The Lexical editor state containing potential wxnode blocks
 * @returns Filtered editor state with only supported wxnode types (in production)
 */
export function removeUnsupportedWxNodes(
	content: SerializedEditorState | null,
): SerializedEditorState | null {
	if (!content || !content.root || !content.root.children) {
		return content;
	}

	// Skip filtering in non-production environments
	const isProduction = process.env.VERCEL_ENV === 'production';

	// Collect unsupported wxnodes for debugging
	const unsupportedWxNodes = content.root.children
		.filter((node: SerializedLexicalNode) => {
			const blockNode = node as SerializedBlockNode;
			return (
				blockNode.type === 'block' &&
				blockNode.fields?.__wxnode &&
				!(
					blockNode.fields.__wxnode.type &&
					wxNodesToBlockTypes[blockNode.fields.__wxnode.type]
				)
			);
		})
		.map((node: SerializedLexicalNode) => {
			const blockNode = node as SerializedBlockNode;
			return {
				type: blockNode.fields?.__wxnode?.type || 'unknown',
				nodeId: blockNode.fields?.__wxnode?.id || 'unknown',
			};
		});

	// Log unsupported nodes to debug system
	if (unsupportedWxNodes.length > 0) {
		console.warn(
			`[removeUnsupportedWxNodes] Found ${unsupportedWxNodes.length} unsupported wxnodes`,
			unsupportedWxNodes,
		);
	}

	if (!isProduction) {
		return content;
	}

	// Filter out blocks with unsupported wxnode types in production
	const filteredChildren = content.root.children.filter(
		(node: SerializedLexicalNode) => {
			const blockNode = node as SerializedBlockNode;
			// Keep non-block nodes
			if (blockNode.type !== 'block') return true;

			// Keep blocks that don't have __wxnode data
			if (!blockNode.fields?.__wxnode) return true;

			// Check if wxnode type is supported
			const wxNodeType = blockNode.fields.__wxnode.type;
			return wxNodeType && wxNodesToBlockTypes[wxNodeType];
		},
	);

	return {
		...content,
		root: {
			...content.root,
			children: filteredChildren,
		},
	};
}
