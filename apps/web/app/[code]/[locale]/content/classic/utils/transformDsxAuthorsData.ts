import type { User } from '@repo/payload/payload-types';
import type {
	DSXArticle,
	DSXAuthorDatum,
} from '@repo/dal/content/articles/types';

type AuthorName = { firstName: string; lastName: string };

export const transformDsxAuthorsData = (
	input: DSXArticle['author_data'],
): User[] => {
	const authors = input.map((author) => {
		const authorImage = author?.authorImage?.[0];

		const { firstName, lastName }: AuthorName = getAuthorName(author);

		return {
			id: `__wxnext_${firstName}_${lastName}`,
			role: ['authenticated'] as Array<'authenticated' | 'admin' | 'editor'>,
			isAuthor: true,
			username: author.email,
			updatedAt: '',
			createdAt: '',
			authorData: {
				firstName,
				lastName,
				bio: author.byline,
				bioUrl: author.bioUrl,
				profilePicture: {
					id: `__wxnext_${author.fullName}`,
					createdAt: '',
					updatedAt: '',
					url: authorImage?.variants?.[0],
					seo: {
						altText: authorImage?.alt_text ?? '',
						caption: '',
					},
				},
			},
		};
	});

	return authors;
};

/**
 * Author data can be stored in a variety of fields, so we need to check each field in order of priority
 * */
const getAuthorName = (author: DSXAuthorDatum): AuthorName => {
	// Prioritize name if available; otherwise, use byline or fullName
	const name = author.name?.trim();
	const byline = author.byline?.trim();
	const fullName = author.fullName?.trim();

	if (name) {
		return convertFullNameIntoAuthorName(name);
	}

	if (byline) {
		return convertBylineIntoAuthorName(byline);
	}

	if (fullName) {
		return convertFullNameIntoAuthorName(fullName);
	}

	return {
		firstName: '',
		lastName: '',
	};
};

const convertBylineIntoAuthorName = (byline: string): AuthorName => {
	const fullNameParts = byline.split(/\s+/);

	// Check if the byline starts with "By" or similar prefix
	if (fullNameParts[0]?.toLowerCase() === 'by') {
		// If it starts with "By", use the second word as first name and the rest as last name
		return {
			firstName: fullNameParts[1] || '',
			lastName: fullNameParts.slice(2).join(' ') || '',
		};
	} else {
		// If no "By" prefix, treat it like a regular name (first word as first name, rest as last name)
		return convertFullNameIntoAuthorName(byline);
	}
};

export const convertFullNameIntoAuthorName = (fullName: string): AuthorName => {
	const fullNameParts = fullName.split(/\s+/);

	return {
		firstName: fullNameParts[0] || '',
		lastName: fullNameParts.slice(1).join(' ') || '',
	};
};
