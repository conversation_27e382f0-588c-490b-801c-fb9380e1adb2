import { DSXArticle } from '@repo/dal/content/articles/types';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { transformDsxAuthorsData } from './transformDsxAuthorsData';
import { User, Image } from '@repo/payload/payload-types';
import { dsxArticleMock } from '@repo/mocks';

describe('transformDsxAuthorsData', () => {
	// Reset mocks before each test
	beforeEach(() => {
		vi.resetAllMocks();
	});

	it('should transform dsx authors correctly when all necessary data is provided', () => {
		const dsxAuthorsData: DSXArticle['author_data'] =
			dsxArticleMock.author_data;

		const authorsData: User[] = transformDsxAuthorsData(dsxAuthorsData);

		expect(authorsData?.[0]?.authorData?.firstName).toBe('Chris');
		expect(authorsData?.[0]?.authorData?.lastName).toBe('Dolce');
		expect(authorsData?.[0]?.authorData?.bioUrl).toBe(
			'/bios/news/2018-08-30-chris-dolce',
		);

		const authorImage = authorsData?.[0]?.authorData?.profilePicture as Image;
		expect(authorImage.url).toBe('https://s.w-x.co/ChrisDCenter_0.jpg');
	});

	it('should transform dsx authors correctly when byline is missing', () => {
		const dsxAuthorsData = [
			{
				...dsxArticleMock.author_data[0],
				byline: '',
			},
		] as DSXArticle['author_data'];

		const authorsData: User[] = transformDsxAuthorsData(dsxAuthorsData);

		expect(authorsData?.[0]?.authorData?.firstName).toBe('Chris');
		expect(authorsData?.[0]?.authorData?.lastName).toBe('Dolce');
	});

	it('should transform dsx authors correctly when fullName is missing', () => {
		const dsxAuthorsData = [
			{
				...dsxArticleMock.author_data[0],
				fullName: '',
			},
		] as DSXArticle['author_data'];

		const authorsData: User[] = transformDsxAuthorsData(dsxAuthorsData);

		expect(authorsData?.[0]?.authorData?.firstName).toBe('Chris');
		expect(authorsData?.[0]?.authorData?.lastName).toBe('Dolce');
	});
});
