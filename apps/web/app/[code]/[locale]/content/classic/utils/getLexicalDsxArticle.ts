import 'server-only';

import { getAssetById } from '@repo/dal/content/assets/byId';
import { getArticlesByAssetName } from '@repo/dal/content/articles/byAssetName';
import {
	getArticlesByCollectionID,
	GetArticlesByCollectionIDProps,
} from '@repo/dal/content/articles/byCollectionId';
import { DSXArticle } from '@repo/dal/content/articles/types';
import { Article } from '@repo/payload/payload-types';
import { transformDsxArticle } from './transformDsxArticle';
import transformDsxCollectionItem, {
	DsxCollectionItem,
} from './transformDsxCollectionItem';

export async function getLexicalDsxArticleByAssetId(
	assetId: string,
	locale: string,
): Promise<Article | null> {
	try {
		const dsxArticle = await getAssetById<DSXArticle>(assetId, locale);
		const lexicalDsxArticle = await transformDsxArticle(dsxArticle, locale);

		return lexicalDsxArticle;
	} catch (error: unknown) {
		console.error(error);

		// Rethrow all errors so they cause a 500 error
		// This ensures caching layers (Vercel and Akamai) know not to cache the error
		throw error;
	}
}

export async function getLexicalDsxArticleByAssetName(
	assetName: string,
	locale: string,
): Promise<Article | null> {
	try {
		const dsxArticles = await getArticlesByAssetName(assetName, locale);

		// Only return null if no articles are found (which will result in a 404)
		if (!dsxArticles || !dsxArticles[0]) {
			return null;
		}

		const lexicalDsxArticle = await transformDsxArticle(dsxArticles[0], locale);

		return lexicalDsxArticle;
	} catch (error: unknown) {
		console.error(error);

		// Rethrow all errors so they cause a 500 error
		// This ensures caching layers (Vercel and Akamai) know not to cache the error
		throw error;
	}
}

export async function getLexicalDsxArticlesByCollectionId(
	args: GetArticlesByCollectionIDProps,
): Promise<DsxCollectionItem[] | null> {
	try {
		const dsxArticles = await getArticlesByCollectionID(args);

		// Only return null if no articles are found (which will result in a 404)
		if (!dsxArticles?.length) {
			return null;
		}

		return dsxArticles.map(transformDsxCollectionItem);
	} catch (error: unknown) {
		console.error(error);

		// Rethrow all errors so they cause a 500 error
		// This ensures caching layers (Vercel and Akamai) know not to cache the error
		throw error;
	}
}
