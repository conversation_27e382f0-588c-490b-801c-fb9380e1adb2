import { describe, it, expect, vi, beforeEach } from 'vitest';
import * as articlesModule from '@repo/dal/content/articles/byAssetName';
import * as assetModule from '@repo/dal/content/assets/byId';
import * as transformModule from './transformDsxArticle';

// Mock server-only module to prevent errors in tests
vi.mock('server-only', () => {
	return {};
});

// Import after mocking server-only
import {
	getLexicalDsxArticleByAssetName,
	getLexicalDsxArticleByAssetId,
} from './getLexicalDsxArticle';

// Mock the modules
vi.mock('@repo/dal/content/articles/byAssetName', () => ({
	getArticlesByAssetName: vi.fn(),
}));

vi.mock('@repo/dal/content/assets/byId', () => ({
	getAssetById: vi.fn(),
}));

vi.mock('./transformDsxArticle', () => ({
	transformDsxArticle: vi.fn(),
}));

describe('getLexicalDsxArticleByAssetName', () => {
	// Create a properly typed network error
	interface NetworkError extends Error {
		code?: string;
		syscall?: string;
		hostname?: string;
	}

	const networkError = new Error(
		'getaddrinfo EBUSY dsx.weather.com',
	) as NetworkError;
	networkError.code = 'EBUSY';
	networkError.syscall = 'getaddrinfo';
	networkError.hostname = 'dsx.weather.com';

	const mockDsxArticle = {
		id: 'test-article-id',
		title: 'Test Article',
		// Minimal mock for testing purposes
	} as unknown as import('@repo/dal/content/articles/types').DSXArticle;

	const mockTransformedArticle = {
		id: 'test-article-id',
		title: 'Test Article',
		// Minimal mock for testing purposes
	} as unknown as import('@repo/payload/payload-types').Article;

	beforeEach(() => {
		vi.resetAllMocks();
	});

	it('should return null when no articles are found', async () => {
		// Mock getArticlesByAssetName to return empty array
		vi.mocked(articlesModule.getArticlesByAssetName).mockResolvedValue([]);

		const result = await getLexicalDsxArticleByAssetName(
			'/test-article',
			'en-US',
		);

		expect(result).toBeNull();
		expect(articlesModule.getArticlesByAssetName).toHaveBeenCalledWith(
			'/test-article',
			'en-US',
		);
	});

	it('should return transformed article when article is found', async () => {
		// Mock getArticlesByAssetName to return an article
		vi.mocked(articlesModule.getArticlesByAssetName).mockResolvedValue([
			mockDsxArticle,
		]);

		// Mock transformDsxArticle to return a transformed article
		vi.mocked(transformModule.transformDsxArticle).mockResolvedValue(
			mockTransformedArticle,
		);

		const result = await getLexicalDsxArticleByAssetName(
			'/test-article',
			'en-US',
		);

		expect(result).toBe(mockTransformedArticle);
		expect(articlesModule.getArticlesByAssetName).toHaveBeenCalledWith(
			'/test-article',
			'en-US',
		);
		expect(transformModule.transformDsxArticle).toHaveBeenCalledWith(
			mockDsxArticle,
			'en-US',
		);
	});

	it('should throw all errors', async () => {
		// Mock getArticlesByAssetName to throw a network error
		vi.mocked(articlesModule.getArticlesByAssetName).mockRejectedValue(
			networkError,
		);

		// Expect the function to throw the network error
		await expect(
			getLexicalDsxArticleByAssetName('/test-article', 'en-US'),
		).rejects.toThrow('getaddrinfo EBUSY dsx.weather.com');

		expect(articlesModule.getArticlesByAssetName).toHaveBeenCalledWith(
			'/test-article',
			'en-US',
		);
	});

	it('should throw non-network errors as well', async () => {
		// Mock getArticlesByAssetName to throw a non-network error
		const nonNetworkError = new Error('Some other error');
		vi.mocked(articlesModule.getArticlesByAssetName).mockRejectedValue(
			nonNetworkError,
		);

		// Expect the function to throw the non-network error
		await expect(
			getLexicalDsxArticleByAssetName('/test-article', 'en-US'),
		).rejects.toThrow('Some other error');

		expect(articlesModule.getArticlesByAssetName).toHaveBeenCalledWith(
			'/test-article',
			'en-US',
		);
	});
});

describe('getLexicalDsxArticleByAssetId', () => {
	// Create a properly typed network error
	interface NetworkError extends Error {
		code?: string;
		syscall?: string;
		hostname?: string;
	}

	const networkError = new Error(
		'getaddrinfo EBUSY dsx.weather.com',
	) as NetworkError;
	networkError.code = 'EBUSY';
	networkError.syscall = 'getaddrinfo';
	networkError.hostname = 'dsx.weather.com';

	const mockDsxArticle = {
		id: 'test-article-id',
		title: 'Test Article',
		// Minimal mock for testing purposes
	} as unknown as import('@repo/dal/content/articles/types').DSXArticle;

	const mockTransformedArticle = {
		id: 'test-article-id',
		title: 'Test Article',
		// Minimal mock for testing purposes
	} as unknown as import('@repo/payload/payload-types').Article;

	beforeEach(() => {
		vi.resetAllMocks();
	});

	it('should return transformed article when article is found', async () => {
		// Mock getAssetById to return an article
		vi.mocked(assetModule.getAssetById).mockResolvedValue(mockDsxArticle);

		// Mock transformDsxArticle to return a transformed article
		vi.mocked(transformModule.transformDsxArticle).mockResolvedValue(
			mockTransformedArticle,
		);

		const result = await getLexicalDsxArticleByAssetId(
			'test-article-id',
			'en-US',
		);

		expect(result).toBe(mockTransformedArticle);
		expect(assetModule.getAssetById).toHaveBeenCalledWith(
			'test-article-id',
			'en-US',
		);
		expect(transformModule.transformDsxArticle).toHaveBeenCalledWith(
			mockDsxArticle,
			'en-US',
		);
	});

	it('should throw all errors', async () => {
		// Mock getAssetById to throw a network error
		vi.mocked(assetModule.getAssetById).mockRejectedValue(networkError);

		// Expect the function to throw the network error
		await expect(
			getLexicalDsxArticleByAssetId('test-article-id', 'en-US'),
		).rejects.toThrow('getaddrinfo EBUSY dsx.weather.com');

		expect(assetModule.getAssetById).toHaveBeenCalledWith(
			'test-article-id',
			'en-US',
		);
	});

	it('should throw non-network errors as well', async () => {
		// Mock getAssetById to throw a non-network error
		const nonNetworkError = new Error('Some other error');
		vi.mocked(assetModule.getAssetById).mockRejectedValue(nonNetworkError);

		// Expect the function to throw the non-network error
		await expect(
			getLexicalDsxArticleByAssetId('test-article-id', 'en-US'),
		).rejects.toThrow('Some other error');

		expect(assetModule.getAssetById).toHaveBeenCalledWith(
			'test-article-id',
			'en-US',
		);
	});
});
