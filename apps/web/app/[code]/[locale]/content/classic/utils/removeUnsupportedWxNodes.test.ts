import { describe, test, expect, vi, beforeEach, afterEach } from 'vitest';
import { removeUnsupportedWxNodes } from './removeUnsupportedWxNodes';
import type { SerializedEditorState } from '@payloadcms/richtext-lexical/lexical';

// Mock the wxNodesToBlockTypes
vi.mock(
	'@repo/payload/lexical/features/WxNodeConverterFeature/transformWxNodesToBlockConfigs',
	() => ({
		wxNodesToBlockTypes: {
			wxnode_video: 'video',
			wxnode_image: 'image',
			wxnode_youtube: 'youtube',
		},
	}),
);

describe('removeUnsupportedWxNodes', () => {
	// Spy on console.warn
	const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

	// Mock environment variables
	const originalVercelEnv = process.env.VERCEL_ENV;

	beforeEach(() => {
		// Reset mocks before each test
		vi.clearAllMocks();
	});

	afterEach(() => {
		// Restore environment variables
		process.env.VERCEL_ENV = originalVercelEnv;
	});

	test('should return null if content is null', () => {
		const result = removeUnsupportedWxNodes(null);
		expect(result).toBeNull();
	});

	test('should return content if root.children is missing', () => {
		const content = { root: {} } as SerializedEditorState;
		const result = removeUnsupportedWxNodes(content);
		expect(result).toBe(content);
	});

	test('should not filter nodes in non-production environment', () => {
		// Set environment to development
		process.env.VERCEL_ENV = 'preview';
		const children = [
			// Supported wxnode
			{
				type: 'block',
				fields: {
					__wxnode: {
						type: 'wxnode_video',
						id: 'video-1',
					},
				},
			},
			// Unsupported wxnode
			{
				type: 'block',
				fields: {
					__wxnode: {
						type: 'wxnode_unsupported',
						id: 'unsupported-1',
					},
				},
			},
			// Regular node
			{
				type: 'paragraph',
				version: 0,
			},
		];
		const content: SerializedEditorState = {
			root: {
				type: 'root',
				version: 1,
				direction: 'ltr',
				format: '',
				indent: 0,
				children: children.map((child) => ({
					...child,
					version: child.version ?? 0,
				})),
			},
		};

		const result = removeUnsupportedWxNodes(content);

		// Should log warning but not filter in non-production
		expect(consoleWarnSpy).toHaveBeenCalled();
		expect(result?.root.children.length).toBe(3);
	});

	test('should filter unsupported wxnodes in production environment', () => {
		// Set environment to production
		process.env.VERCEL_ENV = 'production';
		const children = [
			// Supported wxnode
			{
				type: 'block',
				fields: {
					__wxnode: {
						type: 'wxnode_video',
						id: 'video-1',
					},
				},
			},
			// Unsupported wxnode
			{
				type: 'block',
				fields: {
					__wxnode: {
						type: 'wxnode_unsupported',
						id: 'unsupported-1',
					},
				},
			},
			// Regular node
			{
				type: 'paragraph',
				version: 0,
			},
		];
		const content: SerializedEditorState = {
			root: {
				type: 'root',
				version: 1,
				direction: 'ltr',
				format: '',
				indent: 0,
				children: children.map((child) => ({
					...child,
					version: child.version ?? 0,
				})),
			},
		};

		const result = removeUnsupportedWxNodes(content);

		// Should log warning and filter in production
		expect(consoleWarnSpy).toHaveBeenCalled();
		expect(result?.root.children.length).toBe(2);

		// Should keep supported wxnode and regular node
		expect(result?.root.children[0]).toEqual(content.root.children[0]);
		expect(result?.root.children[1]).toEqual(content.root.children[2]);
	});

	test('should keep non-block nodes', () => {
		// Set environment to production
		process.env.VERCEL_ENV = 'production';

		const content: SerializedEditorState = {
			root: {
				type: 'root',
				version: 1,
				direction: 'ltr',
				format: '',
				indent: 0,
				children: [
					{
						type: 'paragraph',
						version: 0,
					},
					{
						type: 'heading',
						version: 0,
					},
				],
			},
		};

		const result = removeUnsupportedWxNodes(content);

		// Should keep all non-block nodes
		expect(result?.root.children.length).toBe(2);
		expect(result?.root.children).toEqual(content.root.children);
	});

	test('should keep blocks without __wxnode field', () => {
		// Set environment to production
		process.env.VERCEL_ENV = 'production';
		const children = [
			{
				type: 'block',
				fields: {
					someOtherField: 'value',
				},
			},
		];
		const content: SerializedEditorState = {
			root: {
				type: 'root',
				version: 1,
				direction: 'ltr',
				format: '',
				indent: 0,
				children: children.map((child) => ({
					...child,
					version: 0,
				})),
			},
		};

		const result = removeUnsupportedWxNodes(content);

		// Should keep blocks without __wxnode
		expect(result?.root.children.length).toBe(1);
		expect(result?.root.children).toEqual(content.root.children);
	});

	test('should collect and log unsupported wxnodes', () => {
		const children = [
			{
				type: 'block',
				fields: {
					__wxnode: {
						type: 'wxnode_unsupported',
						id: 'unsupported-1',
					},
				},
			},
			{
				type: 'block',
				fields: {
					__wxnode: {
						type: 'wxnode_another_unsupported',
						id: 'unsupported-2',
					},
				},
			},
		];
		const content: SerializedEditorState = {
			root: {
				type: 'root',
				version: 1,
				direction: 'ltr',
				format: '',
				indent: 0,
				children: children.map((child) => ({
					...child,
					version: 0,
				})),
			},
		};

		removeUnsupportedWxNodes(content);

		// Should log warning with details about unsupported nodes
		expect(consoleWarnSpy).toHaveBeenCalledWith(
			'[removeUnsupportedWxNodes] Found 2 unsupported wxnodes',
			[
				{ type: 'wxnode_unsupported', nodeId: 'unsupported-1' },
				{ type: 'wxnode_another_unsupported', nodeId: 'unsupported-2' },
			],
		);
	});
});
