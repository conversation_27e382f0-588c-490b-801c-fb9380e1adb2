import { DSXArticle } from '@repo/dal/content/articles/types';

export interface DsxCollectionItem {
	id: string;
	type: string;
	title: string;
	assetName: string;
	description: string;
	updatedAt: string;
	createdAt: string;
	publishDate: string;
	subHeadline?: string;
	seo: {
		title?: string;
		description?: string;
	};
	featuredImage: {
		seo: {
			altText: string;
			caption: string;
		};
		url: string;
	};
}

export default function transformDsxCollectionItem(
	dsxArticle: DSXArticle,
): DsxCollectionItem {
	const {
		id,
		type,
		title,
		subHeadline,
		description,
		variants,
		publishdate,
		lastmodifieddate,
		assetName,
		seometa,
	} = dsxArticle;

	let featuredImageUrl;

	// this fixes issue where variants is an array of objects instead of an object
	// failsafe for articles out in the wild that have this issue
	if (variants) {
		if (Array.isArray(variants)) {
			featuredImageUrl = variants?.['0'][0];
		} else {
			featuredImageUrl = variants?.['0'] ?? '';
		}
	}

	return {
		id,
		type,
		title,
		assetName,
		description,
		updatedAt: lastmodifieddate.toString(),
		createdAt: publishdate.toString(),
		publishDate: lastmodifieddate.toString(),
		subHeadline: subHeadline ?? seometa?.description,
		seo: {
			title: seometa?.title,
			description: seometa?.description,
		},
		featuredImage: {
			seo: {
				altText: title ?? '',
				caption: '',
			},
			url: featuredImageUrl,
		},
	};
}
