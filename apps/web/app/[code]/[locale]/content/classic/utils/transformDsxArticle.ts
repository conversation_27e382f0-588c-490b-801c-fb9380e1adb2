import 'server-only';

import { Buffer } from 'node:buffer';
import { transformHtmlStringToLexical } from './transformHtmlStringToLexical';
import { type Article, type Tag } from '@repo/payload/payload-types';
import { getAssetById } from '@repo/dal/content/assets/byId';
import { getVideosByIds } from '@repo/dal/content/videos/byIds';
import { getSlideshowAssets } from '@repo/dal/content/slideshow/assets';
import {
	getOrderedCollectionByCollectionName,
	getVideosByCollectionName,
} from '@repo/dal/content/videos/byCollectionName';
import {
	type DSXArticle,
	type DSXWxNode,
	DSXWxNodeSlideshow,
} from '@repo/dal/content/articles/types';
import { type DSXVideo } from '@repo/dal/content/videos/types';
import { type DSXImage } from '@repo/dal/content/types';
import { handleApiCall } from '@/utils/handleApiCall';
import articleCategoriesCollection from './articleCategoriesCollection.json';
import { transformDsxAuthorsData } from './transformDsxAuthorsData';
import { getCollectionById } from '@repo/dal/content/collections/byCollectionId';
import { type CTX } from '@repo/jw-player/types/ctx';

// Helper function to decode HTML entities
function decodeHtmlEntities(text: string): string {
	const entityMap: Record<string, string> = {
		'&amp;': '&',
		'&lt;': '<',
		'&gt;': '>',
		'&quot;': '"',
		'&#39;': "'",
		'&copy;': '©',
		'&reg;': '®',
		'&trade;': '™',
		'&nbsp;': ' ',
		'&mdash;': '—',
		'&ndash;': '–',
		'&hellip;': '…',
	};

	return text.replace(/&[a-zA-Z0-9#]+;/g, (entity) => {
		return entityMap[entity] || entity;
	});
}

export function convertFiguresToSyntheticWxNodes(
	htmlBody: string,
	existingWxNodeIds: string[] = [],
): {
	modifiedHtml: string;
	syntheticWxNodes: DSXWxNode[];
} {
	const syntheticWxNodes: DSXWxNode[] = [];

	// Extract existing wxnode IDs from HTML div elements
	const htmlWxNodeIds: string[] = [];
	const divIdRegex = /<div[^>]+id="(wxn\d+)"[^>]*>/g;
	let match;
	while ((match = divIdRegex.exec(htmlBody)) !== null) {
		if (match[1]) {
			htmlWxNodeIds.push(match[1]);
		}
	}

	// Combine existing wxnode IDs from both sources
	const allExistingIds = [...existingWxNodeIds, ...htmlWxNodeIds];

	// Create a set of existing IDs for O(1) lookup
	const usedIds = new Set(allExistingIds);

	// Generator function for unique IDs
	const generateUniqueWxNodeId = (() => {
		let counter = 1;
		return () => {
			let id: string;
			do {
				id = `wxn${counter.toString().padStart(2, '0')}`;
				counter++;
			} while (usedIds.has(id));
			usedIds.add(id);
			return id;
		};
	})();

	// Find all figure tags and convert them to synthetic wxnodes
	const modifiedHtml = htmlBody.replace(
		/<figure[^>]*>(.*?)<\/figure>/gs,
		(match, figureContent) => {
			// Extract img tag from figure content
			const imgMatch = figureContent.match(/<img([^>]*)>/);
			if (!imgMatch) {
				return match; // Return original if no img found
			}

			const imgAttributes = imgMatch[1];

			// Extract attributes from img tag
			const srcMatch = imgAttributes.match(/src="([^"]*)"/);
			const altMatch = imgAttributes.match(/alt="([^"]*)"/);

			// Extract caption from figcaption or p tag
			let caption = '';
			const figcaptionMatch = figureContent.match(
				/<figcaption[^>]*>(.*?)<\/figcaption>/s,
			);
			if (figcaptionMatch) {
				caption = figcaptionMatch[1].replace(/<[^>]*>/g, '').trim();
				caption = decodeHtmlEntities(caption);
			} else {
				// Fallback to p tag if no figcaption
				const pMatch = figureContent.match(/<p[^>]*>(.*?)<\/p>/s);
				if (pMatch) {
					caption = pMatch[1]
						.replace(/<[^>]*>/g, '') // Remove HTML tags
						.trim();
					// Decode HTML entities
					caption = decodeHtmlEntities(caption);
				}
			}

			if (!srcMatch) {
				return match; // Return original if no src found
			}

			const imageUrl = srcMatch[1];
			const altText = altMatch ? altMatch[1] : '';

			// Generate unique wxnode ID
			const wxNodeId = generateUniqueWxNodeId();

			// Create synthetic wxnode that looks like wxnode_internal_image
			const syntheticWxNode = {
				id: wxNodeId,
				type: 'wxnode_internal_image' as const,
				assetid: '', // No asset ID for external images
				synopsis: caption || '',
				credit: '',
				linkurl: '',
				schema_version: '1.0',
				__wxnext: {
					priority: false,
					imageUrl,
					caption,
					credit: null,
					altText,
					linkUrl: null,
				},
			} as DSXWxNode & {
				__wxnext: {
					priority: boolean;
					imageUrl: string;
					caption: string;
					credit: null;
					altText: string;
					linkUrl: null;
				};
			};

			syntheticWxNodes.push(syntheticWxNode);

			// Return wxnode div tag
			return `<div id="${wxNodeId}" />`;
		},
	);

	return { modifiedHtml, syntheticWxNodes };
}

function convertSelfClosingDivTags(body: string, wxnodes: DSXWxNode[]): string {
	const wxNodeIdMap = wxnodes.reduce((acc, curr) => {
		return {
			...acc,
			[curr.id]: Buffer.from(
				JSON.stringify({
					...curr,
				}),
			).toString('base64'),
		};
	}, {});
	// inject wxnodeConfig as JSON string and add attribute
	return body.replaceAll(
		/(<div id=")(wxn[0-9][0-9]?)"( \/>)/g,
		(match: string, p1: string, p2: string) => {
			// @ts-expect-error ignoring due to laziness
			return `${p1}${p2}" data-config="${wxNodeIdMap[p2]}"></div>`;
		},
	);
}

type ArticleBody = {
	root: {
		type: string;
		children: {
			type: string;
			version: number;
			[k: string]: unknown;
		}[];
		direction: ('ltr' | 'rtl') | null;
		format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
		indent: number;
		version: number;
	};
	[k: string]: unknown;
};

type AsyncOperation = (
	wxNode: DSXWxNode,
	locale: string,
	index?: number,
) => Promise<ProcessedWxNode>;

// Define an interface for the slideshow data structure
interface DSXSlideshow {
	id: string;
	type: string;
	locale: string;
	title: string;
	author: string;
	schema_version: string;
	assets: Array<DSXImage | string>;
	providerid: string;
	publishDate: string;
	lastmodifieddate: string;
	providername: string;
	colls: unknown[];
	tags: {
		keyword: string[];
		iab: {
			v1: string[];
			v2: string[];
			v3: string[];
		};
		storm: string[];
		severe: string;
		locations: {
			regions: string[];
			states: string[];
		};
	};
	flags: {
		[key: string]: boolean | string;
	};
	// Add any other properties that might be present in the slideshow data
	images?: DSXImage[];
	variants?: { [key: string]: string };
}

// Define a return type for the async operations

type ProcessedWxNode = DSXWxNode & {
	__wxnext?: {
		[key: string]: unknown;
	};
};

const WxNodeCallMap: Record<string, AsyncOperation> = {
	wxnode_internal_image: async (wxNode, locale, index) => {
		// Check if this is a synthetic wxnode with pre-populated __wxnext data
		const wxNodeWithNext = wxNode as DSXWxNode & {
			__wxnext?: {
				priority?: boolean;
				imageUrl?: string;
				caption?: string;
				credit?: string | null;
				altText?: string;
				linkUrl?: string | null;
			};
		};

		if (wxNodeWithNext.__wxnext) {
			// This is a synthetic wxnode (from figure conversion)
			// Just ensure priority is set correctly and return
			return {
				...wxNode,
				__wxnext: {
					...wxNodeWithNext.__wxnext,
					priority: index === 0, // Update priority based on position
				},
			};
		}

		// Original logic for real wxnodes with assetid
		let dsxImageAsset;

		if (wxNode.assetid) {
			const { data, error } = await handleApiCall(
				getAssetById<DSXImage | null>(wxNode.assetid, locale),
			);

			if (error) {
				console.error(`Error fetching image asset ${wxNode.assetid}:`, error);
				return wxNode; // Return original wxnode to continue execution
			}

			dsxImageAsset = data;
		}

		if (!dsxImageAsset) {
			return wxNode;
		}

		// We can remove the logging now that we understand the structure

		// Add necessary properties to wxNode so it is used in the frontend
		return {
			...wxNode,
			__wxnext: {
				priority: index === 0,
				imageUrl: dsxImageAsset.variants?.['0'],
				caption: wxNode.synopsis || dsxImageAsset.caption,
				credit: wxNode.credit || dsxImageAsset.imageByline,
				altText: dsxImageAsset.alt_text,
				linkUrl: wxNode.linkurl || null, // Include the linkurl property
			},
		};
	},
	wxnode_slideshow: async (wxNode, _locale) => {
		const slideshowNode = wxNode as DSXWxNodeSlideshow;
		const slideshowId = slideshowNode.slideshow;

		if (!slideshowId) {
			return wxNode;
		}

		try {
			// Get slideshow assets - now get BOTH data and error
			const { data: slideshowData, error: slideshowError } =
				await handleApiCall(
					getSlideshowAssets<DSXSlideshow>(slideshowId, 0, 500),
				);

			if (slideshowError) {
				console.error(
					`Error fetching slideshow data ${slideshowId}:`,
					slideshowError,
				);
				return wxNode;
			}

			// Only proceed if we have valid slideshow data with assets
			if (
				!slideshowData ||
				!Array.isArray(slideshowData.assets) ||
				slideshowData.assets.length === 0
			) {
				return wxNode;
			}

			// Filter out any non-image assets
			const slideshowImages = slideshowData.assets.filter(
				(asset): asset is DSXImage => {
					// Handle the case where asset is a string (asset ID)
					if (typeof asset === 'string') {
						return false;
					}

					// Handle the case where asset is an object (actual image data)
					return (
						typeof asset === 'object' &&
						asset !== null &&
						'type' in asset &&
						asset.type === 'image'
					);
				},
			);

			// If no valid images were found, return original wxNode
			if (slideshowImages.length === 0) {
				return wxNode;
			}

			// Transform each image into the format expected by the test
			const transformedImages = slideshowImages.map((image, index) => {
				return {
					imageUrl: image.variants?.['0'] || '',
					caption: image.caption || '',
					altText: image.alt_text || '',
					id: `int-image-${index + 1}`,
					title: image.title || '',
				};
			});

			// Add necessary properties to wxNode
			return {
				...wxNode,
				__wxnext: {
					images: transformedImages,
					title: slideshowNode.title || '',
				},
			};
		} catch (error) {
			// Log and re-throw any errors from the catch block
			console.error(`Error fetching slideshow data ${slideshowId}:`, error);
			throw error;
		}
	},
	wxnode_video: async (wxNode, locale) => {
		let dsxVideoAsset;

		const collectionId = wxNode?.collection_name;
		let collection = {} as DSXCollection;
		if (collectionId) {
			collection = await getAssetById<DSXCollection>(collectionId);
		}

		// handling wxNode.clipid string
		let clipid = null; // holds the clip id if wxNode.clipid is a single id

		// an array of clip ids
		let clipArray = null;

		// if the wxNode.clipid is a comma separated string...
		if (wxNode?.clipid?.includes(',')) {
			clipArray = wxNode?.clipid
				?.split(',')
				.map((id) => id?.trim())
				.filter((item) => !!item);
		} else if (wxNode?.clipid) {
			// if the clipid is a single id
			clipid = wxNode?.clipid;
		}

		// falls back to the clipid
		const assetid = wxNode?.assetid || clipid;

		// attempt to fetch the actual video asset data from the assetid
		if (assetid) {
			const { data: videoAsset, error: videoError } = await handleApiCall(
				getAssetById<DSXVideo | null>(assetid, locale),
			);

			if (videoError) {
				console.error(`Error fetching video asset ${assetid}:`, videoError);
				return wxNode; // Return original wxnode to continue execution
			}

			dsxVideoAsset = videoAsset;
		}

		// if there is a valid video asset
		if (dsxVideoAsset) {
			const { data: videoIds, error: playlistError } = await handleApiCall(
				fetchPlaylistData(dsxVideoAsset?.playlists || [], 25, locale),
			);

			if (playlistError) {
				console.error(
					`Error fetching playlist data for video ${assetid}:`,
					playlistError,
				);
				return {
					...wxNode,
					__wxnext: {
						...transformDsxVideo(dsxVideoAsset, wxNode, collection),
						playlist: [],
					},
				};
			}

			// if there is no error, and no video ids from a playlist
			// use the clip id array (if it exists) as the list of video ids
			const { data: videos, error: videosError } = await handleApiCall(
				getVideosByIds(videoIds || clipArray || [], 25, locale),
			);

			if (videosError) {
				console.error(
					`Error fetching videos by IDs for video ${assetid}:`,
					videosError,
				);
				return {
					...wxNode,
					__wxnext: {
						...transformDsxVideo(dsxVideoAsset, wxNode, collection),
						playlist: [],
					},
				};
			}

			return {
				...wxNode,
				__wxnext: {
					...transformDsxVideo(dsxVideoAsset, wxNode, collection),
					playlist: (videos || []).map((dsx) =>
						transformDsxVideo(dsx, wxNode, collection),
					),
				},
			};
		}

		// if there's no assetid, but there is a collection name...
		if (!dsxVideoAsset && wxNode.collection_name) {
			// call dsx to get collection and return video asset
			const { data: videoCollection, error: collectionError } =
				await handleApiCall(
					getVideosByCollectionName(wxNode.collection_name, locale),
				);

			if (collectionError) {
				console.error(
					`Error fetching video collection ${wxNode.collection_name}:`,
					collectionError,
				);
				return wxNode; // Return original wxnode to continue execution
			}

			dsxVideoAsset = videoCollection?.[0];
			const playlist =
				videoCollection?.map((coll) =>
					transformDsxVideo(coll, wxNode, collection),
				) || [];

			return {
				...wxNode,
				__wxnext: {
					...transformDsxVideo(dsxVideoAsset, wxNode, collection),
					playlist,
				},
			};
		}

		// if there is no assetid and no collection, but there is a clipArray...
		if (!dsxVideoAsset && clipArray?.length) {
			const { data: videos, error: videosError } = await handleApiCall(
				getVideosByIds(clipArray || [], 25, locale),
			);

			if (videosError) {
				console.error(
					`Error fetching videos by IDs for video ${clipArray?.join(',')}:`,
					videosError,
				);
				return wxNode; // Return original wxnode, since there is no valid asset
			}

			dsxVideoAsset = videos?.shift();
			const playlist = (videos || []).map((dsx) =>
				transformDsxVideo(dsx, wxNode, collection),
			);

			return {
				...wxNode,
				__wxnext: {
					...transformDsxVideo(dsxVideoAsset, wxNode, collection),
					playlist,
				},
			};
		}

		return wxNode;
	},
};

const fetchPlaylistData = async (
	playlists: Record<string, string>[],
	limit = 25,
	locale: string,
): Promise<string[]> => {
	if (!(playlists?.length > 0)) return [];

	const videoIds = [];

	for (const playlist of playlists) {
		const playlistIds = Object.keys(playlist) || [];
		for (const playlistId of playlistIds) {
			const { data: playlistData, error: playlistError } = await handleApiCall(
				getOrderedCollectionByCollectionName(playlistId, locale),
			);

			if (playlistError) {
				console.error(
					`Error fetching playlist data for ${playlistId}:`,
					playlistError,
				);
				continue; // Skip this playlist and continue with others
			}

			videoIds.push(...(playlistData?.video || []));
			if (videoIds?.length > limit) {
				return videoIds.slice(0, limit);
			}
		}
	}

	return videoIds;
};

// exporting interfaces for testing purposes
export interface DSXCollection {
	id: string;
	schema_version: string;
	isPlaylist: boolean;
	title: string;
	description: string;
	url: string;
	ad_metrics: {
		article: {
			metrics: string;
			pagecode: string;
			zone: string;
		};
		video: {
			zone: string;
		};
	};
	seometa?: Record<string, string>;
	background_image?: Record<string, unknown>;
	related_links?: unknown[];
	sponsored?: boolean;
	send_to_unicorn?: boolean;
	icon?: unknown;
}

// exporting for testing purposes only
export const getCtxFromDsx = (
	videoAsset: DSXVideo | null | undefined,
	wxNode: DSXWxNode,
	collection: DSXCollection | undefined,
): CTX => {
	if (!videoAsset) return {} as CTX;

	return {
		jwplayer: '', // jwplayer id, must be set once inside the Video Block
		pcollid: videoAsset?.pcollid || '', // (e.g. news/climate)
		adzone: videoAsset?.adsmetrics?.adzone || '', // (e.g. weather/news/climate)
		assetName: videoAsset?.assetName,
		description: videoAsset?.description ?? '',
		title: videoAsset?.title,
		teaserTitle: videoAsset?.teaserTitle ?? '',
		playlists: videoAsset?.playlists ?? [],
		entitlements: videoAsset?.tags?.entitlements ?? [],
		premium: videoAsset?.premium ?? false,
		iab: videoAsset?.tags?.iab || {}, // iab categories (e.g. {v1:['IAB15-10_Weather','IAB15_Science'],v2:['390_Weather', '464_Science', '467_Environment'],v3:['390_Weather','467_Environment']}
		tagsGeo: (videoAsset?.tags?.geo as string[]) || [],
		tagsKeyword: videoAsset?.tags?.keyword,
		tagsStorm: videoAsset?.tags?.storm as string[],
		providername: videoAsset?.providername || 'no-provider-data', // (e.g. TWC - Digital)
		publishdate: videoAsset?.publishdate ?? '',
		lastmodifieddate: videoAsset?.lastmodifieddate ?? '',
		duration: videoAsset?.duration || '', // duration of the video (e.g. 00:00:56)
		id: videoAsset?.id || '', // internal id (e.g. d7acdcb2-46c9-46de-a1cc-76c7a695eaf5)

		// ads data
		videoIndex: -1, // set in the relevant ad events
		collectionId: wxNode?.collection,
		videoId: videoAsset?.id,
		collectionAdZone: collection?.ad_metrics?.article?.zone, // or ?.video?.zone, if/when video is supported
	};
};

const transformDsxVideo = (
	videoAsset: DSXVideo | null | undefined,
	wxNode: DSXWxNode,
	collection: DSXCollection | undefined,
) => {
	if (!videoAsset) return {};

	const xform = {
		file: videoAsset?.format_urls?.m3u8 || videoAsset?.format_urls?.mp4 || '',
		image: videoAsset?.variants?.[0] || '',
		title: videoAsset?.teaserTitle,
		description: videoAsset?.description,
		tracks: videoAsset?.cc_url
			? [
					{
						file: videoAsset?.cc_url,
						label: 'English',
						kind: 'captions',
						default: false,
					},
				]
			: [],
		custom: {
			ctx: getCtxFromDsx(videoAsset, wxNode, collection),
			wxnode: wxNode,
			collection,
		},
		showDescriptions: wxNode.options?.includes('show_descriptions'),
	};

	return xform;
};

export async function processWxNodeData(
	dsxWxNodes: DSXWxNode[],
	locale: string,
): Promise<ProcessedWxNode[]> {
	// Filter out null values first
	const filteredWxNodes = dsxWxNodes.filter((wxNode) => wxNode !== null);

	const processedWxNodes = await Promise.all(
		filteredWxNodes.map(async (wxNode, index) => {
			if (WxNodeCallMap[wxNode.type]) {
				// @ts-expect-error the check is above, but tsc complains
				return WxNodeCallMap[wxNode.type](wxNode, locale, index);
			}

			return {
				...wxNode,
				__wxnext: {
					_index: index,
				},
			};
		}),
	);

	return processedWxNodes;
}

export interface ProcessedLiveBlog {
	isLiveBlog: boolean;
	eventData: {
		eventStartTime: string;
		eventEndTime: string;
		liveBlogEntries: {
			title: string;
			body: string;
			wxnodes?: ProcessedWxNode[];
			timestamp: string;
			lastmodifieddate?: string;
			publishedDate?: string;
			includeTimestamp?: boolean;
		}[];
	};
}

export interface LiveBlogArticle extends Article {
	liveBlog?: ProcessedLiveBlog;
}

export async function transformDsxArticle(
	input: DSXArticle,
	locale: string,
): Promise<LiveBlogArticle> {
	const dsxBodyString = input?.body ?? '';

	// Extract existing wxnode IDs to avoid collisions
	const existingWxNodeIds = (input?.wxnodes ?? [])
		.filter((node) => !!node?.id)
		.map((node) => node.id);

	// Convert figure tags to synthetic wxnodes BEFORE processing
	const { modifiedHtml: dsxBodyStringWithSyntheticWxNodes, syntheticWxNodes } =
		convertFiguresToSyntheticWxNodes(dsxBodyString, existingWxNodeIds);

	// filtering out null values that exist in some existing DSX wxnode lists
	let dsxWxNodes = (input?.wxnodes ?? [])?.filter((node) => !!node);

	// Add synthetic wxnodes to the existing wxnodes
	dsxWxNodes = [...dsxWxNodes, ...syntheticWxNodes];

	// Process wxnode data with error handling
	const { data: processedWxNodes, error: wxNodeError } = await handleApiCall(
		processWxNodeData(dsxWxNodes, locale),
	);

	if (wxNodeError) {
		console.error('Error processing wxnode data:', wxNodeError);
		// Continue with original wxnodes if there's an error
		dsxWxNodes = dsxWxNodes || [];
	} else {
		dsxWxNodes = processedWxNodes || [];
	}

	const replacedWxNodeTagsBody = convertSelfClosingDivTags(
		dsxBodyStringWithSyntheticWxNodes,
		dsxWxNodes,
	);

	// Transform HTML to Lexical with error handling
	const { data: lexicalBodyResult, error: lexicalError } = await handleApiCall(
		transformHtmlStringToLexical(replacedWxNodeTagsBody),
	);

	// Fallback empty lexical body if transformation fails
	const lexicalBody = lexicalError
		? {
				root: {
					type: 'root',
					children: [
						{
							type: 'paragraph',
							children: [],
							direction: null,
							format: '',
							indent: 0,
							version: 1,
						},
					],
					direction: null,
					format: '',
					indent: 0,
					version: 1,
				},
			}
		: lexicalBodyResult;

	if (lexicalError) {
		console.error('Error transforming HTML to Lexical:', lexicalError);
	}

	// Create a partnerByline object with the appropriate structure for the group field
	const partnerBylineText = input?.partner_byline?.join('');
	// Always create an object, even if partnerBylineText is undefined
	const partnerByline = {
		type: 'text' as const, // Use const assertion to ensure it's exactly "text"
		text: partnerBylineText || '', // Use empty string if partnerBylineText is undefined
	};

	const categoryCollection = articleCategoriesCollection.find(
		(article) => article._id === input.pcollid,
	);

	let authors, externalAuthors;

	if (input.author_data?.length) {
		// if the author is coming from a selected author that should be in the system
		authors = transformDsxAuthorsData(input.author_data);
	} else if (input.author) {
		// if the author is coming from alt_author
		authors = [];
		externalAuthors = input.author.join(', ');
	}

	let featuredImageUrl = input?.variants?.['0'] ?? '';

	// this fixes issue where variants is an array of objects instead of an object
	// failsafe for articles out in the wild that have this issue
	if (input?.variants) {
		if (Array.isArray(input?.variants)) {
			featuredImageUrl = input?.variants?.['0'][0];
		}
	}

	// generating tag data
	const tags: Tag[] = [];

	// generating adZone(s)
	// TODO: The id property is being used to determine if this adzone is for the article or collection
	// TODO: This will need to be updated during Phase 2 when we have content-queries sorted out
	//! id = 'article' | 'collection'
	const adZone = input?.adsmetrics?.adzone;
	if (adZone) {
		tags.push({
			id: '__article_adzone',
			name: adZone,
			type: 'adZones',
			updatedAt: '',
			createdAt: '',
		});
	}

	const pcollid = input?.pcollid;
	if (pcollid) {
		// get the collection dsx from the pcollid
		const { data: collection, error: collectionError } = await handleApiCall(
			getCollectionById(pcollid, locale),
		);

		if (collectionError) {
			console.error(
				`Error fetching collection data for ${pcollid}:`,
				collectionError,
			);
		}

		// get the adzone from the collection
		const collectionAdZone = collection?.ad_metrics?.article?.zone;

		// if the collection has an adzone, add it to the tags
		if (collectionAdZone) {
			tags.push({
				id: '__collection_adzone',
				name: collectionAdZone,
				type: 'adZones',
				updatedAt: '',
				createdAt: '',
			});
		}
	}

	// generating iab tags
	const iabTags = input?.tags?.iab;
	if (iabTags) {
		tags.push({
			id: `__dsx_iab_${iabTags}`,
			name: JSON.stringify(iabTags),
			type: 'iabTags',
			updatedAt: '',
			createdAt: '',
		});
	}

	let processedLiveBlog: ProcessedLiveBlog | undefined = undefined;

	// todo: this is a temporary fix for the live blog data, will be changed to a LiveBlogBlock payload structure
	if (input.liveBlog && input.liveBlog.isLiveBlog && input.liveBlog.eventData) {
		processedLiveBlog = {
			isLiveBlog: true,
			eventData: {
				eventStartTime: input.liveBlog.eventData.eventStartTime,
				eventEndTime: input.liveBlog.eventData.eventEndTime,
				liveBlogEntries: await Promise.all(
					input.liveBlog.eventData.liveBlogEntries.map(async (entry) => {
						const { data: processedWxNodes, error: wxNodeError } =
							await handleApiCall(
								processWxNodeData(entry.wxnodes as DSXWxNode[], 'en-US'),
							);

						if (wxNodeError || !processedWxNodes?.length) return entry;

						return {
							...entry,
							wxnodes: processedWxNodes,
						};
					}),
				),
			},
		};
	}

	return {
		partnerByline,
		headerLayout: 'default',
		id: input.id,
		title: input.title ?? '',
		subHeadline: input.subHeadline ?? input.seometa?.description,
		authors,
		externalAuthors: <AUTHORS>
		updatedAt: input.lastmodifieddate.toString(),
		createdAt: input.publishdate.toString(),
		// NOTE: category tag is required for the article type
		category: {
			relationTo: 'tags',
			value: {
				name: categoryCollection?.title || '',
				slug: categoryCollection?.title,
				fullPath: categoryCollection?._id,
				id: categoryCollection?._id || '',
			} as Tag,
		},
		seo: {
			title: input.seometa?.title,
			description: input.seometa?.description,
			canonicalUrl: input.seometa?.canonical,
		},
		featuredImage: {
			id: `__dsx_image_${input.id}`,
			updatedAt: input.lastmodifieddate.toString(),
			createdAt: input.publishdate.toString(),
			seo: {
				altText: input?.title ?? '',
				caption: '',
			},
			url: featuredImageUrl,
		}, // description: input.description,
		publishDate: input.lastmodifieddate.toString(),
		assetName: input.assetName,
		content: {
			body: lexicalBody as ArticleBody,
		},
		tags, // for adzone
		liveBlog: processedLiveBlog,
	};
}
