import { expect, test } from '@playwright/test';
import { BaseTest } from '@repo/playwright-utils';

test.describe('Content classic byAssetId routing tests', () => {
	const baseTest = new BaseTest();
	// Note: This test may need a valid asset ID for the test environment
	const TEST_ASSET_ID = 'bafb71df-39c1-4431-8d9e-94896d74c14f';

	test.beforeEach(async ({ page }) => {
		await baseTest.setupTest(page);
	});

	test('should render classic article by asset ID page with 200 status', async ({
		page,
	}) => {
		const response = await page.goto(`/content/classic/${TEST_ASSET_ID}`);
		expect(response?.status()).toBe(200);

		await baseTest.waitForDom(page);
		await baseTest.takeScreenshot(page, 'content-classic-byassetid-render');
	});
});
