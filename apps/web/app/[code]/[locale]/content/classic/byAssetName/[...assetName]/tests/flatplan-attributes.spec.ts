import { expect, test } from '@playwright/test';
import { BaseTest } from '@repo/playwright-utils';
import { TestArticlePage } from '@repo/playwright-utils';

// The specific article path we want to test
const ARTICLE_PATH =
	'/news/weather/news/2025-02-19-live-updates-winter-storm-kingston-hits-the-south-mid-atlantic';

test.describe('Article flatplan attributes tests', () => {
	let articlePage: TestArticlePage;
	const baseTest = new BaseTest();

	test.beforeEach(async ({ page }) => {
		// Initialize the BaseTest and TestArticlePage objects
		await baseTest.setupTest(page);
		articlePage = new TestArticlePage(page);
	});

	test('should have correct flatplan data attributes and classes', async ({
		page,
	}) => {
		// Navigate to the article
		const previewUrl =
			process.env.VERCEL_PREVIEW_URL || 'https://local.weather.com';
		await articlePage.navigateToArticle(previewUrl, ARTICLE_PATH);

		// Check for the flatplan_body class
		const flatplanBody = page.locator('.flatplan_body');
		await expect(flatplanBody).toBeVisible();

		// Check for the data-flatplan-id attribute
		const flatplanIdElement = page.locator(
			'[data-flatplan-id="flatplan_body"]',
		);
		await expect(flatplanIdElement).toBeVisible();

		// Verify they're the same element
		const flatplanBodyCount = await flatplanBody.count();
		const flatplanIdCount = await flatplanIdElement.count();
		expect(flatplanBodyCount).toBe(flatplanIdCount);

		// Take screenshot with BaseTest
		await baseTest.takeScreenshot(page, 'article-flatplan-attributes');
	});

	test('should have application/ld+json script tags', async ({ page }) => {
		// Navigate to the article
		const previewUrl =
			process.env.VERCEL_PREVIEW_URL || 'https://local.weather.com';
		await articlePage.navigateToArticle(previewUrl, ARTICLE_PATH);

		// Check for JSON-LD script tags
		const jsonLdScripts = page.locator('script[type="application/ld+json"]');

		// Verify at least one JSON-LD script exists
		const count = await jsonLdScripts.count();
		expect(count).toBeGreaterThan(0);

		// Verify the content of the JSON-LD scripts
		for (let i = 0; i < count; i++) {
			const content = await jsonLdScripts.nth(i).textContent();
			expect(content).toBeTruthy();

			// Parse the JSON to verify it's valid
			const jsonContent = JSON.parse(content || '{}');
			expect(jsonContent['@context']).toBe('https://schema.org');
		}
	});

	test('should have proper meta tags', async ({ page }) => {
		// Navigate to the article
		const previewUrl =
			process.env.VERCEL_PREVIEW_URL || 'https://local.weather.com';
		await articlePage.navigateToArticle(previewUrl, ARTICLE_PATH);

		// Check for og:title meta tag - using first() to handle multiple elements
		const ogTitle = page.locator('meta[property="og:title"]').first();
		await expect(ogTitle).toHaveAttribute('content');

		// Check for twitter:card meta tag - using first() to handle multiple elements
		const twitterCard = page.locator('meta[name="twitter:card"]').first();
		await expect(twitterCard).toHaveAttribute('content');

		// Check for other important meta tags - using first() to handle multiple elements
		const ogType = page.locator('meta[property="og:type"]').first();
		await expect(ogType).toHaveAttribute('content', 'article');

		const ogUrl = page.locator('meta[property="og:url"]').first();
		await expect(ogUrl).toHaveAttribute('content');
	});
});
