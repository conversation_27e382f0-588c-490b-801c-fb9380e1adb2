import React from 'react';
import { unstable_cacheTag as cacheTag } from 'next/cache';
import { notFound } from 'next/navigation';
import { getLexicalDsxArticleByAssetName } from '@/app/[code]/[locale]/content/classic/utils/getLexicalDsxArticle';
import { AnalyticsBoundary } from '@/components/AnalyticsBoundary/AnalyticsBoundary';
import { ArticleHeader } from '@/components/ArticleHeader/ArticleHeader';
import { getLocaleFromPathParams } from '@/utils/getLocaleFromPathParams';
import { DebugCollector } from '@repo/payload/components/FrontendAdminHeader/collectors/DebugCollector';
import { getContextualizedPage } from '@repo/payload/contextParameters/getContextualizedPage';
import { Metadata } from 'next';
import { ArticleJsonLd } from '@/components/JsonLd/JsonLd';
import { TaboolaBlock } from '@repo/payload/blocks';
import {
	buildArticlePageMetadata,
	getArticlePageDebugData,
} from '@repo/payload/collections/Articles/utils/articleData';
import ArticleBody from '@/components/ArticleBody/ArticleBody';
import Helios from '@/components/Helios';
import { getContextParams } from '@repo/payload/contextParameters/extractParameters';
import { articleToCmsAttributes } from '@/components/AnalyticsBoundary/articleToCmsAttributes';
import isAtmosphereArticle from '@/utils/isAtmosphereArticle';
import { NewRelic } from '@repo/newrelic/NewRelic';
import { getNewRelicMeta } from '@/utils/getNewRelicMeta';

const pageKey = '/content';

export const dynamic = 'force-static';

interface ClassicArticleByAssetNameParams {
	locale?: string;
	assetName: string[];
	code: string;
}

interface ClassicArticleByAssetNameProps {
	params: Promise<ClassicArticleByAssetNameParams>;
}

export async function generateMetadata({
	params,
}: ClassicArticleByAssetNameProps): Promise<Metadata> {
	const segments = await params;
	const assetName = `/${segments.assetName.join('/')}`;
	const locale = getLocaleFromPathParams(segments.locale);
	const article = await getLexicalDsxArticleByAssetName(assetName, locale);

	return buildArticlePageMetadata(article);
}

export default async function ClassicArticlePage({
	params,
}: ClassicArticleByAssetNameProps) {
	'use cache';

	const { code, locale: localeParam, assetName: assetNameParam } = await params;

	cacheTag(code);

	const assetName = `/${assetNameParam.join('/')}`;
	const locale = getLocaleFromPathParams(localeParam);
	const article = await getLexicalDsxArticleByAssetName(assetName, locale);

	if (!article) {
		return notFound();
	}

	const articleDebugData = getArticlePageDebugData(article);
	const contextParams = await getContextParams(code);
	const contextParamsResults = await getContextualizedPage({
		...contextParams,
		locale,
		pageKey,
	});

	const deviceClass = contextParams?.deviceClass || '';

	const newRelicMeta = await getNewRelicMeta({
		deviceClass,
		code,
		locale,
		pageKey,
		article,
	});

	return (
		<article className="prose w-full max-w-4xl rounded-md bg-white p-5">
			<DebugCollector
				componentName="ClassicArticlePage"
				data={{
					pageType: 'content',
					locale,
					context: {
						match: contextParamsResults?.match,
					},
				}}
				page={articleDebugData}
			/>

			<AnalyticsBoundary
				pageId={'article'}
				pageLocale={locale}
				deviceClass={deviceClass}
				metricsArticleData={articleToCmsAttributes(article)}
			/>
			<Helios code={code} article={article} />
			<NewRelic meta={newRelicMeta} />
			<TaboolaBlock
				showTaboola={isAtmosphereArticle(article)}
				placements={[
					{
						mode: 'thumbnails-d',
						container: 'taboola-below-content-thumbnails-article',
						placement: 'Below Content Thumbnails - article',
						target_type: 'mix',
					},
				]}
				pageType="article"
			/>
			<ArticleJsonLd article={article} />
			<ArticleHeader article={article} />
			<ArticleBody article={article} classic />
		</article>
	);
}
