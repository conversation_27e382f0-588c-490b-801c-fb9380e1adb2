import { expect, test } from '@playwright/test';
import { BaseTest } from '@repo/playwright-utils';

test.describe('Content classic byAssetName routing tests', () => {
	const baseTest = new BaseTest();
	const TEST_ARTICLE_PATH =
		'/landing-page/news/2023-10-24-test-article-wxnodes';

	test.beforeEach(async ({ page }) => {
		await baseTest.setupTest(page);
	});

	test('should render classic article page with 200 status', async ({
		page,
	}) => {
		const response = await page.goto(TEST_ARTICLE_PATH);
		expect(response?.status()).toBe(200);

		await baseTest.waitForDom(page);
		await baseTest.takeScreenshot(page, 'content-classic-article-render');
	});
});
