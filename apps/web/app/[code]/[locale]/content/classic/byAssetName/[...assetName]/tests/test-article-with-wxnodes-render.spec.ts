import { expect, test } from '@playwright/test';
import { BaseTest } from '@repo/playwright-utils';
import { TestArticlePage } from '@repo/playwright-utils';

// Extend window interface for mParticle testing
declare global {
	interface Window {
		mParticle: {
			logEvent: (
				eventName: string,
				eventType: number,
				attributes: Record<string, any>,
			) => void;
			EventType?: { Other: number };
		};
		mParticleEvents: Array<{
			eventName: string;
			eventType: number;
			attributes: Record<string, any>;
		}>;
		waitForMParticleEvent: (eventName: string) => Promise<void>;
	}
}

// The specific article path we want to test
const ARTICLE_PATH = '/landing-page/news/2023-10-24-test-article-wxnodes';

test.describe('Article page tests', () => {
	let articlePage: TestArticlePage;
	const baseTest = new BaseTest();

	test.beforeEach(async ({ page }) => {
		// Initialize the BaseTest and HomePage objects
		await baseTest.setupTest(page);
		articlePage = new TestArticlePage(page);
	});

	test('should load article page correctly', async ({ page }) => {
		// Navigate and verify
		const previewUrl =
			process.env.VERCEL_PREVIEW_URL || 'https://local.weather.com';

		await articlePage.navigateToArticle(previewUrl, ARTICLE_PATH);

		// Verify article elements
		await articlePage.verifyArticleElements();

		// Take screenshot with BaseTest
		await baseTest.takeScreenshot(page, 'article-page');
	});

	test.skip('should be able to share article via copy link', async ({
		page,
		context,
	}) => {
		// Grant clipboard permissions to browser context
		await context.grantPermissions(['clipboard-read', 'clipboard-write']);

		// Mock mParticle.logEvent before navigating to the page
		await page.addInitScript(() => {
			// Create a mock mParticle object if it doesn't exist
			window.mParticle = window.mParticle || { EventType: { Other: 0 } };

			// Store original logEvent function if it exists
			const originalLogEvent = window.mParticle.logEvent;

			// Create a tracking array for events
			window.mParticleEvents = [];

			// Create event resolvers map
			const eventResolvers: Record<string, (() => void)[]> = {};

			// Function to wait for a specific event
			window.waitForMParticleEvent = (eventName: string) => {
				return new Promise<void>((resolve) => {
					// Check if event already happened
					const eventExists = window.mParticleEvents.some(
						(event) => event.eventName === eventName,
					);

					if (eventExists) {
						resolve();
						return;
					}

					// Otherwise, add resolver to the map
					if (!eventResolvers[eventName]) {
						eventResolvers[eventName] = [];
					}
					eventResolvers[eventName].push(resolve);
				});
			};

			// Mock the logEvent function
			// Use type assertion to avoid read-only property error
			(window.mParticle as any).logEvent = function (
				eventName: string,
				eventType: number,
				attributes: Record<string, any>,
			) {
				// Store the event data for verification
				window.mParticleEvents.push({
					eventName,
					eventType,
					attributes,
				});

				// Resolve any promises waiting for this event
				if (eventResolvers[eventName]) {
					eventResolvers[eventName].forEach((resolve) => resolve());
					eventResolvers[eventName] = [];
				}

				// Call original if it exists
				if (originalLogEvent) {
					return originalLogEvent.call(this, eventName, eventType, attributes);
				}
			};
		});

		// Navigate and verify
		const previewUrl =
			process.env.VERCEL_PREVIEW_URL || 'https://local.weather.com';

		await articlePage.navigateToArticle(previewUrl, ARTICLE_PATH);

		// Click on share button
		await articlePage.articleShareButton.click();

		// Click on copy link button
		await articlePage.articleShareCopyButton.click();

		// Get clipboard content after the button has been clicked
		const handle = await page.evaluateHandle(() =>
			navigator.clipboard.readText(),
		);
		const clipboardContent = await handle.jsonValue();

		// Check that the clipboard contains correct article path
		expect(clipboardContent).toContain(ARTICLE_PATH);

		// Wait for the content-shared event to be tracked
		await page.evaluate(() => window.waitForMParticleEvent('content-shared'));

		// Verify that the mParticle event was sent
		const mParticleEvents = await page.evaluate(() => window.mParticleEvents);

		// Check if at least one event was tracked
		expect(mParticleEvents.length).toBeGreaterThan(0);

		// Find the content-shared event
		const shareEvent = mParticleEvents.find(
			(event: { eventName: string }) => event.eventName === 'content-shared',
		);
		expect(shareEvent).toBeTruthy();

		// Verify the event has the correct sharedMethod
		expect(shareEvent?.attributes.sharedMethod).toBe('linkCopy');
	});

	test.skip('should be able to share article via email', async ({ page }) => {
		// Mock mParticle.logEvent before navigating to the page
		await page.addInitScript(() => {
			// Create a mock mParticle object if it doesn't exist
			window.mParticle = window.mParticle || { EventType: { Other: 0 } };

			// Store original logEvent function if it exists
			const originalLogEvent = window.mParticle.logEvent;

			// Create a tracking array for events
			window.mParticleEvents = [];

			// Create event resolvers map
			const eventResolvers: Record<string, (() => void)[]> = {};

			// Function to wait for a specific event
			window.waitForMParticleEvent = (eventName: string) => {
				return new Promise<void>((resolve) => {
					// Check if event already happened
					const eventExists = window.mParticleEvents.some(
						(event) => event.eventName === eventName,
					);

					if (eventExists) {
						resolve();
						return;
					}

					// Otherwise, add resolver to the map
					if (!eventResolvers[eventName]) {
						eventResolvers[eventName] = [];
					}
					eventResolvers[eventName].push(resolve);
				});
			};

			// Mock the logEvent function
			// Use type assertion to avoid read-only property error
			(window.mParticle as any).logEvent = function (
				eventName: string,
				eventType: number,
				attributes: Record<string, any>,
			) {
				// Store the event data for verification
				window.mParticleEvents.push({
					eventName,
					eventType,
					attributes,
				});

				// Resolve any promises waiting for this event
				if (eventResolvers[eventName]) {
					eventResolvers[eventName].forEach((resolve) => resolve());
					eventResolvers[eventName] = [];
				}

				// Call original if it exists
				if (originalLogEvent) {
					return originalLogEvent.call(this, eventName, eventType, attributes);
				}
			};
		});

		// Navigate to the article
		const previewUrl =
			process.env.VERCEL_PREVIEW_URL || 'https://local.weather.com';

		await articlePage.navigateToArticle(previewUrl, ARTICLE_PATH);

		// No need to mock window.location.href anymore as we've modified the component
		// to store the mailto URL in a data attribute

		// Click on share button
		await articlePage.articleShareButton.click();

		// Verify the email share button is visible
		await expect(articlePage.articleShareEmailButton).toBeVisible();

		// Click on email share button
		await articlePage.articleShareEmailButton.click();

		// Wait for the mailto URL to be captured in the data attribute
		await page.waitForFunction(() =>
			document.body.getAttribute('data-mailto-url'),
		);

		// Get the mailto URL from the data attribute
		const mailtoUrl = await page.evaluate(() =>
			document.body.getAttribute('data-mailto-url'),
		);

		// Verify the mailto link was generated
		expect(mailtoUrl).toBeTruthy();
		expect(mailtoUrl).toContain('mailto:?subject=');

		// Get the current URL from the page to verify it's included in the mailto URL
		const currentUrl = await page.url();
		expect(mailtoUrl).toContain(encodeURIComponent(currentUrl));

		// Wait for the content-shared event to be tracked
		await page.evaluate(() => window.waitForMParticleEvent('content-shared'));

		// Verify that the mParticle event was sent
		const mParticleEvents = await page.evaluate(() => window.mParticleEvents);

		// Check if at least one event was tracked
		expect(mParticleEvents.length).toBeGreaterThan(0);

		// Find the content-shared event
		const shareEvent = mParticleEvents.find(
			(event: { eventName: string }) => event.eventName === 'content-shared',
		);
		expect(shareEvent).toBeTruthy();

		// Verify the event has the correct sharedMethod
		expect(shareEvent?.attributes.sharedMethod).toBe('email');
	});

	test.skip('should be able to share article via X (Twitter)', async ({
		page,
	}) => {
		// Mock mParticle.logEvent before navigating to the page
		await page.addInitScript(() => {
			// Create a mock mParticle object if it doesn't exist
			window.mParticle = window.mParticle || { EventType: { Other: 0 } };

			// Store original logEvent function if it exists
			const originalLogEvent = window.mParticle.logEvent;

			// Create a tracking array for events
			window.mParticleEvents = [];

			// Create event resolvers map
			const eventResolvers: Record<string, (() => void)[]> = {};

			// Function to wait for a specific event
			window.waitForMParticleEvent = (eventName: string) => {
				return new Promise<void>((resolve) => {
					// Check if event already happened
					const eventExists = window.mParticleEvents.some(
						(event) => event.eventName === eventName,
					);

					if (eventExists) {
						resolve();
						return;
					}

					// Otherwise, add resolver to the map
					if (!eventResolvers[eventName]) {
						eventResolvers[eventName] = [];
					}
					eventResolvers[eventName].push(resolve);
				});
			};

			// Mock the logEvent function
			// Use type assertion to avoid read-only property error
			(window.mParticle as any).logEvent = function (
				eventName: string,
				eventType: number,
				attributes: Record<string, any>,
			) {
				// Store the event data for verification
				window.mParticleEvents.push({
					eventName,
					eventType,
					attributes,
				});

				// Resolve any promises waiting for this event
				if (eventResolvers[eventName]) {
					eventResolvers[eventName].forEach((resolve) => resolve());
					eventResolvers[eventName] = [];
				}

				// Call original if it exists
				if (originalLogEvent) {
					return originalLogEvent.call(this, eventName, eventType, attributes);
				}
			};
		});

		// Navigate to the article
		const previewUrl =
			process.env.VERCEL_PREVIEW_URL || 'https://local.weather.com';

		await articlePage.navigateToArticle(previewUrl, ARTICLE_PATH);

		// Store the original window.open function and mock it to capture the URL
		await page.evaluate(() => {
			// Use a data attribute to store the URL instead of modifying window.open
			window.addEventListener('beforeunload', (event) => {
				// This will prevent the actual navigation
				event.preventDefault();
				return (event.returnValue = '');
			});

			// Add a global event listener to capture the Twitter share URL
			document.addEventListener(
				'click',
				(event) => {
					const target = event.target as HTMLElement;
					// Check if the click is on the Twitter share button or its children
					if (target.closest('[data-testid="share-twitter-button"]')) {
						// Extract the URL from the component's handleTwitterShare function
						const twitterUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(window.location.href)}&text=${encodeURIComponent(document.title)}`;
						document.body.setAttribute('data-twitter-share-url', twitterUrl);
					}
				},
				{ capture: true },
			);
		});

		// Click on share button
		await articlePage.articleShareButton.click();

		// Verify the Twitter share button is visible
		await expect(articlePage.articleShareTwitterButton).toBeVisible();

		// Click on Twitter share button
		await articlePage.articleShareTwitterButton.click();

		// Wait for the Twitter share URL to be captured
		await page.waitForFunction(() =>
			document.body.getAttribute('data-twitter-share-url'),
		);

		// Get the Twitter share URL from the data attribute
		const twitterShareUrl = await page.evaluate(() =>
			document.body.getAttribute('data-twitter-share-url'),
		);

		// Verify the Twitter share URL was generated
		expect(twitterShareUrl).toBeTruthy();
		expect(twitterShareUrl).toContain('https://twitter.com/intent/tweet');

		// Get the current URL and title from the page to verify they're included in the Twitter share URL
		const currentUrl = await page.url();
		// const pageTitle = await page.title();

		expect(twitterShareUrl).toContain(encodeURIComponent(currentUrl));
		expect(twitterShareUrl).toContain('text=');

		// Wait for the content-shared event to be tracked
		await page.evaluate(() => window.waitForMParticleEvent('content-shared'));

		// Verify that the mParticle event was sent
		const mParticleEvents = await page.evaluate(() => window.mParticleEvents);

		// Check if at least one event was tracked
		expect(mParticleEvents.length).toBeGreaterThan(0);

		// Find the content-shared event
		const shareEvent = mParticleEvents.find(
			(event: { eventName: string }) => event.eventName === 'content-shared',
		);
		expect(shareEvent).toBeTruthy();

		// Verify the event has the correct sharedMethod
		expect(shareEvent?.attributes.sharedMethod).toBe('twitter');

		// Remove event listeners to clean up
		await page.evaluate(() => {
			// Remove the beforeunload event listener
			window.removeEventListener('beforeunload', (event) => {
				event.preventDefault();
				return (event.returnValue = '');
			});
		});
	});
});
