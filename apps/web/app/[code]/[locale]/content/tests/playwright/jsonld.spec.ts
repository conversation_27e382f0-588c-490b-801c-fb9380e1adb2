import { expect, type Page, test } from '@playwright/test';
import { getJsonLdContent } from '@/components/JsonLd/utils/jsonLdTestUtils';

test.describe('Article JSON-LD Tests', () => {
	test('Article page should have proper meta tags and article schema', async ({
		page,
	}: {
		page: Page;
	}) => {
		// Navigate to a test article
		// Note: This path should be updated to point to a valid article in your environment
		await page.goto(
			'/news/weather/news/2025-02-19-live-updates-winter-storm-kingston-hits-the-south-mid-atlantic',
		);

		// Wait for page to load completely
		await page.waitForLoadState('domcontentloaded');

		// // Verify common meta tags with article type
		// await verifyCommonMetaTags(page, 'article');

		// Verify Article JSON-LD schema
		expect(async () => {
			await verifyArticleJsonLd(page);
		}).not.toThrow();

		// Verify Organization JSON-LD schema
		expect(async () => {
			await verifyOrganizationJsonLd(page);
		}).not.toThrow();
	});
});

/**
 * Verify Article JSON-LD schema
 * @param page Playwright page object
 */
export async function verifyArticleJsonLd(page: any): Promise<void> {
	const articleSchema = await getJsonLdContent(page, '@type', 'NewsArticle');
	if (!articleSchema) {
		throw new Error('Article JSON-LD schema not found');
	}

	if (articleSchema['@context'] !== 'https://schema.org') {
		throw new Error(
			`Expected @context to be "https://schema.org", but got "${articleSchema['@context']}"`,
		);
	}

	if (!articleSchema.headline) {
		throw new Error('Missing headline property in Article JSON-LD schema');
	}
}

/**
 * Verify Organization JSON-LD schema
 * @param page Playwright page object
 */
export async function verifyOrganizationJsonLd(page: any): Promise<void> {
	const orgSchema = await getJsonLdContent(page, '@type', 'Organization');
	if (!orgSchema) {
		throw new Error('Organization JSON-LD schema not found');
	}

	if (orgSchema['@context'] !== 'https://schema.org') {
		throw new Error(
			`Expected @context to be "https://schema.org", but got "${orgSchema['@context']}"`,
		);
	}

	if (!orgSchema.name) {
		throw new Error('Missing name property in Organization JSON-LD schema');
	}

	if (!orgSchema.url) {
		throw new Error('Missing url property in Organization JSON-LD schema');
	}
}
