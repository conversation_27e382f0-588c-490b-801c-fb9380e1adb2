import type { <PERSON>ada<PERSON>, Viewport } from 'next';
import { SpeedInsights } from '@vercel/speed-insights/next';
import { inter } from '@/utils/fonts';
import favicon16x16 from '@/assets/favicons/favicon-16x16.png';
import favicon32x32 from '@/assets/favicons/favicon-32x32.png';
import favicon96x96 from '@/assets/favicons/favicon-96x96.png';
import appleIcon57 from '@/assets/favicons/apple-icon-57x57.png';
import appleIcon60 from '@/assets/favicons/apple-icon-60x60.png';
import appleIcon72 from '@/assets/favicons/apple-icon-72x72.png';
import appleIcon76 from '@/assets/favicons/apple-icon-76x76.png';
import appleIcon114 from '@/assets/favicons/apple-icon-114x114.png';
import appleIcon120 from '@/assets/favicons/apple-icon-120x120.png';
import appleIcon144 from '@/assets/favicons/apple-icon-144x144.png';
import appleIcon152 from '@/assets/favicons/apple-icon-152x152.png';
import appleIcon180 from '@/assets/favicons/apple-icon-180x180.png';
import androidIcon192 from '@/assets/favicons/android-icon-192x192.png';
import { twcSchema } from '@/components/JsonLd/utils/generateSchema';
import { OrganizationJsonLd } from '@/components/JsonLd/JsonLd';
import { VercelToolbar } from '@vercel/toolbar/next';
import { getLocaleFromPathParams } from '@/utils/getLocaleFromPathParams';
import { hasLocale, NextIntlClientProvider } from 'next-intl';
import { setRequestLocale } from 'next-intl/server';
import { getMessages } from 'next-intl/server';
import { LOCALES, isRtlLang } from '@repo/locales';
import { notFound } from 'next/navigation';
import { routing } from '@/i18n/routing';

const SPEED_INSIGHTS_SAMPLE_RATE = process.env
	.NEXT_PUBLIC_SPEED_INSIGHTS_SAMPLE_RATE
	? parseFloat(process.env.NEXT_PUBLIC_SPEED_INSIGHTS_SAMPLE_RATE)
	: 0.1;

export const viewport: Viewport = {
	width: 'device-width',
	initialScale: 1,
	viewportFit: 'cover',
};

export const metadata: Metadata = {
	icons: {
		icon: [
			{ url: favicon16x16.src, sizes: '16x16', type: 'image/png' },
			{ url: favicon32x32.src, sizes: '32x32', type: 'image/png' },
			{ url: favicon96x96.src, sizes: '96x96', type: 'image/png' },
			{ url: androidIcon192.src, sizes: '192x192', type: 'image/png' },
		],
		apple: [
			{ url: appleIcon57.src, sizes: '57x57', type: 'image/png' },
			{ url: appleIcon60.src, sizes: '60x60', type: 'image/png' },
			{ url: appleIcon72.src, sizes: '72x72', type: 'image/png' },
			{ url: appleIcon76.src, sizes: '76x76', type: 'image/png' },
			{ url: appleIcon114.src, sizes: '114x114', type: 'image/png' },
			{ url: appleIcon120.src, sizes: '120x120', type: 'image/png' },
			{ url: appleIcon144.src, sizes: '144x144', type: 'image/png' },
			{ url: appleIcon152.src, sizes: '152x152', type: 'image/png' },
			{ url: appleIcon180.src, sizes: '180x180', type: 'image/png' },
		],
	},
};

export function generateStaticParams() {
	return routing.locales.map((locale) => ({ locale }));
}

export default async function LocaleLayout({
	children,
	params,
}: {
	children: React.ReactNode;
	params: Promise<{ code: string; locale?: string }>;
}) {
	const shouldInjectToolbar = process.env.NODE_ENV === 'development';
	const { locale: localeParam } = await params;

	// Ensure that the incoming locale is valid
	if (!hasLocale(LOCALES, localeParam)) {
		notFound();
	}
	const locale = getLocaleFromPathParams(localeParam);
	const lang = locale.split('-')?.[0] || 'en';
	const dir = isRtlLang(lang) ? 'rtl' : 'ltr';

	// Enable static rendering
	setRequestLocale(locale);

	let messages;

	try {
		messages = await getMessages({
			locale: localeParam,
		});
	} catch (_e) {
		messages = null;
	}

	return (
		<html lang={locale} dir={dir}>
			<body className={`${inter.className} font-sans`}>
				<NextIntlClientProvider
					locale={locale}
					messages={{
						...messages,
					}}
				>
					{children}
					{shouldInjectToolbar && <VercelToolbar />}
					<OrganizationJsonLd
						name={twcSchema.name}
						url={twcSchema.url}
						logo={twcSchema.logo.url}
						sameAs={twcSchema.sameAs}
					/>
					<SpeedInsights sampleRate={SPEED_INSIGHTS_SAMPLE_RATE} />
				</NextIntlClientProvider>
			</body>
		</html>
	);
}
