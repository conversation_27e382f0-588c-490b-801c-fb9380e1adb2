import { unstable_cacheTag as cacheTag } from 'next/cache';
import {
	AdBlock,
	ContentMediaBlock,
	DailyForecastBlock,
	CurrentConditionsBlock,
} from '@repo/payload/blocks';
import { getContextualizedPage } from '@repo/payload/contextParameters/getContextualizedPage';
import { getContextParams } from '@repo/payload/contextParameters/extractParameters';
import {
	mockContentQuery,
	mockContentQuery2,
} from '../../../../home/<USER>';
import { getLocationPointByLocIdParam } from '@repo/dal/locations/point';
import { notFound } from 'next/navigation';
import { tryCatch } from '@repo/utils/tryCatch';
import LocalsuiteNav from '@/components/Header/LocalsuiteNav';
import { AnalyticsBoundary } from '@/components/AnalyticsBoundary/AnalyticsBoundary';
import { getLocaleFromPathParams } from '@/utils/getLocaleFromPathParams';
import { fromLocationPoint } from '@repo/location/utils/fromLocationPoint';
import { LocationBoundary } from '@repo/location/components/LocationBoundary';
import {
	buildPageData,
	buildPageMetadata,
} from '@repo/payload/collections/Pages/utils/pageData';
import { WebPageJsonLd } from '@/components/JsonLd/JsonLd';
import { DebugCollector } from '@repo/payload/components/FrontendAdminHeader/collectors/DebugCollector';
import { Metadata } from 'next';
import { getServerSideURL } from '@repo/payload/utils/getURL';

interface TodayPageByLocIdParams {
	locale?: string;
	locId?: string;
	code: string;
}

interface TodayPageProps {
	params: Promise<TodayPageByLocIdParams>;
}

const pageKey = '/weather/today';

export const dynamic = 'force-static';

function getWeatherTodayUrl(locale: string, locId?: string): string {
	if (locale === 'en-US') {
		return `${getServerSideURL()}/weather/today/l/${locId}`;
	}

	// international locales
	const url = `${getServerSideURL()}/${locale}/weather/today/l/${locId}`;

	return url;
}

export const generateMetadata = async ({
	params,
}: TodayPageProps): Promise<Metadata> => {
	const { code, locale: localeParam, locId } = await params;
	const locale = getLocaleFromPathParams(localeParam);
	const contextParams = await getContextParams(code);
	const { page } = await getContextualizedPage({
		...contextParams,
		locale,
		pageKey,
	});

	const location = locId ? await getLocationData(locId, locale) : null;

	if (!location) {
		return {};
	}

	return await buildPageMetadata(
		page,
		{
			title: `Weather Forecast and Conditions for ${location.presentationName} | weather.com`,
			description: `Today’s and tonight’s ${location.presentationName} weather forecast, weather conditions and Doppler radar from The Weather Channel and weather.com`,
		},
		getWeatherTodayUrl(locale, locId),
	);
};

export default async function TodayPage({ params }: TodayPageProps) {
	'use cache';

	const { code, locale: localeParam, locId } = await params;

	cacheTag(code);

	const locale = getLocaleFromPathParams(localeParam);
	const contextParams = await getContextParams(code);
	const contextParamsResults = await getContextualizedPage({
		...contextParams,
		locale,
		pageKey,
	});

	if (!locId) {
		return notFound();
	}

	// Eventually this should redirect to byCanonicalLocation

	const location = await getLocationData(locId, locale);

	if (!location) {
		return notFound();
	}

	const pageData = buildPageData(contextParamsResults.page, locale, {
		title: `Weather Forecast and Conditions for ${location.presentationName} | weather.com`,
		description: `Today’s and tonight’s ${location.presentationName} weather forecast, weather conditions and Doppler radar from The Weather Channel and weather.com`,
		id: `weather-today-${location.placeId}`,
		status: 'published',
		collection: 'pages',
		tenant: 'twc',
	});

	return (
		<>
			<DebugCollector
				componentName="TodayByCanonicalLocationPage"
				data={{
					pageType: 'weather',
					location,
					contextParams: contextParamsResults,
					// queryParams,
				}}
				page={pageData}
			/>
			<LocalsuiteNav location={location} />
			<LocationBoundary location={location} />
			<AnalyticsBoundary
				pageId="today"
				pageLocale={locale}
				deviceClass={contextParams?.deviceClass || ''}
			/>
			{/* labBG and wx-hero-content divs required for WX_WindowShade ads */}
			<div id="labBG" className="w-full" />
			<div id="wx-hero-content" />
			<div className="mx-auto max-w-7xl px-4 py-4">
				{/* WindowShade (Top banner) advertisement */}
				<AdBlock adId="WX_WindowShade" />
				<WebPageJsonLd
					name={pageData.title || ''}
					url={getWeatherTodayUrl(locale, locId)}
				/>

				{/* Debug section */}
				{/* <div className="bg-gray-200 p-4 rounded border border-gray-300 min-h-[100px] flex items-center justify-center mb-4">
					<p className="text-gray-500">Debug</p>
					{contextParamsResults && (
						<pre>{JSON.stringify(contextParamsResults, null, 2)}</pre>
					)}
				</div> */}

				<div className="flex flex-col gap-4 md:flex-row">
					{/* Left content area (70-75% width) */}
					<div className="space-y-4 md:w-[70%]">
						<CurrentConditionsBlock locationProvider="page" />

						<DailyForecastBlock locationProvider="page" />

						{/* Map/Radar section */}
						<ContentMediaBlock
							contentQuery={mockContentQuery}
							title="Stay Up To Date"
							blockType="ContentMedia"
						/>
					</div>

					{/* Right sidebar (25-30% width) */}
					<div className="space-y-4 md:w-[30%]">
						{/* Advertisement placeholder with subtle label */}
						<AdBlock
							adId="WX_Top300Variable"
							variant="sidebar"
							title="Advertisement"
							height="300px"
							className="mb-4"
						/>

						{/* From Bad To Worse section */}
						<ContentMediaBlock
							contentQuery={mockContentQuery2}
							title="Winter Wellness"
							blockType="ContentMedia"
						/>

						{/* Advertisement */}
						<AdBlock
							adId="WX_Mid300"
							variant="sidebar"
							title="Advertisement"
							height="300px"
							className="mb-4"
						/>
					</div>
				</div>
			</div>
		</>
	);
}

const getLocationData = async (locIdParam: string, locale: string) => {
	const { data: locationPoint, error } = await tryCatch(
		getLocationPointByLocIdParam(locIdParam, locale),
	);

	if (!locationPoint || error) {
		return null;
	}

	return fromLocationPoint(locationPoint) || null;
};
