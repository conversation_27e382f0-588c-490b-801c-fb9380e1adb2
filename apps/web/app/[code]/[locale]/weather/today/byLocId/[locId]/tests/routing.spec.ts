import { expect, test } from '@playwright/test';
import { BaseTest } from '@repo/playwright-utils';

test.describe('Weather today byLocId routing tests', () => {
	const baseTest = new BaseTest();
	const TEST_LOCATION_ID =
		'df6827d57001eefb21014e9786df41cf0abf69f0727b9e2f2e0eb8f1cbc23aba';

	test.beforeEach(async ({ page }) => {
		await baseTest.setupTest(page);
	});

	test('should render weather today page with 200 status', async ({ page }) => {
		const response = await page.goto(`/weather/today/l/${TEST_LOCATION_ID}`);
		expect(response?.status()).toBe(200);

		await baseTest.waitForDom(page);
		await baseTest.takeScreenshot(page, 'weather-today-bylocid-render');
	});
});
