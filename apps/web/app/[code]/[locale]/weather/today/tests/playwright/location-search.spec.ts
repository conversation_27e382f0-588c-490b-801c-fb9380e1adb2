/**
 * Location Search test suite
 *
 * Tests the functionality of the location search feature on the website.
 * These tests verify that users can search for locations and navigate to their weather pages.
 *
 * @remarks
 * The test includes automatic retries configured in playwright.config.ts
 * Screenshots are taken at various steps to assist with debugging
 * The test follows a resilient pattern with multiple fallback strategies:
 * - Checks search field accessibility
 * - <PERSON><PERSON> input debounce with timeouts
 * - Verifies successful navigation through both element detection and URL pattern matching
 *
 * @group navigation
 * @category integration
 */

// TODO: Skiping tests until Vercel fail to fetch error is resolved

import { test, expect } from '@playwright/test';
import { BaseTest } from '@repo/playwright-utils';
import { LocationSearchPage } from '@repo/playwright-utils';
import { TodayPage } from '@repo/playwright-utils';
import { Timeouts } from '@repo/playwright-utils';

test.describe('Location Search', () => {
	let locationSearchPage: LocationSearchPage;
	let todayPage: TodayPage;
	const baseTest = new BaseTest();

	test.beforeEach(async ({ page }) => {
		// Initialize the BaseTest, TodayPage and LocationSearchPage objects
		await baseTest.setupTest(page);
		todayPage = new TodayPage(page);
		locationSearchPage = new LocationSearchPage(page);

		// Navigate to the home page before each test
		await locationSearchPage.goto();
	});

	test.skip('search field should be functional and navigate to location today page', async ({
		page,
	}) => {
		try {
			console.log('Starting location search test');

			// Log device information for debugging
			await locationSearchPage.logDeviceInfo();

			// Wait for todaypage to load via the forecast title
			await todayPage.forecastTitle
				.waitFor({ state: 'visible', timeout: Timeouts.NAVIGATION })
				.catch((error) => {
					console.error('Forecast title not visible:', error);
					throw error;
				});

			// Take a screenshot at the start with device type in the filename
			await baseTest.takeScreenshot(page, 'header-search-start');

			// Ensure the search field is accessible (especially important for mobile)
			const searchFieldAccessible =
				await locationSearchPage.ensureSearchFieldAccessible();
			if (!searchFieldAccessible) {
				console.log('Search field not accessible, test may fail');
				await baseTest.takeScreenshot(page, 'search-field-not-accessible');
			}

			// Type a specific location into the search field with a delay between keystrokes to handle debounce
			for (const char of 'New York City') {
				await locationSearchPage.searchField.type(char);
				await page.waitForTimeout(100); // Add a 100ms delay between keystrokes
			}

			// Wait for search results to appear
			const resultsVisible =
				await locationSearchPage.waitForSearchResults(30000);
			if (!resultsVisible) {
				console.log('No search results found, skipping click on search result');
				throw new Error('No search results found');
			}

			// Take a screenshot of the search results
			await baseTest.takeScreenshot(page, 'search-results-found');

			// Click on the first search result if results exist
			try {
				await locationSearchPage.clickSearchResult(0);
				console.log('Clicked on the first search result');
			} catch {
				console.log('No results to click on, skipping this step');
				return; // Exit the test early if no results are available
			}

			// Take a screenshot after clicking the search result
			await baseTest.takeScreenshot(page, 'header-search-clicked-result');

			// Wait for the URL to change and validate the navigation
			await page
				.waitForURL(todayPage.pathRegex, { timeout: 30000 })
				.catch((error) => {
					console.error('Navigation timeout:', error);
					console.log(
						'Navigation to the location page timed out, but continuing...',
					);
				});

			// Wait for todaypage to load via the forecast title
			await todayPage.forecastTitle
				.waitFor({ state: 'visible', timeout: Timeouts.NAVIGATION })
				.catch(() => {
					console.log('Forecast title not visible, but continuing...');
				});

			await expect(page).toHaveURL(todayPage.pathRegex, { timeout: 30000 });
			console.log('URL validation passed, we are on the correct page');

			// Validate that we are on the correct location today page
			await todayPage.forecastTitle.waitFor({
				state: 'visible',
				timeout: 30000,
			});
			await expect(todayPage.forecastTitle).toContainText('New York City', {
				timeout: 30000,
			});

			// Take a screenshot of the destination page
			await baseTest.takeScreenshot(page, 'header-search-destination');
		} catch (error) {
			try {
				// Attempt to take a failure screenshot to help with debugging
				await baseTest.takeScreenshot(page, 'search-test-failure');
			} catch (screenshotError) {
				console.error('Failed to take failure screenshot:', screenshotError);
			}
			console.error('Location search test failed:', error);
			throw error; // Re-throw to trigger retry
		}
	});
});
