import { test } from '@playwright/test';
import type { Page } from '@playwright/test';
import { verifyWebPageJsonLd } from '@/components/JsonLd/utils/jsonLdTestUtils';

test.describe('Today Page Metadata and JSON-LD Tests', () => {
	test('Today page should have proper meta tags and JSON-LD', async ({
		page,
	}: {
		page: Page;
	}) => {
		// Navigate to today page with a location parameter
		await page.goto(
			'/weather/today/l/a9dd432750b2c4fc6330ea060e46ec62e08ae86ccd9d1e21567a22463842a90c',
		); // Atlanta location

		// Wait for page to load completely
		await page.waitForLoadState('domcontentloaded');

		// Verify WebPage JSON-LD schema
		await verifyWebPageJsonLd(page);
	});
});
