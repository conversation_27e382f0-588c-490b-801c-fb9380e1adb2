import { expect, test } from '@playwright/test';
import { BaseTest } from '@repo/playwright-utils';

test.describe('Weather today byCanonicalLocation routing tests', () => {
	const baseTest = new BaseTest();

	test.beforeEach(async ({ page }) => {
		await baseTest.setupTest(page);
	});

	test('should render weather today canonical location page with 200 status', async ({
		page,
	}) => {
		const response = await page.goto('/en-US/ga/city/atlanta/today');
		expect(response?.status()).toBe(200);

		await baseTest.waitForDom(page);
		await baseTest.takeScreenshot(page, 'weather-today-canonical-render');
	});
});
