import { unstable_cacheTag as cacheTag } from 'next/cache';
import {
	DailyForecastBlock,
	CurrentConditionsBlock,
	AdBlock,
	ContentMediaBlock,
} from '@repo/payload/blocks';
import { getContextualizedPage } from '@repo/payload/contextParameters/getContextualizedPage';
import { DebugCollector } from '@repo/payload/components/FrontendAdminHeader/collectors/DebugCollector';
import { getPathParamsFromCanonicalLocation } from '@repo/location/utils/getPathParamsFromCanonicalLocation';
import {
	mockContentQuery,
	mockContentQuery2,
} from '../../../../home/<USER>';
import { getLocationsByQueryAndType } from '@repo/dal/locations/search';
import { notFound } from 'next/navigation';
import { tryCatch } from '@repo/utils/tryCatch';
import LocalsuiteNav from '@/components/Header/LocalsuiteNav';
import { AnalyticsBoundary } from '@/components/AnalyticsBoundary/AnalyticsBoundary';
import { getLocaleFromPathParams } from '@/utils/getLocaleFromPathParams';
import { fromLocationSearchItem } from '@repo/location/utils/fromLocationSearchItem';
import {
	buildPageData,
	buildPageMetadata,
} from '@repo/payload/collections/Pages/utils/pageData';
import { WebPageJsonLd } from '@/components/JsonLd/JsonLd';
import { Metadata } from 'next';
import { getServerSideURL } from '@repo/payload/utils/getURL';
import { getContextParams } from '@repo/payload/contextParameters/extractParameters';

interface TodayPageParams {
	canonicalLocation: string[];
	code: string;
	locale: string;
}

interface TodayPageProps {
	params: Promise<TodayPageParams>;
}

const pageKey = '/weather/today';

export const dynamic = 'force-static';

function getWeatherTodayUrl(pathParams: {
	locale?: string;
	country?: string;
	adminDistrictCode?: string;
	locationType?: string;
	displayName?: string;
}): string {
	const url = `${getServerSideURL()}/${pathParams.locale}/${pathParams.country}/${pathParams.adminDistrictCode}/${pathParams.locationType}/${pathParams.displayName}`;
	return url;
}

export const generateMetadata = async ({
	params,
}: TodayPageProps): Promise<Metadata> => {
	const { code, locale: localeParam, canonicalLocation } = await params;
	const pathParams = getPathParamsFromCanonicalLocation(canonicalLocation);
	const locale = getLocaleFromPathParams(localeParam);
	const contextParams = await getContextParams(code);
	const { page } = await getContextualizedPage({
		...contextParams,
		locale,
		pageKey,
	});

	const location = await getLocationData(pathParams, locale);

	if (!location) {
		return {};
	}

	return await buildPageMetadata(
		page,
		{
			title: `Weather Forecast and Conditions for ${location.presentationName} | weather.com`,
			description: `Today’s and tonight’s ${location.presentationName} weather forecast, weather conditions and Doppler radar from The Weather Channel and weather.com`,
		},
		getWeatherTodayUrl(pathParams),
	);
};

export default async function TodayPage({ params }: TodayPageProps) {
	'use cache';

	const { code, locale: localeParam, canonicalLocation } = await params;

	cacheTag(code);

	const locale = getLocaleFromPathParams(localeParam);
	// /:country/:adminDistrictCode/:locationType/:displayName
	// Convert catch all segment to individual segments
	const pathParams = getPathParamsFromCanonicalLocation(canonicalLocation);

	const contextParams = await getContextParams(code);
	const contextParamsResults = await getContextualizedPage({
		...contextParams,
		locale,
		pageKey,
	});

	const location = await getLocationData(pathParams, locale);

	if (!location) {
		return notFound();
	}

	const pageData = buildPageData(contextParamsResults.page, locale, {
		title: `Weather Forecast and Conditions for ${location.presentationName} | weather.com`,
		description: `Today’s and tonight’s ${location.presentationName} weather forecast, weather conditions and Doppler radar from The Weather Channel and weather.com`,
		id: `weather-today-${location.placeId}`,
		status: 'published',
		collection: 'pages',
		tenant: 'twc',
	});

	return (
		<>
			<DebugCollector
				componentName="TodayByCanonicalLocationPage"
				data={{
					pageType: 'weather',
					location,
					contextParams: contextParamsResults,
					pathParams,
				}}
				page={pageData}
			/>
			<LocalsuiteNav location={location} />
			<AnalyticsBoundary
				pageId="today"
				pageLocale={locale}
				deviceClass={contextParams?.deviceClass || ''}
			/>
			{/* labBG and wx-hero-content divs required for WX_WindowShade ads */}
			<div id="labBG" className="w-full" />
			<div id="wx-hero-content" />
			<div className="mx-auto max-w-7xl px-4 py-4">
				{/* WindowShade (Top banner) advertisement */}
				<AdBlock adId="WX_WindowShade" />
				<WebPageJsonLd
					name={pageData.title || ''}
					url={getWeatherTodayUrl(pathParams)}
				/>

				<div className="flex flex-col gap-4 md:flex-row">
					{/* Left content area (70-75% width) */}
					<div className="space-y-4 md:w-[70%]">
						<CurrentConditionsBlock locationProvider="page" />

						<DailyForecastBlock locationProvider="page" />

						{/* Map/Radar section */}
						<ContentMediaBlock
							contentQuery={mockContentQuery}
							title="Stay Up To Date"
							blockType="ContentMedia"
						/>
					</div>

					{/* Right sidebar (25-30% width) */}
					<div className="space-y-4 md:w-[30%]">
						{/* Advertisement placeholder with subtle label */}
						<AdBlock
							adId="WX_Top300Variable"
							variant="sidebar"
							title="Advertisement"
							height="300px"
							className="mb-4"
						/>

						{/* From Bad To Worse section */}
						<ContentMediaBlock
							contentQuery={mockContentQuery2}
							title="Winter Wellness"
							blockType="ContentMedia"
						/>

						{/* Advertisement */}
						<AdBlock
							adId="WX_Mid300"
							variant="sidebar"
							title="Advertisement"
							height="300px"
							className="mb-4"
						/>
					</div>
				</div>
			</div>
		</>
	);
}

const getLocationData = async (
	pathParams: ReturnType<typeof getPathParamsFromCanonicalLocation>,
	locale: string,
) => {
	if (!pathParams.locationType) return null;
	// We are doing the WU methodology of canonical urls by passing in as a search query
	const { data: locationSearches, error } = await tryCatch(
		getLocationsByQueryAndType(
			`${pathParams.displayName}, ${pathParams.adminDistrictCode}`,
			pathParams.locationType,
			locale,
		),
	);

	if (!locationSearches || error || !locationSearches[0]) {
		return null;
	}

	return fromLocationSearchItem(locationSearches[0]);
};
