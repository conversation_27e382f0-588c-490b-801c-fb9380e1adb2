import { test, expect } from '@playwright/test';
import { BaseTest } from '@repo/playwright-utils';
import { HomePage } from '@repo/playwright-utils';

test.describe('Homepage Tests', () => {
	let homePage: HomePage;
	const baseTest = new BaseTest();

	test.beforeEach(async ({ page }) => {
		// Initialize the BaseTest and HomePage objects
		await baseTest.setupTest(page);
		homePage = new HomePage(page);
	});

	test('homepage loads successfully without 404', async ({ page }) => {
		try {
			// Navigate to the home page and get the load time
			const result = await homePage.goto();

			// Check if navigation was successful
			expect(result.success).toBe(true);

			// Verify we're not on a 404 page
			const notFound = await homePage.verifyNotFound();
			expect(notFound).toBe(true);

			// Check for successful page load indicators
			const validTitle = await homePage.verifyValidTitle();
			expect(validTitle).toBe(true);

			const hasContent = await homePage.verifyHasContent();
			expect(hasContent).toBe(true);

			// Verify the page loaded within 30 seconds (increased timeout for production URL)
			const loadTimeValid = homePage.verifyLoadTime(result.loadTime, 30000);
			expect(loadTimeValid).toBe(true);

			// Additional check: wait for DOM to be ready to ensure complete page load
			await baseTest.waitForDom(page);

			// Take a screenshot for reference
			await baseTest.takeScreenshot(page, 'homepage-loaded');

			// Final verification that we have a valid page
			console.log(
				`Homepage loaded successfully in ${result.loadTime}ms without 404 errors`,
			);
		} catch (error) {
			console.log(
				`Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
			);
			test.fail(
				true,
				`Development server is not running or page failed to load`,
			);
		}
	});
});
