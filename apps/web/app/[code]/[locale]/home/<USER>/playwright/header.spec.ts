import { test } from '@playwright/test';
import { BaseTest } from '@repo/playwright-utils';
import { HeaderPage } from '@repo/playwright-utils';

test.describe('Header UI Elements', () => {
	let headerPage: HeaderPage;
	const baseTest = new BaseTest();

	test.beforeEach(async ({ page }) => {
		// Initialize the BaseTest and HeaderPage objects
		await baseTest.setupTest(page);
		headerPage = new HeaderPage(page);

		// Navigate to the home page before each test
		await headerPage.goto();
	});

	test('should display logo, search field, profile Sign-in icon, and hamburger menu', async ({
		page,
	}) => {
		// Use BaseTest for network idle and screenshots
		await baseTest.waitForDom(page);

		// Verify all header UI elements are visible
		await headerPage.verifyAllHeaderElementsVisible();

		// Take a screenshot for documentation
		await baseTest.takeScreenshot(page, 'header-elements');
	});

	test('hamburger menu should open when clicked', async ({ page }) => {
		// Use BaseTest for DOM ready
		await baseTest.waitForDom(page);

		// Click the menu button
		await headerPage.clickMenuButton();

		// Check if the navigation menu is visible
		await headerPage.verifyNavigationMenuVisible();

		// Take a screenshot using BaseTest
		await baseTest.takeScreenshot(page, 'hamburger-menu-open');
	});
});
