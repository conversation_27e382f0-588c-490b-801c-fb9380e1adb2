import { test } from '@playwright/test';
import type { Page } from '@playwright/test';
import { verifyWebPageJsonLd } from '@/components/JsonLd/utils/jsonLdTestUtils';

test.describe('Home JSON-LD Tests', () => {
	test('Home page should have proper ld+json tags', async ({
		page,
	}: {
		page: Page;
	}) => {
		// Navigate to home page
		await page.goto('/');

		// Wait for page to load completely
		await page.waitForLoadState('domcontentloaded');

		// Verify WebPage JSON-LD schema
		await verifyWebPageJsonLd(page);
	});
});
