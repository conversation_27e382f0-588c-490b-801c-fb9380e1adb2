import type { Metadata } from 'next';
import LocalsuiteNav from '@/components/Header/LocalsuiteNav';
import { WxApp } from '@/components/WxApp';

// INFO: globals.css is in a child layout because payload has styles that conflict
import '@repo/ui/globals.css';
import { getLocaleFromPathParams } from '@/utils/getLocaleFromPathParams';

export const metadata: Metadata = {
	title:
		'National and Local Weather Radar, Daily Forecast, Hurricane and information from The Weather Channel and weather.com',
	description:
		'The Weather Channel and weather.com provide a national and local weather forecast for cities, as well as weather radar, report and hurricane coverage',
};

export default async function HomeLayout({
	children,
	params,
}: Readonly<{
	children: React.ReactNode;
	params: Promise<{ code: string; locale?: string }>;
}>) {
	const { locale: localeParam } = await params;
	const locale = getLocaleFromPathParams(localeParam);

	return (
		<WxApp locale={locale}>
			<LocalsuiteNav />
			{children}
		</WxApp>
	);
}
