import { expect, test } from '@playwright/test';
import { BaseTest } from '@repo/playwright-utils';

test.describe('Home page routing tests', () => {
	const baseTest = new BaseTest();

	test.beforeEach(async ({ page }) => {
		await baseTest.setupTest(page);
	});

	test('should render home page with 200 status (en-US)', async ({ page }) => {
		const response = await page.goto('/');
		expect(response?.status()).toBe(200);

		await baseTest.waitForDom(page);
		await baseTest.takeScreenshot(page, 'home-en-us-render');
	});

	test('should render home page with 200 status (de-DE)', async ({ page }) => {
		const response = await page.goto('/de-DE');
		expect(response?.status()).toBe(200);

		await baseTest.waitForDom(page);
		await baseTest.takeScreenshot(page, 'home-de-de-render');
	});

	test('should render news page with 200 status', async ({ page }) => {
		const response = await page.goto('/news');
		expect(response?.status()).toBe(200);

		await baseTest.waitForDom(page);
		await baseTest.takeScreenshot(page, 'home-news-render');
	});
});
