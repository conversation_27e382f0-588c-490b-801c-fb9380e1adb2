import React, { Fragment } from 'react';
import { notFound } from 'next/navigation';
import { draftMode } from 'next/headers';
import RefreshRouteOnSave from '@repo/payload/components/RefreshRoute';
import Blocks from '@repo/payload/components/Blocks';
import { DebugCollector } from '@repo/payload/components/FrontendAdminHeader/collectors/DebugCollector';
import { getLocaleFromPathParams } from '@/utils/getLocaleFromPathParams';
import { WebPageJsonLd } from '@/components/JsonLd/JsonLd';
import {
	buildPageData,
	buildPageMetadata,
} from '@repo/payload/collections/Pages/utils/pageData';
import Helios from '@/components/Helios';
import { getServerSideURL } from '@repo/payload/utils/getURL';
import { queryPageByAssetId } from '../../utils/getPayloadPage';
import { NewRelic } from '@repo/newrelic/NewRelic';
import { getNewRelicMeta } from '@/utils/getNewRelicMeta';

export const dynamic = 'force-static';

interface PageParams {
	code: string;
	locale?: string;
	assetId: string;
}

interface PageProps {
	params: Promise<PageParams>;
}

function getPageUrl(locale: string, assetName: string): string {
	const url = `${getServerSideURL()}${assetName}`;
	return url;
}

export async function generateMetadata({ params }: PageProps) {
	const segments = await params;
	const locale = getLocaleFromPathParams(segments.locale);
	const assetId = segments.assetId;
	const page = await queryPageByAssetId({
		assetId,
	});

	if (!page) {
		return notFound();
	}

	return await buildPageMetadata(
		page,
		undefined,
		getPageUrl(locale, page.assetName),
	);
}

export default async function Page({ params }: PageProps) {
	const { isEnabled: draft } = await draftMode();
	const segments = await params;
	const locale = getLocaleFromPathParams(segments.locale);
	const assetId = segments.assetId;
	const page = await queryPageByAssetId({
		assetId,
	});

	if (!page) {
		return notFound();
	}

	const pageData = buildPageData(page, locale, {
		title: page.title,
		id: page.id,
		assetName: page.assetName,
		collection: 'pages',
	});

	const sidebar = page.content.layout?.find(
		(config) => config.region === 'sidebar',
	);
	const main = page.content.layout?.find((config) => config.region === 'main');

	const newRelicMeta = await getNewRelicMeta({
		deviceClass: '',
		code: segments.code,
		locale,
		pageKey: 'content',
	});

	return (
		<Fragment>
			{draft && <RefreshRouteOnSave />}
			<>
				<DebugCollector
					componentName="PageComponent"
					data={{ pageType: 'content' }}
					page={pageData}
				/>
				<WebPageJsonLd
					name={pageData.title || ''}
					url={getPageUrl(locale, page.assetName)}
				/>
				<Helios page={page} />
				<NewRelic meta={newRelicMeta} />
				<div className="mx-auto max-w-7xl space-y-4 px-4">
					{/* Full width area (70-75% width) */}
					{page.title && <h1 className="text-2xl">{page.title}</h1>}\
					<div className="flex flex-col gap-4 md:flex-row">
						{/* Left content area (70-75% width) */}
						<div className="space-y-4 md:w-[70%]">
							<Blocks blocks={main ? main.blocks : null} />
						</div>

						{/* Right sidebar (25-30% width) */}
						<div className="md:w-[30%]">
							<Blocks blocks={sidebar ? sidebar.blocks : null} />
						</div>
					</div>
				</div>
			</>
		</Fragment>
	);
}
