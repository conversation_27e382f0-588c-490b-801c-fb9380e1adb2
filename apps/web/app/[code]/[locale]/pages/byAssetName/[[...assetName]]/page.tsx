import React, { Fragment } from 'react';
import { unstable_cacheTag as cacheTag } from 'next/cache';
import { notFound } from 'next/navigation';
import { draftMode } from 'next/headers';
import RefreshRouteOnSave from '@repo/payload/components/RefreshRoute';
import Blocks from '@repo/payload/components/Blocks';
import { getContextualizedPage } from '@repo/payload/contextParameters/getContextualizedPage';
import { DebugCollector } from '@repo/payload/components/FrontendAdminHeader/collectors/DebugCollector';
import { getLocaleFromPathParams } from '@/utils/getLocaleFromPathParams';
import { WebPageJsonLd } from '@/components/JsonLd/JsonLd';
import {
	buildPageData,
	buildPageMetadata,
} from '@repo/payload/collections/Pages/utils/pageData';
import Helios from '@/components/Helios';
import { getServerSideURL } from '@repo/payload/utils/getURL';
import { queryPageByAssetName } from '../../utils/getPayloadPage';
import { getContextParams } from '@repo/payload/contextParameters/extractParameters';
import { NewRelic } from '@repo/newrelic/NewRelic';
import { getNewRelicMeta } from '@/utils/getNewRelicMeta';

export const dynamic = 'force-static';

interface PageParams {
	locale?: string;
	assetName: string[];
	code: string;
}

interface PageProps {
	params: Promise<PageParams>;
}

function getPageUrl(locale: string, assetName: string): string {
	const url = `${getServerSideURL()}${assetName}`;
	return url;
}

export async function generateMetadata({ params }: PageProps) {
	const segments = await params;
	const locale = getLocaleFromPathParams(segments.locale);
	const assetNameArray = segments.assetName;
	const assetName = assetNameArray ? assetNameArray.join('/') : '';
	const payloadAssetName =
		locale === 'en-US' ? `/${assetName}` : `/${locale}/${assetName}`;
	const page = await queryPageByAssetName({
		assetName: payloadAssetName,
	});

	return await buildPageMetadata(
		page,
		undefined,
		getPageUrl(locale, payloadAssetName),
	);
}

export default async function Page({ params }: PageProps) {
	'use cache';

	const { isEnabled: draft } = await draftMode();
	const { code, assetName: assetNameParam, locale: localeParam } = await params;

	cacheTag(code);

	const locale = getLocaleFromPathParams(localeParam);
	const assetNameArray = assetNameParam;
	const assetName = assetNameArray ? assetNameArray.join('/') : '';
	const payloadAssetName =
		locale === 'en-US' ? `/${assetName}` : `/${locale}/${assetName}`;
	const contextParams = await getContextParams(code);
	const contextParamsResults = await getContextualizedPage({
		...contextParams,
		locale,
		pageKey: payloadAssetName,
	});
	const { page } = contextParamsResults || {};

	if (!page) {
		return notFound();
	}

	const pageData = buildPageData(page, locale, {
		title: page.title,
		id: page.id,
		assetName: assetName,
		collection: 'pages',
	});

	const sidebar = page.content.layout?.find(
		(config) => config.region === 'sidebar',
	);
	const main = page.content.layout?.find((config) => config.region === 'main');

	const deviceClass = contextParams?.deviceClass || '';

	const newRelicMeta = await getNewRelicMeta({
		deviceClass,
		code,
		locale,
		pageKey: 'content',
	});

	return (
		<Fragment>
			{draft && <RefreshRouteOnSave />}
			<>
				<DebugCollector
					componentName="PageComponent"
					data={{ pageType: 'content' }}
					page={pageData}
				/>
				<WebPageJsonLd
					name={pageData.title || ''}
					url={getPageUrl(locale, payloadAssetName)}
				/>
				<Helios page={page} code={code} />
				<NewRelic meta={newRelicMeta} />
				<div className="mx-auto max-w-7xl space-y-4 px-4">
					{/* Full width area (70-75% width) */}
					{page.title && <h1 className="text-2xl">{page.title}</h1>}
					<div className="min-h-[150px] w-full rounded border border-gray-300 bg-gray-200 p-4">
						<h4 className="text-gray-500">Debug</h4>
						{contextParamsResults && (
							<pre>{JSON.stringify(contextParamsResults.match, null, 2)}</pre>
						)}
					</div>
					<div className="flex flex-col gap-4 md:flex-row">
						{/* Left content area (70-75% width) */}
						<div className="space-y-4 md:w-[70%]">
							<Blocks blocks={main ? main.blocks : null} />
						</div>

						{/* Right sidebar (25-30% width) */}
						<div className="md:w-[30%]">
							<Blocks blocks={sidebar ? sidebar.blocks : null} />
						</div>
					</div>
				</div>
			</>
		</Fragment>
	);
}
