import { draftMode } from 'next/headers';
import { getPayload } from 'payload';
import configPromise from '@repo/payload/payload-config';
import { getLocale } from '@repo/payload/utils/getSupportedLocale';

export const queryPageByAssetName = async ({
	assetName,
	locale,
}: {
	assetName: string;
	locale?: string;
}) => {
	const { isEnabled: draft } = await draftMode();

	const payload = await getPayload({ config: configPromise });
	const validPayloadLocale = getLocale(locale);

	console.log(
		`Querying page by assetName: ${assetName}, locale: ${validPayloadLocale}`,
	);

	// First try with the exact assetName
	const response = await payload.find({
		collection: 'pages',
		depth: 2,
		limit: 1,
		overrideAccess: true,
		pagination: false,
		draft,
		where: {
			assetName: {
				// assetName should have a locale in it
				equals: assetName,
			},
		},
	});

	if (response.docs.length > 0) {
		return response.docs[0];
	}

	return null;
};

export const queryPageByAssetId = async ({
	assetId,
	locale,
}: {
	assetId: string;
	locale?: string;
}) => {
	const { isEnabled: draft } = await draftMode();

	const payload = await getPayload({ config: configPromise });
	const validPayloadLocale = getLocale(locale);

	console.log(
		`Querying page by assetId: ${assetId}, locale: ${validPayloadLocale}`,
	);

	// First try with the exact assetName
	const response = await payload.find({
		collection: 'pages',
		depth: 2,
		limit: 1,
		overrideAccess: true,
		pagination: false,
		draft,
		where: {
			id: {
				equals: assetId,
			},
		},
	});

	if (response.docs.length > 0) {
		return response.docs[0];
	}

	return null;
};
