import { NextResponse } from 'next/server';
import { getPayload } from 'payload';
import config from '@repo/payload/payload-config';
import { getVideosByCollectionName } from '@repo/dal/content/videos/byCollectionName';
import { DSXVideo } from '@repo/dal/content/videos/types';

export const dynamic = 'force-static';

export async function GET() {
	let logger;
	try {
		const payload = await getPayload({ config });
		logger = payload.logger;

		const samsungKoreaConfig = await payload.findGlobal({
			slug: 'samsung-korea',
		});

		// keywordQuantities declared in desired order
		const keywordQuantities: Record<string, number> = {
			'samsung-kr-forecast':
				samsungKoreaConfig?.keywordLimits?.forecastLimit || 1,
			'samsung-kr-weather':
				samsungKoreaConfig?.keywordLimits?.weatherLimit || 1,
		};

		let orderedList: DSXVideo[] = [];
		try {
			orderedList = await getVideosByCollectionName(
				'pl-samsung-korea',
				'ko-KR',
			);
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : String(error);
			logger?.error('Error to fetch video by collection:', errorMessage);
			return NextResponse.json(
				{ error: `Error to fetch video by collection: ${errorMessage}` },
				{ status: 500 },
			);
		}

		const sortedOrderedList = orderedList.sort((a, b) => {
			return (
				new Date(b.lastmodifieddate).getTime() -
				new Date(a.lastmodifieddate).getTime()
			);
		});

		const splittedResponse = new Map<string, DSXVideo[]>();

		// Initialize map with empty arrays for each keyword in order
		for (const [keyword] of Object.entries(keywordQuantities)) {
			splittedResponse.set(keyword, []);
		}

		for (const item of sortedOrderedList) {
			if (item.tags?.keyword) {
				for (const keyword of item.tags.keyword) {
					const limit = keywordQuantities[keyword];
					const arr = splittedResponse.get(keyword);
					if (!limit || !arr) continue;

					if (arr.length < limit) {
						arr.push(item);
					}
				}
			}
		}

		// Join items in the order of keywordQuantities
		const joinedResponse: DSXVideo[] = [];
		for (const [_, items] of splittedResponse) {
			joinedResponse.push(...items);
		}

		if (joinedResponse.length == 0) {
			return new NextResponse(null, { status: 204 });
		}

		return NextResponse.json(joinedResponse);
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logger?.error('Internal server error:', errorMessage);
		return NextResponse.json(
			{ error: `Internal server error: ${errorMessage}` },
			{ status: 500 },
		);
	}
}
