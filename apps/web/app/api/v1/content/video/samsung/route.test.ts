import { describe, it, expect, beforeEach, vi } from 'vitest';
import { GET } from './route';
import { getPayload } from 'payload';
import { getVideosByCollectionName } from '@repo/dal/content/videos/byCollectionName';
import type { Mock } from 'vitest';

vi.mock('payload');
vi.mock('@repo/dal/content/videos/byCollectionName');

const mockedGetPayload = getPayload as Mock;
const mockedGetVideosByCollectionName = getVideosByCollectionName as Mock;

const mockLogger = {
	info: vi.fn(),
	error: vi.fn(),
	warn: vi.fn(),
};

const mockSamsungKoreaConfig = {
	keywordLimits: {
		forecastLimit: 2,
		weatherLimit: 1,
	},
};

const mockVideos: any[] = [
	{
		id: '1',
		lastmodifieddate: new Date('2024-08-14T12:00:00Z'),
		tags: {
			keyword: ['samsung-kr-forecast'],
			geo: [],
			locations: { regions: [], states: [] },
			iab: { v1: [], v2: [], v3: [] },
			ai: { v1: [], v2: [], v3: [] },
			storm: [],
			entitlements: [],
		},
		title: 'Forecast Video 1',
		description: 'Latest forecast',
	},
	{
		id: '2',
		lastmodifieddate: new Date('2024-08-14T11:00:00Z'),
		tags: {
			keyword: ['samsung-kr-weather'],
			geo: [],
			locations: { regions: [], states: [] },
			iab: { v1: [], v2: [], v3: [] },
			ai: { v1: [], v2: [], v3: [] },
			storm: [],
			entitlements: [],
		},
		title: 'Weather Video 1',
		description: 'Current weather',
	},
	{
		id: '3',
		lastmodifieddate: new Date('2024-08-14T10:00:00Z'),
		tags: {
			keyword: ['samsung-kr-forecast'],
			geo: [],
			locations: { regions: [], states: [] },
			iab: { v1: [], v2: [], v3: [] },
			ai: { v1: [], v2: [], v3: [] },
			storm: [],
			entitlements: [],
		},
		title: 'Forecast Video 2',
		description: 'Another forecast',
	},
	{
		id: '4',
		lastmodifieddate: new Date('2024-08-14T09:00:00Z'),
		tags: {
			keyword: ['samsung-kr-weather'],
			geo: [],
			locations: { regions: [], states: [] },
			iab: { v1: [], v2: [], v3: [] },
			ai: { v1: [], v2: [], v3: [] },
			storm: [],
			entitlements: [],
		},
		title: 'Weather Video 2',
		description: 'Older weather',
	},
];

describe('GET /api/v1/content/video/samsung', () => {
	beforeEach(() => {
		vi.clearAllMocks();
		mockedGetPayload.mockResolvedValue({
			logger: mockLogger,
			findGlobal: vi.fn().mockResolvedValue(mockSamsungKoreaConfig),
		});
	});

	it('should return a sorted and filtered list of videos', async () => {
		mockedGetVideosByCollectionName.mockResolvedValue(mockVideos);

		const response = await GET();
		const json = await response.json();

		expect(response.status).toBe(200);
		expect(json).toHaveLength(3);
		expect(json[0].id).toBe('1'); // newest forecast
		expect(json[1].id).toBe('3'); // second newest forecast
		expect(json[2].id).toBe('2'); // newest weather
		expect(mockLogger.error).not.toHaveBeenCalled();
	});

	it('should return 204 No Content when no videos are found', async () => {
		mockedGetVideosByCollectionName.mockResolvedValue([]);

		const response = await GET();

		expect(response.status).toBe(204);
		expect(response.body).toBe(null);
	});

	it('should return 500 if getVideosByCollectionName fails', async () => {
		const error = new Error('Failed to fetch videos');
		mockedGetVideosByCollectionName.mockRejectedValue(error);

		const response = await GET();
		const json = await response.json();

		expect(response.status).toBe(500);
		expect(json.error).toContain('Error to fetch video by collection');
		expect(mockLogger.error).toHaveBeenCalledWith(
			'Error to fetch video by collection:',
			'Failed to fetch videos',
		);
	});

	it('should return 500 if getPayload fails', async () => {
		const error = new Error('Payload connection failed');
		mockedGetPayload.mockRejectedValue(error);

		const response = await GET();
		const json = await response.json();

		expect(response.status).toBe(500);
		expect(json.error).toContain('Internal server error');
	});

	it('should handle items with no tags gracefully', async () => {
		const videosWithMissingTags = [
			...mockVideos,
			{
				id: '5',
				lastmodifieddate: new Date(),
				title: 'Video with no tags',
				description: 'description',
			},
		];
		mockedGetVideosByCollectionName.mockResolvedValue(videosWithMissingTags);

		const response = await GET();
		const json = await response.json();

		expect(response.status).toBe(200);
		expect(json.length).toBe(3);
	});
});
