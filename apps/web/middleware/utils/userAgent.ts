import { type NextRequest, userAgent } from 'next/server';

// device classes
const mobile = 'mobile';
const tablet = 'tablet';
const desktop = 'desktop';
const defaultUserAgent = desktop;

// user agent patterns
const mobilePattern = new RegExp(/Mobi|SM-[TNGAFP]/);
const tabletPattern = new RegExp(/iPad|Tablet/);
const tvPattern = new RegExp(/TV/);

/**
 * Returns a device class - one of "mobile", "tablet", "desktop" - based on the
 * request's user-agent.
 */
export function deriveDeviceClass(request: NextRequest): string {
	const { ua } = userAgent(request);

	if (ua.match(mobilePattern)) {
		return mobile;
	}

	if (ua.match(tabletPattern)) {
		return tablet;
	}

	if (ua.match(tvPattern)) {
		// TVs are treated as desktop
		return desktop;
	}

	// Default to desktop
	return defaultUserAgent;
}
