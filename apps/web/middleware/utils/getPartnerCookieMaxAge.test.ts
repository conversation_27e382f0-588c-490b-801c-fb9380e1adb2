import { describe, expect, it } from 'vitest';
import { getPartnerCookieMaxAge } from './getPartnerCookieMaxAge';

describe('getPartnerCookieMaxAge', () => {
	it('should return null for null partner value', () => {
		expect(getPartnerCookieMaxAge(null)).toBe(null);
	});

	it('should return null for empty string partner value', () => {
		expect(getPartnerCookieMaxAge('')).toBe(null);
	});

	it('should return 7200 for Samsung partner (lowercase)', () => {
		expect(getPartnerCookieMaxAge('samsung')).toBe(7200);
	});

	it('should return 7200 for Samsung partner (uppercase)', () => {
		expect(getPartnerCookieMaxAge('SAMSUNG')).toBe(7200);
	});

	it('should return 7200 for Samsung partner (mixed case)', () => {
		expect(getPartnerCookieMaxAge('Samsung')).toBe(7200);
	});

	it('should return 7200 for partner value containing samsung', () => {
		expect(getPartnerCookieMaxAge('samsung-mobile')).toBe(7200);
		expect(getPartnerCookieMaxAge('partner-samsung-app')).toBe(7200);
	});

	it('should return 1800 for non-Samsung partner values', () => {
		expect(getPartnerCookieMaxAge('google')).toBe(1800);
		expect(getPartnerCookieMaxAge('apple')).toBe(1800);
		expect(getPartnerCookieMaxAge('microsoft')).toBe(1800);
		expect(getPartnerCookieMaxAge('partner-123')).toBe(1800);
	});
});
