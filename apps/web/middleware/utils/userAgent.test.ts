import { describe, it, expect, vi } from 'vitest';
import { deriveDeviceClass } from './userAgent';
import { NextRequest } from 'next/server';

// Mock the next/server userAgent function
vi.mock('next/server', async () => {
	const actual = await vi.importActual('next/server');
	return {
		...actual,
		userAgent: vi.fn((req) => {
			// Extract the user agent from the mocked request
			const ua = req.headers.get('user-agent') || '';

			// Simple device type detection for the mock
			// This is a simplified version of what Next.js would do
			let deviceType = '';
			if (ua.includes('Mobile') || ua.includes('Mobi')) {
				deviceType = 'mobile';
			} else if (ua.includes('Tablet') || ua.includes('iPad')) {
				deviceType = 'tablet';
			} else {
				deviceType = 'desktop';
			}

			return {
				ua,
				device: {
					type: deviceType,
				},
			};
		}),
	};
});

// Helper function to create a mock NextRequest with a specific user agent
function createMockRequest(userAgentString: string): NextRequest {
	return {
		headers: {
			get: (name: string) =>
				name.toLowerCase() === 'user-agent' ? userAgentString : null,
		},
	} as unknown as NextRequest;
}

describe('deriveDeviceClass', () => {
	// Test mobile devices
	describe('Mobile devices', () => {
		it('should detect iPhone as mobile', () => {
			const req = createMockRequest(
				'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
			);
			expect(deriveDeviceClass(req)).toBe('mobile');
		});

		it('should detect iPad with Mobile in user agent as mobile (mobile pattern takes precedence)', () => {
			const req = createMockRequest(
				'Mozilla/5.0 (iPad; CPU OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
			);
			expect(deriveDeviceClass(req)).toBe('mobile');
		});

		it('should detect TV devices with Mobile in user agent as mobile (mobile pattern takes precedence)', () => {
			const req = createMockRequest(
				'AppleTV6,2/11.1 (Apple TV; PID:5) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15J582 TV',
			);
			expect(deriveDeviceClass(req)).toBe('mobile');
		});

		it('should detect Android mobile as mobile', () => {
			const req = createMockRequest(
				'Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
			);
			expect(deriveDeviceClass(req)).toBe('mobile');
		});

		it('should detect Samsung mobile device pattern (SM-G)', () => {
			const req = createMockRequest(
				'Mozilla/5.0 (Linux; Android 11; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
			);
			expect(deriveDeviceClass(req)).toBe('mobile');
		});

		it('should detect Samsung mobile device pattern (SM-N)', () => {
			const req = createMockRequest(
				'Mozilla/5.0 (Linux; Android 10; SM-N975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
			);
			expect(deriveDeviceClass(req)).toBe('mobile');
		});

		it('should detect Windows Phone as mobile', () => {
			const req = createMockRequest(
				'Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 950) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Mobile Safari/537.36 Edge/15.15063',
			);
			expect(deriveDeviceClass(req)).toBe('mobile');
		});
	});

	// Test tablet devices
	describe('Tablet devices', () => {
		it('should detect iPad as tablet when no mobile pattern is present', () => {
			const req = createMockRequest(
				'Mozilla/5.0 (iPad; CPU OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/604.1',
			);
			expect(deriveDeviceClass(req)).toBe('tablet');
		});

		it('should detect Android tablet as tablet when no mobile pattern is present', () => {
			const req = createMockRequest(
				'Mozilla/5.0 (Linux; Android 11; Tab A7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Safari/537.36 Tablet',
			);
			expect(deriveDeviceClass(req)).toBe('tablet');
		});

		it('should detect Amazon Fire tablet as tablet', () => {
			const req = createMockRequest(
				'Mozilla/5.0 (Linux; Android 9; KFMAWI) AppleWebKit/537.36 (KHTML, like Gecko) Silk/92.2.1 like Chrome/92.0.4515.131 Safari/537.36 Tablet',
			);
			expect(deriveDeviceClass(req)).toBe('tablet');
		});
	});

	// Test TV devices (should return desktop)
	describe('TV devices', () => {
		it('should detect Smart TV as desktop', () => {
			const req = createMockRequest(
				'Mozilla/5.0 (SMART-TV; Linux; Tizen 5.5) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/3.0 Chrome/69.0.3497.106 TV Safari/537.36',
			);
			expect(deriveDeviceClass(req)).toBe('desktop');
		});

		it('should detect Apple TV as desktop when no mobile pattern is present', () => {
			const req = createMockRequest(
				'AppleTV6,2/11.1 (Apple TV; PID:5) AppleWebKit/605.1.15 (KHTML, like Gecko) TV',
			);
			expect(deriveDeviceClass(req)).toBe('desktop');
		});

		it('should detect Android TV as desktop when no mobile pattern is present', () => {
			const req = createMockRequest(
				'Mozilla/5.0 (Linux; Android 9; SHIELD Android TV) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Safari/537.36 TV',
			);
			expect(deriveDeviceClass(req)).toBe('desktop');
		});
	});

	// Test desktop devices
	describe('Desktop devices', () => {
		it('should detect Chrome on Windows as desktop', () => {
			const req = createMockRequest(
				'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
			);
			expect(deriveDeviceClass(req)).toBe('desktop');
		});

		it('should detect Firefox on macOS as desktop', () => {
			const req = createMockRequest(
				'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0',
			);
			expect(deriveDeviceClass(req)).toBe('desktop');
		});

		it('should detect Safari on macOS as desktop', () => {
			const req = createMockRequest(
				'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
			);
			expect(deriveDeviceClass(req)).toBe('desktop');
		});

		it('should detect Edge on Windows as desktop', () => {
			const req = createMockRequest(
				'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59',
			);
			expect(deriveDeviceClass(req)).toBe('desktop');
		});
	});

	// Test edge cases
	describe('Edge cases', () => {
		it('should default to desktop for empty user agent', () => {
			const req = createMockRequest('');
			expect(deriveDeviceClass(req)).toBe('desktop');
		});

		it('should handle unusual mobile user agents', () => {
			const req = createMockRequest(
				'Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; SM-T217S Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 MobileSafari/534.30',
			);
			expect(deriveDeviceClass(req)).toBe('mobile');
		});

		it('should handle unusual tablet user agents', () => {
			const req = createMockRequest(
				'Mozilla/5.0 (Linux; Android 7.0; Pixel C Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/52.0.2743.98 Safari/537.36 Tablet',
			);
			expect(deriveDeviceClass(req)).toBe('tablet');
		});

		it('should handle bot user agents as desktop', () => {
			const req = createMockRequest(
				'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
			);
			expect(deriveDeviceClass(req)).toBe('desktop');
		});
	});
});
