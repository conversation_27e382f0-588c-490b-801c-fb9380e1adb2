/**
 * Determines the maxAge for the partner cookie based on the partner value.
 * Samsung partners get 2 hours (7200s), others get 30 minutes (1800s).
 *
 * @param partnerValue - The partner value to evaluate
 * @returns The maxAge in seconds, or null if no partner value is provided
 */
export function getPartnerCookieMaxAge(
	partnerValue: string | null,
): number | null {
	if (!partnerValue) return null;
	return partnerValue.toLowerCase().includes('samsung') ? 7200 : 1800;
}
