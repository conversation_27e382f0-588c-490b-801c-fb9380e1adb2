import { hasLocale } from 'next-intl';
import { getRequestConfig } from 'next-intl/server';
import { defaultLocale } from './config';
import { LOCALES } from '@repo/locales';

export default getRequestConfig(async ({ locale }) => {
	const validatedLocale = hasLocale(LOCALES, locale) ? locale : defaultLocale;

	return {
		locale: validatedLocale,
		messages: (await import(`./messages/${validatedLocale}.json`)).default,
	};
});
