name: CI

on:
  repository_dispatch:
    types:
      - vercel.deployment.success

env:
  NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
  CI: true
  TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
  TURBO_TEAM: ${{ vars.TURBO_TEAM }}
  TURBO_CACHE: remote:rw
  HUSKY: 0

jobs:
  extract-deployment-and-jira-info:
    name: Extract Deployment and JIRA Information
    runs-on: ubuntu-latest
    outputs:
      project: ${{ steps.extract-deployment-info.outputs.project }}
      pr_number: ${{ steps.extract-deployment-info.outputs.pr_number }}
      sha: ${{ steps.extract-deployment-info.outputs.sha }}
      url: ${{ steps.extract-deployment-info.outputs.url }}
      is_main_branch: ${{ steps.extract-deployment-info.outputs.is_main }}
      pr_title: ${{ steps.get-pr-details.outputs.pr_title }}
      branch_name: ${{ steps.get-pr-details.outputs.branch_name }}
      jira_issues: ${{ steps.extract-jira-issues.outputs.jira_issues }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v5
        with:
          fetch-depth: 0

      - name: Extract deployment information
        id: extract-deployment-info
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const script = require('./.github/workflows/scripts/extract-deployment-info.js')
            await script({github, context, core})

      - name: Get PR details
        id: get-pr-details
        if: steps.extract-deployment-info.outputs.is_main != 'true' && steps.extract-deployment-info.outputs.pr_number != ''
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const prNumber = ${{ steps.extract-deployment-info.outputs.pr_number }};

            try {
              const { data: pr } = await github.rest.pulls.get({
                owner: context.repo.owner,
                repo: context.repo.repo,
                pull_number: prNumber
              });

              core.setOutput('pr_title', pr.title);
              core.setOutput('branch_name', pr.head.ref);

              console.log(`PR Title: ${pr.title}`);
              console.log(`Branch Name: ${pr.head.ref}`);
            } catch (error) {
              console.log(`Error fetching PR details: ${error.message}`);
              core.setOutput('pr_title', '');
              core.setOutput('branch_name', '');
            }

      - uses: pnpm/action-setup@v4
        if: steps.extract-deployment-info.outputs.is_main != 'true' && steps.extract-deployment-info.outputs.pr_number != ''

      - name: Setup Node.js environment
        if: steps.extract-deployment-info.outputs.is_main != 'true' && steps.extract-deployment-info.outputs.pr_number != ''
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "pnpm"

      - name: Setup NPM configuration
        if: steps.extract-deployment-info.outputs.is_main != 'true' && steps.extract-deployment-info.outputs.pr_number != ''
        run: |
          # Make script executable
          chmod +x ./scripts/npmrc-manager.sh

          # Setup .npmrc with NPM_TOKEN
          ./scripts/npmrc-manager.sh setup

          # Verify .npmrc setup
          ./scripts/npmrc-manager.sh test

          # Verify NPM token access
          ./scripts/npmrc-manager.sh verify
        continue-on-error: true

      - name: Install dependencies
        if: steps.extract-deployment-info.outputs.is_main != 'true' && steps.extract-deployment-info.outputs.pr_number != ''
        run: pnpm install

      - name: Extract JIRA issues using AI
        id: extract-jira-issues
        if: steps.extract-deployment-info.outputs.is_main != 'true' && steps.extract-deployment-info.outputs.pr_number != ''
        uses: actions/github-script@v7
        env:
          PR_TITLE: ${{ steps.get-pr-details.outputs.pr_title }}
          BRANCH_NAME: ${{ steps.get-pr-details.outputs.branch_name }}
          AI_API_KEY: ${{ secrets.AI_API_KEY }}
          AI_BASE_URL: ${{ secrets.AI_BASE_URL }}
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const script = require('./.github/actions/update-jira-deployment-url/extract-jira-issues.js')
            return await script({github, context, core, require})

  update-jira-comments:
    name: Update JIRA with Deployment URL
    needs: extract-deployment-and-jira-info
    if: needs.extract-deployment-and-jira-info.outputs.is_main_branch != 'true' && needs.extract-deployment-and-jira-info.outputs.jira_issues != '[]' && needs.extract-deployment-and-jira-info.outputs.jira_issues != ''
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v5
        with:
          fetch-depth: 0

      - uses: pnpm/action-setup@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "pnpm"

      - name: Setup NPM configuration
        run: |
          # Make script executable
          chmod +x ./scripts/npmrc-manager.sh

          # Setup .npmrc with NPM_TOKEN
          ./scripts/npmrc-manager.sh setup

          # Verify .npmrc setup
          ./scripts/npmrc-manager.sh test

          # Verify NPM token access
          ./scripts/npmrc-manager.sh verify
        continue-on-error: true

      - name: Install dependencies
        run: pnpm install

      - name: Update JIRA issues with deployment URL
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const script = require('./.github/actions/update-jira-deployment-url/post-jira-comment.js');
            await script({
              github,
              context,
              core,
              require,
              jiraIssues: JSON.parse('${{ needs.extract-deployment-and-jira-info.outputs.jira_issues }}'),
              deploymentUrl: '${{ needs.extract-deployment-and-jira-info.outputs.url }}',
              prNumber: '${{ needs.extract-deployment-and-jira-info.outputs.pr_number }}',
              prTitle: '${{ needs.extract-deployment-and-jira-info.outputs.pr_title }}',
              branchName: '${{ needs.extract-deployment-and-jira-info.outputs.branch_name }}',
              commitSha: '${{ needs.extract-deployment-and-jira-info.outputs.sha }}',
              jiraUrl: '${{ secrets.JIRA_URL }}',
              jiraApiToken: '${{ secrets.JIRA_API_TOKEN }}',
              jiraUsername: '${{ secrets.JIRA_USERNAME }}'
            });
