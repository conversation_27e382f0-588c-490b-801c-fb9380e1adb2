module.exports = async ({ github, context, core }) => {
	console.log('Event name:', context.eventName);
	console.log('Event action:', context.payload.action);
	console.log(
		'Client payload:',
		JSON.stringify(context.payload.client_payload, null, 2),
	);
	let prNumber = null,
		sha = null,
		deploymentUrl = null,
		isMainBranch = false,
		gitRef = null;

	const repoNameKebabCase = context.repo.repo.toLowerCase().replace(/_/g, '-');
	let projectSuffix = 'web'; // Default suffix

	if (context.payload.client_payload?.project?.name) {
		const clientProjectName = context.payload.client_payload.project.name;
		const expectedPrefix = `${repoNameKebabCase}-`;
		if (clientProjectName.startsWith(expectedPrefix)) {
			const suffix = clientProjectName.substring(expectedPrefix.length);
			if (suffix) {
				// Ensure suffix is not empty
				projectSuffix = suffix;
			}
		} else {
			// If it doesn't start with the full repo prefix, assume clientProjectName is the suffix itself
			projectSuffix = clientProjectName;
		}
	}
	// Ensure projectSuffix is not empty, if it somehow became empty, default to 'web'
	if (!projectSuffix) {
		projectSuffix = 'web';
	}

	if (context.payload.client_payload) {
		deploymentUrl = context.payload.client_payload.url;
		// Continue with git info extraction:
		if (context.payload.client_payload.git) {
			gitRef = context.payload.client_payload.git.ref;
			sha = context.payload.client_payload.git.sha; // Primary source for SHA
			if (gitRef === 'main' || gitRef === 'refs/heads/main') {
				isMainBranch = true;
			}
		}
		if (context.payload.client_payload.environment === 'production') {
			isMainBranch = true; // Production environment implies main branch
		}
	}

	// If not on the main branch and we have a SHA, try to find the PR via commit SHA
	if (!isMainBranch && sha) {
		try {
			console.log(`Attempting to find PR for SHA: ${sha}`);
			const { data: prsAssociatedWithCommit } =
				await github.rest.repos.listPullRequestsAssociatedWithCommit({
					owner: context.repo.owner,
					repo: context.repo.repo,
					commit_sha: sha,
				});

			if (prsAssociatedWithCommit.length > 0) {
				// Sort by updated_at descending to get the most recent PR
				// This handles cases where a commit might be part of multiple PRs (e.g. cherry-picks, rebases)
				prsAssociatedWithCommit.sort(
					(a, b) =>
						new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime(),
				);
				prNumber = prsAssociatedWithCommit[0].number;
				console.log(`Found PR #${prNumber} associated with SHA ${sha}`);
			} else {
				console.log(
					`No PRs found associated with SHA ${sha}. This might be a direct push to a feature branch not yet associated with a PR.`,
				);
			}
		} catch (error) {
			console.log(`Error finding PR for SHA ${sha}: ${error.message}`);
			// Log the error but allow the script to continue, as a PR might not exist yet for a given commit.
		}
	} else if (isMainBranch) {
		console.log('On main branch, not searching for PR number.');
	} else if (!sha) {
		console.log('No SHA found in payload, cannot search for PR by commit.');
	}

	core.setOutput('project', projectSuffix);
	if (prNumber) core.setOutput('pr_number', prNumber);
	// Ensure SHA is set as output if available, even if PR isn't found
	if (sha) core.setOutput('sha', sha);
	else core.setOutput('sha', 'N/A');
	if (deploymentUrl) core.setOutput('url', deploymentUrl);
	core.setOutput('is_main', isMainBranch.toString());

	console.log(
		`Summary: Project=${projectSuffix}, PR=${prNumber || 'N/A'}, SHA=${sha || 'N/A'}, URL=${deploymentUrl || 'N/A'}, IsMain=${isMainBranch}`,
	);
};
