module.exports = async ({ core, fs, path }) => {
	const apolloInputPath =
		process.env.APOLLO_JSON_INPUT_PATH || 'apollo_aggregated_test_details.json';
	const mochaOutputPath =
		process.env.MOCHA_JSON_OUTPUT_PATH || 'apollo-mocha-results.json';

	core.info(`Starting Apollo to Mocha JSON conversion.`);
	core.info(`Reading Apollo results from: ${apolloInputPath}`);

	if (!fs.existsSync(apolloInputPath)) {
		core.setFailed(`Apollo input file not found: ${apolloInputPath}`);
		return;
	}

	const apolloRawData = fs.readFileSync(apolloInputPath, 'utf-8');
	let apolloData;
	try {
		apolloData = JSON.parse(apolloRawData); // This should be an array of items
		if (!Array.isArray(apolloData)) {
			core.setFailed(
				`Apollo input file (${apolloInputPath}) does not contain a JSON array. Found: ${typeof apolloData}`,
			);
			return;
		}
	} catch (e) {
		core.setFailed(
			`Failed to parse Apollo JSON from ${apolloInputPath}: ${e.message}`,
		);
		return;
	}

	core.info(`Found ${apolloData.length} test items in Apollo results.`);

	const mochaOutput = {
		stats: {
			duration: 0, // Only duration as per MochaJsonStats
		},
		passes: [],
		pending: [],
		failures: [],
	};

	let minStartTime = Infinity;
	let maxEndTime = 0;
	// const suiteNames = new Set(); // Not strictly needed for the new stats, but can be kept for logging if desired

	apolloData.forEach((item) => {
		const test = {
			title: item.name || 'Unnamed Test',
			fullTitle: item.nam,
			file: item.codeRef,
			duration: undefined, // Optional, will be set if available
			// currentRetry: item.retries ? item.retries.length : 0, // Not in MochaJsonTest, removing
			err: { message: undefined, stack: undefined }, // Must exist, can be empty
		};

		let fullTitleParts = [];
		if (item.pathNames && item.pathNames.itemPaths) {
			item.pathNames.itemPaths.forEach((p) => {
				fullTitleParts.push(p.name);
				// suiteNames.add(p.name);
			});
		}
		fullTitleParts.push(test.title);
		test.fullTitle = fullTitleParts.join(' ');

		if (item.startTime && item.endTime) {
			const startTime = new Date(item.startTime).getTime();
			const endTime = new Date(item.endTime).getTime();
			if (!isNaN(startTime) && !isNaN(endTime)) {
				test.duration = endTime - startTime; // Set duration for the test
				if (startTime < minStartTime) {
					minStartTime = startTime;
				}
				if (endTime > maxEndTime) {
					maxEndTime = endTime;
				}
			} else {
				core.warning(
					`Invalid startTime or endTime for item ID ${item.id}: ${item.startTime}, ${item.endTime}`,
				);
			}
		}

		// Determine pass/fail/pending and add to the correct array
		if (item.status === 'PASSED') {
			mochaOutput.passes.push(test);
		} else if (item.status === 'FAILED') {
			test.err = {
				message: item.description || 'Test failed without a specific message.',
				stack: item.description || '', // No direct stack trace from this RP item structure
			};
			if (item.issue && item.issue.issueType) {
				test.err.message += ` (Issue: ${item.issue.issueType}${item.issue.comment ? ` - ${item.issue.comment}` : ''})`;
				if (test.err.stack)
					test.err.stack += `\nIssue: ${item.issue.issueType}${item.issue.comment ? ` - ${item.issue.comment}` : ''}`;
				else
					test.err.stack = `Issue: ${item.issue.issueType}${item.issue.comment ? ` - ${item.issue.comment}` : ''}`;
			}
			mochaOutput.failures.push(test);
		} else if (item.status === 'SKIPPED' || item.status === 'CANCELLED') {
			mochaOutput.pending.push(test);
		} else {
			core.info(
				`Test item ID ${item.id} has status '${item.status}', categorizing as pending.`,
			);
			// Default to pending for other statuses like INTERRUPTED, INFO, WARN
			mochaOutput.pending.push(test);
		}
	});

	// Calculate overall duration for stats
	if (minStartTime !== Infinity && maxEndTime !== 0) {
		mochaOutput.stats.duration = maxEndTime - minStartTime;
	} else if (apolloData.length > 0) {
		// Fallback if no valid start/end times found but tests exist
		mochaOutput.stats.duration = 0;
		core.warning(
			'Could not determine accurate overall start/end times from test items. Overall duration set to 0.',
		);
	}

	core.info(`Writing Mocha JSON results to: ${mochaOutputPath}`);
	core.info(
		`Stats: Passes - ${mochaOutput.passes.length}, Failures - ${mochaOutput.failures.length}, Pending - ${mochaOutput.pending.length}`,
	);
	try {
		fs.writeFileSync(mochaOutputPath, JSON.stringify(mochaOutput, null, 2));
		core.info('Conversion complete.');
	} catch (e) {
		core.setFailed(
			`Failed to write Mocha JSON output to ${mochaOutputPath}: ${e.message}`,
		);
	}
};
