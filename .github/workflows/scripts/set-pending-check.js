// .github/workflows/scripts/set-pending-check.js
module.exports = async ({ github, context, core }) => {
	const projectName = process.env.PROJECT_NAME;
	const sha = process.env.COMMIT_SHA;
	const deploymentUrl = process.env.DEPLOYMENT_URL;
	const checkName = `E2E Tests (${projectName})`;

	if (!sha) {
		core.setFailed('Commit SHA is missing, cannot create check run.');
		return;
	}
	if (!projectName) {
		core.setFailed('Project name is missing, cannot create check run.');
		return;
	}

	try {
		await github.rest.checks.create({
			owner: context.repo.owner,
			repo: context.repo.repo,
			name: checkName,
			head_sha: sha,
			status: 'queued', // Set to 'queued' as deployment is pending
			started_at: new Date().toISOString(),
			details_url: deploymentUrl || undefined, // Link to the Vercel deployment preview
			output: {
				title: `${checkName} - Deployment Pending`,
				summary: `Vercel deployment for the **${projectName}** project is pending. E2E tests will run once deployment is successful.\nPreview URL (when ready): ${deploymentUrl || 'Not yet available'}`,
			},
		});
		console.log(
			`Successfully created 'queued' check run: ${checkName} for SHA ${sha}`,
		);
	} catch (error) {
		console.error(
			`Error creating 'queued' check run ${checkName} for SHA ${sha}:`,
			error,
		);
		// Check if the error is because the check already exists and try to update if it's in a non-completed state
		if (error.message && error.message.includes('already exists')) {
			console.log(
				"Check run already exists. Attempting to update if it's not completed.",
			);
			try {
				const { data: checks } = await github.rest.checks.listForRef({
					owner: context.repo.owner,
					repo: context.repo.repo,
					ref: sha,
					check_name: checkName,
				});
				if (checks.total_count > 0) {
					const existingCheck = checks.check_runs[0];
					if (existingCheck.status !== 'completed') {
						await github.rest.checks.update({
							owner: context.repo.owner,
							repo: context.repo.repo,
							check_run_id: existingCheck.id,
							status: 'queued', // Re-assert queued status or update details
							details_url: deploymentUrl || existingCheck.details_url,
							output: {
								title: `${checkName} - Deployment Pending (Re-triggered)`,
								summary: `Vercel deployment for the **${projectName}** project is pending (possibly re-triggered). E2E tests will run once deployment is successful.\nPreview URL (when ready): ${deploymentUrl || 'Not yet available'}`,
							},
						});
						console.log(
							`Successfully updated existing check run ${existingCheck.id} to 'queued'.`,
						);
					} else {
						console.log(
							`Existing check run ${existingCheck.id} is already completed. No update performed.`,
						);
					}
				}
			} catch (updateError) {
				console.error(`Error updating existing check run:`, updateError);
				core.setFailed(
					`Failed to create or update pending check: ${updateError.message}`,
				);
			}
		} else {
			core.setFailed(`Failed to create pending check: ${error.message}`);
		}
	}
};
