module.exports = async ({ github, context, core }) => {
	const owner = context.repo.owner;
	const repo = context.repo.repo;

	// Retrieve environment variables
	const projectName = process.env.PROJECT_NAME;
	const commitSha = process.env.COMMIT_SHA;
	let playwrightConclusion = process.env.PLAYWRIGHT_CONCLUSION || 'skipped';
	let apolloProcessedConclusion =
		process.env.APOLLO_PROCESSED_CONCLUSION || 'skipped';
	const apolloRawConclusion = process.env.APOLLO_RAW_CONCLUSION || 'skipped';
	const apolloReportLinkMarkdown =
		process.env.APOLLO_REPORT_LINK_MARKDOWN || '';
	const workflowRunUrl = process.env.WORKFLOW_RUN_URL;

	core.info(`Project Name: ${projectName}`);
	core.info(`Commit SHA: ${commitSha}`);
	core.info(`Playwright Conclusion: ${playwrightConclusion}`);
	core.info(`Apollo Processed Conclusion: ${apolloProcessedConclusion}`);
	core.info(`Apollo Raw Conclusion: ${apolloRawConclusion}`);
	core.info(`Apollo Report Link Markdown: ${apolloReportLinkMarkdown}`);
	core.info(`Workflow Run URL: ${workflowRunUrl}`);

	if (!projectName || !commitSha || !workflowRunUrl) {
		core.setFailed(
			'Missing required environment variables: PROJECT_NAME, COMMIT_SHA, or WORKFLOW_RUN_URL.',
		);
		return;
	}

	// If Apollo processed conclusion is skipped but raw conclusion is available and more definitive (e.g. failure)
	if (
		apolloProcessedConclusion === 'skipped' &&
		(apolloRawConclusion === 'failure' ||
			apolloRawConclusion === 'success' ||
			apolloRawConclusion === 'cancelled')
	) {
		core.info(
			`Using Apollo Raw Conclusion '${apolloRawConclusion}' as Processed Conclusion was '${apolloProcessedConclusion}'.`,
		);
		apolloProcessedConclusion = apolloRawConclusion;
	}
	// Ensure playwrightConclusion is not empty if it was passed
	if (process.env.PLAYWRIGHT_CONCLUSION === '')
		playwrightConclusion = 'skipped';

	let overallConclusion = 'success'; // Optimistic default

	if (
		playwrightConclusion === 'failure' ||
		apolloProcessedConclusion === 'failure'
	) {
		overallConclusion = 'failure';
	} else if (
		playwrightConclusion === 'cancelled' ||
		apolloProcessedConclusion === 'cancelled'
	) {
		overallConclusion = 'cancelled';
	} else if (
		playwrightConclusion === 'skipped' &&
		apolloProcessedConclusion === 'skipped'
	) {
		// Only if both are explicitly skipped and neither failed nor was cancelled
		if (
			process.env.PLAYWRIGHT_CONCLUSION === 'skipped' &&
			(process.env.APOLLO_PROCESSED_CONCLUSION === 'skipped' ||
				(process.env.APOLLO_PROCESSED_CONCLUSION === '' &&
					process.env.APOLLO_RAW_CONCLUSION === 'skipped'))
		) {
			overallConclusion = 'skipped';
		}
	}
	// If one is success and other is skipped/neutral, it's success (covered by default)

	const checkName = `E2E Tests (${projectName})`;
	const title = `E2E Tests (${projectName}): ${overallConclusion.toUpperCase()}`;
	let summary = `**Overall E2E Status: ${overallConclusion.toUpperCase()}**\n\n`;
	summary += `**Playwright Tests:** ${playwrightConclusion || 'N/A'}\n`;
	summary += `**Apollo Tests:** ${apolloProcessedConclusion || 'N/A'}\n`;
	if (apolloReportLinkMarkdown && apolloReportLinkMarkdown.trim() !== '') {
		summary += `${apolloReportLinkMarkdown.trim()}\n`;
	}
	summary += `\n[View Full E2E Workflow Summary](${workflowRunUrl})`;

	core.info(`Attempting to update check: ${checkName}`);
	core.info(`Title: ${title}`);
	core.info(`Summary: ${summary}`);
	core.info(`Overall Conclusion: ${overallConclusion}`);

	try {
		const { data: checks } = await github.rest.checks.listForRef({
			owner,
			repo,
			ref: commitSha,
			check_name: checkName,
		});

		if (checks.check_runs.length === 0) {
			core.warning(
				`No existing check run found with name '${checkName}' for SHA ${commitSha}. Creating a new one.`,
			);
			// This case should ideally not happen if set-pending-e2e-checks.yml ran correctly
			await github.rest.checks.create({
				owner,
				repo,
				name: checkName,
				head_sha: commitSha,
				status: 'completed',
				conclusion: overallConclusion,
				completed_at: new Date().toISOString(),
				output: { title, summary },
			});
			core.info(`Successfully created new check run '${checkName}'.`);
			return;
		}

		const check_run_id = checks.check_runs[0].id;
		await github.rest.checks.update({
			owner,
			repo,
			check_run_id,
			status: 'completed',
			conclusion: overallConclusion,
			completed_at: new Date().toISOString(),
			output: { title, summary },
		});
		core.info(
			`Successfully updated check run '${checkName}' (ID: ${check_run_id}) with conclusion '${overallConclusion}'.`,
		);
	} catch (error) {
		core.error(
			`Error finding or updating GitHub check '${checkName}': ${error.message}`,
		);
		core.setFailed(`Failed to find or update check run: ${error.message}`);
	}
};
