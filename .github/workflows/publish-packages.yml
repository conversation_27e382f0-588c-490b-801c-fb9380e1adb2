name: Publish Packages

on:
  workflow_run:
    workflows: ["CI"]
    branches: [main]
    types:
      - completed

env:
  NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
  CI: true
  TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
  TURBO_TEAM: ${{ vars.TURBO_TEAM }}
  TURBO_CACHE: remote:rw

jobs:
  check-versions-and-publish:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write # Needed for AWS authentication

    steps:
      - name: Checkout code
        uses: actions/checkout@v5

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - name: Setup pnpm
        uses: pnpm/action-setup@v4

      - name: Setup NPM configuration
        run: |
          # Make script executable
          chmod +x ./scripts/npmrc-manager.sh

          # Setup .npmrc with NPM_TOKEN
          ./scripts/npmrc-manager.sh setup

          # Verify .npmrc setup
          ./scripts/npmrc-manager.sh test

          # Verify NPM token access
          ./scripts/npmrc-manager.sh verify
        continue-on-error: true  # Continue even if token doesn't have access to private packages

      - name: Install dependencies
        run: pnpm install

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_CODEARTIFACT_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_CODEARTIFACT_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Get CodeArtifact auth token
        id: codeartifact-token
        run: |
          TOKEN=$(aws codeartifact get-authorization-token \
            --domain wxu-web-npm \
            --domain-owner 219585867719 \
            --query authorizationToken \
            --output text)
          echo "token=$TOKEN" >> $GITHUB_OUTPUT

      - name: Configure npm registry
        run: |
          echo "//wxu-web-npm-219585867719.d.codeartifact.us-east-1.amazonaws.com/npm/npm-store/:_authToken=${{ steps.codeartifact-token.outputs.token }}" > .npmrc
          echo "//wxu-web-npm-219585867719.d.codeartifact.us-east-1.amazonaws.com/npm/npm-store/:always-auth=true" >> .npmrc

      - name: Publish packages
        run: |
          # Publish the packages built by the CI workflow
          pnpm turbo run publish
