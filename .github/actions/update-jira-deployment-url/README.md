# Update JIRA Deployment URL Action

This GitHub Action automatically posts Vercel deployment URLs to JIRA issues based on pull request titles and branch names. It uses AI to intelligently extract JIRA issue keys and includes deduplication logic to avoid posting duplicate comments.

## Overview

The workflow is triggered by Vercel deployment success events and:

1. **Extracts JIRA Issues**: Uses AI (Claude-3.5-Son<PERSON> via LiteLLM) to identify JIRA issue keys from PR titles and branch names
2. **Posts Deployment URLs**: Adds formatted comments to identified JIRA issues with deployment information
3. **Prevents Duplicates**: Checks existing comments to avoid posting duplicate deployment URLs
4. **Fails Silently**: Continues processing even if individual issues fail (as requested)

## Workflow Integration

The workflow `update-jira-deployment-url.yml`:

- Triggers on `vercel.deployment.success` repository dispatch events
- Only runs for pull requests (not main branch deployments)
- Uses `actions/github-script@v7` with manual package installation
- Extracts deployment information using existing scripts
- Processes JIRA issues in parallel

## JIRA Issue Detection

The AI analyzes both PR titles and branch names to find JIRA patterns:

### Supported Patterns
- Standard JIRA format: `[A-Z]+-\d+` (e.g., `CW-2664`, `ABC-123`, `PROJ-456`)
- Case-sensitive (JIRA keys are always uppercase)
- Multiple issues per PR/branch supported

### Examples
- **PR Title**: `CW-2664: Flags Part 2` → Extracts `CW-2664`
- **Branch Name**: `refactor/glaskawiec/CW-2590/move-popover-to-ui` → Extracts `CW-2590`
- **Multiple Issues**: `Fix CW-123 and resolve ABC-456 issues` → Extracts both `CW-123` and `ABC-456`

## Comment Format

The action posts well-formatted comments to JIRA issues:

```markdown
🚀 **Deployment Ready**

**PR**: #123 - Feature implementation
**Branch**: `feature/my-branch`
**Deployment**: https://my-deployment.vercel.app
**Commit**: `abc1234`
**Deployed**: 2025-01-02T10:30:00.000Z

---
*Automated deployment notification from GitHub Actions*
<!-- DEPLOYMENT-COMMENT-123-abc1234 -->
```

## Deduplication Logic

The action prevents duplicate comments by:

1. **Checking Existing Comments**: Searches for deployment comments from the same PR
2. **Comparing URLs**: If the same deployment URL exists, skips posting
3. **Allowing Updates**: If a different deployment URL is found, posts a new comment
4. **Using Identifiers**: Hidden HTML comments track PR number and commit SHA

## Configuration

### Required Secrets

The workflow requires these GitHub secrets:

- `AI_API_KEY`: LiteLLM API key for AI-powered JIRA extraction
- `AI_BASE_URL`: LiteLLM base URL (https://api.mercury.weather.com/litellm/v1)
- `JIRA_URL`: JIRA instance URL (https://weather.atlassian.net)
- `JIRA_API_TOKEN`: JIRA API token for authentication

### Dependencies

The workflow installs these npm packages dynamically:

- `@ai-sdk/openai`: OpenAI SDK for LiteLLM integration
- `ai`: AI SDK for structured output generation
- `zod`: Schema validation for AI responses
- `jira-client`: JIRA API client

## Error Handling

The action is designed to fail silently:

- **AI API Failures**: Logs error, continues with empty JIRA list
- **JIRA API Failures**: Logs error, continues with other issues
- **Authentication Issues**: Logs error, skips affected issues
- **Not Found Issues**: Logs warning, continues processing

## Files Structure

```
.github/actions/update-jira-deployment-url/
├── extract-jira-issues.js        # AI-powered JIRA extraction (github-script module)
├── post-jira-comment.js          # JIRA comment posting with deduplication (github-script module)
├── README.md                     # This documentation
└── SETUP.md                      # Setup instructions
```

## Usage Example

The workflow is automatically triggered by Vercel deployments. No manual intervention required.

### Manual Testing

To test the AI extraction locally:

```javascript
// Install dependencies first
// npm install @ai-sdk/openai ai zod

const script = require('./.github/actions/update-jira-deployment-url/extract-jira-issues.js');

// Mock the github-script environment
const mockCore = {
  setOutput: (name, value) => console.log(`Output ${name}: ${value}`)
};

// Set environment variables
process.env.PR_TITLE = 'CW-2664: Flags Part 2';
process.env.BRANCH_NAME = 'feature/CW-2590/implementation';
process.env.AI_API_KEY = 'your-api-key';
process.env.AI_BASE_URL = 'https://api.mercury.weather.com/litellm/v1';

// Run extraction
script({ github: {}, context: {}, core: mockCore, require });
```

## Monitoring

Check GitHub Actions logs for:

- JIRA issues detected by AI
- Confidence scores for extractions
- Success/failure status for each JIRA comment
- Deduplication decisions
- Error messages (if any)

## Troubleshooting

### Common Issues

1. **No JIRA Issues Found**: Check PR title and branch name format
2. **AI API Errors**: Verify API key and base URL configuration
3. **JIRA Permission Errors**: Ensure API token has comment permissions
4. **Duplicate Comments**: Check deduplication logic in logs

### Debug Information

The action logs detailed information:

- PR title and branch name being analyzed
- AI analysis results with confidence scores
- JIRA issues being processed
- Existing comment checks
- Success/failure status for each operation
