module.exports = async ({
	github,
	context,
	core,
	require,
	jiraIssues,
	deploymentUrl,
	prNumber,
	prTitle,
	branchName,
	commitSha,
	jiraUrl,
	jiraApiToken,
	jiraUsername,
}) => {
	try {
		console.log(`Processing ${jiraIssues.length} JIRA issues`);

		if (!jiraIssues || jiraIssues.length === 0) {
			console.log('No JIRA issues to process');
			return;
		}

		// Initialize JIRA client
		const JiraClient = require('jira-client');
		const jira = new JiraClient({
			protocol: 'https',
			host: jiraUrl.replace('https://', ''),
			username: jiraUsername, // Use a service account email
			password: jiraApiToken,
			apiVersion: '2',
			strictSSL: true,
		});

		const shortSha = commitSha.substring(0, 7);
		const timestamp = new Date().toISOString();
		const prUrl = `https://github.com/${context.repo.owner}/${context.repo.repo}/pull/${prNumber}`;

		// Create the comment identifier for deduplication
		const commentIdentifier = `<!-- DEPLOYMENT-COMMENT-${prNumber}-${shortSha} -->`;

		// Create the comment body
		const commentBody = `🚀 **Deployment Ready**

*PR*: ${prUrl}
*Branch*: ${branchName}
*Deployment*: ${deploymentUrl}
*Commit*: ${shortSha}
*Deployed*: ${timestamp}

---
*Automated deployment notification from GitHub Actions*
${commentIdentifier}`;

		// Process each JIRA issue
		for (const issue of jiraIssues) {
			try {
				console.log(`Processing JIRA issue: ${issue.key}`);

				// Get existing comments for this issue
				const comments = await jira.getComments(issue.key);

				// Check if we already have a deployment comment for this PR/commit
				const existingComment = comments.comments.find(
					(comment) =>
						comment.body &&
						comment.body.includes(`DEPLOYMENT-COMMENT-${prNumber}-`),
				);

				if (existingComment) {
					// Check if it's the same deployment URL
					if (existingComment.body.includes(deploymentUrl)) {
						console.log(
							`Skipping ${issue.key} - deployment URL already posted`,
						);
						continue;
					} else {
						console.log(
							`Found different deployment URL for ${issue.key} - adding new comment`,
						);
					}
				}

				// Add the comment to JIRA
				await jira.addComment(issue.key, commentBody);
				console.log(`✅ Successfully posted deployment URL to ${issue.key}`);

				// Add a small delay to avoid rate limiting
				await new Promise((resolve) => setTimeout(resolve, 500));
			} catch (issueError) {
				console.error(
					`❌ Error processing JIRA issue ${issue.key}:`,
					issueError.message,
				);

				// Check if it's a permission or not found error
				if (issueError.statusCode === 404) {
					console.log(`Issue ${issue.key} not found or no access`);
				} else if (issueError.statusCode === 403) {
					console.log(`No permission to comment on ${issue.key}`);
				} else {
					console.log(
						`Unexpected error for ${issue.key}: ${issueError.statusCode}`,
					);
				}

				// Continue with other issues (fail silently as requested)
				continue;
			}
		}

		console.log('✅ Completed processing all JIRA issues');
	} catch (error) {
		console.error('❌ Error in JIRA comment posting:', error);

		// Fail silently as requested
		console.log('Continuing despite error (failing silently)');
	}
};
