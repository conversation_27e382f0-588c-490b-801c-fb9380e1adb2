module.exports = async ({ github, context, core }) => {
	const fs = require('fs');
	const path = require('path');

	// Helper function to delay execution
	const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

	// Helper function to make authenticated GET requests to ReportPortal
	async function getRpData(url, rpToken) {
		core.debug(`Fetching from RP: ${url}`);
		const headers = { Authorization: `Bearer ${rpToken}` };
		try {
			const response = await fetch(url, { method: 'GET', headers: headers });
			if (!response.ok) {
				core.warning(
					`ReportPortal API request failed with status ${response.status} for URL: ${url}`,
				);
				// Log response body for debugging if available
				try {
					const errorBody = await response.text();
					core.debug(`Error response body: ${errorBody}`);
				} catch (e) {
					core.debug('Could not read error response body.');
				}
				return null;
			}
			try {
				return await response.json();
			} catch (e) {
				const body = await response.text();
				core.error(
					`Failed to parse JSON response from ReportPortal: ${e.message}. Body: ${body}`,
				);
				return null;
			}
		} catch (error) {
			core.error(
				`Fetch request to ReportPortal failed for URL ${url}: ${error.message}`,
			);
			return null;
		}
	}

	// New helper function to fetch all test items for a launch with pagination
	async function fetchAllTestItemsForLaunch(launchId, rpToken, rpApiUrl, core) {
		let allItems = [];
		let currentPage = 1; // ReportPortal uses 1-based indexing for page numbers
		let totalPages = 1;
		const pageSize = 25; // Default page size

		core.info(`Fetching all test items for launch ID: ${launchId}`);

		do {
			const url = `${rpApiUrl}/item?filter.eq.launchId=${launchId}&filter.eq.type=STEP&filter.eq.hasChildren=false&filter.eq.hasStats=true&page.size=${pageSize}&page.page=${currentPage}`;
			const response = await getRpData(url, rpToken);

			if (response && response.content) {
				allItems = allItems.concat(response.content);
				if (response.page) {
					totalPages = response.page.totalPages;
					core.debug(
						`Fetched page ${response.page.number || currentPage}/${totalPages} for launch ${launchId}. Items on page: ${response.content.length}. Total items so far: ${allItems.length}`,
					);
				} else {
					core.warning(
						`Pagination info missing in response for launch ${launchId}, page ${currentPage}. Assuming single page.`,
					);
					break;
				}
			} else {
				core.error(
					`Failed to fetch items for launch ${launchId}, page ${currentPage}.`,
				);
				break; // Exit loop on error or no response
			}
			currentPage++;
		} while (currentPage <= totalPages);

		core.info(
			`Finished fetching items for launch ${launchId}. Total items fetched: ${allItems.length}`,
		);
		return allItems;
	}

	const projectName = process.env.PROJECT_NAME;
	const prNumber = process.env.PR_NUMBER;
	const deploymentUrl = process.env.DEPLOYMENT_URL;
	const commitSha = process.env.COMMIT_SHA;
	const rpApiUrl = process.env.RP_API_URL;
	const rpUiUrl = process.env.RP_UI_URL;
	const rpToken = process.env.RP_TOKEN;
	const apolloSuitesConfigPath = process.env.APOLLO_SUITES_CONFIG_PATH;
	const githubRepository = process.env.GITHUB_REPOSITORY;

	core.info(`--- Inputs ---
    Project Name: ${projectName}
    PR Number: ${prNumber}
    Deployment URL: ${deploymentUrl}
    Commit SHA: ${commitSha}
    Apollo Suites Config Path: ${apolloSuitesConfigPath}
    RP API URL: ${rpApiUrl}
    RP UI URL: ${rpUiUrl}
  ----------------`);

	if (!fs.existsSync(apolloSuitesConfigPath)) {
		core.setFailed(
			`Error: Apollo suites configuration file not found at ${apolloSuitesConfigPath}`,
		);
		return;
	}

	const configFileContents = fs.readFileSync(apolloSuitesConfigPath, 'utf8');
	const allSuitesConfig = JSON.parse(configFileContents);
	const projectSuites = allSuitesConfig[projectName] || [];

	if (projectSuites.length === 0) {
		core.info(`No Apollo test suites configured for project: ${projectName}`);
		return;
	}

	core.info(
		`Found ${projectSuites.length} suites for project ${projectName}: ${JSON.stringify(projectSuites.map((s) => s.name))}`,
	);

	let allSuiteResults = [];
	let allProcessedLaunchItems = []; // Initialize accumulator for all test items from all launches
	let overallApolloConclusion = 'success'; // Assume success until a failure

	// Ensure directories for results exist
	const suiteResultsDir = 'apollo_suite_results';
	const projectSummaryDir = 'apollo_project_results';
	fs.mkdirSync(suiteResultsDir, { recursive: true });
	fs.mkdirSync(projectSummaryDir, { recursive: true });

	// Calculate timestamps in milliseconds
	const currentUnixTimestampMs = Date.now();
	const plus1HourUnixTimestampMs = currentUnixTimestampMs + 3600000; // 1 hour in milliseconds

	// Construct the URL with the new time filter
	const launchSearchUrl = `${rpApiUrl}/launch/latest?filter.has.compositeAttribute=pr:${prNumber}&filter.has.compositeAttribute=sha:${commitSha}&filter.btw.startTime=${currentUnixTimestampMs},${plus1HourUnixTimestampMs}`;

	core.info(`Using Report Portal API: ${launchSearchUrl}`);

	for (const suite of projectSuites) {
		const suiteName = suite.name;
		const testGroup = suite.test_group;
		let launchId = null;
		let suiteResult = {
			name: suiteName,
			test_group: testGroup,
			status: 'pending',
			passed: 0,
			failed: 0,
			skipped: 0,
			total: 0,
			report_url: '',
			error_message: '',
		};

		core.startGroup(
			`Processing Suite: ${suiteName} (Test Group: ${testGroup})`,
		);

		core.info(
			`Dispatching test run for suite: ${suiteName}, group: ${testGroup}`,
		);
		const dispatchPayload = {
			event_type: 'run-wx-next-tests',
			client_payload: {
				pr_url: `https://github.com/${githubRepository}/pull/${prNumber}`,
				pr_number: prNumber,
				test_url: deploymentUrl,
				test_sha: commitSha,
				test_group: testGroup,
			},
		};

		try {
			await github.request(
				'POST /repos/TheWeatherCompany/wxu-web-automation/dispatches',
				{
					event_type: dispatchPayload.event_type,
					client_payload: dispatchPayload.client_payload,
					headers: { 'X-GitHub-Api-Version': '2022-11-28' },
				},
			);
			core.info(`Dispatch sent for ${suiteName}.`);
		} catch (error) {
			core.error(
				`Failed to send dispatch for suite ${suiteName}: ${error.message}`,
			);
			suiteResult.status = 'failure';
			suiteResult.error_message = `DISPATCH_ERROR: ${error.message}`;
			allSuiteResults.push(suiteResult);
			overallApolloConclusion = 'failure';
			core.endGroup();
			continue;
		}

		core.info('Waiting for launch to start...');
		const launchStartTimeoutMinutes = 8;
		const launchStartRetryWaitSeconds = 15;
		const launchStartMaxAttempts =
			(launchStartTimeoutMinutes * 60) / launchStartRetryWaitSeconds;
		let launchFound = false;

		for (let attempt = 1; attempt <= launchStartMaxAttempts; attempt++) {
			const response = await getRpData(launchSearchUrl, rpToken);
			if (response && response.content && response.content.length > 0) {
				launchId = response.content[0].id;
				suiteResult.report_url = `${rpUiUrl}/${launchId}`;
				core.info(
					`Found launch ID: ${launchId} for test_group ${testGroup} on attempt ${attempt}. Report URL: ${suiteResult.report_url}`,
				);
				launchFound = true;
				break;
			}
			core.info(
				`Launch for ${testGroup} not available (attempt ${attempt}/${launchStartMaxAttempts}), retrying in ${launchStartRetryWaitSeconds}s...`,
			);
			await delay(launchStartRetryWaitSeconds * 1000);
		}

		if (!launchFound) {
			core.error(
				`Launch for suite ${suiteName} (test_group ${testGroup}) did not start within ${launchStartTimeoutMinutes} minutes.`,
			);
			suiteResult.status = 'failure';
			suiteResult.error_message = 'Launch did not start in ReportPortal.';
			allSuiteResults.push(suiteResult);
			overallApolloConclusion = 'failure';
			core.endGroup();
			continue;
		}

		core.info(`Waiting for launch ${launchId} to complete...`);
		const launchCompleteTimeoutMinutes = 60;
		const launchCompleteRetryWaitSeconds = 30;
		const launchCompleteMaxAttempts =
			(launchCompleteTimeoutMinutes * 60) / launchCompleteRetryWaitSeconds;
		let launchCompleted = false;

		for (let attempt = 1; attempt <= launchCompleteMaxAttempts; attempt++) {
			const launchStatusUrl = `${rpApiUrl}/launch?filter.eq.id=${launchId}`;
			const response = await getRpData(launchStatusUrl, rpToken);
			if (response && response.content && response.content.length > 0) {
				const launchData = response.content[0];
				suiteResult.status = launchData.status;
				if (launchData.status !== 'IN_PROGRESS') {
					core.info(
						`Launch ${launchId} completed with status: ${launchData.status}`,
					);
					const stats = launchData.statistics.executions;
					suiteResult.total = stats.total || 0;
					suiteResult.passed = stats.passed || 0;
					suiteResult.failed = stats.failed || 0;
					suiteResult.skipped = stats.skipped || 0;

					const suiteJsonPath = path.join(
						suiteResultsDir,
						`test_results_${projectName}_${testGroup}.json`,
					);
					fs.writeFileSync(suiteJsonPath, JSON.stringify(launchData, null, 2));
					core.info(
						`Saved detailed results for suite ${suiteName} to ${suiteJsonPath}`,
					);

					if (launchData.status === 'FAILED' || suiteResult.failed > 0) {
						suiteResult.status = 'failure';
						overallApolloConclusion = 'failure';
					} else if (
						launchData.status === 'PASSED' ||
						launchData.status === 'COMPLETED'
					) {
						suiteResult.status = 'success';
					} else {
						suiteResult.status = 'unknown';
						overallApolloConclusion = 'failure';
					}
					launchCompleted = true;
					break;
				}
				core.info(
					`Launch ${launchId} status: ${launchData.status} (attempt ${attempt}/${launchCompleteMaxAttempts}), retrying in ${launchCompleteRetryWaitSeconds}s...`,
				);
			} else {
				core.warning(
					`Could not get status for launch ${launchId} (attempt ${attempt}/${launchCompleteMaxAttempts}).`,
				);
			}
			await delay(launchCompleteRetryWaitSeconds * 1000);
		}

		if (!launchCompleted) {
			core.error(
				`Launch ${launchId} for suite ${suiteName} did not complete within ${launchCompleteTimeoutMinutes} minutes.`,
			);
			suiteResult.status = 'failure';
			suiteResult.error_message =
				'Launch did not complete in ReportPortal (timed out).';
			overallApolloConclusion = 'failure';
		}

		if (launchId && launchCompleted) {
			core.info(`Fetching all test items for completed launch ID: ${launchId}`);
			const launchItems = await fetchAllTestItemsForLaunch(
				launchId,
				rpToken,
				rpApiUrl,
				core,
			);
			if (launchItems && launchItems.length > 0) {
				allProcessedLaunchItems = allProcessedLaunchItems.concat(launchItems);
				core.info(
					`Added ${launchItems.length} items from launch ${launchId}. Total items so far: ${allProcessedLaunchItems.length}`,
				);
			} else {
				core.warning(
					`No test items found or fetched for launch ID: ${launchId}`,
				);
			}
		}

		allSuiteResults.push(suiteResult);
		core.endGroup();
	} // End of for-each-suite loop

	const aggregatedItemsPath = 'apollo_aggregated_test_details.json';
	try {
		fs.writeFileSync(
			aggregatedItemsPath,
			JSON.stringify(allProcessedLaunchItems, null, 2),
		);
		core.info(
			`Successfully saved all aggregated test items to ${aggregatedItemsPath}`,
		);
	} catch (error) {
		core.error(
			`Failed to save aggregated test items to ${aggregatedItemsPath}: ${error.message}`,
		);
		overallApolloConclusion = 'failure';
	}

	// The core.setOutput calls for conclusion, summary_text, and report_url_markdown are removed
	// as this information will be derived from the aggregated JSON in a subsequent step/job.
	core.info(
		`Overall Apollo Conclusion for Project ${projectName}: ${overallApolloConclusion}`,
	);

	// Still create the project summary JSON, but without the direct outputs that were removed.
	// The 'conclusion' in this JSON will reflect the script's own assessment.
	const projectJsonPath = path.join(
		projectSummaryDir,
		`pr-${prNumber}-apollo-results-${projectName}.json`,
	);
	const projectJsonData = {
		project_name: projectName,
		pr_number: prNumber,
		commit_sha: commitSha,
		deployment_url: deploymentUrl,
		test_type: 'apollo',
		// This conclusion is based on this script's processing.
		// The final job conclusion will be determined by the new mocha-json processing job.
		conclusion_from_script: overallApolloConclusion,
		timestamp: new Date().toISOString(),
		detailed_suites_results: allSuiteResults, // This still contains summary per suite
		// summary_text and report_url_markdown are removed from here as well,
		// as they were tied to the direct core.setOutput calls.
	};
	fs.writeFileSync(projectJsonPath, JSON.stringify(projectJsonData, null, 2));
	core.info(`Created Apollo project summary JSON at ${projectJsonPath}`);
	core.info('Apollo test processing complete.');

	if (overallApolloConclusion === 'failure') {
		core.setFailed(
			'Apollo tests reported one or more failures or errors during execution.',
		);
	}
};
