name: 'Run Project-Specific Apollo E2E Tests'
description: 'Triggers and monitors Apollo E2E tests based on project configuration, dispatching to wxu-web-automation and polling ReportPortal.'

inputs:
  project_name:
    description: 'The name of the project being tested (e.g., web, docs)'
    required: true
  pr_number:
    description: 'The PR number associated with the deployment'
    required: true
  deployment_url:
    description: 'The Vercel deployment URL to test against'
    required: true
  commit_sha:
    description: 'The commit SHA being tested'
    required: true
  rp_api_url:
    description: 'ReportPortal API URL'
    required: true
  rp_ui_url: # New input
    description: 'ReportPortal UI URL for constructing report links'
    required: true
  rp_token:
    description: 'ReportPortal API token'
    required: true
  automation_dispatch_token:
    description: 'Token for dispatching to wxu-web-automation'
    required: true
  github_token: # This is the standard GitHub token, might be different from automation_dispatch_token
    description: 'GitHub token for API calls'
    required: true
  apollo_suites_config_path:
    description: 'Path to the JSON file configuring Apollo test suites per project'
    required: true

outputs:
  conclusion:
    description: 'Overall conclusion of Apollo tests for the project (success, failure, neutral)'
    value: ${{ steps.run-apollo-script.outputs.conclusion }}
  summary_text:
    description: 'Aggregated summary of Apollo test results for the project'
    value: ${{ steps.run-apollo-script.outputs.summary_text }}
  report_url_markdown:
    description: 'Markdown formatted link(s) to relevant Apollo ReportPortal reports'
    value: ${{ steps.run-apollo-script.outputs.report_url_markdown }}

runs:
  using: "composite"
  steps:
    - name: Execute Apollo Test Logic
      id: run-apollo-script
      uses: actions/github-script@v7
      with:
        github-token: ${{ inputs.automation_dispatch_token }} # For dispatching
        script: |
          const script = require('./.github/actions/e2e-tests/run-apollo-project/run-apollo-script.js')
          await script({github, context, core})
      env:
        PROJECT_NAME: ${{ inputs.project_name }}
        PR_NUMBER: ${{ inputs.pr_number }}
        DEPLOYMENT_URL: ${{ inputs.deployment_url }}
        COMMIT_SHA: ${{ inputs.commit_sha }}
        RP_API_URL: ${{ inputs.rp_api_url }}
        RP_UI_URL: ${{ inputs.rp_ui_url }}
        RP_TOKEN: ${{ inputs.rp_token }}
        GH_TOKEN: ${{ inputs.github_token }}
        APOLLO_SUITES_CONFIG_PATH: ${{ inputs.apollo_suites_config_path }}
        GITHUB_REPOSITORY: ${{ github.repository }}

    - name: Upload Apollo Suite JSON Results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: apollo-suite-results-${{ inputs.project_name }}-${{ inputs.pr_number }}
        path: apollo_suite_results/ # Uploads all files in this directory
        retention-days: 7

    - name: Upload Apollo Project Summary JSON Artifact
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: pr-${{ inputs.pr_number }}-apollo-results-${{ inputs.project_name }}
        path: apollo_project_results/pr-${{ inputs.pr_number }}-apollo-results-${{ inputs.project_name }}.json
        retention-days: 7
