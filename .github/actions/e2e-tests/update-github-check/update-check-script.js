module.exports = async ({ github, context, core }) => {
	// Inputs from environment variables
	const playwrightConclusion = process.env.INPUT_PLAYWRIGHT_CONCLUSION;
	const apolloConclusion = process.env.INPUT_APOLLO_CONCLUSION;
	const overallConclusionOverride =
		process.env.INPUT_OVERALL_CONCLUSION_OVERRIDE;
	const overallTitleOverride = process.env.INPUT_OVERALL_TITLE_OVERRIDE;
	const overallSummaryOverride = process.env.INPUT_OVERALL_SUMMARY_OVERRIDE;
	const projectName = process.env.INPUT_PROJECT_NAME;
	const playwrightReportLink = process.env.INPUT_PLAYWRIGHT_REPORT_LINK;
	const playwrightSummary = process.env.INPUT_PLAYWRIGHT_SUMMARY;
	const apolloSummary = process.env.INPUT_APOLLO_SUMMARY;
	const apolloReportLinkMarkdown =
		process.env.INPUT_APOLLO_REPORT_LINK_MARKDOWN;
	const commitSha = process.env.INPUT_COMMIT_SHA;

	// Step 1: Determine Overall Conclusion and Content
	let finalConclusion = '';
	if (overallConclusionOverride) {
		finalConclusion = overallConclusionOverride;
	} else if (
		playwrightConclusion === 'failure' ||
		apolloConclusion === 'failure'
	) {
		finalConclusion = 'failure';
	} else if (
		playwrightConclusion === 'skipped' &&
		apolloConclusion === 'skipped'
	) {
		finalConclusion = 'skipped';
	} else if (
		playwrightConclusion === 'neutral' &&
		apolloConclusion === 'neutral'
	) {
		finalConclusion = 'neutral';
	} else if (
		(playwrightConclusion === 'neutral' ||
			playwrightConclusion === 'skipped') &&
		apolloConclusion === 'success'
	) {
		finalConclusion = 'success';
	} else if (
		(apolloConclusion === 'neutral' || apolloConclusion === 'skipped') &&
		playwrightConclusion === 'success'
	) {
		finalConclusion = 'success';
	} else {
		finalConclusion = 'success'; // Default
	}

	let finalTitle = overallTitleOverride;
	if (!finalTitle) {
		finalTitle = `E2E Tests (${projectName}): ${finalConclusion.toUpperCase()}`;
	}

	let playwrightLinkMarkdown = '';
	if (playwrightReportLink) {
		playwrightLinkMarkdown = `[View Playwright Artifacts](${playwrightReportLink})`;
	}

	let finalSummary = overallSummaryOverride;
	if (!finalSummary) {
		const summaryPlaywrightPart = `**Playwright Tests:** ${playwrightSummary}\n${playwrightLinkMarkdown}`;
		const summaryApolloPart = `\n\n**Apollo Tests:** ${apolloSummary}\n${apolloReportLinkMarkdown}`;
		finalSummary = summaryPlaywrightPart + summaryApolloPart;
	}

	// Step 2: Create or Update GitHub Check Run
	const owner = context.repo.owner;
	const repo = context.repo.repo;
	const checkName = `E2E Tests (${projectName})`;

	let check_run_id;
	try {
		const { data: checks } = await github.rest.checks.listForRef({
			owner,
			repo,
			ref: commitSha,
			check_name: checkName,
		});
		if (checks.total_count > 0) {
			check_run_id = checks.check_runs[0].id;
			core.info(`Found existing check run: ${check_run_id}`);
			await github.rest.checks.update({
				owner,
				repo,
				check_run_id,
				status: 'completed',
				conclusion: finalConclusion,
				completed_at: new Date().toISOString(),
				output: { title: finalTitle, summary: finalSummary },
			});
			core.info(
				`Updated check run ${check_run_id} with conclusion ${finalConclusion}`,
			);
		} else {
			const { data: newCheck } = await github.rest.checks.create({
				owner,
				repo,
				name: checkName,
				head_sha: commitSha,
				status: 'completed',
				conclusion: finalConclusion,
				completed_at: new Date().toISOString(),
				output: { title: finalTitle, summary: finalSummary },
			});
			check_run_id = newCheck.id;
			core.info(
				`Created new check run ${check_run_id} with conclusion ${finalConclusion}`,
			);
		}
	} catch (error) {
		core.error(`Error creating/updating check run: ${error.message}`);
		// Set an action output or fail the action if critical
		core.setFailed(`Failed to create/update check run: ${error.message}`);
		return; // Exit if check run update fails
	}

	// Set the overall conclusion as an output of the script step
	core.setOutput('result', finalConclusion);
};
