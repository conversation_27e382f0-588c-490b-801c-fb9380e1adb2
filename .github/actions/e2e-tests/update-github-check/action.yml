name: 'Update Combined E2E GitHub Check'
description: 'Creates or updates a GitHub Check Run with combined E2E test results.'

inputs:
  project_name:
    description: 'The name of the project'
    required: true
  commit_sha:
    description: 'The commit SHA for the check run'
    required: true
  playwright_conclusion:
    description: 'Conclusion from Playwright tests (success, failure, neutral, skipped)'
    required: true
  playwright_summary:
    description: 'Summary text from Playwright tests'
    required: true
  playwright_report_link:
    description: 'Link to Playwright reports/artifacts'
    required: false
    default: ''
  apollo_conclusion:
    description: 'Conclusion from Apollo tests (success, failure, neutral, skipped)'
    required: true
  apollo_summary:
    description: 'Summary text from Apollo tests'
    required: true
  apollo_report_link_markdown:
    description: 'Markdown formatted link(s) to Apollo reports'
    required: false
    default: ''
  github_token:
    description: 'GitHub token for API calls'
    required: true
  # Override inputs for special cases like skipped main branch deployments
  overall_title_override:
    description: 'Optional override for the check run title'
    required: false
  overall_summary_override:
    description: 'Optional override for the check run summary'
    required: false
  overall_conclusion_override:
    description: 'Optional override for the check run conclusion'
    required: false

outputs:
  overall_conclusion:
    description: 'The final calculated overall conclusion for the check run.'
    value: ${{ steps.update_check_script.outputs.result }}

runs:
  using: "composite"
  steps:
    - name: Update GitHub Check Run
      id: update_check_script
      uses: actions/github-script@v7
      env:
        INPUT_PLAYWRIGHT_CONCLUSION: ${{ inputs.playwright_conclusion }}
        INPUT_APOLLO_CONCLUSION: ${{ inputs.apollo_conclusion }}
        INPUT_OVERALL_CONCLUSION_OVERRIDE: ${{ inputs.overall_conclusion_override }}
        INPUT_OVERALL_TITLE_OVERRIDE: ${{ inputs.overall_title_override }}
        INPUT_OVERALL_SUMMARY_OVERRIDE: ${{ inputs.overall_summary_override }}
        INPUT_PROJECT_NAME: ${{ inputs.project_name }}
        INPUT_PLAYWRIGHT_REPORT_LINK: ${{ inputs.playwright_report_link }}
        INPUT_PLAYWRIGHT_SUMMARY: ${{ inputs.playwright_summary }}
        INPUT_APOLLO_SUMMARY: ${{ inputs.apollo_summary }}
        INPUT_APOLLO_REPORT_LINK_MARKDOWN: ${{ inputs.apollo_report_link_markdown }}
        INPUT_COMMIT_SHA: ${{ inputs.commit_sha }}
      with:
        github-token: ${{ inputs.github_token }}
        script: |
          const script = require('./.github/actions/e2e-tests/update-github-check/update-check-script.js')
          await script({github, context, core})
