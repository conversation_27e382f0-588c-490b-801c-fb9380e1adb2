module.exports = async ({ github, context, core }) => {
	const prNumber = process.env.PR_NUMBER;
	const resultsJson = process.env.RESULTS_JSON;
	const commitSha = process.env.COMMIT_SHA;
	const projectName = process.env.PROJECT_NAME;
	// Use JOB_NAME as a base for a unique hidden identifier.
	// The default 'E2E Test Summary' from action.yml will be used if not overridden.
	const jobIdentifier = process.env.JOB_NAME || 'E2E Test Summary';

	if (!prNumber) {
		core.warning('PR_NUMBER is not set. Skipping comment posting.');
		return;
	}

	if (!resultsJson) {
		core.warning('RESULTS_JSON is not set. Skipping comment posting.');
		return;
	}
	if (!commitSha) {
		core.warning('COMMIT_SHA is not set. Skipping comment posting.');
		return;
	}
	if (!projectName) {
		core.warning('PROJECT_NAME is not set. Skipping comment posting.');
		return;
	}

	let parsedResults;
	try {
		parsedResults = JSON.parse(resultsJson);
	} catch (error) {
		core.error(`Failed to parse RESULTS_JSON: ${error.message}`);
		core.setFailed('Invalid JSON provided for E2E results.');
		return;
	}

	// Validate required fields in parsedResults
	const requiredKeys = [
		'sha1',
		'status',
		'playwright_results',
		'apollo_results',
		'workflow_run_url',
	];
	for (const key of requiredKeys) {
		if (!(key in parsedResults)) {
			core.error(`RESULTS_JSON is missing required key: ${key}`);
			core.setFailed(`Invalid JSON structure in RESULTS_JSON: Missing ${key}.`);
			return;
		}
	}
	if (
		typeof parsedResults.playwright_results !== 'object' ||
		parsedResults.playwright_results === null ||
		typeof parsedResults.apollo_results !== 'object' ||
		parsedResults.apollo_results === null
	) {
		core.error(
			'playwright_results or apollo_results is not an object in RESULTS_JSON.',
		);
		core.setFailed(
			'Invalid JSON structure: playwright_results or apollo_results must be objects.',
		);
		return;
	}

	// Construct the comment body using the new format
	// Hidden identifier for finding the comment later. Uses the jobIdentifier for uniqueness.
	const botCommentIdentifier = `<!-- ${jobIdentifier}-${projectName} -->`;

	let commentBody = `${botCommentIdentifier}\n`;
	commentBody += `# E2E Test Summary for ${projectName}\n\n`;
	commentBody += `SHA1: \`${parsedResults.sha1}\`\n`;
	commentBody += `Status: ${parsedResults.status}\n\n`;
	commentBody += `| Test Runner | Passed | Failed | Skipped | Duration (s) |\n`;
	commentBody += `|-------------|--------|--------|---------|--------------|\n`;

	const formatTime = (timeMs) => {
		if (typeof timeMs === 'number') {
			return (timeMs / 1000).toFixed(2);
		}
		return timeMs || 'N/A'; // if time is not a number or undefined/null
	};

	const pr = parsedResults.playwright_results;
	const ar = parsedResults.apollo_results;

	commentBody += `| Playwright | ${pr.passed === undefined ? 'N/A' : pr.passed} | ${pr.failed === undefined ? 'N/A' : pr.failed} | ${pr.skipped === undefined ? 'N/A' : pr.skipped} | ${formatTime(pr.time)} |\n`;
	commentBody += `| Apollo     | ${ar.passed === undefined ? 'N/A' : ar.passed} | ${ar.failed === undefined ? 'N/A' : ar.failed} | ${ar.skipped === undefined ? 'N/A' : ar.skipped} | ${formatTime(ar.time)} |\n\n`;
	commentBody += `[View Workflow Summary](${parsedResults.workflow_run_url})`;

	try {
		// Check for existing comments by this action to update if possible
		const { data: comments } = await github.rest.issues.listComments({
			owner: context.repo.owner,
			repo: context.repo.repo,
			issue_number: prNumber,
		});

		// Use the hidden HTML comment identifier
		const existingComment = comments.find(
			(comment) =>
				comment.user.login === 'github-actions[bot]' &&
				comment.body.includes(botCommentIdentifier),
		);

		if (existingComment) {
			core.info(
				`Updating existing comment ID ${existingComment.id} on PR #${prNumber}`,
			);
			await github.rest.issues.updateComment({
				owner: context.repo.owner,
				repo: context.repo.repo,
				comment_id: existingComment.id,
				body: commentBody,
			});
		} else {
			core.info(`Creating new comment on PR #${prNumber}`);
			await github.rest.issues.createComment({
				owner: context.repo.owner,
				repo: context.repo.repo,
				issue_number: prNumber,
				body: commentBody,
			});
		}
		core.info('Successfully posted/updated E2E results comment.');
	} catch (error) {
		core.error(`Failed to post/update comment: ${error.message}`);
		core.setFailed(`Action failed to comment on PR: ${error.message}`);
	}
};
