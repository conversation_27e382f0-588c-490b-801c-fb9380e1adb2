name: 'Post E2E Test Results Comment'
description: 'Posts or updates a comment on a PR with E2E test results.'

inputs:
  github_token:
    description: 'GitHub token for API operations'
    required: true
  pr_number:
    description: 'The PR number to comment on'
    required: true
  results_json:
    description: 'A JSON string containing the test results (sha1, status, playwright_results, apollo_results, workflow_run_url)'
    required: true
  commit_sha:
    description: 'The commit SHA being reported on'
    required: true
  project_name:
    description: 'The name of the project (e.g., web, docs)'
    required: true
  # job_name is still used for the botCommentIdentifier, but the visible header is now fixed.
  # Consider removing if a fixed hidden identifier is preferred.
  job_name:
    description: 'Identifier for the comment, used to find existing comments. Should be unique for this type of summary.'
    required: false
    default: 'E2E Test Summary'

runs:
  using: "composite"
  steps:
    - name: Post or Update PR Comment
      id: post-comment
      uses: actions/github-script@v7
      with:
        github-token: ${{ inputs.github_token }}
        script: |
          const script = require('./.github/actions/e2e-tests/post-e2e-results-comment/post-comment-script.js')
          await script({github, context, core})
      env:
        PR_NUMBER: ${{ inputs.pr_number }}
        RESULTS_JSON: ${{ inputs.results_json }}
        COMMIT_SHA: ${{ inputs.commit_sha }}
        PROJECT_NAME: ${{ inputs.project_name }}
        JOB_NAME: ${{ inputs.job_name }} # Used for the hidden comment identifier
