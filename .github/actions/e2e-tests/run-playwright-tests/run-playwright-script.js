module.exports = async ({ core }) => {
	const { execSync } = require('child_process');
	const fs = require('fs');
	const path = require('path');

	const projectName = process.env.PROJECT_NAME;
	const deploymentUrl = process.env.DEPLOYMENT_URL;
	const commitSha = process.env.COMMIT_SHA;
	const shardIndex = process.env.SHARD_INDEX;
	const totalShards = process.env.TOTAL_SHARDS;
	const vercelAutomationBypassSecret =
		process.env.VERCEL_AUTOMATION_BYPASS_SECRET;

	core.info(`--- Playwright Shard Execution ---
    Project: ${projectName}
    Deployment URL: ${deploymentUrl}
    Commit SHA: ${commitSha}
    Shard: ${shardIndex}/${totalShards}
  ---------------------------------`);

	const playwrightCmdBase = `pnpm --filter ${projectName} test:e2e`;
	const playwrightCmd = `${playwrightCmdBase} --shard=${shardIndex}/${totalShards}`;

	const resultsDir = path.join(
		process.cwd(),
		'apps',
		projectName,
		'playwright-results',
	);
	const junitFile = path.join(
		resultsDir,
		`shard-${shardIndex}-junit-results.xml`,
	);
	const htmlReportDir = path.join(
		resultsDir,
		`shard-${shardIndex}-html-report`,
	);

	// Ensure results directory exists for this project, or Playwright might fail.
	// Playwright itself should create playwright-results, but let's be safe.
	if (!fs.existsSync(resultsDir)) {
		fs.mkdirSync(resultsDir, { recursive: true });
		core.info(`Created Playwright results directory: ${resultsDir}`);
	}

	let conclusion = 'success';
	let summaryText = `Playwright Shard ${shardIndex}/${totalShards} (Project: ${projectName}): Execution started.`;
	let executionError = null;

	try {
		core.info(`Executing Playwright command: ${playwrightCmd}`);
		execSync(playwrightCmd, {
			stdio: 'inherit',
			env: {
				...process.env,
				PLAYWRIGHT_JUNIT_OUTPUT_NAME: junitFile,
				PLAYWRIGHT_HTML_REPORT_DIR: htmlReportDir,
				DEPLOYMENT_URL: deploymentUrl, // Ensure it's passed to the test env
				VERCEL_AUTOMATION_BYPASS_SECRET: vercelAutomationBypassSecret,
			},
		});
		summaryText = `Playwright Shard ${shardIndex}/${totalShards} (Project: ${projectName}): Tests completed.`;
		core.info(summaryText);

		// Check JUnit for failures to set conclusion more accurately
		if (fs.existsSync(junitFile)) {
			const junitContent = fs.readFileSync(junitFile, 'utf8');
			const failureMatch = junitContent.match(
				/<testsuite[^>]*failures="(\d+)"/,
			);
			const errorMatch = junitContent.match(/<testsuite[^>]*errors="(\d+)"/);
			const testsMatch = junitContent.match(/<testsuite[^>]*tests="(\d+)"/); // Check if any tests ran

			let failures = 0;
			let errors = 0;
			let tests = 0;

			if (failureMatch && failureMatch[1]) {
				failures = parseInt(failureMatch[1], 10);
			}
			if (errorMatch && errorMatch[1]) {
				errors = parseInt(errorMatch[1], 10);
			}
			if (testsMatch && testsMatch[1]) {
				tests = parseInt(testsMatch[1], 10);
			}

			if (failures > 0 || errors > 0) {
				conclusion = 'failure';
				summaryText = `Playwright Shard ${shardIndex}/${totalShards} (Project: ${projectName}): Tests finished with ${failures} failures and ${errors} errors.`;
			} else if (tests === 0) {
				conclusion = 'neutral'; // Or 'success' if no tests is acceptable
				summaryText = `Playwright Shard ${shardIndex}/${totalShards} (Project: ${projectName}): No tests were executed in this shard.`;
			} else {
				conclusion = 'success';
				summaryText = `Playwright Shard ${shardIndex}/${totalShards} (Project: ${projectName}): All tests passed.`;
			}
		} else {
			core.warning(
				`JUnit results file not found at ${junitFile}. Cannot determine detailed shard status.`,
			);
			// If the command succeeded but no JUnit, it might mean no tests ran or an issue with reporting.
			// Default to success as the command itself didn't throw, but this might need adjustment.
			conclusion = 'neutral'; // More accurate if no tests ran or reported
			summaryText = `Playwright Shard ${shardIndex}/${totalShards} (Project: ${projectName}): JUnit file not found. Assuming no tests run or reported.`;
		}
	} catch (error) {
		conclusion = 'failure';
		executionError = error.message;
		summaryText = `Playwright Shard ${shardIndex}/${totalShards} (Project: ${projectName}): Execution failed. Error: ${error.message}`;
		core.error(summaryText);
	}

	core.setOutput('conclusion', conclusion);
	core.setOutput('summary_text', summaryText);
	// Output artifact name for this specific shard, including SHA for uniqueness if needed later
	core.setOutput(
		'report_artifact_name',
		`playwright-results-${projectName}-shard-${shardIndex}-${commitSha}`,
	);

	if (executionError) {
		core.setFailed(`Playwright shard execution failed: ${executionError}`);
	}
};
