name: 'Run Playwright Tests (Sharded)'
description: 'Installs Playwright browsers and runs tests for a specific shard.'

inputs:
  project:
    description: 'The project to test (e.g., web, docs)'
    required: true
  deployment_url:
    description: 'The Vercel deployment URL to test against'
    required: true
  commit_sha:
    description: 'The commit SHA being tested'
    required: true
  shard_index:
    description: 'The current shard index (1-based)'
    required: true
  total_shards:
    description: 'The total number of shards'
    required: true
  vercel_automation_bypass_secret:
    description: 'Secret for Vercel automation bypass header'
    required: false

outputs:
  conclusion:
    description: 'Conclusion of the test run for this shard (success, failure, neutral)'
    value: ${{ steps.set-final-outputs.outputs.conclusion }}
  summary_text:
    description: 'Summary of test results for this shard'
    value: ${{ steps.set-final-outputs.outputs.summary_text }}
  report_artifact_name:
    description: 'Name of the uploaded artifact for this shard'
    value: ${{ steps.set-final-outputs.outputs.report_artifact_name }}

runs:
  using: "composite"
  steps:
    - name: Validate project and test script
      id: validate-project
      uses: actions/github-script@v7
      env:
        INPUT_PROJECT: ${{ inputs.project }}
      with:
        script: |
          const fs = require('fs');
          const path = require('path');
          // Assuming 'core' is globally available
          const project = process.env.INPUT_PROJECT;
          const projectPath = path.join('apps', project);

          function setValidationOutput(conclusion, summary) {
            core.setOutput('conclusion', conclusion);
            core.setOutput('summary_text', summary);
            core.setOutput('can_run_tests', 'false');
          }

          if (!fs.existsSync(projectPath) || !fs.statSync(projectPath).isDirectory()) {
            core.info(`Project directory ${projectPath} not found.`);
            setValidationOutput('neutral', `Project ${project} not found.`);
            return;
          }

          const playwrightConfigPath = path.join(projectPath, 'playwright.config.ts');
          if (!fs.existsSync(playwrightConfigPath)) {
            core.info(`playwright.config.ts not found in ${projectPath}.`);
            setValidationOutput('neutral', `No Playwright config for ${project}.`);
            return;
          }

          const packageJsonPath = path.join(projectPath, 'package.json');
          if (!fs.existsSync(packageJsonPath)) {
            core.info(`package.json not found in ${projectPath}.`);
            setValidationOutput('neutral', `No package.json for ${project}.`);
            return;
          }

          try {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            if (!packageJson.scripts || !packageJson.scripts['test:e2e']) {
              core.info(`test:e2e script not found in ${packageJsonPath} for project ${project}.`);
              setValidationOutput('neutral', `No test:e2e script for ${project}.`);
              return;
            }
          } catch (error) {
            core.error(`Error parsing ${packageJsonPath}: ${error.message}`);
            setValidationOutput('failure', `Error reading package.json for ${project}.`);
            return;
          }

          core.info(`Project ${project} validated successfully for Playwright tests.`);
          core.setOutput('can_run_tests', 'true');
          // If can_run_tests is true, conclusion and summary_text will be set by later steps or default to empty
          // Ensuring they are initialized if not set by validation failure:
          core.setOutput('conclusion', ''); // Default to empty, will be overwritten if validation fails
          core.setOutput('summary_text', ''); // Default to empty

    - name: Execute Playwright Shard Logic
      if: steps.validate-project.outputs.can_run_tests == 'true'
      shell: bash
      run: pnpm --filter ${{ inputs.project }} test:e2e --shard=${{ inputs.shard_index }}/${{ inputs.total_shards }}
      env:
        VERCEL_AUTOMATION_BYPASS_SECRET: ${{ inputs.vercel_automation_bypass_secret }}
        VERCEL_PREVIEW_URL: ${{ inputs.deployment_url }}

    - name: Upload Playwright Blob Report for Shard ${{ inputs.shard_index }}
      if: ${{ !cancelled() }}
      uses: actions/upload-artifact@v4
      with:
        name: playwright-blob-report-${{ inputs.shard_index }}
        path: |
          apps/${{ inputs.project }}/blob-report/
          !apps/${{ inputs.project }}/playwright-results/
          !apps/${{ inputs.project }}/playwright-results/junit-results.xml # Exclude top-level if it exists
        retention-days: 1
