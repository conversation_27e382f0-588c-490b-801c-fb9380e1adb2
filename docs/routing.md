# Routing

This is the order in which routing occurs assuming <PERSON><PERSON><PERSON><PERSON> has successfully routed the request to `wx-next`

1.  Middleware hits first and handles redirects
	* Check to see if in bloom-filter before calling `/api/payload/redirects`
	* If not in bloom filter, continue
	* Check rewrites and rewrite with prefix `[code]` if found in rewrites list, else continue to default pattern of filesystem routes
		The paths should be present in rewrites if it needs "context parameters". Eg. deviceClass, partner, user tiers.
		If it does not need these, then it does not need to be in the `[code]` directory of filesystem routing
2. Filesystem routes in `app/*`
   Allows for routing to other paths that are not in `[code]`. Eg. `/test/logging` or `/test/icons`
	 API is also considered filesystem route, but because middleware is ignored for it, it skips middleware.
3. `Fallback` in `rewrites` in `VERCEL_TARGET_ENV` `development` or `preview` go to `dev.weather.com`
   For split brain environments like `dev.weather.com`, the Akamai rules are first above all, which means only routing that <PERSON><PERSON><PERSON><PERSON> knows about
	 will forward appropriately to the right origins.  Even if fallback is present in `rewrites`, <PERSON><PERSON><PERSON><PERSON> would not know about
	 any path that falls under fallback, and rewrite to a default origin that is specified or 404 to not-found BFF service from wxu-web.
	 In local development, fallback routing requires the presence of `VERCEL_TARGET_ENV` set to `development`. Ensure that `.env` or `.env.local`
	 has that variable set.


## [Next.js Routing Execution Order](https://nextjs.org/docs/app/api-reference/file-conventions/middleware#execution-order)

Middleware will be invoked for **every route in your project**. Given this, it's crucial to use [matchers](https://nextjs.org/docs/app/api-reference/file-conventions/middleware#matcher) to precisely target or exclude specific routes. The following is the execution order:

1. `headers` from `next.config.js`
2. `redirects` from `next.config.js`
3. Middleware (`rewrites`, `redirects`, etc.)
4. `beforeFiles` (`rewrites`) from `next.config.js`
5. Filesystem routes (`public/`, `_next/static/`, `pages/`, `app/`, etc.)
6. `afterFiles` (`rewrites`) from `next.config.js`
7. Dynamic Routes (`/blog/[slug]`)
8. `fallback` (`rewrites`) from `next.config.js`
