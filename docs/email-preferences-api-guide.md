# Email Preferences API Developer Guide

This guide provides comprehensive documentation for integrating with the `/api/v1/email-preferences` API, based on the implementation patterns used in the wx-next project.

## Table of Contents

1. [API Overview](#api-overview)
2. [Environment Configuration](#environment-configuration)
3. [Authentication](#authentication)
4. [API Endpoints](#api-endpoints)
5. [Data Types & Interfaces](#data-types--interfaces)
6. [API Call Sequences](#api-call-sequences)
7. [Implementation Examples](#implementation-examples)
8. [Error Handling](#error-handling)
9. [Best Practices](#best-practices)

## API Overview

The Email Preferences API allows applications to manage user email subscription preferences. It supports three main operations:

- **Get Status**: Retrieve current subscription status for a user
- **Set Preferences**: Update subscription preferences and email address
- **Change Email**: Update the user's email address

### Base URLs by Environment

| Environment | Base URL | UPSx Authentication |
|-------------|----------|-------------------|
| Development | `https://dev.weather.com` | UPSx Stage |
| Staging | `https://stage.weather.com` | UPSx Prod |
| Production | `https://weather.com` | UPSx Prod |

## Environment Configuration

The API endpoints are configured using environment variables:

```typescript
// Environment variable setup
NEXT_PUBLIC_WX_HOST=dev.weather.com  // or stage.weather.com, weather.com
```

### URL Configuration Pattern

```typescript
// URL configuration helper
function getEmailPreferencesStatusUrlConfig(): UrlFromParams {
  return {
    host: process.env.NEXT_PUBLIC_WX_HOST,
    protocol: 'https',
    pathname: '/api/v1/email-preferences/status',
  };
}
```

### Authentication Dependencies

The Email Preferences API relies on UPSx (User Profile Service) for authentication:

- **Development environment** (`dev.weather.com`) authenticates against **UPSx Stage**
- **Staging environment** (`stage.weather.com`) authenticates against **UPSx Prod**
- **Production environment** (`weather.com`) authenticates against **UPSx Prod**

This means users must be authenticated through the appropriate UPSx environment before making email preferences API calls. The authentication cookies from UPSx are used to authorize requests to the email preferences endpoints.

## Authentication

All API calls require authentication via UPSx (User Profile Service) cookies. The email preferences API validates these cookies to authorize requests.

### Authentication Flow

1. User authenticates with UPSx (User Profile Service)
2. UPSx sets authentication cookies in the browser
3. Email preferences API calls include these cookies via `credentials: 'include'`
4. Email preferences API validates the UPSx cookies to authorize the request

### Environment-Specific Authentication

| Email Preferences Environment | UPSx Authentication Environment |
|------------------------------|--------------------------------|
| `dev.weather.com` | UPSx Stage |
| `stage.weather.com` | UPSx Prod |
| `weather.com` | UPSx Prod |

### Request Configuration

```typescript
const response = await fetch(url, {
  method: 'GET', // or PUT/POST
  credentials: 'include', // Required to send UPSx authentication cookies
  headers: {
    'Content-Type': 'application/json',
  },
});
```

## API Endpoints

### 1. Get Email Preferences Status

**Endpoint**: `GET /api/v1/email-preferences/status`

**Purpose**: Retrieve current subscription status for the authenticated user.

**Request**:
```http
GET /api/v1/email-preferences/status HTTP/1.1
Host: dev.weather.com
Content-Type: application/json
Cookie: [UPSx authentication cookies]
```

**Response**:
```json
{
  "upsEmail": "<EMAIL>",
  "brazeEmail": "<EMAIL>",
  "externalId": "user-12345",
  "userSubscribed": true,
  "userAliases": [
    {
      "alias_name": "user_id",
      "alias_label": "12345"
    }
  ],
  "subscriptions": [
    {
      "subscriptionGroupId": "group-123",
      "subscriptionGroupName": "daily-newsletters-1",
      "isSubscribed": true,
      "attributes": {
        "location": "place-id-123",
        "latitude": 40.7128,
        "longitude": -74.0060,
        "dmaCode": "501",
        "adminDistrict": "New York",
        "adminDistrictCode": "NY",
        "postalCode": "10001"
      }
    },
    {
      "subscriptionGroupId": "group-456",
      "subscriptionGroupName": "marketing-emails-1",
      "isSubscribed": false
    }
  ]
}
```

### 2. Set Email Preferences

**Endpoint**: `PUT /api/v1/email-preferences/set`

**Purpose**: Update subscription preferences, optionally including email address and mParticle ID.

**Request**:
```http
PUT /api/v1/email-preferences/set HTTP/1.1
Host: dev.weather.com
Content-Type: application/json
Cookie: [UPSx authentication cookies]

{
  "subscriptions": [
    {
      "subscriptionGroupName": "daily-newsletters-1",
      "isSubscribed": true,
      "attributes": {
        "location": "place-id-123",
        "latitude": 40.7128,
        "longitude": -74.0060,
        "dmaCode": "501",
        "adminDistrict": "New York",
        "adminDistrictCode": "NY",
        "postalCode": "10001"
      }
    }
  ],
  "email": "<EMAIL>",
  "mParticleId": "mp-12345"
}
```

**Response (Success)**:
```json
{
  "status": "success",
  "message": "Email preferences updated successfully"
}
```

**Response (Error)**:
```json
{
  "status": "error",
  "error": "Invalid subscription group",
  "emailError": "Invalid email format"
}
```

### 3. Change Email Address

**Endpoint**: `POST /api/v1/email-preferences/change-email`

**Purpose**: Update the user's email address.

**Request**:
```http
POST /api/v1/email-preferences/change-email HTTP/1.1
Host: dev.weather.com
Content-Type: application/json
Cookie: [UPSx authentication cookies]

{
  "newEmail": "<EMAIL>"
}
```

**Response**:
```json
{
  "upsEmail": "<EMAIL>",
  "brazeEmail": "<EMAIL>",
  "externalId": "user-12345",
  "userSubscribed": true,
  "userAliases": [
    {
      "alias_name": "user_id",
      "alias_label": "12345"
    }
  ],
  "subscriptions": [
    // ... subscription data
  ]
}
```

## Data Types & Interfaces

### Subscription Group Names

```typescript
type SubscriptionGroupName =
  | 'marketing-emails-1'
  | 'daily-newsletters-1'
  | 'wu-marketing-emails-1';
```

### Core Interfaces

```typescript
interface SubscriptionGroup {
  subscriptionGroupName: SubscriptionGroupName;
  isSubscribed: boolean;
  attributes?: Record<string, string | number | null>;
}

interface SubscriptionGroupWithId extends SubscriptionGroup {
  subscriptionGroupId: string;
}

interface UserAlias {
  alias_name: string;
  alias_label: string;
}

interface EmailPreferencesStatusResponse {
  upsEmail: string;
  brazeEmail: string;
  externalId: string;
  userSubscribed: boolean;
  userAliases: UserAlias[];
  subscriptions: SubscriptionGroupWithId[];
}

interface EmailPreferencesSetParams {
  subscriptions?: SubscriptionGroup[];
  email?: string;
  mParticleId?: string;
}

interface EmailPreferencesSetResponse {
  status: 'success' | 'error';
  message?: string;
  error?: string;
  emailError?: string;
}
```

## API Call Sequences

### 1. Newsletter Signup Flow (Anonymous User)

```mermaid
sequenceDiagram
    participant User
    participant App
    participant API

    User->>App: Enter email & location
    App->>API: PUT /api/v1/email-preferences/set
    Note over API: Include email in request body
    API-->>App: Success/Error response
    App->>App: Update local state
    App-->>User: Show confirmation/error
```

**Implementation**:
```typescript
// 1. Prepare subscription data
const subscriptionData = {
  subscriptions: [{
    subscriptionGroupName: 'daily-newsletters-1',
    isSubscribed: true,
    attributes: {
      location: 'place-id-123',
      latitude: 40.7128,
      longitude: -74.0060,
      dmaCode: '501',
      adminDistrict: 'New York',
      adminDistrictCode: 'NY',
      postalCode: '10001'
    }
  }],
  email: '<EMAIL>' // Include for anonymous users
};

// 2. Make API call
const response = await fetch('/api/v1/email-preferences/set', {
  method: 'PUT',
  credentials: 'include',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(subscriptionData)
});

// 3. Handle response
const result = await response.json();
if (result.status === 'success') {
  // Update local state, show success message
} else {
  // Handle error
}
```

### 2. Authenticated User Preference Update

```mermaid
sequenceDiagram
    participant User
    participant App
    participant API

    App->>API: GET /api/v1/email-preferences/status
    API-->>App: Current preferences
    App->>App: Display current state
    User->>App: Update preferences
    App->>API: PUT /api/v1/email-preferences/set
    Note over API: No email needed (authenticated)
    API-->>App: Success/Error response
    App->>App: Update local state
```

**Implementation**:
```typescript
// 1. Get current status
const statusResponse = await fetch('/api/v1/email-preferences/status', {
  method: 'GET',
  credentials: 'include',
  headers: { 'Content-Type': 'application/json' }
});
const currentStatus = await statusResponse.json();

// 2. Update specific subscription
const updateData = {
  subscriptions: [{
    subscriptionGroupName: 'marketing-emails-1',
    isSubscribed: false // Unsubscribe from marketing emails
  }]
  // No email field needed for authenticated users
};

// 3. Send update
const updateResponse = await fetch('/api/v1/email-preferences/set', {
  method: 'PUT',
  credentials: 'include',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(updateData)
});
```

### 3. Email Address Change Flow

```mermaid
sequenceDiagram
    participant User
    participant App
    participant API

    User->>App: Enter new email
    App->>API: POST /api/v1/email-preferences/change-email
    API-->>App: Updated preferences with new email
    App->>App: Update local state
    App-->>User: Confirm email change
```

**Implementation**:
```typescript
// Change email address
const changeEmailData = {
  newEmail: '<EMAIL>'
};

const response = await fetch('/api/v1/email-preferences/change-email', {
  method: 'POST',
  credentials: 'include',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(changeEmailData)
});

const updatedPreferences = await response.json();
// Response includes full preference data with updated email
```

## Implementation Examples

### React Hook for Email Preferences

```typescript
import { useCallback } from 'react';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';

export function useEmailPreferences() {
  // Fetch current status
  const { data: preferences, error, mutate } = useSWR(
    'email-preferences-status',
    async () => {
      const response = await fetch('/api/v1/email-preferences/status', {
        method: 'GET',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch preferences');
      }

      return response.json();
    },
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      refreshInterval: 0
    }
  );

  // Update preferences mutation
  const { trigger: updatePreferences, isMutating } = useSWRMutation(
    'email-preferences-set',
    async (_, { arg }: { arg: EmailPreferencesSetParams }) => {
      const response = await fetch('/api/v1/email-preferences/set', {
        method: 'PUT',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(arg)
      });

      const result = await response.json();

      if (result.status === 'error') {
        throw new Error(result.error || 'Failed to update preferences');
      }

      return result;
    },
    {
      onSuccess: () => {
        // Revalidate preferences after successful update
        mutate();
      }
    }
  );

  // Change email mutation
  const { trigger: changeEmail } = useSWRMutation(
    'email-preferences-change-email',
    async (_, { arg }: { arg: { newEmail: string } }) => {
      const response = await fetch('/api/v1/email-preferences/change-email', {
        method: 'POST',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(arg)
      });

      if (!response.ok) {
        throw new Error('Failed to change email');
      }

      return response.json();
    },
    {
      onSuccess: () => {
        mutate();
      }
    }
  );

  return {
    preferences,
    error,
    isLoading: !preferences && !error,
    isUpdating: isMutating,
    updatePreferences,
    changeEmail
  };
}
```

### Newsletter Signup Component

```typescript
import React, { useState } from 'react';
import { useEmailPreferences } from './useEmailPreferences';

export function NewsletterSignup() {
  const [email, setEmail] = useState('');
  const [location, setLocation] = useState(null);
  const { updatePreferences, isUpdating } = useEmailPreferences();

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      await updatePreferences({
        subscriptions: [{
          subscriptionGroupName: 'daily-newsletters-1',
          isSubscribed: true,
          attributes: {
            location: location.placeId,
            latitude: location.latitude,
            longitude: location.longitude,
            adminDistrict: location.adminDistrict,
            postalCode: location.postalCode
          }
        }],
        email: email // Include for anonymous users
      });

      // Show success message
      alert('Successfully subscribed to newsletter!');
    } catch (error) {
      // Handle error
      alert('Failed to subscribe. Please try again.');
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        placeholder="Enter your email"
        required
      />

      {/* Location input component */}
      <LocationInput
        value={location}
        onChange={setLocation}
        placeholder="Enter your location"
        required
      />

      <button
        type="submit"
        disabled={isUpdating || !email || !location}
      >
        {isUpdating ? 'Subscribing...' : 'Subscribe'}
      </button>
    </form>
  );
}
```

## Error Handling

### Common Error Scenarios

1. **Authentication Errors** (401 Unauthorized)
   ```json
   {
     "error": "Unauthorized",
     "message": "User not authenticated"
   }
   ```

2. **Validation Errors** (400 Bad Request)
   ```json
   {
     "status": "error",
     "error": "Invalid subscription group name",
     "emailError": "Invalid email format"
   }
   ```

3. **Server Errors** (500 Internal Server Error)
   ```json
   {
     "error": "Internal server error",
     "message": "An unexpected error occurred"
   }
   ```

### Error Handling Pattern

```typescript
async function handleApiCall(apiCall: () => Promise<Response>) {
  try {
    const response = await apiCall();

    if (!response.ok) {
      if (response.status === 401) {
        // Handle authentication error
        throw new Error('User not authenticated');
      } else if (response.status === 400) {
        // Handle validation error
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.emailError || 'Validation error');
      } else {
        // Handle other errors
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    }

    return await response.json();
  } catch (error) {
    console.error('API call failed:', error);
    throw error;
  }
}
```

## Best Practices

### 1. Caching Strategy

- **Status Endpoint**: Cache for 1-15 minutes to reduce API calls
- **Set Endpoint**: No caching, always fresh
- **Change Email**: No caching, always fresh

```typescript
// SWR configuration example
const { data } = useSWR(
  'email-preferences-status',
  fetcher,
  {
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
    refreshInterval: 15 * 60 * 1000, // 15 minutes
    dedupingInterval: 60 * 1000 // 1 minute
  }
);
```

### 2. State Management

- Use atomic state management (Jotai) for subscription data
- Normalize subscription data with defaults
- Handle expiry for cached preferences

```typescript
// Jotai atom for email preferences
export const emailPreferencesAtom = atom<{
  userId: string;
  expiry: number;
  subscriptions: SubscriptionGroupWithId[];
} | null>(null);
```

### 3. Location Attributes

Always include comprehensive location data for newsletter subscriptions:

```typescript
const locationAttributes = {
  location: location.placeId,           // Required
  latitude: location.latitude,          // Required
  longitude: location.longitude,        // Required
  dmaCode: locationPoint?.dmaCd || null,
  adminDistrict: location.adminDistrict,
  adminDistrictCode: location.adminDistrictCode || null,
  postalCode: location.postalCode
};
```

### 4. Anonymous vs Authenticated Users

```typescript
// For anonymous users, include email in request
const requestData = {
  subscriptions: [...],
  ...(isAnonymous && { email: userEmail })
};
```

### 5. Error Recovery

- Implement retry logic for network failures
- Provide clear error messages to users
- Fall back to default subscription states when API fails

### 6. Performance Optimization

- Use SWR for automatic caching and revalidation
- Debounce rapid subscription changes
- Batch multiple subscription updates when possible

### 7. Testing Considerations

- Mock API responses for unit tests
- Test all error scenarios
- Verify authentication cookie handling
- Test across all three environments

## Environment-Specific Notes

### Development Environment
- **Email Preferences API**: `dev.weather.com`
- **UPSx Authentication**: UPSx Stage environment
- More verbose logging enabled
- Relaxed rate limiting
- Used for local development and testing

### Staging Environment
- **Email Preferences API**: `stage.weather.com`
- **UPSx Authentication**: UPSx Prod environment
- Production-like behavior
- Used for integration testing and pre-production validation
- Same authentication backend as production

### Production Environment
- **Email Preferences API**: `weather.com`
- **UPSx Authentication**: UPSx Prod environment
- Strict rate limiting
- Full monitoring and alerting
- Live user data and transactions

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Verify UPSx cookies are being sent with `credentials: 'include'`
   - Check cookie domain and path settings
   - Ensure user is properly authenticated with the correct UPSx environment:
     - Dev email preferences → UPSx Stage authentication required
     - Stage/Prod email preferences → UPSx Prod authentication required
   - Verify cross-environment authentication setup is correct

2. **CORS Issues**
   - Verify the request origin is allowed
   - Check that credentials are included in requests

3. **Validation Errors**
   - Verify subscription group names match exactly
   - Ensure email format is valid
   - Check that required location attributes are provided

4. **Rate Limiting**
   - Implement exponential backoff for retries
   - Cache responses appropriately
   - Avoid rapid successive API calls

This guide provides a comprehensive foundation for integrating with the Email Preferences API. For additional support or questions, consult the API team or refer to the wx-next implementation examples.
