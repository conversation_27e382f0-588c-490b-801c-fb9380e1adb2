import { useEffect, useMemo } from 'react';
import { atom, useSetAtom, useStore, WritableAtom } from 'jotai';

/**
 * A custom hook that updates atom values when their corresponding props change.
 *
 * Unlike the standard Jotai hydration which only sets initial values once,
 * this hook will check if the atom's current value differs from the new value
 * and update it accordingly. This is useful for components that receive
 * updated props from their parent and need to keep atoms in sync.
 *
 * @param atoms - An array of tuples containing [atom, value] pairs
 * @example
 * ```tsx
 * useRehydrateAtoms([
 *   [pageIdAtom, props.pageId],
 *   [pageLocaleAtom, props.pageLocale],
 * ]);
 * ```
 *
 * @see {@link https://github.com/pmndrs/jotai/discussions/2337#discussioncomment-8151007} for the inspiration behind this approach
 */
export const useRehydrateAtoms = (
	/* eslint-disable-next-line @typescript-eslint/no-explicit-any */
	atoms: [WritableAtom<unknown, any, unknown>, unknown][],
) => {
	const store = useStore();

	const rehydrateAtoms = useSetAtom(
		useMemo(
			() =>
				atom(null, (_get, set) => {
					for (const [currentAtom, currentValue] of atoms) {
						if (store.get(currentAtom) !== currentValue) {
							console.debug(
								'Atom change detected, updating - store value:',
								store.get(currentAtom),
								'new value:',
								currentValue,
							);

							set(currentAtom, currentValue);
						}
					}
				}),
			[atoms, store],
		),
	);

	useEffect(() => {
		// When atoms change, rehydrateAtoms will trigger and compare the values
		// and update accordingly.
		rehydrateAtoms();
	}, [rehydrateAtoms]);
};
