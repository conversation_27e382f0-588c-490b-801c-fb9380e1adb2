// Format the time from validTimeLocal (e.g., "2025-03-20T19:17:00-0400")
export const formatTime = (
	timeString: string,
	locale?: Intl.LocalesArgument,
): string => {
	if (!timeString) return '';
	try {
		const date = new Date(timeString);

		// Format time like "7:17 pm EDT"
		const timeOptions: Intl.DateTimeFormatOptions = {
			hour: 'numeric',
			minute: '2-digit',
			hour12: true,
			timeZoneName: 'short',
		};

		if (!locale) {
			return date.toLocaleTimeString('en-US', timeOptions);
		}

		return date.toLocaleTimeString(locale, timeOptions);
	} catch (error) {
		console.error('Error formatting time:', error);
		return '';
	}
};
