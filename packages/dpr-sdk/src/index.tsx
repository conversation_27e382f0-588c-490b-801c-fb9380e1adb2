'use client';

import Script from 'next/script';
import { getCookie } from 'cookies-next/client';
import { atom, getDefaultStore } from 'jotai';

/**
 * Atom for storing whether window.DprSdk has been initialized
 */
export const dprSdkIsInitializedAtom = atom<boolean>(false);

function onLoad() {
	if (window.top === window.self) {
		// Ensure DprSdk is available before calling init
		if (window.DprSdk) {
			window.DprSdk.init({
				getApplicationInfo: () => ({ id: 'weather.com', version: '2.0.0' }),
				getUserRegime: () => getCookie('twc-privacy'),
			});

			// update atom to indicate window.DprSdk was loaded
			// use store to set dprSdkIsInitializedAtom without needing hooks
			getDefaultStore().set(dprSdkIsInitializedAtom, true);
		}
	} else {
		try {
			// Ensure DprSdk is available on window.top before assigning
			if (window.top?.DprSdk) {
				window.DprSdk = window.top.DprSdk;
			}
		} catch (_error) {
			// do nothing.
		}
	}
}

export default function DprSdk() {
	return (
		<Script
			async
			src="https://weather.com/api/v1/script/dprSdkScript.js"
			onLoad={onLoad}
		/>
	);
}
