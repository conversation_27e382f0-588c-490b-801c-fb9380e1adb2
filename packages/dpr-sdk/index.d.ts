interface DprSdkApplicationInfo {
	id: string;
	version: string;
}

interface DprSdkInitOptions {
	/**
	 * A function that returns application information.
	 */
	getApplicationInfo: () => DprSdkApplicationInfo;
	/**
	 * A function that returns the user regime string, typically from a cookie.
	 * The return type `string | undefined` matches the behavior of `getCookie` from `cookies-next`.
	 */
	getUserRegime: () => string | undefined;
}

interface DprFeatureFlags {
	taboola?: boolean;
	// Other feature flags can be added here as they are discovered
}

interface DprSdkObject {
	/**
	 * Initializes the DprSdk with the provided options.
	 */
	init: (options: DprSdkInitOptions) => void;
	/**
	 * Retrieves an object containing feature flags.
	 * Returns an object with feature flags, or undefined if not available.
	 */
	getFeatureFlags: () => DprFeatureFlags | undefined;
	/**
	 * A function that returns the user ccpa sale opt in string
	 */
	getCcpaSaleOptInString: () => string | null;
	/**
	 * A function that returns the user gdpr opt in string
	 */
	getGdprConsentOptInsString: () => string;
	/**
	 * A function that returns the user consent based on the name
	 */
	getUserConsent: (consentName: string) => boolean;
}

declare global {
	interface Window {
		DprSdk?: DprSdkObject;
	}
	interface Navigator {
		globalPrivacyControl?: boolean;
	}
}

export {};
