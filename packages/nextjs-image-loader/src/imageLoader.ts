/**
 * Fastly image loader
 *
 * This function is refernced in next.config.mjs and is used in combination with next/image.
 * It formats next/image URLs for the Fastly image optimization service.
 * It adds the following parameters:
 * - width: The requested width of the image
 * - format=auto: Automatically selects the best format based on browser support
 * - optimize=medium: Applies medium level optimization
 * - quality: Only added if explicitly provided
 */

// Docs: https://developer.fastly.com/reference/io/
export default function imageLoader({
	src,
	width,
	quality,
}: {
	src: string;
	width: number;
	quality?: number;
}) {
	if (!src || src.indexOf('w-x.co') === -1) return src;

	const url = new URL(src);
	url.searchParams.set('format', 'auto');
	url.searchParams.set('optimize', 'medium');
	url.searchParams.set('width', width.toString());

	if (quality) {
		url.searchParams.set('quality', quality.toString());
	}

	return url.href;
}
