/* eslint-disable @next/next/no-before-interactive-script-outside-document */
import Script from 'next/script';
import { rum } from './rum';
import { createLogger } from '@repo/logger';

const logger = createLogger('NewRelic');

export type ActionName = string;
export type AttributeName = string;
export type AttributeValue = string | number | boolean | null;
export type AttributeMap = Record<AttributeName, AttributeValue>;

declare global {
	interface Window {
		NREUM: {
			addPageAction: (name: ActionName, attributes?: AttributeMap) => void;
			setCustomAttribute: (
				name: AttributeName,
				value: AttributeValue,
				persist?: boolean,
			) => void;
		};
	}
}

const rumAccountId = process.env.NEW_RELIC_RUM_ACCOUNT_ID;
const rumAppId = process.env.NEW_RELIC_RUM_APP_ID;
const rumLicenseKey = process.env.NEW_RELIC_RUM_LICENSE_KEY;
interface NewRelicProps {
	meta?: Record<string, string | number | boolean | null>;
}

/**
 * Adds New Relic's Real-time User Metrics (RUM) library to a page.
 * Must be used within an app's root layout.
 * @param meta - Key-value pairs of metadata to be sent to New Relic
 */
export function NewRelic({ meta = {} }: NewRelicProps) {
	const rumScript = rum({
		accountId: rumAccountId,
		appId: rumAppId,
		licenseKey: rumLicenseKey,
	});

	const metaJs = prepareNewRelicMetaJs(meta);
	const newRelicScripts = [];

	if (rumScript) {
		newRelicScripts.push(
			<Script key="newrelic-rum" id="newrelic-rum" strategy="beforeInteractive">
				{rumScript}
			</Script>,
		);

		if (metaJs) {
			newRelicScripts.push(
				<Script
					key="newrelic-meta"
					id="newrelic-meta"
					strategy="beforeInteractive"
				>
					{metaJs.concat('\nwindow.NREUM.addPageAction("clientSideEnd");')}
				</Script>,
			);
		}
	}

	return newRelicScripts.length ? <>{newRelicScripts}</> : null;
}

const metaPrefix = 'meta.';

/**
 * Sends a RUM event to New Relic with optional metadata
 */
export function sendReumEvent(name: string, meta = {}) {
	try {
		window?.NREUM?.addPageAction?.(name, {
			...prefixKeys(meta, metaPrefix),
		});
	} catch (e: unknown) {
		logger.error(
			'New Relic: sendReumEvent error',
			e instanceof Error ? e.message : String(e),
		);
	}
}

/**
 * Prefixes all keys in an object with a given prefix
 */
const prefixKeys = (obj: AttributeMap, prefix = ''): AttributeMap =>
	Object.fromEntries(
		Object.entries(obj).map(([key, value]) => [`${prefix}${key}`, value]),
	);

/**
 * Prepares a new relic meta object for injection into the page
 */
const prepareNewRelicMetaJs = (
	metaObj: Record<string, string | number | boolean | null>,
) => {
	return Object.entries(metaObj)
		.map(([name, value]) => {
			return `window.NREUM.setCustomAttribute('meta.${name}', ${JSON.stringify(value)});`;
		})
		.join('\n');
};
