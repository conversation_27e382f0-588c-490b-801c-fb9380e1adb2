import { urlFrom } from '../../../utils/http/urlBuilder';
import { getHeaders, getKalliopeJsonApiUrlConfig } from '../../urls/kalliope';
import { JsonApiCreateResponse, JsonApiGetResponse, VideoData } from './types';

export type { VideoData };

interface CreateVideoParams {
	data: VideoData;
}

export async function createKalliopeVideo({ data }: CreateVideoParams) {
	const fetchUrl = urlFrom(getKalliopeJsonApiUrlConfig({ type: 'node/video' }));

	const response = await fetch(fetchUrl, {
		method: 'POST',
		headers: getHeaders(),
		body: JSON.stringify(data),
	});

	if (!response.ok) {
		let message;
		if (response.status >= 400 && response.status <= 500) {
			const data = (await response.json()) as JsonApiCreateResponse;

			message =
				`[${response.status}]` +
				data.errors
					.map((error) => `${error.title}: ${error.detail}`)
					.join(', ');
		} else {
			message = `Unexpected error: ${response.statusText}`;
		}
		throw new Error(message);
	}

	return response.json() as Promise<JsonApiCreateResponse>;
}

export async function findKalliopeVideoByMediaId(mediaId: string) {
	const url = urlFrom(
		getKalliopeJsonApiUrlConfig({
			type: 'node/video',
			query: {
				'filter[attributeFilter][condition][value]': mediaId,
				'filter[attributeFilter][condition][path]': 'field_media_id',
			},
		}),
	);

	const response = await fetch(url, {
		method: 'GET',
		headers: getHeaders(),
	});

	if (!response.ok) {
		throw new Error(`[${response.status}] Failed to fetch video by media ID`);
	}

	const result = (await response.json()) as JsonApiGetResponse;

	return result.data[0];
}
