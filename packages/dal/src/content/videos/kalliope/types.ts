export interface VideoRelationships {
	field_pcoll: {
		data: {
			type: 'taxonomy_term--collection';
			id: string;
		};
	};
	field_provider: {
		data: {
			type: 'taxonomy_term--provider';
			id: string;
		};
	};
	field_interests: {
		data: {
			type: 'taxonomy_term--interests';
			id: string;
		};
	};
	field_playlists: {
		data: {
			type: 'taxonomy_term--playlists';
			id: string;
		};
	};
	field_alternate_video_thumb?: {
		data?: {
			type: 'media--image';
			id?: string;
		};
	};
	field_keywords?: {
		data?: {
			type: 'taxonomy_term--tags';
			id: string;
		};
	};
}

export interface VideoData {
	data: {
		readonly type: 'node--video';
		id?: string;
		attributes: {
			status: boolean;
			title: string;
			langcode: string[];
			field_teaser_title: string;
			field_alt_author: string;
			field_publish_date: string;
			field_ingest_date: string;
			field_seo_title: string;
			field_sub_headline: string;
			field_media_id: string;
			field_source_name: string;
			field_canonical_url: string;
			field_seo_url: string;
			field_video_thumb_url?: string;
			field_mobile_teaser_headline: string;
			field_video_source_url: string;
			field_video_variants: string[];
			field_format_urls: string[];
			body: string;
			field_meta_tags?: {
				value: Record<string, string>;
			};
		};
		relationships?: VideoRelationships;
	};
}

interface JsonApiErrorSource {
	pointer: string;
}

interface JsonApiError {
	title: string;
	status: string;
	detail: string;
	source: JsonApiErrorSource;
}

interface JsonApiMetaLinksSelf {
	href: string;
}

interface JsonApiMetaLinks {
	self: JsonApiMetaLinksSelf;
}

interface JsonApiMeta {
	links: JsonApiMetaLinks;
}

interface JsonApi {
	version: string;
	meta: JsonApiMeta;
}

export interface JsonApiCreateResponse {
	jsonapi: JsonApi;
	errors: JsonApiError[];
	data?: VideoData | FileData | MediaImageData;
}

export interface JsonApiGetResponse {
	jsonapi: {
		version: string;
		meta: {
			links: {
				self: {
					href: string;
				};
			};
		};
	};
	data: VideoNode[] | FileData[] | MediaImageData[];
	meta: {
		count: number;
	};
	links: {
		self: {
			href: string;
		};
	};
}

export interface FileData {
	type: string;
	id: string;
	links: {
		self: {
			href: string;
		};
	};
	attributes: {
		drupal_internal__fid: number;
		langcode: string;
		filename: string;
		uri: {
			value: string;
			url: string;
			dsx_image_cut: string;
		};
		filemime: string;
		filesize: number;
		status: boolean;
		created: string;
		changed: string;
	};
	relationships: {
		uid: {
			data: {
				type: string;
				id: string;
				meta: {
					drupal_internal__target_id: number;
				};
			};
			links: {
				related: {
					href: string;
				};
				self: {
					href: string;
				};
			};
		};
	};
}

export interface VideoNode {
	type: string;
	id: string;
	links: {
		self: {
			href: string;
		};
	};
	attributes: {
		drupal_internal__nid: number;
		drupal_internal__vid: number;
		langcode: string;
		revision_timestamp: string;
		revision_log: string;
		status: boolean;
		title: string;
		created: string;
		changed: string;
		promote: boolean;
		sticky: boolean;
		default_langcode: boolean;
		revision_translation_affected: boolean;
		metatag: unknown;
		path: {
			alias: string;
			pid: number;
			langcode: string;
		};
		content_translation_source: string;
		content_translation_outdated: boolean;
		body: unknown;
		field_aspect_ratio: unknown;
		field_authentication_required: boolean;
		field_cc_url: unknown;
		field_distro: boolean;
		field_duration: unknown;
		field_expire_date: unknown;
		field_format_urls: unknown[];
		field_iab_override: unknown;
		field_ingest_date: string;
		field_is_live_stream: boolean;
		field_is_premium: boolean;
		field_media_id: string;
		field_meta_tags: unknown;
		field_publish_date: string;
		field_samsung_insight_category: unknown;
		field_seo_url: string;
		field_severity_value: number;
		field_source_guid: unknown;
		field_source_name: unknown;
		field_sub_headline: string;
		field_teaser_title: string;
		field_transcript: unknown;
		field_unicorn_guid: unknown;
		field_video_query_strings: unknown[];
		field_video_source_url: {
			uri: string;
			title: string;
			options: unknown[];
		};
		field_video_thumb_option: string;
		field_video_thumb_url: unknown;
		field_video_variants: string[];
	};
	relationships: {
		node_type: Relationship;
		revision_uid: Relationship;
		uid: Relationship;
		field_ai_tags: Relationship;
		field_alternate_video_thumb: Relationship;
		field_analytic_tags: Relationship;
		field_animated_gif: Relationship;
		field_article_author: Relationship;
		field_background: Relationship;
		field_entitlements: Relationship;
		field_iab_tags: Relationship;
		field_interests: Relationship;
		field_keywords: Relationship;
		field_locations: Relationship;
		field_pcoll: Relationship;
		field_playlists: Relationship;
		field_provider: Relationship;
		field_storm_tags: Relationship;
	};
}

interface RelationshipData {
	type: string;
	id: string;
	meta: {
		drupal_internal__target_id: number;
	};
}

interface Relationship {
	data: Array<RelationshipData> | RelationshipData | null;
	links: {
		related: {
			href: string;
		};
		self: {
			href: string;
		};
	};
}

export interface FileUri {
	value: string;
	url: string;
	dsx_image_cut: string;
}

export interface FileAttributes {
	drupal_internal__fid: number;
	langcode: string;
	filename: string;
	uri: FileUri;
	filemime: string;
	filesize: number;
	status: boolean;
	created: string;
	changed: string;
}

export interface UserMeta {
	drupal_internal__target_id: number;
}

export interface UserData {
	type: string;
	id: string;
	meta: UserMeta;
}

export interface RelationshipLinks {
	related: {
		href: string;
	};
	self: {
		href: string;
	};
}

export interface Relationships {
	uid: {
		data: UserData;
		links: RelationshipLinks;
	};
}

export interface FileLinks {
	self: {
		href: string;
	};
}

export interface RootLinks {
	self: {
		href: string;
	};
}

export interface FileFileResponse {
	jsonapi: JsonApi;
	data: FileData;
	links: RootLinks;
}

interface JsonApi {
	version: string;
	meta: {
		links: {
			self: {
				href: string;
			};
		};
	};
}

interface Path {
	alias: string | null;
	pid: string | null;
	langcode: string;
}

interface MediaImageDataAttributes {
	drupal_internal__mid: number;
	drupal_internal__vid: number;
	langcode: string;
	revision_created: string;
	revision_log_message: string;
	status: boolean;
	name: string;
	created: string;
	changed: string;
	default_langcode: boolean;
	revision_translation_affected: boolean;
	metatag: unknown;
	path: Path;
	field_alt_text: unknown;
	field_caption: unknown;
	field_exif_data: unknown;
	field_image_byline: unknown;
	field_image_title: unknown;
	field_property: string;
	field_publish_date: string;
	field_seo_url: unknown;
	field_source_guid: unknown;
	field_source_name: unknown;
}

interface Relationship {
	data: RelationshipData | RelationshipData[] | null;
	links: RelationshipLinks;
}

interface MediaImageDataRelationships {
	bundle: Relationship;
	revision_user: Relationship;
	uid: Relationship;
	thumbnail: Relationship;
	field_geo_tags: Relationship;
	field_keywords: Relationship;
	field_locations: Relationship;
	field_media_image: Relationship;
	field_pcoll: Relationship;
	field_provider: Relationship;
}

export interface MediaImageData {
	type: string;
	id: string;
	links: {
		self: { href: string };
	};
	attributes: MediaImageDataAttributes;
	relationships: MediaImageDataRelationships;
}
