import { urlFrom } from '../../../utils/http/urlBuilder';
import { getHeaders, getKalliopeJsonApiUrlConfig } from '../../urls/kalliope';
import {
	FileData,
	JsonApiCreateResponse,
	JsonApiGetResponse,
	MediaImageData,
} from './types';

const supportedImageFileTypes = ['image/jpg', 'image/jpeg', 'image/png'];

function getExtensionFromMimeType(mimeType: string) {
	const parts = mimeType.split('/');

	if (parts.length !== 2) {
		throw new Error(`Invalid MIME type: ${mimeType}`);
	}

	const ext = parts[1];

	if (ext === 'jpeg') {
		return 'jpg'; // Normalize 'jpeg' to 'jpg'
	}

	return ext;
}

async function getImageBufferFromUrl(url: string) {
	const imageResponse = await fetch(url);

	if (imageResponse.status !== 200) {
		throw new Error(
			`Failed to fetch image from ${url}: ${imageResponse.statusText}`,
		);
	}

	const blob = (await imageResponse.blob()) as Blob;

	if (!supportedImageFileTypes.includes(blob.type)) {
		throw new Error(
			`Unsupported image type: ${blob.type} (Expected: ${supportedImageFileTypes.join(', ')})`,
		);
	}

	const buffer = (await blob.arrayBuffer()) as ArrayBuffer;

	return {
		buffer,
		extension: getExtensionFromMimeType(blob.type),
	};
}

async function getFileByFilename(filename: string) {
	const url = urlFrom(
		getKalliopeJsonApiUrlConfig({
			type: 'file/file',
			query: {
				'filter[attributeFilter][condition][value]': filename,
				'filter[attributeFilter][condition][path]': 'filename',
			},
		}),
	);

	const response = await fetch(url, {
		method: 'GET',
		headers: getHeaders(),
	});

	if (!response.ok) {
		if (response.status === 404) {
			return null;
		}
		throw new Error(`[${response.status}] Failed to fetch file by filename`);
	}

	const result = (await response.json()) as JsonApiGetResponse;

	return result.data[0] as FileData;
}

async function createMediaImageFile(
	filename: string,
	buffer: ArrayBuffer,
): Promise<FileData | null> {
	const url = urlFrom(
		getKalliopeJsonApiUrlConfig({
			type: 'media/image',
			path: 'field_media_image',
		}),
	);

	const response = await fetch(url, {
		method: 'POST',
		headers: getHeaders({
			'Content-Type': 'application/octet-stream',
			'Content-Disposition': `file; filename="${filename}"`,
		}),
		body: Buffer.from(buffer),
	});

	if (!response.ok) {
		throw new Error(`[${response.status}] Failed to upload file`);
	}

	const result = (await response.json()) as JsonApiCreateResponse;

	if (result.errors) {
		throw new Error(
			`Failed to create media image file: ${result.errors
				.map((error) => `${error.title}: ${error.detail}`)
				.join(', ')}`,
		);
	}

	return result.data as FileData;
}

export async function createKalliopeMediaImage(file: FileData) {
	const url = urlFrom(
		getKalliopeJsonApiUrlConfig({
			type: 'media/image',
		}),
	);

	const response = await fetch(url, {
		method: 'POST',
		headers: getHeaders(),
		body: JSON.stringify({
			data: {
				type: 'media--image',
				relationships: {
					field_media_image: {
						data: {
							type: file.type,
							id: file.id,
						},
					},
				},
			},
		}),
	});

	if (!response.ok) {
		throw new Error(`[${response.status}] Failed to create media image`);
	}

	const result = (await response.json()) as JsonApiCreateResponse;

	if (result.errors) {
		throw new Error(
			`Failed to create media image: ${result.errors
				.map((error) => `${error.title}: ${error.detail}`)
				.join(', ')}`,
		);
	}

	return result.data as MediaImageData;
}

interface UploadImageParams {
	url: string;
	slug: string;
}

export async function uploadKalliopeImage({ url, slug }: UploadImageParams) {
	const { buffer, extension } = await getImageBufferFromUrl(url);

	const fileName = `${slug}.${extension}`;

	const existingFile = await getFileByFilename(fileName);

	if (existingFile) {
		return existingFile;
	}

	return await createMediaImageFile(fileName, buffer);
}
