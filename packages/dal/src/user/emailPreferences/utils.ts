import { EmailPreferencesStatusResponse, SubscriptionGroupName } from './types';

export const findSubscriptionGroup = (
	subscriptions: EmailPreferencesStatusResponse['subscriptions'] | null,
	groupName: SubscriptionGroupName,
	defaultValue?: EmailPreferencesStatusResponse['subscriptions'][0],
) => {
	if (!subscriptions || !Array.isArray(subscriptions)) {
		return defaultValue || null;
	}

	return (
		subscriptions.find(
			(subscription) => subscription.subscriptionGroupName === groupName,
		) ||
		defaultValue ||
		null
	);
};
