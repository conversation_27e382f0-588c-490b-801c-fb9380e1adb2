# User Module

## Introduction

The User Module provides comprehensive user management functionality for the wx-next platform. It handles authentication, user profile management, and user preferences through a clean, well-structured API built on <PERSON><PERSON> for state management and the UPSX SDK for backend communication.

This module enables:

- User authentication (login, logout, registration)
- User profile management
- User preferences management (locations, map settings)
- Server and client-side authentication status verification

## Architecture

### State Management

The User Module uses Jotai for state management, with a focus on atomic, composable state:

- **Core State Atoms**:
  - `userStateAtom`: Tracks login status
  - `userProfileAtom`: Stores user profile data with persistence
  - `userPreferencesAtom`: Manages user preferences

### Authentication Flow

1. **Server-Side Authentication**:

   - Uses cookies to verify authentication status
   - JWT validation for secure authentication
   - HOC pattern (`withUser`) for server components

2. **Client-Side Authentication**:
   - `useUser` hook for client components
   - Verification via lightweight API calls
   - Persistent storage for user data

### Data Flow

```mermaid
graph TD
    A[Server Cookies] -->|JWT Validation| B[Authentication Status]
    B -->|withUser HOC| C[Server Components]
    B -->|Hydration| D[Client State]
    D -->|useUser Hook| E[Client Components]
    E -->|User Actions| F[UPSX API]
    F -->|Update| D
```

## Core Components

### Hooks

#### `useUser`

Primary hook for accessing and managing user state in client components.

```typescript
const { isLoading, error, isLoggedIn, user, refetch, logout } = useUser();
```

- **Returns**:
  - `isLoading`: Boolean indicating if user data is being loaded
  - `error`: Error object if authentication failed
  - `isLoggedIn`: Boolean indicating if user is authenticated
  - `user`: User profile data
  - `refetch`: Function to refresh user data
  - `logout`: Function to log out the current user

### Higher-Order Components

#### `withUser`

HOC that provides authentication status to server components.

```typescript
const MyComponentWithUser = withUser(MyComponent);
```

- **Provides**:
  - `isUserLoggedIn`: Boolean indicating if user is authenticated

### Atoms

#### Authentication Atoms

- `userStateAtom`: Core atom for authentication state
- `userProfileAtom`: Persistent storage for user profile
- `isLoggedInAtom`: Derived atom for login status
- `userIdAtom`: Derived atom for user ID

#### Preference Atoms

- `userPreferencesAtom`: Core atom for user preferences
- `userLocationsAtom`: Derived atom for saved locations
- `primaryLocationAtom`: Derived atom for primary location
- `mapSettingsAtom`: Atom for map display preferences

### Utilities

#### `upsxClient`

Singleton client for UPSX API communication.

#### `upsxCookies`

Server-side utilities for cookie-based authentication.

## Usage Examples

### Authentication

#### Login

```typescript
import { login } from "@repo/user/atoms/auth";

// In a component or form handler
const handleLogin = async (email, password) => {
	const result = await login(email, password);
	if (result.success) {
		// Redirect or update UI
	} else {
		// Handle error
	}
};
```

#### Logout

```typescript
import { useUser } from "@repo/user/hooks/useUser";

// In a component
const { logout } = useUser();

const handleLogout = async () => {
	await logout();
	// UI will update automatically
};
```

#### Registration

```typescript
import { register } from "@repo/user/atoms/auth";

const handleRegister = async (userData) => {
	const result = await register({
		email: userData.email,
		password: userData.password,
		firstName: userData.firstName,
		lastName: userData.lastName,
	});

	if (result.success) {
		// Registration successful
	} else {
		// Handle error
	}
};
```

### User Profile

#### Accessing User Profile

```typescript
import { useUser } from '@repo/user/hooks/useUser';

const UserProfile = () => {
  const { user, isLoading } = useUser();

  if (isLoading) return <Loading />;

  return (
    <div>
      <h1>Welcome, {user?.firstName || 'User'}</h1>
      <p>Email: {user?.email}</p>
    </div>
  );
};
```

### User Preferences

#### Managing Locations

```typescript
import {
	addLocation,
	removeLocation,
	setPrimaryLocation,
} from "@repo/user/atoms/preferences/location";

// Add a new location
const handleAddLocation = async (location) => {
	await addLocation({
		name: location.name,
		latitude: location.latitude,
		longitude: location.longitude,
	});
};

// Remove a location
const handleRemoveLocation = async (locationName) => {
	await removeLocation(locationName);
};

// Set primary location
const handleSetPrimary = async (locationName) => {
	await setPrimaryLocation(locationName);
};
```

#### Managing Map Settings

```typescript
import {
	updateMapOpacity,
	updateMapAutoplay,
} from "@repo/user/atoms/preferences/map";

// Update map opacity
const handleOpacityChange = async (opacity) => {
	await updateMapOpacity(opacity);
};

// Toggle autoplay
const handleAutoplayToggle = async (enabled) => {
	await updateMapAutoplay(enabled);
};
```

### Server Components

```typescript
import { withUser } from '@/components/withUser';

const ServerComponent = ({ isUserLoggedIn }) => {
  return (
    <div>
      {isUserLoggedIn ? (
        <p>Welcome back! Here's your personalized content.</p>
      ) : (
        <p>Please log in to see personalized content.</p>
      )}
    </div>
  );
};

export default withUser(ServerComponent);
```

### Client Components

```typescript
'use client';

import { useUser } from '@repo/user/hooks/useUser';

const ClientComponent = () => {
  const { isLoggedIn, user } = useUser();

  return (
    <div>
      {isLoggedIn ? (
        <p>Welcome, {user?.firstName}!</p>
      ) : (
        <p>Please log in</p>
      )}
    </div>
  );
};
```

## API Reference

### Hooks Ref

#### `useUser()`

Returns user state and functions for managing authentication.

### Authentication Functions

#### `login(email: string, password: string)`

Authenticates a user with email and password.

#### `logout()`

Logs out the current user.

#### `register(userData: UserRegistrationData)`

Registers a new user.

### Location Functions

#### `addLocation(location: Location)`

Adds a location to user preferences.

#### `removeLocation(locationName: string)`

Removes a location from user preferences.

#### `setPrimaryLocation(locationName: string)`

Sets a location as the primary location.

#### `reorderLocations(locations: Location[])`

Reorders the user's saved locations.

### Map Preference Functions

#### `updateMapSettings(mapSettings: TWCPreference['mapSettings'])`

Updates all map settings.

#### `updateMapAnimationSpeed(animationSpeed: number)`

Updates map animation speed.

#### `updateMapAutoplay(autoplay: boolean)`

Updates map autoplay setting.

#### `updateMapOpacity(opacity: number)`

Updates map opacity.

## Best Practices

### Server vs. Client Components

- Use `withUser` HOC for server components
- Use `useUser` hook for client components
- Keep authentication logic in the user module

### Error Handling

- Always check for errors when calling authentication functions
- Provide user-friendly error messages
- Handle loading states appropriately

### Performance Considerations

- Use derived atoms for computed values
- Leverage Jotai's fine-grained updates to prevent unnecessary re-renders
- Use `atomWithStorage` for persistent data that should survive page refreshes

### Security

- Never store sensitive information in client-side state
- Always validate authentication on the server for protected routes
- Use the provided JWT validation utilities for secure authentication

## Integration with UI

The User Module is designed to integrate seamlessly with UI components:

- Header components can use `UserProfileMenuClient` with `withUser` HOC
- Forms can use authentication functions directly
- Protected routes can verify authentication status server-side

## Environment Configuration

The User Module requires the following environment variables:

- `NEXT_PUBLIC_UPSX_API_URL`: Base URL for UPSX API
- `UPSX_PEM`: Public key for JWT validation
- `UPSX_HMAC`: Secret for HMAC JWT validation
- `DEV_OVERRIDE_KEY`: (Optional) Key for development override
