{"name": "@repo/user", "version": "0.0.1", "type": "module", "private": true, "scripts": {"lint": "eslint --max-warnings=0 .", "check-types": "tsc --noEmit", "test": "vitest run"}, "exports": {"./atoms": "./src/atoms/index.ts", "./atoms/wxu/user": "./src/atoms/wxu/user.ts", "./atoms/preferredLocation": "./src/atoms/preferredLocation.ts", "./atoms/profile": "./src/atoms/profile.ts", "./atoms/types": "./src/atoms/types.ts", "./atoms/preferences/*": "./src/atoms/preferences/*.ts", "./hooks/*": "./src/hooks/*.ts", "./swrFallbacks/*": "./src/swrFallbacks/*.ts", "./utils/*": "./src/utils/*.ts"}, "devDependencies": {"@playwright/test": "catalog:dev", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@repo/vitest-config": "workspace:*", "@repo/playwright-utils": "workspace:*", "@testing-library/react": "catalog:dev", "@types/node": "catalog:node", "@types/react": "catalog:web", "@types/react-dom": "catalog:web", "eslint": "catalog:dev", "typescript": "catalog:dev", "vitest": "catalog:dev"}, "peerDependencies": {"class-variance-authority": "catalog:web", "next": "catalog:web", "react": "catalog:web", "react-dom": "catalog:web"}, "dependencies": {"@twc/upsx-sdk": "workspace:*", "@repo/dal": "workspace:*", "@repo/navigation": "workspace:*", "@repo/units": "workspace:*", "jotai": "catalog:web", "jose": "^6.0.10", "jotai-optics": "catalog:web", "swr": "catalog:web", "cookies-next": "catalog:web", "uuid": "^11.1.0"}}