/**
 * Test suite for verifying UPSX network calls in the application
 *
 * These tests monitor network requests sent to UPSX subdomains and verify their response statuses
 * are as expected (200 OK). The suite includes three test cases:
 * 1. Count unique paths with "upsx" subdomain and verify 200 status codes
 * 2. Verify no requests with "upsx" subdomain return non-200 status codes
 * 3. Capture and log all "upsx" subdomain requests for debugging purposes
 *
 * @requires Development server to be running (pnpm turbo dev)
 * @uses BaseTest - Base test utility class
 * @uses HeaderPage - Page object for header navigation
 */
import { test, expect } from '@playwright/test';
import { BaseTest, Timeouts } from '@repo/playwright-utils';
import { HeaderPage } from '@repo/playwright-utils';

test.describe('UPSX Network Calls Test', () => {
	let headerPage: HeaderPage;
	const baseTest = new BaseTest();

	test.beforeEach(async ({ page }) => {
		// Initialize the BaseTest and HeaderPage objects
		await baseTest.setupTest(page);
		headerPage = new HeaderPage(page);
	});

	test('should count unique paths with "upsx" subdomain and verify 200 status', async ({
		page,
	}) => {
		// Create a Map to store unique paths and their status codes
		const upsxRequests = new Map<string, number>();

		// Listen for all network requests before navigating
		await page.route('**/*', async (route) => {
			// Continue the route to allow the request to proceed
			await route.continue();
		});

		// Listen for all responses
		page.on('response', (response) => {
			const url = response.url();
			const status = response.status();

			try {
				// Parse the URL to extract hostname and path
				const parsedUrl = new URL(url);
				const hostname = parsedUrl.hostname;
				const path = parsedUrl.pathname;

				// Check if the hostname contains "upsx"
				if (hostname.includes('upsx')) {
					// Store the path and status code
					upsxRequests.set(path, status);
				}
			} catch (error) {
				console.log(
					`[Test: Count unique paths with "upsx" subdomain] Error parsing URL ${url}: ${error instanceof Error ? error.message : 'Unknown error'}`,
				);
			}
		});

		// Navigate to the home page
		await headerPage.goto();

		// Wait for network to be idle to ensure all requests are captured
		await baseTest.waitForDom(page);
		// Wait dynamically for all "upsx" subdomain responses to complete
		await Promise.all(
			Array.from(upsxRequests.keys()).map((path) =>
				page.waitForResponse(
					(response) =>
						response.url().includes(path) && response.status() === 200,
				),
			),
		);
		await page.waitForTimeout(Timeouts.SHORT);

		upsxRequests.forEach((status, path) => {
			expect
				.soft(
					status,
					`Expected status 200 for path: ${path}, but received: ${status}`,
				)
				.toBe(200);
		});
		// Verify all requests return 200 status
		upsxRequests.forEach((status, path) => {
			expect(status).toBe(200);
		});
	});
	test('should verify no requests with "upsx" subdomain return non-200 status', async ({
		page,
	}) => {
		// Create a Map to store unique paths and their status codes
		const upsxRequests = new Map<string, number>();

		// Listen for all network requests before navigating
		await page.route('**/*', async (route) => {
			await route.continue();
		});

		// Listen for all responses
		page.on('response', (response) => {
			const url = response.url();

			// Filter upfront for URLs containing "upsx"
			if (url.includes('upsx')) {
				const status = response.status();

				try {
					const parsedUrl = new URL(url);
					const path = parsedUrl.pathname;

					// Store the path and status code
					upsxRequests.set(path, status);
				} catch (error) {
					console.log(
						`Error parsing URL ${url}: ${error instanceof Error ? error.message : 'Unknown error'}`,
					);
				}
			}
		});

		// Navigate to the home page
		const navigationSuccessful = await headerPage.goto();

		// Wait for DOM to be ready to ensure all requests are captured
		await baseTest.waitForDom(page);

		// Additional wait to ensure all responses are processed
		await page.waitForTimeout(2000);

		// Verify no requests with "upsx" subdomain return non-200 status
		let non200Requests = 0;
		upsxRequests.forEach((status, path) => {
			if (status !== 200) {
				console.log(`⚠️ Non-200 status for path: ${path}, Status: ${status}`);
				non200Requests++;
			}
		});

		console.log(`Total non-200 requests: ${non200Requests}`);
		expect(non200Requests).toBe(0);
	});

	test('should capture and log all "upsx" subdomain requests', async ({
		page,
	}) => {
		const upsxRequests = new Map<string, number>();

		await page.route('**/*', async (route) => {
			await route.continue();
		});

		page.on('response', (response) => {
			const url = response.url();
			const status = response.status();

			try {
				const parsedUrl = new URL(url);
				const hostname = parsedUrl.hostname;
				const path = parsedUrl.pathname;

				if (hostname.includes('upsx')) {
					upsxRequests.set(path, status);
				}
			} catch (error) {
				console.log(
					`Error parsing URL ${url}: ${error instanceof Error ? error.message : 'Unknown error'}`,
				);
			}
		});

		await headerPage.goto();
		await baseTest.waitForDom(page);
		await page.waitForTimeout(Timeouts.SHORT);

		console.log(
			`Captured ${upsxRequests.size} requests with "upsx" subdomain:`,
		);
		upsxRequests.forEach((status, path) => {
			console.log(`Path: ${path}, Status: ${status}`);
			// Add a separate expect.message call instead of providing a message as second argument
			expect.soft(upsxRequests.size).toBeGreaterThan(0);
			if (upsxRequests.size === 0) {
				console.error(
					'Expected at least one request with "upsx" subdomain, but none were captured.',
				);
			}

			expect(upsxRequests.size).toBeGreaterThan(0);
		});
	});
});
