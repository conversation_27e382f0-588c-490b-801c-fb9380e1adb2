'use client';

import { atomWithStorage } from 'jotai/utils';

/**
 * Interface for the preferred location value
 */
export interface WxuPreferredLocationValue {
	countryCode?: string;
	placeId?: string;
	postalCode?: string;
}

/**
 * Interface for the preferred location data structure
 */
export interface WxuPreferredLocationData {
	updatedAt?: number;
	value?: WxuPreferredLocationValue;
}

/**
 * Atom for managing preferred location data
 *
 * This atom syncs with localStorage under the key "wxu-web/keyval:preferredLocation"
 * and stores:
 * - updatedAt: timestamp of when the data was last updated
 * - value: object containing countryCode, placeId, and postalCode
 */
export const wxuPreferredLocationAtom =
	atomWithStorage<WxuPreferredLocationData | null>(
		'wxu-web/keyval:preferredLocation',
		null,
		undefined,
		{
			getOnInit: true,
		},
	);
