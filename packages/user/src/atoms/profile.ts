'use client';

import { atom } from 'jotai';
import { userProfileAtom } from '.';

/**
 * User email atom
 */
export const userEmailAtom = atom((get) => get(userProfileAtom)?.email);

/**
 * User first name atom
 */
export const userFirstNameAtom = atom((get) => get(userProfileAtom)?.firstName);

/**
 * User gender atom
 */
export const userGenderAtom = atom((get) => get(userProfileAtom)?.gender);

/**
 * Is user email verified atom
 */
export const userIsEmailVerifiedAtom = atom(
	(get) => get(userProfileAtom)?.emailVerified,
);
