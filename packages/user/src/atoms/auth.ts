// 'use client';

// import { atom, useSetAtom, useAtomValue } from 'jotai';
// import type { UserRegistrationData } from './types';
// import { userProfileAtom, userPreferencesAtom } from '.';
// import { upsxClient } from '../utils/upsxClient';
// import { UNIT_IMPERIAL } from '@repo/units';
// import { v4 } from 'uuid';

// /**
//  * User ID atom
//  */
// export const userIdAtom = atom((get) => {
// 	const state = get(userProfileAtom);
// 	return state?.userID || '';
// });

// /**
//  * Login a user
//  */
// export const login = async (email: string, password: string) => {
// 	const setUserProfile = useSetAtom(userProfileAtom);
// 	const setUserPreferences = useSetAtom(userPreferencesAtom);
// 	const currentPreferences = useAtomValue(userPreferencesAtom);

// 	try {
// 		const response = await upsxClient.auth.login({ email, password });

// 		// Get account info
// 		const account = await upsxClient.account.getAccount();
// 		setUserProfile(account);

// 		// Get user preferences
// 		try {
// 			const serverPreferences = await upsxClient.preference.getPreference();

// 			// Merge with any local preferences that might not be on the server yet
// 			setUserPreferences({
// 				...serverPreferences,
// 				// Keep local unit preference if server doesn't have one
// 				unit: serverPreferences.unit || currentPreferences.unit,
// 			});
// 		} catch (prefError) {
// 			console.error('Failed to load preferences:', prefError);
// 			// Continue with authentication even if preferences fail to load
// 		}

// 		return { success: true };
// 	} catch (error) {
// 		return { success: false, error };
// 	}
// };

// /**
//  * Logout a user
//  */
// export const logout = async () => {
// 	const setUserProfile = useSetAtom(userProfileAtom);
// 	const userPreferences = useAtomValue(userPreferencesAtom);
// 	const setUserPreferences = useSetAtom(userPreferencesAtom);

// 	try {
// 		await upsxClient.auth.logout();
// 	} catch (error) {
// 		console.error('Logout error:', error);
// 		// Continue with local logout even if API call fails
// 	} finally {
// 		// Clear user profile
// 		setUserProfile(null);

// 		// Reset preferences to anonymous state but keep unit preference
// 		setUserPreferences({
// 			userID: v4(),
// 			unit: userPreferences.unit, // Preserve unit preference
// 			locations: [],
// 			locale: userPreferences.locale || 'en_US', // Preserve locale if set
// 			mapSettings: {
// 				animationSpeed: 1,
// 				autoplay: true,
// 				opacity: 100,
// 				roadOverlay: 'default',
// 				style: 'default',
// 			},
// 			favoriteWeatherComponents: [],
// 			favoriteActivityComponents: [],
// 			dashboard: [],
// 		});
// 	}
// };

// /**
//  * Register a new user
//  */
// export const register = async (userData: UserRegistrationData) => {
// 	try {
// 		const response = await upsxClient.auth.register(userData);

// 		return { success: true, userId: response.user.id };
// 	} catch (error) {
// 		return { success: false, error };
// 	}
// };

// /**
//  * Set user as anonymous
//  */
// export const setAnonymousUser = () => {
// 	const setUserPreferences = useSetAtom(userPreferencesAtom);

// 	// Set default preferences for anonymous users
// 	setUserPreferences({
// 		userID: 'anonymous',
// 		unit: UNIT_IMPERIAL, // Default to imperial units
// 		locations: [], // No saved locations for anonymous users
// 		locale: 'en_US', // Default locale
// 		mapSettings: {
// 			animationSpeed: 1,
// 			autoplay: true,
// 			opacity: 100,
// 			roadOverlay: 'default',
// 			style: 'default',
// 		},
// 		favoriteWeatherComponents: [],
// 		favoriteActivityComponents: [],
// 		dashboard: [],
// 	});
// };
