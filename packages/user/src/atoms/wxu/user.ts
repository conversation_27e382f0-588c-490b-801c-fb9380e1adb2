'use client';

import { focusAtom } from 'jotai-optics';
import { atomWithStorage } from 'jotai/utils';
import type { Account } from '@twc/upsx-sdk';

// Define the structure of the wxuUser data
interface WxuUserData {
	recentLocations?: string[];
	userHasLoggedInBefore?: boolean;
	userPreference?: WxuUserPreference;
	[key: string]: unknown;
}

interface WxuUserPreference {
	dashboard?: WxuUserPreferenceDashboardItem[];
	locale?: string;
	locations?: WxuUserPreferenceLocation[];
	unit?: string;
	userID?: string;
	[key: string]: unknown;
}

interface WxuUserPreferenceLocation {
	coordinate?: string;
	name?: string;
	placeID?: string;
	position?: number;
}

interface WxuUserPreferenceDashboardItem {
	data?: unknown[];
	locations?: Record<string, unknown>[];
	position?: number;
	type?: string;
}

export const wxuUserAtom = atomWithStorage<WxuUserData>(
	'wxu-web/keyval:user',
	{},
	undefined,
	{
		getOnInit: true,
	},
);

export const wxuUserAnonymousIdAtom = focusAtom(wxuUserAtom, (optic) =>
	optic.prop('anonymousId'),
);

export const userHasLoggedInBeforeAtom = focusAtom(
	wxuUserAtom,
	(optic) => optic.prop('userHasLoggedInBefore') || false,
);

export const wxuUserRecentLocationsAtom = focusAtom(
	wxuUserAtom,
	(optic) => optic.prop('recentLocations') || [],
);

export const wxuUserPreferenceAtom = focusAtom(
	wxuUserAtom,
	(optic) => optic.prop('userPreference') || {},
);

export interface WxuUserAccount {
	userAccount?: WxuUserAccountUserAccount | null;
	userAccountResponse?: Account | null;
}

export interface WxuUserAccountUserAccount {
	email?: string;
	firstName?: string;
	hasPassword?: boolean;
	userID?: string;
}

const wxuUserAccountAtom = atomWithStorage<WxuUserAccount>(
	'wxu-web/keyval:user-account',
	{},
	undefined,
	{
		getOnInit: true,
	},
);

export const wxuUserAccountUserAccountResponseAtom = focusAtom(
	wxuUserAccountAtom,
	(optic) => optic.prop('userAccountResponse') || null,
);
