'use client';

import { atomWithStorage } from 'jotai/utils';
import type { Account, TWCPreference } from './types';
import { UNIT_IMPERIAL, UnitsSystem } from '@repo/units';
import { v4 } from 'uuid';

/**
 * User profile atom with persistent storage
 */
export const userProfileAtom = atomWithStorage<Account | null>(
	'userProfile',
	null,
);

/*
 * User preferences interface
 * This interface extends TWCPreference and modifies the 'unit' property
 * to be a string instead of a code.  UPSx returns code, but in localStorage
 * we want to store the name of the unit system (e.g., 'Imperial', 'Metric').
 */
export interface UserPreferences extends TWCPreference {
	unit: UnitsSystem['name'];
}

/**
 * User preferences atom with persistent storage
 */
export const userPreferencesAtom = atomWithStorage<UserPreferences>(
	'userPreferences',
	{
		userID: v4(),
		unit: UNIT_IMPERIAL, // Default to imperial units
		locations: [],
		locale: 'en_US',
		mapSettings: {
			animationSpeed: 1,
			autoplay: true,
			opacity: 100,
			roadOverlay: 'default',
			style: 'default',
		},
		favoriteWeatherComponents: [],
		favoriteActivityComponents: [],
		dashboard: [],
	},
);
