'use client';

import { atom } from 'jotai';
import type { Location } from '../types';
import { userPreferencesAtom } from '../';
import { focusAtom } from 'jotai-optics';

/**
 * User locations atom
 */
export const userLocationsAtom = focusAtom(
	userPreferencesAtom,
	(optic) => optic.prop('locations') || [],
);

/**
 * User locations read only atom
 */
export const userLocationsGetAtom = atom(
	(get) => get(userPreferencesAtom)?.locations || [],
);

/**
 * Primary location read only atom
 */
export const primaryLocationGetAtom = atom((get) => {
	const locations = get(userLocationsAtom);
	// Find location with position 0 or the first location
	return (
		locations?.find((loc: Location) => loc.position === 0) ||
		locations?.[0] ||
		null
	);
});

/**
 * Has locations read only atom
 */
export const hasLocationsGetAtom = atom(
	(get) => get(userLocationsAtom)?.length || 0 > 0,
);
