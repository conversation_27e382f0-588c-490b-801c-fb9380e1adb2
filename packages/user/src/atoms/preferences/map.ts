'use client';

import { atom } from 'jotai';
import { userPreferencesAtom } from '../';

/**
 * Map settings atom
 */
export const mapSettingsAtom = atom(
	(get) =>
		get(userPreferencesAtom)?.mapSettings || {
			animationSpeed: 1,
			autoplay: true,
			opacity: 100,
			roadOverlay: 'default',
			style: 'default',
		},
);

/**
 * Map animation speed atom
 */
export const mapAnimationSpeedAtom = atom(
	(get) => get(mapSettingsAtom).animationSpeed,
);

/**
 * Map autoplay atom
 */
export const mapAutoplayAtom = atom((get) => get(mapSettingsAtom).autoplay);

/**
 * Map opacity atom
 */
export const mapOpacityAtom = atom((get) => get(mapSettingsAtom).opacity);

/**
 * Map road overlay atom
 */
export const mapRoadOverlayAtom = atom(
	(get) => get(mapSettingsAtom).roadOverlay,
);

/**
 * Map style atom
 */
export const mapStyleAtom = atom((get) => get(mapSettingsAtom).style);
