'use client';

import { atom } from 'jotai';
import { UNIT_IMPERIAL, UNIT_METRIC, UNIT_HYBRID } from '@repo/units';
import { userPreferencesAtom } from '../';

/**
 * User unit preference atom
 * This atom reads the unit preference from localStorage via userPreferencesAtom
 * Default is UNIT_IMPERIAL if not set
 */
export const userUnitPreferenceAtom = atom(
	(get) => get(userPreferencesAtom)?.unit || UNIT_IMPERIAL,
);

/**
 * Is imperial units atom
 * Returns true if the current unit preference is UNIT_IMPERIAL
 */
export const isImperialUnitsAtom = atom(
	(get) => get(userUnitPreferenceAtom) === UNIT_IMPERIAL,
);

/**
 * Is metric units atom
 * Returns true if the current unit preference is UNIT_METRIC
 */
export const isMetricUnitsAtom = atom(
	(get) => get(userUnitPreferenceAtom) === UNIT_METRIC,
);

/**
 * Is hybrid units atom
 * Returns true if the current unit preference is UNIT_HYBRID
 */
export const isHybridUnitsAtom = atom(
	(get) => get(userUnitPreferenceAtom) === UNIT_HYBRID,
);
