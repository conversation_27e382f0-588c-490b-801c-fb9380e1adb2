'use client';

import { atom } from 'jotai';
import { userPreferencesAtom } from '../';

/**
 * User locale atom
 */
export const userLocaleAtom = atom(
	(get) => get(userPreferencesAtom)?.locale || 'en_US',
);

/**
 * User language atom
 */
export const userLanguageAtom = atom((get) => {
	const locale = get(userLocaleAtom);
	return locale.split('_')[0];
});

/**
 * User country atom
 */
export const userCountryAtom = atom((get) => {
	const locale = get(userLocaleAtom);
	return locale.split('_')[1];
});
