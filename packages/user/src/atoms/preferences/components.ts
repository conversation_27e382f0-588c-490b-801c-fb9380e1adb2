'use client';

import { atom } from 'jotai';
import { userPreferencesAtom } from '../';

/**
 * Favorite weather components atom
 */
export const favoriteWeatherComponentsAtom = atom(
	(get) => get(userPreferencesAtom)?.favoriteWeatherComponents || [],
);

/**
 * Favorite activity components atom
 */
export const favoriteActivityComponentsAtom = atom(
	(get) => get(userPreferencesAtom)?.favoriteActivityComponents || [],
);

/**
 * Dashboard settings atom
 */
export const dashboardSettingsAtom = atom(
	(get) => get(userPreferencesAtom)?.dashboard || [],
);
