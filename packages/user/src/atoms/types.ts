'use client';

import type {
	Account,
	TWCPreference as BaseTWCPreference,
	Location,
} from '@twc/upsx-sdk';

/**
 * User state type
 */
export interface UserState {
	isLoggedIn: boolean;
	isUserPremium?: boolean;
	subscriptionTier?: number; // Using number to match UserSubscriptionTiers enum
}

/**
 * User registration data
 */
export interface UserRegistrationData {
	email: string;
	password: string;
	firstName?: string;
	lastName?: string;
}

/**
 * Extended TWC preference with additional fields
 */
export interface TWCPreference extends BaseTWCPreference {
	/**
	 * User's recent searches
	 */
	recentSearches?: Location[];
}

/**
 * Update TWC preference request
 */
export interface UpdateTWCPreferenceRequest {
	unit?: string; // e = English/Imperial, m = Metric, h = Hybrid
	locations?: Location[]; // This is the standard field we'll use for favorites
	locale?: string;
	mapSettings?: BaseTWCPreference['mapSettings'];
	favoriteWeatherComponents?: unknown[];
	favoriteActivityComponents?: unknown[];
	dashboard?: unknown[];
	recentSearches?: Location[];
	[key: string]: unknown;
}

// Re-export types from upsx-sdk for convenience
export type { Account, Location };
// Export our extended TWCPreference instead of the base one
