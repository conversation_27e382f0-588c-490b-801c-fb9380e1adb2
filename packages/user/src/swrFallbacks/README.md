# SWR Fallbacks

## Overview

The `swrFallbacks` system provides a structured approach to server-side data fetching for SWR (stale-while-revalidate), ensuring consistent key usage between server actions and client components. This pattern is particularly valuable for data that should be available immediately on page load without requiring a client-side fetch, such as user authentication state.

## Core Concept

The primary purpose of `swrFallbacks` is to ensure that the SWR key used in client components always matches the key used in the server action. This prevents key mismatches that could lead to unnecessary refetching or stale data.

## Implementation

### Key Components

1. **`swrFallbackConfigs` Interface**

```typescript
export interface swrFallbackConfigs {
	key: string; // The SWR cache key
	action: () => Promise<unknown>; // Server action that fetches the data
}
```

2. **Server Actions**

These are Next.js server actions (marked with 'use server') that fetch data on the server side:

```typescript
// Example configuration
export const upsxUserConfig: swrFallbackConfigs = {
	key: UPSX_USER_CONFIG, // Constant key defined in constants.ts
	action: async () => {
		"use server";

		const userData = await getUserData();
		return userData;
	},
};
```

3. **Initialization Function**

```typescript
export const initializeProvider = (actions: swrFallbackConfigs[]) => {
	return actions.reduce((config: Record<string, Promise<unknown>>, action) => {
		config[action.key] = action.action();
		return config;
	}, {});
};
```

## Usage Pattern

### 1. Define Constants for SWR Keys

```typescript
// In constants.ts
export const UPSX_USER_CONFIG = "upsx/user";
export const UPSX_COOKIE_PURCHASE_CONFIG = "upsx/cookie/purchase";
```

### 2. Create Server Actions with Matching Keys

```typescript
// In swrFallbacks/index.ts
export const upsxUserConfig: swrFallbackConfigs = {
	key: UPSX_USER_CONFIG,
	action: async () => {
		"use server";

		const userData = await getUserData();
		return userData;
	},
};
```

### 3. Initialize the SWR Provider with Fallbacks

```typescript
// In WxApp/index.tsx
<SWRProvider
  fallback={{
    ...initializeProvider([upsxUserConfig, upsxCookiePurchaseConfig]),
  }}
>
  {children}
</SWRProvider>
```

### 4. Use the Same Keys in Client Components

```typescript
// In useUser.ts
const { data: user } = useSWRImmutable(UPSX_USER_CONFIG, null, {
	suspense: true,
});
```

## Benefits

1. **Consistency**: Ensures the same key is used on both server and client
2. **Type Safety**: Provides a structured interface for server actions
3. **Performance**: Eliminates loading states for critical data by providing initial values
4. **Developer Experience**: Centralizes the definition of SWR keys and their corresponding data fetching logic

## Implementation Details

### SWR Provider

The SWR provider is configured to use a Map-based cache with localStorage persistence:

```typescript
export const SWRProvider = ({ children, fallback = {} }: SWRProviderProps) => {
  // Create a Map-based cache
  const [cacheMap] = useState<CacheMap>(() => new Map());

  // Setup localStorage persistence
  useEffect(() => {
    // Load from localStorage on mount
    // Save to localStorage on beforeunload and periodically
  }, [cacheMap]);

  // Create provider function
  const provider: SWRConfiguration['provider'] = () => cacheMap;

  return (
    <SWRConfig value={{ provider, fallback }}>
      {children}
    </SWRConfig>
  );
};
```

### Server-Side Data Fetching

The server actions use Next.js's cookies API to access cookie data securely:

```typescript
// Example of server-side data fetching
export async function getUserData() {
	const userData = await getCookieData();

	const isUserLoggedIn = userData?.status !== "none";

	// Transform data for client consumption

	return {
		userData,
		userID: userData?.id,
		isUserLoggedIn,
		subscriptionTier,
		isUserPremium,
	};
}
```

## Best Practices

1. **Consistent Key Management**: Always define keys as constants in a dedicated file
2. **Type Safety**: Use TypeScript interfaces for all data structures
3. **Error Handling**: Include proper error handling in server actions
4. **Minimal Transformations**: Keep data transformations minimal and focused
5. **Security**: Use 'server-only' marker to prevent server code from being included in client bundles

## Example Workflow

1. Server renders the page and executes the server actions defined in swrFallbacks
2. The results are passed to the SWR provider as fallback data
3. Client components use the same keys to access this data without triggering additional fetches
4. When needed, client components can trigger revalidation of this data

This pattern ensures a seamless experience where critical data is available immediately on page load while still enabling dynamic updates when needed.

## Creating New SWR Fallbacks

To create a new SWR fallback:

1. Add a new constant in `constants.ts`:

```typescript
export const MY_NEW_CONFIG = "my/new/config";
```

2. Create a new configuration in `index.ts`:

```typescript
export const myNewConfig: swrFallbackConfigs = {
	key: MY_NEW_CONFIG,
	action: async () => {
		"use server";

		// Fetch or compute data on the server
		const data = await fetchMyData();
		return data;
	},
};
```

3. Add the new config to the provider initialization in your app component:

```typescript
<SWRProvider
  fallback={{
    ...initializeProvider([
      upsxUserConfig,
      upsxCookiePurchaseConfig,
      myNewConfig // Add your new config here
    ]),
  }}
>
  {children}
</SWRProvider>
```

4. Use the same key in your client components:

```typescript
const { data, error, isLoading } = useSWR(MY_NEW_CONFIG, null);
```

By following this pattern, you ensure that the key used in the client component always matches the key used in the server action, preventing inconsistencies and improving the developer experience.
