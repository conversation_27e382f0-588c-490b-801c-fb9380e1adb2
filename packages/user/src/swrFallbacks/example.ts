import { swrFallbackConfigs } from './index';

// Example constant for the SWR key
export const EXAMPLE_CONFIG = 'example/data';

/**
 * Example server function that fetches some data
 * In a real implementation, this would fetch data from an API or database
 */
async function fetchExampleData() {
	// Simulate fetching data
	return {
		id: '123',
		name: 'Example Data',
		timestamp: new Date().toISOString(),
	};
}

/**
 * Example SWR fallback configuration
 *
 * This demonstrates how to create a new SWR fallback:
 * 1. Define a constant for the key
 * 2. Create a server action that fetches the data
 * 3. Export a configuration object that combines them
 */
export const exampleConfig: swrFallbackConfigs = {
	key: EXAMPLE_CONFIG,
	action: async () => {
		'use server';

		// Fetch data using the server function
		const data = await fetchExampleData();

		// You can transform the data here if needed
		return {
			...data,
			// Add any additional properties or transformations
			isExample: true,
		};
	},
};

/**
 * Usage in client component:
 *
 * import { EXAMPLE_CONFIG } from '@repo/user/swrFallbacks/example';
 * import useSWR from 'swr';
 *
 * function MyComponent() {
 *   const { data, error, isLoading } = useSWR(EXAMPLE_CONFIG, null);
 *
 *   if (isLoading) return <div>Loading...</div>;
 *   if (error) return <div>Error loading data</div>;
 *
 *   return (
 *     <div>
 *       <h1>{data.name}</h1>
 *       <p>ID: {data.id}</p>
 *       <p>Timestamp: {data.timestamp}</p>
 *     </div>
 *   );
 * }
 *
 * Usage in app component:
 *
 * import { exampleConfig } from '@repo/user/swrFallbacks/example';
 *
 * <SWRProvider
 *   fallback={{
 *     ...initializeProvider([
 *       upsxUserConfig,
 *       upsxCookiePurchaseConfig,
 *       exampleConfig // Add the new config here
 *     ]),
 *   }}
 * >
 *   {children}
 * </SWRProvider>
 */
