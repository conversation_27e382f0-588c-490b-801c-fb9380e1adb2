# Contributing to SWR Fallbacks

This document provides guidelines for contributing to the SWR Fallbacks system in the wx-next project.

## Adding a New SWR Fallback

When adding a new SWR fallback, follow these steps to ensure consistency and maintainability:

### 1. Define a Constant Key

Add a new constant in `constants.ts`:

```typescript
// In constants.ts
export const MY_NEW_CONFIG = "my/new/config";
```

Use a descriptive name that clearly indicates the purpose of the fallback. Follow the naming pattern of existing keys, using forward slashes to indicate hierarchy.

### 2. Create a Server Action

Create a server action function that fetches the required data. This function should:

- Be marked with `'use server'`
- Handle errors gracefully
- Return data in a format ready for client consumption
- Include proper TypeScript types

```typescript
// Example server action
async function fetchMyData() {
	"use server";

	try {
		// Fetch data from API, database, etc.
		const response = await someApiCall();

		// Transform data if needed
		const transformedData = transformResponse(response);

		return transformedData;
	} catch (error) {
		console.error("Error fetching data:", error);
		// Return a sensible default or null
		return null;
	}
}
```

### 3. Create a Fallback Configuration

Create a configuration object that follows the `swrFallbackConfigs` interface:

```typescript
// In index.ts or a new file
export const myNewConfig: swrFallbackConfigs = {
	key: MY_NEW_CONFIG,
	action: fetchMyData,
};
```

### 4. Add to Provider Initialization

Add your new configuration to the provider initialization in the appropriate app component:

```typescript
// In WxApp/index.tsx or similar
<SWRProvider
  fallback={{
    ...initializeProvider([
      upsxUserConfig,
      upsxCookiePurchaseConfig,
      myNewConfig // Add your new config here
    ]),
  }}
>
  {children}
</SWRProvider>
```

### 5. Use in Client Components

Use the same key in your client components:

```typescript
// In your client component
const { data, error, isLoading } = useSWR(MY_NEW_CONFIG, null);

// Use data in your component
```

## Best Practices

### Key Management

- Always define keys as constants in a dedicated file
- Use descriptive names that indicate the purpose of the data
- Follow a consistent naming pattern (e.g., 'domain/entity/action')
- Never hardcode keys directly in components

### Server Actions

- Keep server actions focused on a single responsibility
- Include proper error handling
- Use TypeScript for type safety
- Add comments explaining complex logic
- Use the 'server-only' marker to prevent client-side usage

### Data Transformation

- Transform data on the server to minimize client-side processing
- Keep transformations minimal and focused
- Ensure transformed data is ready for immediate use in components
- Include proper TypeScript types for transformed data

### Testing

- Write tests for server actions to ensure they handle errors correctly
- Test the integration with client components
- Verify that fallbacks are correctly provided to components

## Common Pitfalls

### Key Mismatches

Ensure the key used in the client component exactly matches the key defined in the constants file. Even a small difference will result in the fallback data not being used.

### Server-Only Code

Be careful not to include client-side code in server actions. Use the 'server-only' package to prevent accidental client-side usage of server-only code.

### Data Size

Be mindful of the size of data returned by server actions. Large data sets can impact initial page load performance. Consider pagination or other strategies for large data sets.

### Error Handling

Always handle errors in server actions to prevent uncaught exceptions. Return sensible defaults or null values when errors occur.

## Example

See `example.ts` for a complete example of how to create and use a new SWR fallback.
