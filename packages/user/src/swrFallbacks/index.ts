import {
	getUserData,
	getPremiumCookieData,
} from '@repo/user/utils/upsxCookies';
import { UPSX_COOKIE_PURCHASE_CONFIG, UPSX_USER_CONFIG } from './constants';

export interface swrFallbackConfigs {
	key: string;
	action: () => Promise<unknown>;
}

export const upsxUserConfig: swrFallbackConfigs = {
	key: UPSX_USER_CONFIG,
	action: async () => {
		'use server';

		const userData = await getUserData();

		return userData;
	},
};

export const upsxCookiePurchaseConfig: swrFallbackConfigs = {
	key: UPSX_COOKIE_PURCHASE_CONFIG,
	action: async () => {
		'use server';

		const premiumData = await getPremiumCookieData();

		return premiumData;
	},
};

export const initializeProvider = (actions: swrFallbackConfigs[]) => {
	return actions.reduce((config: Record<string, Promise<unknown>>, action) => {
		config[action.key] = action.action();

		return config;
	}, {});
};
