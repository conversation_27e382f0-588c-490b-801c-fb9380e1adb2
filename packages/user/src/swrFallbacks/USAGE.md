# SWR Fallbacks Usage Guide

This guide provides practical examples of how to use the SWR Fallbacks system in the wx-next project.

## Basic Usage

### Server Component Setup

In your server component or layout, initialize the SWR provider with fallbacks:

```tsx
// app/layout.tsx or another server component
import { SWRProvider } from "@/components/WxApp/SWRProvider";
import {
	initializeProvider,
	upsxUserConfig,
	upsxCookiePurchaseConfig,
} from "@repo/user/swrFallbacks";

export default function Layout({ children }: { children: React.ReactNode }) {
	return (
		<SWRProvider
			fallback={{
				...initializeProvider([upsxUserConfig, upsxCookiePurchaseConfig]),
			}}
		>
			{children}
		</SWRProvider>
	);
}
```

### Client Component Usage

In your client components, use the same keys to access the data:

```tsx
// UserProfile.tsx (client component)
"use client";

import { useEffect } from "react";
import useSWR from "swr";
import { UPSX_USER_CONFIG } from "@repo/user/swrFallbacks/constants";

export function UserProfile() {
	// Use the same key as defined in the fallback config
	const { data: user, error, isLoading } = useSWR(UPSX_USER_CONFIG);

	// Handle loading state
	if (isLoading) {
		return <div>Loading user profile...</div>;
	}

	// Handle error state
	if (error) {
		return <div>Error loading user profile</div>;
	}

	// Handle no user data
	if (!user) {
		return <div>No user data available</div>;
	}

	// Render user profile
	return (
		<div>
			<h1>User Profile</h1>
			<p>User ID: {user.userID}</p>
			<p>Logged in: {user.isUserLoggedIn ? "Yes" : "No"}</p>
			<p>Premium: {user.isUserPremium ? "Yes" : "No"}</p>
		</div>
	);
}
```

## Advanced Usage

### Conditional Data Fetching

You can conditionally fetch data based on the availability of other data:

```tsx
"use client";

import useSWR from "swr";
import { UPSX_USER_CONFIG } from "@repo/user/swrFallbacks/constants";

export function UserPreferences() {
	// Get user data from SWR fallback
	const { data: user } = useSWR(UPSX_USER_CONFIG);

	// Only fetch preferences if user is logged in
	const { data: preferences } = useSWR(
		user?.isUserLoggedIn ? `user/preferences/${user.userID}` : null,
		() => fetchUserPreferences(user.userID),
	);

	// Render component based on available data
	// ...
}
```

### Revalidation

You can trigger revalidation of fallback data when needed:

```tsx
"use client";

import { useEffect } from "react";
import useSWR, { mutate } from "swr";
import { UPSX_USER_CONFIG } from "@repo/user/swrFallbacks/constants";

export function UserProfileWithRefresh() {
	const { data: user } = useSWR(UPSX_USER_CONFIG);

	// Function to refresh user data
	const refreshUserData = async () => {
		await mutate(UPSX_USER_CONFIG);
	};

	return (
		<div>
			<h1>User Profile</h1>
			{/* Display user data */}
			<button onClick={refreshUserData}>Refresh</button>
		</div>
	);
}
```

### Error Handling

Handle errors gracefully in both server actions and client components:

```tsx
// Server action with error handling
export const myDataConfig: swrFallbackConfigs = {
	key: MY_DATA_CONFIG,
	action: async () => {
		"use server";

		try {
			const data = await fetchData();
			return data;
		} catch (error) {
			console.error("Error fetching data:", error);
			// Return a default value or null
			return null;
		}
	},
};

// Client component with error handling
("use client");

import useSWR from "swr";
import { MY_DATA_CONFIG } from "@/constants";

export function DataDisplay() {
	const { data, error } = useSWR(MY_DATA_CONFIG);

	if (error) {
		return <ErrorComponent message="Failed to load data" />;
	}

	if (!data) {
		return <EmptyStateComponent message="No data available" />;
	}

	return <DataComponent data={data} />;
}
```

## Integration with Other Patterns

### With Container/Presenter Pattern

SWR Fallbacks work well with the Container/Presenter pattern:

```tsx
// Container component
"use client";

import useSWR from "swr";
import { WEATHER_DATA_CONFIG } from "@/constants";
import { WeatherDisplay } from "./WeatherDisplay";

export function WeatherContainer() {
	// Get data from SWR fallback
	const { data, error, isLoading } = useSWR(WEATHER_DATA_CONFIG);

	// Pass data and state to presenter component
	return (
		<WeatherDisplay weatherData={data} isLoading={isLoading} error={error} />
	);
}

// Presenter component
export function WeatherDisplay({ weatherData, isLoading, error }) {
	if (isLoading) {
		return <LoadingComponent />;
	}

	if (error) {
		return <ErrorComponent />;
	}

	return <div>{/* Render weather data */}</div>;
}
```

### With Jotai Atoms

You can use SWR Fallbacks with Jotai atoms for more complex state management:

```tsx
// Define an atom that depends on SWR data
import { atom, useAtom } from "jotai";
import useSWR from "swr";
import { USER_CONFIG } from "@/constants";

// Atom that depends on SWR data
const userSettingsAtom = atom((get) => {
	const user = get(userAtom);
	return {
		theme: user?.settings?.theme || "light",
		notifications: user?.settings?.notifications || false,
	};
});

// Component that uses both SWR and atoms
export function UserSettings() {
	// Get user data from SWR fallback
	const { data: user } = useSWR(USER_CONFIG);

	// Get derived settings from atom
	const [settings, setSettings] = useAtom(userSettingsAtom);

	// Component implementation
	// ...
}
```

## Performance Considerations

### Optimizing Data Size

Be mindful of the size of data returned by server actions:

```tsx
// Good: Return only the necessary data
export const userConfigOptimized: swrFallbackConfigs = {
	key: USER_CONFIG_OPTIMIZED,
	action: async () => {
		"use server";

		const userData = await getUserData();

		// Return only the necessary fields
		return {
			id: userData.id,
			name: userData.name,
			email: userData.email,
			// Don't include large or unnecessary fields
		};
	},
};
```

### Caching Strategies

Configure SWR caching based on data characteristics:

```tsx
// For frequently changing data
const { data } = useSWR(WEATHER_DATA_KEY, null, {
	revalidateOnFocus: true,
	dedupingInterval: 60000, // 1 minute
});

// For relatively static data
const { data } = useSWR(USER_PREFERENCES_KEY, null, {
	revalidateOnFocus: false,
	dedupingInterval: 3600000, // 1 hour
});
```

## Debugging

### Inspecting Fallback Data

You can inspect the fallback data in your browser's React DevTools:

1. Open React DevTools
2. Find the SWRConfig component
3. Check the `value.fallback` prop to see all available fallback data

### Common Issues

1. **Key Mismatch**: Ensure the key used in useSWR exactly matches the key in the fallback config
2. **Missing Data**: Check that the server action is returning the expected data
3. **Type Errors**: Ensure proper TypeScript types for both server action return values and client usage
