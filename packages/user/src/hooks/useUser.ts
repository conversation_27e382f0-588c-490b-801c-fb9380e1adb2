'use client';

import { useState } from 'react';
import { useAtom } from 'jotai';
import useS<PERSON>, { mutate } from 'swr';
import useSWRImmutable from 'swr/immutable';
import {
	useGetCookie,
	getCookie as syncGetCookies,
	setC<PERSON>ie as syncSet<PERSON><PERSON>ie,
	deleteCookie,
} from 'cookies-next';
import { usePageNavigation } from '@repo/navigation/hooks/usePageNavigation';
import { userProfileAtom } from '../atoms';
import { upsxClient } from '../utils/upsxClient';
import {
	wxuUserAccountUserAccountResponseAtom,
	wxuUserAnonymousIdAtom,
} from '@repo/user/atoms/wxu/user';
import { Account } from '@twc/upsx-sdk';
import { UserSubscriptionTiers } from '../utils/consts';
import { v4 } from 'uuid';

export interface User {
	userID: string;
	isUserLoggedIn: boolean;
	subscriptionTier: UserSubscriptionTiers;
	isUserPremium: boolean;
}
export interface UseUser {
	error?: Error | null;
	DISCONNECTED_user?: Omit<User, 'userID'>;
	user: User;
	userProfile?: Account | null;
	logout: () => void;
}

/**
 * Hook for managing user state with upsx-sdk
 *
 * This hook:
 * 1. Verifies user status with user/id request from server
 * 2. Uses stored account data if available, otherwise makes an account request
 * 3. Stores account data in persistent atom for future use
 * 4. Provides logout functionality to clear session and local state
 *
 * CAUTION:
 * Variables prefixed with DISCONNECTED are not within the react lifecycle and can cause hydration mismatches.
 */
export function useUser(): UseUser {
	const [error, setError] = useState<Error | null>(null);
	const [userProfile, setUserProfile] = useAtom(userProfileAtom);
	const [wxuUserAnonymousId, setWxuUserAnonymousIdAtom] = useAtom(
		wxuUserAnonymousIdAtom,
	);
	const [wxuUserAccount, setWxuUserAccount] = useAtom(
		wxuUserAccountUserAccountResponseAtom,
	);
	const { refresh } = usePageNavigation();

	// useGetCookie to make sure we are mounted
	const getCookie = useGetCookie();

	// Get twc-user cookie
	const userTierCookie =
		getCookie('twc-user')?.toString() || UserSubscriptionTiers.none.toString();
	const userTier = parseInt(userTierCookie);
	// USE WITH CAUTION: DISCONNECTED from react lifecycle, will cause hydration mismatch if used. Mainly for external usage
	const DISCONNECTED_userTierCookie =
		syncGetCookies('twc-user')?.toString() ||
		UserSubscriptionTiers.none.toString();
	const DISCONNECTED_userTier = parseInt(DISCONNECTED_userTierCookie);

	// Client Anonymous Id logic
	const clientAnonymousIdCookie = syncGetCookies('ANON_C');
	const anonId = clientAnonymousIdCookie || wxuUserAnonymousId || v4();

	if (!clientAnonymousIdCookie) {
		syncSetCookie('ANON_C', anonId, {
			path: '/',
			domain: '.weather.com',
			secure: true,
		});
	}

	// Generate persistent anonymousId if not defined in localStorage
	if (
		!wxuUserAnonymousId ||
		// In case of a mismatch. Ensures both localStorage and client cookie have the same value.
		(wxuUserAnonymousId &&
			clientAnonymousIdCookie &&
			wxuUserAnonymousId !== clientAnonymousIdCookie)
	) {
		setWxuUserAnonymousIdAtom(anonId);
	}

	// Get storage from wxu if available
	if (!userProfile && wxuUserAccount) {
		setUserProfile(wxuUserAccount as Account);
	}

	// Only fetch user ID if we have a cookie
	const userIdKey = userTier > UserSubscriptionTiers.none ? 'upsx/id' : null;
	const userIdSWR = useSWRImmutable(
		userIdKey,
		() => upsxClient.account.getAccountId(),
		{
			shouldRetryOnError: (error) => error.name !== 'APIError',
		},
	);
	const { userID = '' } = userIdSWR?.data || {};

	// Only fetch account data if:
	// 1. We don't already have a userProfile in state
	// 2. We have a userId from the server
	const shouldFetchAccount = !userProfile && userID;
	const accountKey = shouldFetchAccount ? 'upsx/account' : null;

	// Fetch account data conditionally and store in userProfile
	useSWR(accountKey, () => upsxClient.account.getAccount(), {
		shouldRetryOnError: (error) => error.name !== 'APIError',
		onSuccess: (data) => {
			// Update the userProfileAtom when account data is successfully fetched
			setUserProfile(data);
			setWxuUserAccount(data);
		},
	});

	/**
	 * Logs out the current user
	 *
	 * This function:
	 * 1. Calls the upsxClient.auth.logout() method to log out on the server
	 * 2. Updates local state by setting isUserLoggedIn to false
	 * 3. Clears the user profile data
	 * 4. Refreshes the router to update the UI
	 *
	 * @returns A promise that resolves when logout is complete
	 */
	const logout = async () => {
		try {
			// Call the server-side logout endpoint
			await upsxClient.auth.logout();

			// Clear SWR cache
			mutate(
				(key) => typeof key === 'string' && key.startsWith('/upsx'),
				undefined,
				{ revalidate: false },
			);

			// Update local state
			setUserProfile(null);
			setWxuUserAccount(null);
			deleteCookie('twc-user', {
				domain: '.weather.com',
			});

			// Refresh the router to update the UI
			refresh({ mpa: true });

			return true;
		} catch (err) {
			setError(err instanceof Error ? err : new Error('Failed to log out'));
			return false;
		}
	};

	return {
		error,
		DISCONNECTED_user: {
			isUserLoggedIn: DISCONNECTED_userTier > UserSubscriptionTiers.none,
			subscriptionTier: DISCONNECTED_userTier,
			isUserPremium: DISCONNECTED_userTier > UserSubscriptionTiers.standard,
		},
		user: {
			userID: userID,
			isUserLoggedIn: userTier > UserSubscriptionTiers.none,
			subscriptionTier: userTier,
			isUserPremium: userTier > UserSubscriptionTiers.standard,
		},
		userProfile,
		logout,
	};
}
