'use client';

import { use<PERSON><PERSON> } from 'jotai';
import { userPreferencesAtom } from '../atoms';
import type { UpdateTWCPreferenceRequest, TWCPreference } from '../atoms/types';
import { upsxClient } from '../utils/upsxClient';
import { wxuUserPreferenceAtom } from '../atoms/wxu/user';
import { useUser } from '@repo/user/hooks/useUser';
import { unitsSystemByName, UNITS_COOKIE_KEY } from '@repo/units';
import { setCookie } from 'cookies-next/client';
import useSWR from 'swr';

/**
 * Custom hook for updating user preferences
 *
 * This hook allows both logged-in and non-logged-in users to update their preferences.
 * For all users, preferences are immediately saved to localStorage.
 * For logged-in users, preferences are also synced with the server.
 *
 * @returns A function that updates preferences locally and syncs with the server if logged in
 */
export function useUpdatePreferences() {
	const {
		user: { isUserLoggedIn },
	} = useUser();
	const [userPreferences, setUserPreferences] = useAtom(userPreferencesAtom);
	const [wxuUserPreference, setWxuUserPreference] = useAtom(
		wxuUserPreferenceAtom,
	);

	useSWR(
		isUserLoggedIn ? 'upsx/preferences' : null,
		() => upsxClient.preference.getPreference(),
		{
			onSuccess(data) {
				updateLocalState(data);
			},
		},
	);

	/**
	 * Updates local state with new preferences
	 */
	const updateLocalState = (preferences: UpdateTWCPreferenceRequest) => {
		// Update main preferences atom
		const updatedPreferences = {
			...userPreferences,
			...preferences,
		};
		setUserPreferences(updatedPreferences);

		// Update WXU user preference atom
		setWxuUserPreference({
			...wxuUserPreference,
			/* eslint-disable-next-line @typescript-eslint/no-explicit-any */
			...(preferences as any),
		});

		return updatedPreferences;
	};

	/**
	 * Syncs preferences to UPSX server
	 * Only called for logged-in users
	 */
	const syncToUpsx = async (preferences: Partial<TWCPreference>) => {
		if (!isUserLoggedIn) return null;

		try {
			// Update preferences on the server
			await upsxClient.preference.updatePreference(preferences);
		} catch (error) {
			console.error('Failed to sync preferences with server:', error);
			// Return null to indicate server sync failed
			return null;
		}
	};

	/**
	 * Main update function that orchestrates local updates and server sync
	 */
	const updatePreferences = async (preferences: UpdateTWCPreferenceRequest) => {
		try {
			// Step 1: Always update local state immediately for responsive UI
			const updatedPreferences = updateLocalState(preferences);

			// If updating unit, add cookie
			if (preferences?.unit) {
				// Get the UnitsSystem object for the unit name
				const unitsSystem = unitsSystemByName(preferences.unit);
				const unitCode = unitsSystem?.code || 'e';

				if (unitCode) {
					// Set cookie with the UnitsSystem value
					setCookie(UNITS_COOKIE_KEY, unitCode, {
						domain: 'weather.com',
					});
				}
			}

			// Step 2: For logged-in users, also sync with server
			if (isUserLoggedIn) {
				await syncToUpsx(updatedPreferences);
			}

			return { success: true };
		} catch (error) {
			console.error('Failed to update preferences:', error);
			return {
				success: false,
				error:
					error instanceof Error
						? error.message
						: 'Failed to update preferences',
			};
		}
	};

	return updatePreferences;
}
