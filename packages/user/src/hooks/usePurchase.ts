'use client';

import { getCookie } from 'cookies-next';
import useSWRImmutable from 'swr/immutable';
import { upsxClient } from '../utils/upsxClient';

export function usePurchase() {
	const { data: purchase } = useSWRImmutable('upsx/cookie/purchase', () =>
		getUpsxToken().then((data) => data),
	);

	useSWRImmutable(
		// Make token call only if purchase isn't already valid
		purchase ? null : 'upsx/purchase/token',
		() => getUpsxToken().then((data) => data),
	);

	/**
	 * Gets UPSX premium cookie with UPSX purchase Token if premium cookie doesn't already exists
	 *
	 * @returns A promise that resolves boolean on UPSX token call or null if user is not logged in or already has premium
	 */
	const getUpsxToken = async (): Promise<boolean | null> => {
		try {
			// Check for premium cookie
			const premiumCookie = getCookie('premium');
			if (premiumCookie) {
				return null;
			}

			// Get the purchase token from the UPSX SDK
			await upsxClient.purchase.getPurchaseToken();

			return true;
		} catch (err) {
			console.error('Failed to get UPSX token:', err);
			return false;
		}
	};

	return {};
}

export default usePurchase;
