# useUser Hook

## Introduction

The `useUser` hook is a core component of the wx-next authentication system, providing client components with access to user state and authentication functionality. It serves as the primary interface between client components and the user authentication system.

This hook:

1. Verifies user status with a lightweight `user/id` request
2. Uses stored account data if available, otherwise makes an account request
3. Updates login state based on request success/failure
4. Stores account data in persistent atom for future use
5. Provides getUpsxToken function for logged-in users without premium
6. Provides logout functionality to clear session and local state

## API Reference

```typescript
const { isLoading, error, isLoggedIn, user, getUpsxToken, refetch, logout } =
	useUser();
```

### Return Values

| Property       | Type                             | Description                                           |
| -------------- | -------------------------------- | ----------------------------------------------------- |
| `isLoading`    | `boolean`                        | Indicates if user data is being loaded                |
| `error`        | `Error \| null`                  | Error object if authentication failed, null otherwise |
| `isLoggedIn`   | `boolean`                        | Indicates if user is authenticated                    |
| `user`         | `Account \| null`                | User profile data if authenticated, null otherwise    |
| `getUpsxToken` | `() => Promise<string \| null>`  | Function to get UPSX token for users without premium  |
| `refetch`      | `() => Promise<Account \| null>` | Function to refresh user data                         |
| `logout`       | `() => Promise<boolean>`         | Function to log out the current user                  |

### TypeScript Interfaces

The hook uses the following key interfaces:

```typescript
// From apps/web/user/atoms/types.ts
interface UserState {
	isLoggedIn: boolean;
}

// From packages/upsx-sdk/src/models/account.ts
interface Account {
	userID: string;
	email: string;
	firstName?: string;
	lastName?: string;
	// Additional account properties...
}
```

## Implementation Details

### Authentication Flow

1. **Initial Load**:

   - The hook runs `verifyAndFetchUser` on mount via useEffect
   - Sets `isLoading` to true while verification is in progress

2. **Verification Process**:

   - Makes a lightweight `getUserId` request to check authentication status
   - If successful, sets `isLoggedIn` to true
   - If unsuccessful, sets `isLoggedIn` to false and clears user data

3. **User Data Retrieval**:

   - If user is authenticated and no cached data exists, fetches full account details
   - If cached data exists in the `userProfileAtom`, uses that data without making an API call
   - Updates the persistent `userProfileAtom` with fresh data when fetched

4. **Error Handling**:
   - Catches any errors during verification or data fetching
   - Sets appropriate error state and marks user as logged out
   - Clears user profile data on error

### State Management

The hook uses Jotai atoms for state management:

- `userStateAtom`: Tracks login status
- `userProfileAtom`: Stores user profile data with persistence via `atomWithStorage`

This approach provides:

- Persistent storage across page refreshes
- Atomic updates that minimize re-renders
- Consistent state across components

### Logout Process

The `logout` function:

1. Calls the server-side logout endpoint via `upsxClient.auth.logout()`
2. Updates local state by setting `isLoggedIn` to false
3. Clears the user profile data
4. Refreshes the router to update the UI

## Usage Examples

### Basic Usage

```tsx
"use client";

import { useUser } from "@repo/user/hooks/useUser";

const UserGreeting = () => {
	const { isLoading, error, isLoggedIn, user } = useUser();

	if (isLoading) {
		return <div>Loading user data...</div>;
	}

	if (error) {
		return <div>Error: {error.message}</div>;
	}

	return (
		<div>
			{isLoggedIn ? (
				<h1>Welcome, {user?.firstName || "User"}!</h1>
			) : (
				<h1>Welcome, Guest!</h1>
			)}
		</div>
	);
};
```

### Using the Logout Function

```tsx
"use client";

import { useUser } from "@repo/user/hooks/useUser";
import { Button } from "@repo/ui/components/Button";

const LogoutButton = () => {
	const { logout } = useUser();
	const [isLoggingOut, setIsLoggingOut] = useState(false);

	const handleLogout = async () => {
		try {
			setIsLoggingOut(true);
			await logout();
			// No need to redirect - router.refresh() is called by logout()
		} catch (error) {
			setIsLoggingOut(false);
			console.error("Logout failed:", error);
		}
	};

	return (
		<Button onClick={handleLogout} disabled={isLoggingOut}>
			{isLoggingOut ? "Logging out..." : "Sign Out"}
		</Button>
	);
};
```

### Refreshing User Data

```tsx
"use client";

import { useUser } from "@repo/user/hooks/useUser";
import { Button } from "@repo/ui/components/Button";

const RefreshUserData = () => {
	const { refetch, user } = useUser();
	const [isRefreshing, setIsRefreshing] = useState(false);

	const handleRefresh = async () => {
		try {
			setIsRefreshing(true);
			await refetch();
		} catch (error) {
			console.error("Failed to refresh user data:", error);
		} finally {
			setIsRefreshing(false);
		}
	};

	return (
		<div>
			<p>Last updated: {new Date().toLocaleTimeString()}</p>
			<Button onClick={handleRefresh} disabled={isRefreshing}>
				{isRefreshing ? "Refreshing..." : "Refresh User Data"}
			</Button>
			{user && <pre>{JSON.stringify(user, null, 2)}</pre>}
		</div>
	);
};
```

### Using the UPSX Token

```tsx
"use client";

import { useUser } from "@repo/user/hooks/useUser";
import { useState } from "react";

const UpsxTokenExample = () => {
	const { isLoggedIn, getUpsxToken } = useUser();
	const [isLoading, setIsLoading] = useState(false);

	// Function to make API requests with the token
	const makeApiRequest = async () => {
		setIsLoading(true);
		try {
			const token = await getUpsxToken();
			if (!token) {
				console.error("No UPSX token available");
				return;
			}

			const response = await fetch("https://api.example.com/data", {
				headers: {
					Authorization: `Bearer ${token}`,
				},
			});
			const data = await response.json();
			// Process data...
		} catch (error) {
			console.error("API request failed:", error);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div>
			{isLoggedIn ? (
				<div>
					<button onClick={makeApiRequest} disabled={isLoading}>
						{isLoading ? "Loading..." : "Make API Request"}
					</button>
				</div>
			) : (
				<p>User is not logged in</p>
			)}
		</div>
	);
};
```

## Best Practices

### When to Use useUser vs. withUser HOC

- **Use `useUser` hook for**:

  - Client components that need user data or authentication state
  - Components that need to trigger logout or refresh user data
  - Interactive UI elements that change based on authentication status

- **Use `withUser` HOC for**:
  - Server components that only need to know if a user is logged in
  - Components that don't need detailed user profile information
  - Static UI elements that change based on authentication status

### Error Handling

- Always check the `error` property before assuming authentication succeeded
- Provide user-friendly error messages for authentication failures
- Consider implementing retry logic for transient errors
- Log authentication errors for debugging purposes

```tsx
if (error) {
	if (error.message.includes("network")) {
		return <div>Network error. Please check your connection.</div>;
	} else if (error.message.includes("unauthorized")) {
		return <div>Your session has expired. Please log in again.</div>;
	} else {
		return <div>An error occurred: {error.message}</div>;
	}
}
```

### Performance Considerations

- The hook uses caching to avoid redundant API calls
- Only fetch user data when necessary
- Consider memoizing components that use the hook to prevent unnecessary re-renders
- Use the `isLoading` state to show appropriate loading indicators

## Integration Examples

### With Navigation Components

```tsx
"use client";

import Link from "next/link";
import { useUser } from "@repo/user/hooks/useUser";

const Navigation = () => {
	const { isLoggedIn, user } = useUser();

	return (
		<nav>
			<ul>
				<li>
					<Link href="/">Home</Link>
				</li>
				<li>
					<Link href="/weather">Weather</Link>
				</li>
				{isLoggedIn ? (
					<>
						<li>
							<Link href="/account">My Account</Link>
						</li>
						<li>
							<Link href="/preferences">Preferences</Link>
						</li>
						<li>Welcome, {user?.firstName || "User"}</li>
					</>
				) : (
					<>
						<li>
							<Link href="/login">Sign In</Link>
						</li>
						<li>
							<Link href="/register">Register</Link>
						</li>
					</>
				)}
			</ul>
		</nav>
	);
};
```

### With Protected Content

```tsx
"use client";

import { useUser } from "@repo/user/hooks/useUser";
import { LoginPrompt } from "@/components/LoginPrompt";

const ProtectedContent = () => {
	const { isLoading, isLoggedIn } = useUser();

	if (isLoading) {
		return <div>Loading...</div>;
	}

	if (!isLoggedIn) {
		return <LoginPrompt message="Please log in to view this content" />;
	}

	return (
		<div>
			<h1>Protected Content</h1>
			<p>This content is only visible to authenticated users.</p>
		</div>
	);
};
```

### With User Profile Components

```tsx
"use client";

import { useUser } from "@repo/user/hooks/useUser";
import { ProfileForm } from "@/components/ProfileForm";

const UserProfilePage = () => {
	const { isLoading, error, user, refetch } = useUser();

	if (isLoading) {
		return <div>Loading profile...</div>;
	}

	if (error) {
		return <div>Error loading profile: {error.message}</div>;
	}

	if (!user) {
		return <div>User not found</div>;
	}

	const handleProfileUpdate = async (updatedData) => {
		// Update profile logic here
		await updateUserProfile(updatedData);
		// Refresh user data to get updated profile
		await refetch();
	};

	return (
		<div>
			<h1>User Profile</h1>
			<ProfileForm initialData={user} onSubmit={handleProfileUpdate} />
		</div>
	);
};
```

## Related Components

- `withUser` HOC: Server-side counterpart for authentication
- `UserProfileMenuClient`: Example component using the useUser hook
- `upsxClient`: Utility for API communication

## See Also

- [User Module README](../README.md): Complete documentation of the User module
- [Authentication Atoms](../atoms/auth.ts): State management for authentication
- [User Preferences](../atoms/preferences/index.ts): User preference management
