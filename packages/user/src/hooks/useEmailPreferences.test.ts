import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useEmailPreferences } from './useEmailPreferences';
import { useUser, UseUser } from '@repo/user/hooks/useUser';
import { useAtom } from 'jotai/index';
import useSWR from 'swr';
import { SubscriptionGroupWithId } from '@repo/dal/user/emailPreferences/types';
import { UserSubscriptionTiers } from '@repo/user/utils/consts';

// Mock dependencies
vi.mock('@repo/user/hooks/useUser');
vi.mock('jotai/index');
vi.mock('swr');

describe('useEmailPreferences', () => {
	// Test fixtures
	const mockUserId = 'test-user-123';
	const mockSubscriptions: SubscriptionGroupWithId[] = [
		{
			subscriptionGroupName: 'daily-newsletters-1',
			subscriptionGroupId: '123',
			isSubscribed: true,
			attributes: {
				location: 'New York',
			},
		},
		{
			subscriptionGroupName: 'marketing-emails-1',
			subscriptionGroupId: '456',
			isSubscribed: false,
		},
	];

	const mockEmailPreferences = {
		userId: mockUserId,
		expiry: Date.now() + 900000, // 15 minutes from now
		subscriptions: mockSubscriptions,
	};

	const mockExpiredEmailPreferences = {
		userId: mockUserId,
		expiry: Date.now() - 1000, // Expired
		subscriptions: mockSubscriptions,
	};

	// Mock setup
	const mockUseUser = vi.mocked(useUser);
	const mockUseAtom = vi.mocked(useAtom) as unknown as ReturnType<typeof vi.fn>;
	const mockUseSWR = vi.mocked(useSWR);

	// Mock setters and getters
	const mockSetEmailPreferences = vi.fn();

	// Create mock user objects
	const mockLoggedOutUser: UseUser = {
		user: {
			userID: '',
			isUserLoggedIn: false,
			subscriptionTier: UserSubscriptionTiers.none,
			isUserPremium: false,
		},
		error: null,
		logout: vi.fn(),
	};

	const mockLoggedInUser: UseUser = {
		user: {
			userID: mockUserId,
			isUserLoggedIn: true,
			subscriptionTier: UserSubscriptionTiers.standard,
			isUserPremium: false,
		},
		error: null,
		logout: vi.fn(),
	};

	beforeEach(() => {
		vi.resetAllMocks();

		// Default mock implementations
		mockUseUser.mockReturnValue(mockLoggedOutUser);
		mockUseAtom.mockReturnValue([null, mockSetEmailPreferences]);
		mockUseSWR.mockReturnValue({
			data: null,
			error: null,
			isLoading: false,
			isValidating: false,
			mutate: vi.fn(),
		});
	});

	it('should return null subscriptions for logged-out user', () => {
		// Setup: User is not logged in
		mockUseUser.mockReturnValue(mockLoggedOutUser);
		mockUseAtom.mockReturnValue([null, mockSetEmailPreferences]);

		// Execute
		const { result } = renderHook(() => useEmailPreferences());

		// Verify
		expect(result.current.subscriptions).toBeNull();
		expect(mockUseSWR).toHaveBeenCalledWith(
			undefined, // key should be undefined when user is not logged in
			expect.any(Function),
			expect.any(Object),
		);
	});

	it('should not fetch data when valid unexpired preferences exist', () => {
		// Setup: User is logged in and has valid preferences
		mockUseUser.mockReturnValue(mockLoggedInUser);
		mockUseAtom.mockReturnValue([
			mockEmailPreferences,
			mockSetEmailPreferences,
		]);

		// Execute
		const { result } = renderHook(() => useEmailPreferences());

		// Verify
		expect(result.current.subscriptions).toBe(mockSubscriptions);
		expect(mockUseSWR).toHaveBeenCalledWith(
			undefined, // key should be undefined when valid preferences exist
			expect.any(Function),
			expect.any(Object),
		);
	});

	it('should fetch data when preferences are expired', () => {
		// Setup: User is logged in but preferences are expired
		mockUseUser.mockReturnValue(mockLoggedInUser);
		mockUseAtom.mockReturnValue([
			mockExpiredEmailPreferences,
			mockSetEmailPreferences,
		]);

		// Execute
		renderHook(() => useEmailPreferences());

		// Verify
		expect(mockUseSWR).toHaveBeenCalledWith(
			`getEmailPreferencesStatus/${mockUserId}`,
			expect.any(Function),
			expect.any(Object),
		);
	});

	it('should fetch data when user is logged in but has no preferences', () => {
		// Setup: User is logged in but has no preferences
		mockUseUser.mockReturnValue(mockLoggedInUser);
		mockUseAtom.mockReturnValue([null, mockSetEmailPreferences]);

		// Execute
		renderHook(() => useEmailPreferences());

		// Verify
		expect(mockUseSWR).toHaveBeenCalledWith(
			`getEmailPreferencesStatus/${mockUserId}`,
			expect.any(Function),
			expect.any(Object),
		);
	});

	it('should update preferences when API returns data', () => {
		// Setup: User is logged in but has no preferences
		mockUseUser.mockReturnValue(mockLoggedInUser);
		mockUseAtom.mockReturnValue([null, mockSetEmailPreferences]);

		// Mock SWR to call onSuccess with data
		mockUseSWR.mockImplementation((key: any, fetcher: any, options: any) => {
			if (options?.onSuccess) {
				options.onSuccess({ subscriptions: mockSubscriptions });
			}
			return {
				data: { subscriptions: mockSubscriptions },
				error: null,
				isLoading: false,
				isValidating: false,
				mutate: vi.fn(),
			};
		});

		// Execute
		renderHook(() => useEmailPreferences());

		// Verify
		expect(mockSetEmailPreferences).toHaveBeenCalledWith(
			expect.objectContaining({
				userId: mockUserId,
				expiry: expect.any(Number),
				subscriptions: expect.arrayContaining([
					expect.objectContaining({
						subscriptionGroupName: 'daily-newsletters-1',
					}),
					expect.objectContaining({
						subscriptionGroupName: 'marketing-emails-1',
					}),
				]),
			}),
		);
	});

	it('should not update preferences when API returns data but user is not logged in', () => {
		// Setup: User is not logged in
		mockUseUser.mockReturnValue(mockLoggedOutUser);
		mockUseAtom.mockReturnValue([null, mockSetEmailPreferences]);

		// Mock SWR to call onSuccess with data
		mockUseSWR.mockImplementation((key: any, fetcher: any, options: any) => {
			if (options?.onSuccess) {
				options.onSuccess({ subscriptions: mockSubscriptions });
			}
			return {
				data: { subscriptions: mockSubscriptions },
				error: null,
				isLoading: false,
				isValidating: false,
				mutate: vi.fn(),
			};
		});

		// Execute
		renderHook(() => useEmailPreferences());

		// Verify
		expect(mockSetEmailPreferences).not.toHaveBeenCalled();
	});

	it('should correctly merge subscriptions when updating', () => {
		// Setup: User is logged in with existing preferences
		mockUseUser.mockReturnValue(mockLoggedInUser);

		const existingSubscriptions: SubscriptionGroupWithId[] = [
			{
				subscriptionGroupName: 'daily-newsletters-1',
				subscriptionGroupId: '123',
				isSubscribed: false, // Different from new data
				attributes: {
					location: 'Chicago', // Different from new data
				},
			},
		];

		mockUseAtom.mockReturnValue([
			{
				userId: mockUserId,
				expiry: Date.now() + 900000,
				subscriptions: existingSubscriptions,
			},
			mockSetEmailPreferences,
		]);

		// Execute
		const { result } = renderHook(() => useEmailPreferences());

		// Call updateSubscriptions with new data
		act(() => {
			result.current.updateSubscriptions(mockSubscriptions);
		});

		// Verify merged subscriptions
		expect(mockSetEmailPreferences).toHaveBeenCalledWith(
			expect.objectContaining({
				subscriptions: expect.arrayContaining([
					expect.objectContaining({
						subscriptionGroupName: 'daily-newsletters-1',
						isSubscribed: true, // Updated from new data
						attributes: {
							location: 'New York', // Updated from new data
						},
					}),
					expect.objectContaining({
						subscriptionGroupName: 'marketing-emails-1',
						isSubscribed: false,
					}),
				]),
			}),
		);
	});

	it('should not update subscriptions when user is not logged in', () => {
		// Setup: User is not logged in
		mockUseUser.mockReturnValue(mockLoggedOutUser);
		mockUseAtom.mockReturnValue([null, mockSetEmailPreferences]);

		// Execute
		const { result } = renderHook(() => useEmailPreferences());

		// Call updateSubscriptions
		act(() => {
			result.current.updateSubscriptions(mockSubscriptions);
		});

		// Verify
		expect(mockSetEmailPreferences).not.toHaveBeenCalled();
	});

	it('should use DEFAULT_SUBSCRIPTIONS when no previous subscriptions exist', () => {
		// Setup: User is logged in but has no subscriptions
		mockUseUser.mockReturnValue(mockLoggedInUser);
		mockUseAtom.mockReturnValue([
			{
				userId: mockUserId,
				expiry: Date.now() + 900000,
				subscriptions: null, // No existing subscriptions
			},
			mockSetEmailPreferences,
		]);

		// New subscriptions to update with
		const newSubscriptions: SubscriptionGroupWithId[] = [
			{
				subscriptionGroupName: 'marketing-emails-1',
				subscriptionGroupId: '456',
				isSubscribed: true, // Different from DEFAULT_SUBSCRIPTIONS
			},
		];

		// Execute
		const { result } = renderHook(() => useEmailPreferences());

		// Call updateSubscriptions
		act(() => {
			result.current.updateSubscriptions(newSubscriptions);
		});

		// Verify DEFAULT_SUBSCRIPTIONS are used and merged with new data
		expect(mockSetEmailPreferences).toHaveBeenCalledWith(
			expect.objectContaining({
				subscriptions: expect.arrayContaining([
					expect.objectContaining({
						subscriptionGroupName: 'daily-newsletters-1',
						isSubscribed: false, // From DEFAULT_SUBSCRIPTIONS
					}),
					expect.objectContaining({
						subscriptionGroupName: 'marketing-emails-1',
						isSubscribed: true, // From new subscriptions
					}),
				]),
			}),
		);
	});
});
