import { useAtom, useAtomValue } from 'jotai';
import { getLocationPointByPlaceId } from '@repo/dal/locations/point';
import {
	wxuPreferredLocationAtom,
	type WxuPreferredLocationData,
} from '../atoms/preferredLocation';
import { primaryLocationGetAtom } from '../atoms/preferences/location';
import { wxuUserRecentLocationsAtom } from '../atoms/wxu/user';
import useSWR from 'swr';
import { useParams } from 'next/navigation';

/**
 * Manages the user's preferred location. Location data is persisted to
 * localStorage.
 */
export function usePreferredLocation() {
	const params = useParams();
	const locale = params?.locale as string;
	const [preferredLocation, setPreferredLocation] = useAtom(
		wxuPreferredLocationAtom,
	);
	const primaryPlaceID = useAtomValue(primaryLocationGetAtom)?.placeID;
	const recentPlaceID = useAtomValue(wxuUserRecentLocationsAtom)?.[0];
	const preferredPlaceID = preferredLocation?.value?.placeId;
	const primaryOrRecentID = primaryPlaceID || recentPlaceID;

	const shouldFetch =
		!!primaryOrRecentID && primaryOrRecentID !== preferredPlaceID;

	// primary or recent does not match preferred, get new data
	const locationFetcher = (placeId: string) => {
		return getLocationPointByPlaceId(placeId, locale);
	};
	useSWR(
		shouldFetch ? ['location-data', primaryOrRecentID] : null,
		() => locationFetcher(primaryOrRecentID!),
		{
			revalidateOnFocus: false,
			dedupingInterval: 300000, // 5 minutes
			onSuccess: (data) => {
				const locationData: WxuPreferredLocationData = {
					updatedAt: Date.now(),
					value: {
						countryCode: data?.location.countryCode,
						postalCode: data?.location.postalCode,
						placeId: data?.location.placeId,
					},
				};
				setPreferredLocation(locationData);
			},
		},
	);

	return preferredLocation;
}
