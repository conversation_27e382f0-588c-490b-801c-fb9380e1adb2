import { useAtom } from 'jotai/index';
import useSWR from 'swr';
import { SubscriptionGroupWithId } from '@repo/dal/user/emailPreferences/types';
import { getEmailPreferencesStatus } from '@repo/dal/user/emailPreferences/getEmailPreferencesStatus';
import { emailPreferencesAtom } from '../atoms/emailPreferences';
import { useUser } from '@repo/user/hooks/useUser';
import { DEFAULT_SUBSCRIPTIONS } from '@repo/dal/user/emailPreferences/const';
import { useCallback } from 'react';

function normalizeSubscriptions(
	subscriptions: SubscriptionGroupWithId[] | null | undefined,
	previousSubscriptions?: SubscriptionGroupWithId[] | null,
) {
	const existingSubscriptions = previousSubscriptions?.length
		? previousSubscriptions
		: DEFAULT_SUBSCRIPTIONS;
	const subscriptionsMap: Record<string, SubscriptionGroupWithId> = {};

	if (!subscriptions) return existingSubscriptions;

	existingSubscriptions?.forEach((sub) => {
		subscriptionsMap[sub.subscriptionGroupName] = sub;
	});
	subscriptions.forEach((sub) => {
		subscriptionsMap[sub.subscriptionGroupName] = sub;
	});

	return Object.values(subscriptionsMap);
}

export const useEmailPreferences = () => {
	const [emailPreferences, setEmailPreferences] = useAtom(emailPreferencesAtom);
	const { user } = useUser();
	const userId = user?.userID || null;
	const userLoggedIn = user?.isUserLoggedIn || false;

	const updateSubscriptions = useCallback(
		(subscriptions: SubscriptionGroupWithId[]) => {
			if (!userId) return;

			setEmailPreferences({
				userId,
				expiry: Date.now() + 15 * 60000, // 15 minutes
				subscriptions: normalizeSubscriptions(
					subscriptions,
					emailPreferences?.subscriptions,
				),
			});
		},
		[userId, setEmailPreferences, emailPreferences?.subscriptions],
	);

	const hasValidUserPreferences =
		emailPreferences &&
		emailPreferences.userId === userId &&
		emailPreferences.expiry &&
		emailPreferences.expiry > Date.now();

	useSWR(
		// don't fetch if user not logged in or we already have valid unexpired preferences
		!userId || !userLoggedIn || hasValidUserPreferences
			? undefined
			: `getEmailPreferencesStatus/${userId}`,
		() => getEmailPreferencesStatus(),
		{
			revalidateOnFocus: false,
			revalidateOnReconnect: false,
			refreshInterval: 0,
			errorRetryCount: 2,
			onSuccess: (preference) => {
				if (!userId || !preference) return;

				updateSubscriptions(preference.subscriptions);
			},
		},
	);

	const subscriptions =
		userId && emailPreferences ? emailPreferences.subscriptions : null;

	return { subscriptions, updateSubscriptions };
};
