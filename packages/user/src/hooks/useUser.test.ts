import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { useUser } from './useUser';

// Mock dependencies
vi.mock('@repo/navigation/hooks/usePageNavigation', () => ({
	usePageNavigation: vi.fn(() => ({
		refresh: vi.fn(),
	})),
}));

// Mock cookies-next
vi.mock('cookies-next', () => ({
	useGetCookie: vi.fn(),
	deleteCookie: vi.fn(),
	getCookie: vi.fn(),
	setCookie: vi.fn(),
}));

// Mock Jotai
vi.mock('jotai', () => {
	const actualJotai = vi.importActual('jotai');
	return {
		...actualJotai,
		useAtom: vi.fn(),
		useAtomValue: vi.fn(),
		atom: vi.fn(() => ({})),
	};
});

// Mock SWR
vi.mock('swr', () => {
	return {
		default: vi.fn(),
		mutate: vi.fn(),
	};
});

// Mock SWR/immutable
vi.mock('swr/immutable', () => {
	return {
		default: vi.fn(),
	};
});

// Mock atoms
vi.mock('../atoms', () => ({
	userProfileAtom: {},
}));

// Mock wxu atoms
vi.mock('../atoms/wxu/user', () => ({
	userAccountAtom: {},
	userHasLoggedInBeforeAtom: {},
	wxuUserAccountUserAccountResponseAtom: {},
	wxuUserAnonymousIdAtom: {},
}));

// Mock uuid
vi.mock('uuid', () => ({
	v4: vi.fn(() => 'mock-uuid-v4'),
}));

// Import mocked atoms
import { userProfileAtom } from '../atoms';

// Mock upsxClient
vi.mock('../utils/upsxClient', () => ({
	upsxClient: {
		account: {
			getAccountId: vi.fn(),
			getAccount: vi.fn(),
		},
		auth: {
			logout: vi.fn(),
		},
		purchase: {
			getPurchaseToken: vi.fn(),
		},
	},
}));

// Import mocked modules
import { usePageNavigation } from '@repo/navigation/hooks/usePageNavigation';
import { useAtom, useAtomValue } from 'jotai';
import { upsxClient } from '../utils/upsxClient';
import useSWR, { mutate } from 'swr';
import useSWRImmutable from 'swr/immutable';
import { v4 } from 'uuid';
import {
	wxuUserAccountUserAccountResponseAtom,
	wxuUserAnonymousIdAtom,
} from '../atoms/wxu/user';
import { useGetCookie, deleteCookie, getCookie, setCookie } from 'cookies-next';

describe('useUser hook', () => {
	// Mock data
	const mockUserId = 'test-user-id';
	const mockAccount = {
		userID: 'test-user-id',
		email: '<EMAIL>',
		firstName: 'Test',
		lastName: 'User',
		emailVerified: true,
		hasPassword: true,
		isAccountLocked: false,
		isFirstLogin: false,
		hasAppleSSO: false,
		hasGoogleSSO: false,
	};

	// Mock state setters
	const setUserProfile = vi.fn();
	const setWxuUserAnonymousIdAtom = vi.fn();
	const setWxuUserAccount = vi.fn();

	// Mock navigation
	const mockNavigation = { refresh: vi.fn() };

	// Mock SWR hooks
	const mockUseSWR = {
		data: mockAccount,
		error: null,
		isLoading: false,
	};

	const mockUserIdSWR = {
		data: { userID: mockUserId },
		error: null,
		isLoading: false,
	};

	// Mock cookie functions - this is the function returned by useGetCookie()
	const mockGetCookie = vi.fn((name: string) => {
		if (name === 'twc-user') {
			return '0'; // Default to standard tier
		}
		return null;
	});

	beforeEach(() => {
		// Reset mocks
		vi.resetAllMocks();

		// Setup default mock implementations
		(useAtom as any).mockImplementation((atom: unknown) => {
			// For userProfileAtom
			if (atom === userProfileAtom) {
				return [null, setUserProfile];
			}
			// For wxuUserAnonymousIdAtom
			if (atom === wxuUserAnonymousIdAtom) {
				return [null, setWxuUserAnonymousIdAtom];
			}
			// For wxuUserAccountUserAccountResponseAtom
			if (atom === wxuUserAccountUserAccountResponseAtom) {
				return [null, setWxuUserAccount];
			}
			return [null, vi.fn()];
		});

		// Mock useAtomValue to return null for userAccountAtom
		(useAtomValue as any).mockReturnValue(null);

		(usePageNavigation as any).mockReturnValue(mockNavigation);

		// Mock SWR hooks
		(useSWR as any).mockImplementation((key: string | null) => {
			if (key === 'upsx/account') {
				return mockUseSWR;
			}
			return { data: null, error: null, isLoading: false };
		});

		// Mock SWRImmutable for user ID
		(useSWRImmutable as any).mockImplementation((key: string | null) => {
			if (key === 'upsx/id') {
				return mockUserIdSWR;
			}
			return { data: undefined, error: null, isLoading: false };
		});

		// Default API responses
		(upsxClient.account.getAccountId as any).mockResolvedValue({
			userID: mockUserId,
		});

		// Default API responses
		(upsxClient.account.getAccountId as any).mockResolvedValue(mockUserId);
		(upsxClient.account.getAccount as any).mockResolvedValue(mockAccount);
		(upsxClient.auth.logout as any).mockResolvedValue(undefined);

		// Setup the cookie mock functions for all tests
		(useGetCookie as any).mockReturnValue(mockGetCookie);

		// Setup the syncGetCookies mock (the imported getCookie function)
		// Use type assertion to bypass TypeScript's type checking for mocks
		(getCookie as any).mockImplementation((name: string) => {
			if (name === 'twc-user') {
				return '0'; // Default to standard tier
			}
			if (name === 'ANON_C') {
				return null; // Default to no anonymous cookie
			}
			return null;
		});

		// Setup setCookie mock
		(setCookie as any).mockImplementation(() => {});
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	describe('Initial state', () => {
		it('should start with error=null', () => {
			const { result } = renderHook(() => useUser());
			expect(result.current.error).toBe(null);
		});

		it('should return user object with correct structure', () => {
			const { result } = renderHook(() => useUser());
			expect(result.current.user).toEqual({
				userID: mockUserId,
				isUserLoggedIn: true,
				subscriptionTier: 0,
				isUserPremium: false,
			});
		});

		it('should mark user as premium when tier > standard', () => {
			// Mock premium tier cookie
			(mockGetCookie as any).mockReturnValueOnce('2'); // Premium tier

			const { result } = renderHook(() => useUser());
			expect(result.current.user.isUserPremium).toBe(true);

			// Reset for other tests
			mockGetCookie.mockImplementation((name) => {
				if (name === 'twc-user') {
					return '0'; // Standard tier
				}
				return null;
			});
		});

		it('should handle no cookie case', () => {
			// Mock no cookie
			mockGetCookie.mockImplementation(() => null);

			// Mock useSWRImmutable to return null data
			(useSWRImmutable as any).mockImplementation(() => {
				return { data: null, error: null, isLoading: false };
			});

			const { result } = renderHook(() => useUser());
			expect(result.current.user).toEqual({
				userID: '', // The hook defaults to empty string when no userID is available
				isUserLoggedIn: false,
				subscriptionTier: -1,
				isUserPremium: false,
			});

			// Reset for other tests
			mockGetCookie.mockImplementation((name) => {
				if (name === 'twc-user') {
					return '0'; // Standard tier
				}
				return null;
			});

			// Reset SWRImmutable mock
			(useSWRImmutable as any).mockImplementation((key: string | null) => {
				if (key === 'upsx/id') {
					return mockUserIdSWR;
				}
				return { data: undefined, error: null, isLoading: false };
			});
		});
	});

	describe('SWR integration', () => {
		it('should use useSWR for user ID verification when cookie exists', () => {
			// Make sure cookie is set
			mockGetCookie.mockImplementation((name) => {
				if (name === 'twc-user') {
					return '0'; // Standard tier
				}
				return null;
			});

			// Reset SWRImmutable mock to track new calls
			(useSWRImmutable as any).mockClear();

			renderHook(() => useUser());

			// Check that useSWRImmutable was called with the correct key
			expect(useSWRImmutable).toHaveBeenCalledWith(
				'upsx/id',
				expect.any(Function),
				expect.objectContaining({
					shouldRetryOnError: expect.any(Function),
				}),
			);
		});

		it('should not fetch user ID when cookie is null', () => {
			// Mock no cookie
			mockGetCookie.mockImplementation(() => null);

			// Reset SWRImmutable mock to track new calls
			(useSWRImmutable as any).mockClear();

			renderHook(() => useUser());

			// Check that useSWRImmutable was called with null
			expect(useSWRImmutable).toHaveBeenCalledWith(
				null,
				expect.any(Function),
				expect.any(Object),
			);

			// Reset for other tests
			mockGetCookie.mockImplementation((name) => {
				if (name === 'twc-user') {
					return '0'; // Standard tier
				}
				return null;
			});
		});

		it('should use useSWR for account data when userProfile is null and userId exists', () => {
			// Make sure cookie is set
			mockGetCookie.mockImplementation((name) => {
				if (name === 'twc-user') {
					return '0'; // Standard tier
				}
				return null;
			});

			// Setup conditions for account fetch
			(useAtom as any).mockImplementation((atom: unknown) => {
				// For userProfileAtom
				if (atom === userProfileAtom) {
					return [null, setUserProfile]; // userProfile is null
				}
				// For wxuUserAnonymousIdAtom
				if (atom === wxuUserAnonymousIdAtom) {
					return [null, setWxuUserAnonymousIdAtom];
				}
				// For wxuUserAccountUserAccountResponseAtom
				if (atom === wxuUserAccountUserAccountResponseAtom) {
					return [null, setWxuUserAccount];
				}
				return [null, vi.fn()];
			});

			// Reset SWR mocks to track new calls
			(useSWR as any).mockClear();
			(useSWRImmutable as any).mockClear();

			// Make sure useSWRImmutable returns a valid userID
			(useSWRImmutable as any).mockImplementation((key: string | null) => {
				if (key === 'upsx/id') {
					return {
						data: { userID: mockUserId },
						error: null,
						isLoading: false,
					};
				}
				return { data: undefined, error: null, isLoading: false };
			});

			renderHook(() => useUser());

			// Check that useSWR was called with the correct key for account data
			expect(useSWR).toHaveBeenCalledWith(
				'upsx/account',
				expect.any(Function),
				expect.objectContaining({
					shouldRetryOnError: expect.any(Function),
					onSuccess: expect.any(Function),
				}),
			);
		});

		it('should not fetch account data when userProfile exists', () => {
			// Setup conditions where userProfile already exists
			(useAtom as any).mockImplementation(() => {
				return [mockAccount, setUserProfile]; // userProfile exists
			});

			renderHook(() => useUser());

			// Check if useSWR was called with null for the account fetch
			const accountFetchCall = (useSWR as any).mock.calls.find(
				(call: any[]) => call[1] && call[1].toString().includes('getAccount()'),
			);
			expect(accountFetchCall[0]).toBe(null);
		});

		it('should not fetch account data when userId is null', () => {
			// Setup conditions where userId is null
			(useAtom as any).mockImplementation(() => {
				return [null, setUserProfile]; // userProfile is null
			});

			// Mock useSWRImmutable to return null userId
			(useSWRImmutable as any).mockImplementation(() => {
				return { data: undefined, error: null, isLoading: false };
			});

			// Reset useSWR mock
			(useSWR as any).mockClear();

			// Make sure useSWR is called with null for account data
			(useSWR as any).mockImplementation((key: string | null) => {
				return { data: null, error: null, isLoading: false };
			});

			renderHook(() => useUser());

			// Check that useSWR was called with null for account data
			expect(useSWR).toHaveBeenCalledWith(
				null,
				expect.any(Function),
				expect.any(Object),
			);
		});

		it('should update userProfile when account data is fetched successfully', () => {
			// Make sure cookie is set
			mockGetCookie.mockImplementation((name) => {
				if (name === 'twc-user') {
					return '0'; // Standard tier
				}
				return null;
			});

			// Setup conditions for account fetch
			(useAtom as any).mockImplementation((atom: unknown) => {
				// For userProfileAtom
				if (atom === userProfileAtom) {
					return [null, setUserProfile]; // userProfile is null
				}
				// For wxuUserAnonymousIdAtom
				if (atom === wxuUserAnonymousIdAtom) {
					return ['existing-uuid', setWxuUserAnonymousIdAtom]; // Prevent UUID generation
				}
				// For wxuUserAccountUserAccountResponseAtom
				if (atom === wxuUserAccountUserAccountResponseAtom) {
					return [null, setWxuUserAccount];
				}
				return [null, vi.fn()];
			});

			// Reset mocks
			setUserProfile.mockClear();
			setWxuUserAccount.mockClear();

			// Mock useSWR to call onSuccess directly
			(useSWR as any).mockImplementation(
				(key: string | null, fetcher: any, options: any) => {
					if (key === 'upsx/account' && options && options.onSuccess) {
						// Call onSuccess directly with mock data
						options.onSuccess(mockAccount);
					}
					return { data: mockAccount, error: null, isLoading: false };
				},
			);

			renderHook(() => useUser());

			// Check that setUserProfile was called with the mock account
			expect(setUserProfile).toHaveBeenCalledWith(mockAccount);
			// Check that setWxuUserAccount was also called with the mock account
			expect(setWxuUserAccount).toHaveBeenCalledWith(mockAccount);
		});

		it('should retry on errors that are not APIError', () => {
			// Make sure cookie is set
			mockGetCookie.mockImplementation((name) => {
				if (name === 'twc-user') {
					return '0'; // Standard tier
				}
				return null;
			});

			// Setup conditions for account fetch
			(useAtom as any).mockImplementation(() => {
				return [null, setUserProfile]; // userProfile is null
			});

			// Define the shouldRetryOnError function that's used in the hook
			const shouldRetryOnErrorFn = (error: Error) => error.name !== 'APIError';

			renderHook(() => useUser());

			// Test the shouldRetryOnError function directly
			expect(shouldRetryOnErrorFn(new Error('Generic error'))).toBe(true);

			// Should not retry on APIError
			const apiError = new Error('API Error');
			apiError.name = 'APIError';
			expect(shouldRetryOnErrorFn(apiError)).toBe(false);
		});
	});

	describe('wxuUserAccount integration', () => {
		it('should use wxuUserAccount when userProfile is null', () => {
			// Setup conditions where userProfile is null but wxuUserAccount exists
			(useAtom as any).mockImplementation((atom: unknown) => {
				if (atom === userProfileAtom) {
					return [null, setUserProfile]; // userProfile is null
				}
				if (atom === wxuUserAccountUserAccountResponseAtom) {
					return [mockAccount, setWxuUserAccount]; // wxuUserAccount exists
				}
				return [null, vi.fn()];
			});

			renderHook(() => useUser());
			expect(setUserProfile).toHaveBeenCalledWith(mockAccount);
		});

		it('should not use wxuUserAccount when userProfile exists', () => {
			// Setup conditions where userProfile already exists
			(useAtom as any).mockImplementation((atom: unknown) => {
				if (atom === userProfileAtom) {
					return [mockAccount, setUserProfile]; // userProfile exists
				}
				if (atom === wxuUserAccountUserAccountResponseAtom) {
					return [
						{ ...mockAccount, firstName: 'Different' },
						setWxuUserAccount,
					]; // Different wxuUserAccount
				}
				return [null, vi.fn()];
			});

			renderHook(() => useUser());
			expect(setUserProfile).not.toHaveBeenCalled();
		});

		it('should not use wxuUserAccount when it is null', () => {
			// Setup conditions where both userProfile and wxuUserAccount are null
			(useAtom as any).mockImplementation((atom: unknown) => {
				if (atom === userProfileAtom) {
					return [null, setUserProfile]; // userProfile is null
				}
				if (atom === wxuUserAccountUserAccountResponseAtom) {
					return [null, setWxuUserAccount]; // wxuUserAccount is also null
				}
				return [null, vi.fn()];
			});

			renderHook(() => useUser());
			expect(setUserProfile).not.toHaveBeenCalled();
		});
	});

	describe('wxuUserAnonymousId integration', () => {
		it('should generate a new UUID when wxuUserAnonymousId is null', () => {
			// Setup conditions where wxuUserAnonymousId is null
			(useAtom as any).mockImplementation((atom: unknown) => {
				// For wxuUserAnonymousIdAtom
				if (atom === wxuUserAnonymousIdAtom) {
					return [null, setWxuUserAnonymousIdAtom];
				}
				return [null, vi.fn()];
			});

			renderHook(() => useUser());

			// Verify that v4 was called to generate a UUID
			expect(v4).toHaveBeenCalled();

			// Verify that the generated UUID was set in the atom
			expect(setWxuUserAnonymousIdAtom).toHaveBeenCalledWith('mock-uuid-v4');
		});

		it('should not generate a UUID when wxuUserAnonymousId already exists', () => {
			// Setup conditions where wxuUserAnonymousId already exists
			(useAtom as any).mockImplementation((atom: unknown) => {
				// For wxuUserAnonymousIdAtom
				if (atom === wxuUserAnonymousIdAtom) {
					return ['existing-uuid', setWxuUserAnonymousIdAtom];
				}
				return [null, vi.fn()];
			});

			renderHook(() => useUser());

			// Verify that v4 was not called
			expect(v4).not.toHaveBeenCalled();

			// Verify that the atom was not updated
			expect(setWxuUserAnonymousIdAtom).not.toHaveBeenCalled();
		});

		it('should set ANON_C cookie when it does not exist', () => {
			// Setup conditions where ANON_C cookie does not exist
			(getCookie as any).mockImplementation((name: string) => {
				if (name === 'twc-user') {
					return '0';
				}
				if (name === 'ANON_C') {
					return null; // No ANON_C cookie
				}
				return null;
			});

			// Setup wxuUserAnonymousId as null to trigger UUID generation
			(useAtom as any).mockImplementation((atom: unknown) => {
				if (atom === wxuUserAnonymousIdAtom) {
					return [null, setWxuUserAnonymousIdAtom];
				}
				return [null, vi.fn()];
			});

			renderHook(() => useUser());

			// Verify that setCookie was called with the generated UUID
			expect(setCookie).toHaveBeenCalledWith('ANON_C', 'mock-uuid-v4', {
				path: '/',
				domain: '.weather.com',
				secure: true,
			});
		});

		it('should not set ANON_C cookie when it already exists', () => {
			// Setup conditions where ANON_C cookie already exists
			(getCookie as any).mockImplementation((name: string) => {
				if (name === 'twc-user') {
					return '0';
				}
				if (name === 'ANON_C') {
					return 'existing-anon-id'; // ANON_C cookie exists
				}
				return null;
			});

			// Setup wxuUserAnonymousId as existing to prevent UUID generation
			(useAtom as any).mockImplementation((atom: unknown) => {
				if (atom === wxuUserAnonymousIdAtom) {
					return ['existing-anon-id', setWxuUserAnonymousIdAtom];
				}
				return [null, vi.fn()];
			});

			renderHook(() => useUser());

			// Verify that setCookie was not called
			expect(setCookie).not.toHaveBeenCalled();
		});

		it('should sync wxuUserAnonymousId with client cookie when they mismatch', () => {
			// Setup conditions where wxuUserAnonymousId and client cookie mismatch
			(getCookie as any).mockImplementation((name: string) => {
				if (name === 'twc-user') {
					return '0';
				}
				if (name === 'ANON_C') {
					return 'cookie-anon-id'; // Different from atom value
				}
				return null;
			});

			// Setup wxuUserAnonymousId with different value
			(useAtom as any).mockImplementation((atom: unknown) => {
				if (atom === wxuUserAnonymousIdAtom) {
					return ['atom-anon-id', setWxuUserAnonymousIdAtom]; // Different from cookie
				}
				return [null, vi.fn()];
			});

			renderHook(() => useUser());

			// Verify that the atom was updated to match the cookie
			expect(setWxuUserAnonymousIdAtom).toHaveBeenCalledWith('cookie-anon-id');
		});

		it('should use existing client cookie when wxuUserAnonymousId is null', () => {
			// Setup conditions where client cookie exists but wxuUserAnonymousId is null
			(getCookie as any).mockImplementation((name: string) => {
				if (name === 'twc-user') {
					return '0';
				}
				if (name === 'ANON_C') {
					return 'existing-cookie-id';
				}
				return null;
			});

			// Setup wxuUserAnonymousId as null
			(useAtom as any).mockImplementation((atom: unknown) => {
				if (atom === wxuUserAnonymousIdAtom) {
					return [null, setWxuUserAnonymousIdAtom];
				}
				return [null, vi.fn()];
			});

			renderHook(() => useUser());

			// Verify that the atom was set to the existing cookie value
			expect(setWxuUserAnonymousIdAtom).toHaveBeenCalledWith(
				'existing-cookie-id',
			);

			// Verify that v4 was not called since we used the existing cookie
			expect(v4).not.toHaveBeenCalled();
		});
	});

	describe('DISCONNECTED_user property', () => {
		// No need for a separate beforeEach here, we'll reset the mock in each test

		it('should return DISCONNECTED_user object with correct structure', () => {
			// Mock the syncGetCookies function
			(getCookie as any).mockReturnValue('0'); // Standard tier

			const { result } = renderHook(() => useUser());
			expect(result.current.DISCONNECTED_user).toEqual({
				isUserLoggedIn: true,
				subscriptionTier: 0,
				isUserPremium: false,
			});
		});

		it('should mark DISCONNECTED_user as premium when tier > standard', () => {
			// Mock premium tier cookie
			(getCookie as any).mockReturnValue('2'); // Premium tier

			const { result } = renderHook(() => useUser());
			expect(result.current.DISCONNECTED_user!.isUserPremium).toBe(true);
		});

		it('should handle no cookie case for DISCONNECTED_user', () => {
			// Mock no cookie
			(getCookie as any).mockReturnValue(null);

			const { result } = renderHook(() => useUser());
			expect(result.current.DISCONNECTED_user!).toEqual({
				isUserLoggedIn: false,
				subscriptionTier: -1,
				isUserPremium: false,
			});
		});

		it('should have DISCONNECTED_user with different state than regular user', () => {
			// Mock different values for useGetCookie and syncGetCookies
			// Regular user cookie (from useGetCookie) is standard tier
			mockGetCookie.mockImplementation((name: string) => {
				if (name === 'twc-user') {
					return '0'; // Standard tier for regular user
				}
				return null;
			});

			// DISCONNECTED_user cookie (from syncGetCookies/getCookie) is premium tier
			// Use type assertion to bypass TypeScript's type checking for mocks
			(getCookie as any).mockImplementation((name: string) => {
				if (name === 'twc-user') {
					return '2'; // Premium tier for DISCONNECTED_user
				}
				return null;
			});

			const { result } = renderHook(() => useUser());

			// Regular user should be standard tier
			expect(result.current.user).toEqual({
				userID: mockUserId,
				isUserLoggedIn: true,
				subscriptionTier: 0,
				isUserPremium: false,
			});

			// DISCONNECTED_user should be premium tier
			expect(result.current.DISCONNECTED_user!).toEqual({
				isUserLoggedIn: true,
				subscriptionTier: 2,
				isUserPremium: true,
			});
		});

		it('should have DISCONNECTED_user logged out while regular user is logged in', () => {
			// Regular user cookie (from useGetCookie) is standard tier (logged in)
			mockGetCookie.mockImplementation((name: string) => {
				if (name === 'twc-user') {
					return '0'; // Standard tier for regular user (logged in)
				}
				return null;
			});

			// DISCONNECTED_user cookie (from syncGetCookies/getCookie) is none (logged out)
			// Use mockReturnValue instead of mockImplementation to bypass TypeScript's type checking
			(getCookie as any).mockReturnValue('-1'); // None tier for DISCONNECTED_user (logged out)

			const { result } = renderHook(() => useUser());

			// Regular user should be logged in
			expect(result.current.user.isUserLoggedIn).toBe(true);

			// DISCONNECTED_user should be logged out
			expect(result.current.DISCONNECTED_user!.isUserLoggedIn).toBe(false);
		});

		it('should have DISCONNECTED_user logged in while regular user is logged out', () => {
			// Regular user cookie (from useGetCookie) is none tier (logged out)
			// Use mockReturnValue instead of mockImplementation to bypass TypeScript's type checking
			(mockGetCookie as any).mockReturnValue('-1'); // None tier for regular user (logged out)

			// DISCONNECTED_user cookie (from syncGetCookies/getCookie) is standard tier (logged in)
			// Use type assertion to bypass TypeScript's type checking for mocks
			(getCookie as any).mockImplementation((name: string) => {
				if (name === 'twc-user') {
					return '0'; // Standard tier for DISCONNECTED_user (logged in)
				}
				return null;
			});

			const { result } = renderHook(() => useUser());

			// Regular user should be logged out
			expect(result.current.user.isUserLoggedIn).toBe(false);

			// DISCONNECTED_user should be logged in
			expect(result.current.DISCONNECTED_user!.isUserLoggedIn).toBe(true);
		});
	});

	describe('logout function', () => {
		it('should call upsxClient.auth.logout', async () => {
			const { result } = renderHook(() => useUser());

			await act(async () => {
				await result.current.logout();
			});

			expect(upsxClient.auth.logout).toHaveBeenCalled();
		});

		it('should clear SWR cache with mutate', async () => {
			const { result } = renderHook(() => useUser());

			await act(async () => {
				await result.current.logout();
			});

			expect(mutate).toHaveBeenCalledWith(expect.any(Function), undefined, {
				revalidate: false,
			});
		});

		it('should clear userProfileAtom', async () => {
			const { result } = renderHook(() => useUser());

			await act(async () => {
				await result.current.logout();
			});

			expect(setUserProfile).toHaveBeenCalledWith(null);
		});

		it('should clear wxuUserAccount', async () => {
			const { result } = renderHook(() => useUser());

			await act(async () => {
				await result.current.logout();
			});

			expect(setWxuUserAccount).toHaveBeenCalledWith(null);
		});

		it('should call router.refresh', async () => {
			const { result } = renderHook(() => useUser());

			await act(async () => {
				await result.current.logout();
			});

			expect(mockNavigation.refresh).toHaveBeenCalledWith({ mpa: true });
		});

		it('should delete the twc-user cookie', async () => {
			const { result } = renderHook(() => useUser());

			await act(async () => {
				await result.current.logout();
			});

			expect(deleteCookie).toHaveBeenCalledWith('twc-user', {
				domain: '.weather.com',
			});
		});

		it('should return true when logout succeeds', async () => {
			const { result } = renderHook(() => useUser());

			let returnValue;
			await act(async () => {
				returnValue = await result.current.logout();
			});

			expect(returnValue).toBe(true);
		});

		it('should handle logout errors', async () => {
			(upsxClient.auth.logout as any).mockRejectedValueOnce(
				new Error('Failed to log out'),
			);

			const { result } = renderHook(() => useUser());

			let returnValue;
			await act(async () => {
				returnValue = await result.current.logout();
			});

			// Wait for error to update
			await waitFor(() => expect(result.current.error).toBeInstanceOf(Error));

			expect(result.current.error?.message).toBe('Failed to log out');
			expect(returnValue).toBe(false);
		});
	});
});
