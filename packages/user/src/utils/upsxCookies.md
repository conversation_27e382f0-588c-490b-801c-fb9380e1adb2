# upsxCookies Utility

## Introduction

The `upsxCookies` utility is a server-side module that provides cookie-based authentication verification for the wx-next platform. It handles JWT validation, subscription status determination, and provides a secure way to verify user authentication status on the server.

This utility is designed to work exclusively on the server side (note the `'server-only'` import) and provides the foundation for server-side authentication checks used by components like the `withUser` HOC.

## Core Functionality

- **Cookie-based Authentication**: Extracts and validates authentication tokens from cookies
- **JWT Validation**: Supports both RS256 (asymmetric) and HMAC (symmetric) JWT validation
- **Subscription Status**: Determines user subscription level based on token payload
- **Development Override**: Supports development testing with cookie overrides

## API Reference

### `getCookieSubStatus()`

Determines the user's subscription status based on authentication cookies.

```typescript
async function getCookieSubStatus(): Promise<string>;
```

**Returns**:

- `string`: The user's subscription status
  - `'none'`: User is not authenticated
  - `'standard'`: User is authenticated with standard access
  - Other values: Premium subscription types based on payload

**Process**:

1. Retrieves authentication tokens from cookies
2. Checks for development override cookie
3. Validates the ID token using RS256
4. Checks if the user is registered
5. Validates premium token using HMAC if present
6. Returns appropriate subscription status

**Example**:

```typescript
import { getCookieSubStatus } from "@repo/user/utils/upsxCookies";

// In a server component or API route
const userStatus = await getCookieSubStatus();
const isAuthenticated = userStatus !== "none";
```

### `getCookieTokens()`

Retrieves authentication tokens from cookies.

```typescript
async function getCookieTokens(): Promise<{
	idToken: string | undefined;
	premium: string | undefined;
}>;
```

**Returns**:

- Object containing:
  - `idToken`: The ID token for authentication (may be undefined)
  - `premium`: The premium token for subscription status (may be undefined)

**Example**:

```typescript
import { getCookieTokens } from "@repo/user/utils/upsxCookies";

// In a server component or API route
const { idToken, premium } = await getCookieTokens();
```

### `validateHMACJWT(token: string, secret: string)`

Validates a JWT token using HMAC SHA-256.

```typescript
async function validateHMACJWT(
	token: string,
	secret: string,
): Promise<any | null>;
```

**Parameters**:

- `token`: The JWT token to validate
- `secret`: The secret key for HMAC validation

**Returns**:

- The decoded JWT payload if valid
- `null` if the token is invalid or verification fails

**Example**:

```typescript
import { validateHMACJWT } from "@repo/user/utils/upsxCookies";

const payload = await validateHMACJWT(token, process.env.UPSX_HMAC as string);
if (payload) {
	// Token is valid
}
```

### `validateRSJWT(token: string, key: string)`

Validates a JWT token using RS256 (asymmetric cryptography).

```typescript
async function validateRSJWT(token: string, key: string): Promise<any | null>;
```

**Parameters**:

- `token`: The JWT token to validate
- `key`: The public key in PEM format for RS256 validation

**Returns**:

- The decoded JWT payload if valid
- `null` if the token is invalid or verification fails

**Example**:

```typescript
import { validateRSJWT } from "@repo/user/utils/upsxCookies";

const payload = await validateRSJWT(token, process.env.UPSX_PEM as string);
if (payload) {
	// Token is valid
}
```

## Integration with Other Components

### withUser HOC

The `upsxCookies` utility is primarily used by the `withUser` HOC to provide authentication status to server components:

```typescript
// From apps/web/components/withUser/index.tsx
export function withUser<P extends WithUserProps>(
  Component: React.ComponentType<P>,
) {
  return async function WithUserComponent(props: Omit<P, keyof WithUserProps>) {
    const userStatus = await getCookieSubStatus();
    const isUserLoggedIn = userStatus && userStatus !== 'none';

    return <Component {...(props as P)} isUserLoggedIn={isUserLoggedIn} />;
  };
}
```

This integration allows server components to access authentication status without client-side code.

### Server-Side Authentication Flow

1. User logs in through client-side authentication
2. Authentication tokens are stored as cookies
3. Server components use `withUser` HOC which calls `getCookieSubStatus()`
4. `getCookieSubStatus()` validates tokens and determines subscription status
5. Components receive authentication status and render accordingly

## Environment Configuration

The utility requires the following environment variables, which should now be base64 encoded:

- `UPSX_PEM`: Base64 encoded public key in PEM format for RS256 JWT validation
- `UPSX_HMAC`: Base64 encoded secret key for HMAC JWT validation
- `DEV_OVERRIDE_KEY`: (Optional) Base64 encoded key for development override

These should be configured in the project's environment variables. The utility will automatically decode these base64 encoded values before using them.

## Usage Examples

### Server Component Authentication

```typescript
import { withUser } from '@/components/withUser';

const ServerComponent = ({ isUserLoggedIn }) => {
  return (
    <div>
      {isUserLoggedIn ? (
        <p>Welcome back! Here's your personalized content.</p>
      ) : (
        <p>Please log in to see personalized content.</p>
      )}
    </div>
  );
};

export default withUser(ServerComponent);
```

### Subscription Status Checking

```typescript
import { getCookieSubStatus } from '@repo/user/utils/upsxCookies';

async function SubscriptionContent() {
  const subStatus = await getCookieSubStatus();

  if (subStatus === 'none') {
    return <p>Please log in to access content.</p>;
  }

  if (subStatus === 'standard') {
    return <StandardContent />;
  }

  // Premium content for other subscription types
  return <PremiumContent tier={subStatus} />;
}
```

### Development Testing

```typescript
// Set a development cookie to override subscription status
// This can be done in browser devtools
document.cookie =
	'dev={"key":"your-dev-override-key","twcSubs":"premium"}; path=/';
```

## Best Practices

### Security Considerations

- **Environment Variables**: Store JWT keys and secrets securely in environment variables
- **Server-Only Usage**: Never expose JWT validation logic on the client side
- **Token Expiration**: Consider token expiration when designing authentication flows
- **Error Handling**: Implement proper error handling for token validation failures

### Performance Optimization

- **Caching**: Consider caching validation results for frequently accessed routes
- **Minimal Validation**: Only validate tokens when necessary
- **Efficient Crypto**: Use efficient cryptographic operations

### Error Handling

- **Graceful Degradation**: Default to unauthenticated state on validation failures
- **Logging**: Log validation errors for debugging (but not sensitive information)
- **User Experience**: Provide clear feedback when authentication fails

## Related Components

- [`withUser` HOC](../../components/withUser/index.tsx): Server-side authentication wrapper
- [`useUser` Hook](../hooks/useUser.ts): Client-side authentication hook
- [`upsxClient`](./upsxClient.ts): Client for UPSX API communication

## See Also

- [User Module README](../README.md): Complete documentation of the User module
- [Authentication Atoms](../atoms/auth.ts): State management for authentication
- [User Preferences](../atoms/preferences/index.ts): User preference management
