'use client';

import { UPSXClient } from '@twc/upsx-sdk';

/**
 * Create a singleton UPSX client
 *
 * Note: In a real implementation, you would create a proper client
 * with the correct configuration. For this example, we're creating
 * a simplified version that doesn't rely on private properties.
 */
const createUPSXClient = () => {
	return new UPSXClient({
		baseUrl:
			process.env.NEXT_PUBLIC_UPSX_API_URL || 'https://stage-upsx.weather.com',
	});
};

/**
 * Singleton UPSX client instance
 */
export const upsxClient = createUPSXClient();
