import 'server-only';

import { cookies } from 'next/headers';
import { getCookie } from 'cookies-next';
import {
	jwtVerify,
	importSPK<PERSON>,
	type JWTVerifyResult,
	type JWTPayload,
} from 'jose';
import { UserSubscriptionTiers, UserSubscriptionTiersMap } from './consts';

export interface UserData {
	id?: string | null | undefined;
	status: string;
}

export interface UserDataState {
	userData: UserData;
	userID: string | null | undefined;
	isUserLoggedIn: boolean;
	subscriptionTier: UserSubscriptionTiers;
	isUserPremium: boolean;
}

/**
 * Decodes a base64 encoded string to UTF-8
 * @param value The base64 encoded string to decode
 * @returns The decoded string or empty string if decoding fails
 */
function decodeBase64(value: string | undefined): string {
	if (!value) return '';
	try {
		return Buffer.from(value, 'base64').toString('utf-8');
	} catch (error) {
		console.error('Failed to decode base64 string:', error);
		return '';
	}
}

export async function getUserData(): Promise<UserDataState> {
	const userData = await getCookieData();

	const isUserLoggedIn = userData?.status !== 'none';

	const subscriptionTier: UserSubscriptionTiers = isUserLoggedIn
		? UserSubscriptionTiersMap[
				userData?.status as keyof typeof UserSubscriptionTiersMap
			] || UserSubscriptionTiers.standard
		: UserSubscriptionTiers.none;

	const isUserPremium = subscriptionTier > UserSubscriptionTiers.standard;

	return {
		userData,
		userID: userData?.id,
		isUserLoggedIn,
		subscriptionTier,
		isUserPremium,
	};
}

export async function getPremiumCookieData(): Promise<JWTPayload | undefined> {
	const { premium } = await getCookieTokens();

	if (!premium) {
		return undefined;
	}

	const premiumJSON = await validateHMACJWT(
		premium,
		decodeBase64(process.env.UPSX_HMAC),
	);

	return premiumJSON?.payload;
}

export async function getCookieData() {
	const res: {
		id: string | null | undefined;
		status: string;
	} = {
		id: undefined,
		status: 'none',
	};

	const { idToken } = await getCookieTokens();

	/**
	 * Validate idToken jwt using sKey
	 * If Valid, idJson holds [payload] key as object
	 */
	if (!idToken) {
		return res;
	}

	const validTokens: {
		idJSON?: JWTVerifyResult;
		premiumJSON?: JWTPayload;
	} = {};

	validTokens.idJSON = await validateRSJWT(
		idToken,
		decodeBase64(process.env.UPSX_PEM),
	);

	if (!validTokens.idJSON) {
		return res;
	}

	/**
	 * Check the idJSON payload
	 * validate if its registered or not
	 */
	res.status =
		validTokens.idJSON?.payload?.registered === 'true' ? 'standard' : 'none';

	res.id = validTokens.idJSON?.payload?.sub;

	/**
	 * Valid the json and check if it is valid
	 * set the response headers based on if it's valid or not.
	 */
	validTokens.premiumJSON = await getPremiumCookieData();

	if (!validTokens?.premiumJSON?.premium) {
		return res;
	}

	res.status = (validTokens.premiumJSON?.product as string) || res.status;

	return res;
}

export async function getCookieTokens() {
	const idToken = await getCookie('id_token', { cookies });
	const premium = await getCookie('premium', { cookies });

	return {
		idToken,
		premium,
	};
}

export async function validateHMACJWT(
	token: string,
	secret: string,
): Promise<JWTVerifyResult | undefined> {
	try {
		/**
		 * Generate HMAC key for JWT Validation for premium
		 */
		const pKey = await crypto.subtle.importKey(
			'raw',
			new TextEncoder().encode(secret).buffer,
			{
				name: 'HMAC',
				hash: 'SHA-256',
			},
			false,
			['verify'],
		);

		/**
		 * Check if the premium cookie is present
		 * Valid the json and check if it is valid
		 * set the response headers based on if it's valid or not.
		 */
		if (!token) return undefined;
		const json = await jwtVerify(token, pKey);

		return json;
	} catch (_err) {
		return undefined;
	}
}

export async function validateRSJWT(
	token: string,
	key: string,
): Promise<JWTVerifyResult | undefined> {
	try {
		const iKey = await importSPKI(key, 'RS256');
		/**
		 * Check if the premium cookie is present
		 * Valid the json and check if it is valid
		 * set the response headers based on if it's valid or not.
		 */
		if (!token) return undefined;
		const json = await jwtVerify(token, iKey);

		return json;
	} catch (_err) {
		return undefined;
	}
}
