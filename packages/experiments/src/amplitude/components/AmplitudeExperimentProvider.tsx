'use client';

import React, {
	createContext,
	useContext,
	useEffect,
	useState,
	useRef,
} from 'react';
import {
	ExperimentClient,
	ExperimentUser,
	Variant,
	ExperimentConfig,
	Experiment,
	Exposure,
} from '@amplitude/experiment-js-client';
import canUseDOM from '@repo/utils/canUseDOM';
import { wxuUserAnonymousIdAtom } from '@repo/user/atoms/wxu/user';
import { useAtomValue } from 'jotai';
import { useEnrolledInExperimentEvent } from '@repo/analytics/mparticle/hooks/useEnrolledInExperimentEvent';

const apiKey = process.env.NEXT_PUBLIC_AMPLITUDE_EXPERIMENT_API_KEY || '';
const isDevelopmentMode = process.env.VERCEL_TARGET_ENV !== 'production';

/**
 * Custom hook to safely initialize and manage the Amplitude Experiment client
 * This encapsulates the initialization logic and ensures it's only done once
 * in a React-friendly way that works with SSR and concurrent rendering
 *
 * @param trackExposure Function to track experiment exposures
 */
function useAmplitudeExperimentClient(
	trackExposure: (exposure: Exposure) => void,
) {
	const clientRef = useRef<ExperimentClient | null>(null);
	const [isInitialized, setIsInitialized] = useState(false);

	// Initialize only once on client-side
	useEffect(() => {
		// Skip initialization if we're not in a browser, already initialized, or missing API key
		if (!canUseDOM || isInitialized || !apiKey) {
			return;
		}

		try {
			// Configure the experiment client
			const config: ExperimentConfig = {
				debug: isDevelopmentMode,
				fetchTimeoutMillis: 5000,
				retryFetchOnFailure: true,
				fetchOnStart: false, // Control fetch manually
				exposureTrackingProvider: {
					// Include the exposure tracking function in the initial config
					track: (exposure: Exposure) => {
						try {
							// Log the exposure object to understand its structure
							console.debug('[Experiment] Raw Exposure:', exposure);

							// Track the experiment enrollment event
							trackExposure(exposure);

							console.debug('[Experiment] Exposure tracked:', exposure);
						} catch (error) {
							console.error('[Experiment] Failed to track exposure:', error);
						}
					},
				},
			};

			clientRef.current = Experiment.initialize(apiKey, config);
			setIsInitialized(true);
			console.debug('Amplitude Experiment SDK initialized');
		} catch (error) {
			console.error('Failed to initialize Amplitude Experiment SDK:', error);
		}
	}, [isInitialized, trackExposure]);

	return clientRef.current;
}

export interface ExperimentContextType {
	client: ExperimentClient | null;
	variant: (flagKey: string, fallback?: string | Variant) => Variant;
	fetch: (user?: ExperimentUser) => Promise<ExperimentClient | void>;
}

// Create the experiment context
const ExperimentContext = createContext<ExperimentContextType | null>(null);

export interface AmplitudeExperimentProviderProps {
	children: React.ReactNode;
}

/**
 * AmplitudeExperiment component
 * Initializes the Amplitude Experiment SDK and provides experiment context
 * This component should be added to your layout
 */
export const ExperimentProvider: React.FC<AmplitudeExperimentProviderProps> = ({
	children,
}) => {
	const anonId = useAtomValue(wxuUserAnonymousIdAtom);
	const trackEnrolledInExperiment = useEnrolledInExperimentEvent();

	// Use our custom hook to get the client, passing the tracking function
	const client = useAmplitudeExperimentClient(trackEnrolledInExperiment);

	// Fetch experiments on each mount (this is fine)
	useEffect(() => {
		const fetchExperiments = async () => {
			if (client && anonId) {
				const experimentUser = {
					device_id: anonId as string,
					user_properties: {},
				};

				console.debug(
					'[Experiment] Fetching experiments for user:',
					experimentUser,
				);
				await client.fetch(experimentUser);
			}
		};

		fetchExperiments();
	}, [client, anonId]);

	const variant = (flagKey: string, fallback?: string | Variant): Variant => {
		if (!client) {
			return fallback as Variant;
		}
		const retrievedVariant = client.variant(flagKey, fallback);

		console.debug('[Experiment] Current Variant:', retrievedVariant);

		return retrievedVariant;
	};

	const fetch = (user?: ExperimentUser) => {
		if (!client) {
			return Promise.resolve();
		}

		return client.fetch(user);
	};

	const contextValue: ExperimentContextType = {
		client,
		variant,
		fetch,
	};

	return (
		<ExperimentContext.Provider value={contextValue}>
			{children}
		</ExperimentContext.Provider>
	);
};

/**
 * Hook to access experiment context
 * @returns The experiment context
 */
export const useExperimentContext = () => {
	const context = useContext(ExperimentContext);

	if (!context) {
		throw new Error(
			'useExperimentContext must be used within an AmplitudeExperimentProvider',
		);
	}

	return context;
};

export default ExperimentProvider;
