import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { Variant } from '@amplitude/experiment-js-client';
import { useExperiment } from './useExperiment';
import { useExperimentContext } from '../components/AmplitudeExperimentProvider';

// Mock the useExperimentContext hook
vi.mock('../components/AmplitudeExperimentProvider', () => ({
	useExperimentContext: vi.fn(),
}));

const mockUseExperimentContext = vi.mocked(useExperimentContext);

describe('useExperiment', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	afterEach(() => {
		vi.resetAllMocks();
	});

	describe('when experiment context is not available', () => {
		beforeEach(() => {
			// Mock returning null when context is not available
			mockUseExperimentContext.mockReturnValue(null as any);
		});

		it('should return string fallback when provided', () => {
			const result = useExperiment('test-flag', 'fallback-value');
			expect(result).toBe('fallback-value');
		});

		it('should return Variant fallback when provided', () => {
			const fallbackVariant: Variant = {
				value: 'fallback-variant',
				payload: { key: 'value' },
			};

			const result = useExperiment('test-flag', fallbackVariant);
			expect(result).toEqual(fallbackVariant);
		});

		it('should return undefined when no fallback is provided', () => {
			const result = useExperiment('test-flag');
			expect(result).toBeUndefined();
		});
	});

	describe('when experiment context is available', () => {
		const mockVariant = vi.fn();
		const mockExperimentContext = {
			client: null,
			variant: mockVariant,
			fetch: vi.fn(),
		} as any;

		beforeEach(() => {
			mockUseExperimentContext.mockReturnValue(mockExperimentContext);
		});

		it('should call context.variant with correct parameters and return result', () => {
			const expectedVariant: Variant = {
				value: 'test-value',
				payload: { feature: 'enabled' },
			};
			mockVariant.mockReturnValue(expectedVariant);

			const result = useExperiment('test-flag', 'fallback');

			expect(mockVariant).toHaveBeenCalledWith('test-flag', 'fallback');
			expect(result).toEqual(expectedVariant);
		});

		it('should handle string fallback parameter correctly', () => {
			const expectedVariant: Variant = {
				value: 'context-value',
			};
			mockVariant.mockReturnValue(expectedVariant);

			const result = useExperiment('feature-flag', 'default-off');

			expect(mockVariant).toHaveBeenCalledWith('feature-flag', 'default-off');
			expect(result).toEqual(expectedVariant);
		});

		it('should handle Variant fallback parameter correctly', () => {
			const fallbackVariant: Variant = {
				value: 'fallback',
				payload: { default: true },
			};
			const expectedVariant: Variant = {
				value: 'active',
				payload: { feature: 'enabled' },
			};
			mockVariant.mockReturnValue(expectedVariant);

			const result = useExperiment('complex-flag', fallbackVariant);

			expect(mockVariant).toHaveBeenCalledWith('complex-flag', fallbackVariant);
			expect(result).toEqual(expectedVariant);
		});

		it('should handle no fallback parameter correctly', () => {
			const expectedVariant: Variant = {
				value: 'default',
			};
			mockVariant.mockReturnValue(expectedVariant);

			const result = useExperiment('no-fallback-flag');

			expect(mockVariant).toHaveBeenCalledWith('no-fallback-flag', undefined);
			expect(result).toEqual(expectedVariant);
		});

		it('should return variant even when context.variant returns null', () => {
			mockVariant.mockReturnValue(null);

			const result = useExperiment('null-flag', 'fallback');

			expect(mockVariant).toHaveBeenCalledWith('null-flag', 'fallback');
			expect(result).toBeNull();
		});
	});

	describe('error handling', () => {
		it('should throw error when useExperimentContext throws', () => {
			const errorMessage =
				'useExperimentContext must be used within an AmplitudeExperimentProvider';
			mockUseExperimentContext.mockImplementation(() => {
				throw new Error(errorMessage);
			});

			expect(() => {
				useExperiment('test-flag');
			}).toThrow(errorMessage);
		});
	});

	describe('hook behavior with different parameter types', () => {
		const mockVariant = vi.fn();
		const mockExperimentContext = {
			client: null,
			variant: mockVariant,
			fetch: vi.fn(),
		} as any;

		beforeEach(() => {
			mockUseExperimentContext.mockReturnValue(mockExperimentContext);
		});

		it('should handle multiple calls with different flag keys', () => {
			const variant1: Variant = { value: 'variant1' };
			const variant2: Variant = { value: 'variant2' };

			mockVariant.mockReturnValueOnce(variant1).mockReturnValueOnce(variant2);

			const result1 = useExperiment('flag-1', 'fallback1');
			const result2 = useExperiment('flag-2', 'fallback2');

			expect(mockVariant).toHaveBeenCalledTimes(2);
			expect(mockVariant).toHaveBeenNthCalledWith(1, 'flag-1', 'fallback1');
			expect(mockVariant).toHaveBeenNthCalledWith(2, 'flag-2', 'fallback2');
			expect(result1).toEqual(variant1);
			expect(result2).toEqual(variant2);
		});
	});

	describe('type safety', () => {
		const mockVariant = vi.fn();
		const mockExperimentContext = {
			client: null,
			variant: mockVariant,
			fetch: vi.fn(),
		} as any;

		beforeEach(() => {
			mockUseExperimentContext.mockReturnValue(mockExperimentContext);
		});

		it('should handle complex Variant objects with all properties', () => {
			const complexVariant: Variant = {
				value: 'complex',
				payload: {
					nested: {
						object: true,
						array: [1, 2, 3],
						string: 'test',
					},
					boolean: false,
					number: 42,
				},
			};

			mockVariant.mockReturnValue(complexVariant);

			const result = useExperiment('complex-flag', { value: 'fallback' });

			expect(result).toEqual(complexVariant);
			expect(result.payload).toEqual(complexVariant.payload);
		});

		it('should handle empty payload correctly', () => {
			const variantWithEmptyPayload: Variant = {
				value: 'test-value',
				payload: {},
			};

			mockVariant.mockReturnValue(variantWithEmptyPayload);

			const result = useExperiment('empty-payload-flag');

			expect(result).toEqual(variantWithEmptyPayload);
			expect(result.payload).toEqual({});
		});

		it('should handle variant without payload', () => {
			const variantWithoutPayload: Variant = {
				value: 'simple-value',
			};

			mockVariant.mockReturnValue(variantWithoutPayload);

			const result = useExperiment('simple-flag');

			expect(result).toEqual(variantWithoutPayload);
			expect(result.payload).toBeUndefined();
		});
	});

	describe('fallback behavior edge cases', () => {
		beforeEach(() => {
			mockUseExperimentContext.mockReturnValue(null as any);
		});

		it('should handle empty string fallback', () => {
			const result = useExperiment('test-flag', '');
			expect(result).toBe('');
		});

		it('should handle zero as fallback', () => {
			const result = useExperiment('test-flag', 0 as any);
			expect(result).toBe(0);
		});

		it('should handle false as fallback', () => {
			const result = useExperiment('test-flag', false as any);
			expect(result).toBe(false);
		});

		it('should handle null as fallback', () => {
			const result = useExperiment('test-flag', null as any);
			expect(result).toBeNull();
		});
	});
});
