'use client';

import { Variant } from '@amplitude/experiment-js-client';
import { useExperimentContext } from '../components/AmplitudeExperimentProvider';

/**
 * Hook to access experiment variants
 * @param flagKey - The feature flag key to check
 * @param fallback - Optional fallback value if the flag is not found
 * @returns The variant object that can be destructured
 *
 * @example
 * const { value, ...otherProps } = useExperiment('feature-flag-key', { value: 'off' });
 */
export function useExperiment(
	flagKey: string,
	fallback?: string | Variant,
): Variant {
	const experimentContext = useExperimentContext();

	if (!experimentContext) {
		return fallback as Variant;
	}

	return experimentContext.variant(flagKey, fallback);
}

export default useExperiment;
