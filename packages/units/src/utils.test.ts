import { describe, it, expect } from 'vitest';
import { windDirectionCardinalToDeg, getComputedWindDirection } from './utils';

// Test data for all cardinal directions
const CARDINAL_DIRECTIONS = [
	['N', 0],
	['NNE', 22.5],
	['NE', 45],
	['ENE', 67.5],
	['E', 90],
	['ESE', 112.5],
	['SE', 135],
	['SSE', 157.5],
	['S', 180],
	['SSW', 202.5],
	['SW', 225],
	['WSW', 247.5],
	['W', 270],
	['WNW', 292.5],
	['NW', 315],
	['NNW', 337.5],
] as const;

// Test data for priority logic scenarios
const PRIORITY_TEST_CASES = [
	{
		wind: 45,
		cardinal: 'N',
		expected: 45,
		scenario: 'numeric takes priority over cardinal',
	},
	{
		wind: 0,
		cardinal: 'E',
		expected: 0,
		scenario: 'zero numeric value is valid',
	},
	{
		wind: 360,
		cardinal: 'S',
		expected: 360,
		scenario: 'boundary numeric value is valid',
	},
	{
		wind: undefined,
		cardinal: 'N',
		expected: 0,
		scenario: 'falls back to cardinal when numeric undefined',
	},
	{
		wind: NaN,
		cardinal: 'S',
		expected: 180,
		scenario: 'falls back to cardinal when numeric NaN',
	},
	{
		wind: Infinity,
		cardinal: 'W',
		expected: 270,
		scenario: 'falls back to cardinal when numeric Infinity',
	},
	{
		wind: -Infinity,
		cardinal: 'NW',
		expected: 315,
		scenario: 'falls back to cardinal when numeric -Infinity',
	},
	{
		wind: undefined,
		cardinal: undefined,
		expected: undefined,
		scenario: 'returns undefined when both undefined',
	},
	{
		wind: undefined,
		cardinal: '',
		expected: undefined,
		scenario: 'returns undefined when cardinal is empty',
	},
] as const;

describe('windDirectionCardinalToDeg', () => {
	// Test all cardinal direction conversions
	it.each(CARDINAL_DIRECTIONS)(
		'converts "%s" to %d degrees',
		(cardinal, expected) => {
			expect(windDirectionCardinalToDeg(cardinal)).toBe(expected);
		},
	);

	// Test invalid cardinal directions
	it.each([
		['INVALID', 'invalid direction'],
		['', 'empty string'],
		['north', 'lowercase'],
		['NNS', 'non-existent direction'],
	])('returns undefined for %s', (cardinal, scenario) => {
		expect(windDirectionCardinalToDeg(cardinal)).toBeUndefined();
	});
});

describe('getComputedWindDirection', () => {
	// Test priority logic and edge cases
	it.each(PRIORITY_TEST_CASES)('$scenario', ({ wind, cardinal, expected }) => {
		expect(getComputedWindDirection(wind, cardinal)).toBe(expected);
	});

	// Test that function handles all cardinal directions correctly as fallback
	it.each(CARDINAL_DIRECTIONS)(
		'falls back to cardinal "%s" (%d degrees) when numeric is invalid',
		(cardinal, expected) => {
			expect(getComputedWindDirection(NaN, cardinal)).toBe(expected);
		},
	);
});
