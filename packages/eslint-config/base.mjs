import js from '@eslint/js';
import eslintConfigPrettier from 'eslint-config-prettier';
import turbo from 'eslint-plugin-turbo';
import tseslint from 'typescript-eslint';
import globals from 'globals';
// import onlyWarn from 'eslint-plugin-only-warn';

/**
 * A shared ESLint configuration for the repository.
 *
 * @type {import('eslint').Linter.Config}
 * */
export const config = [
	{
		ignores: ['dist/**', '.next/**', '.turbo/**'],
	},
	js.configs.recommended,
	eslintConfigPrettier,
	...tseslint.configs.recommended,
	{
		plugins: {
			turbo,
		},
		rules: {
			'turbo/no-undeclared-env-vars': [
				'error',
				{
					// list out any env vars that are not relevant for turbo builds
					allowList: [
						'NODE_ENV', // provided by nextjs on dev or build commands
						'VERCEL', // provided by vercel
						'ANALYZE', // used for local analyzing of next build
						'CI', // used for determining if running in CI environment, not for build
						'VERCEL_AUTOMATION_BYPASS_SECRET', // only for playwright, not for build
						'MONGODB_CA', // not used
						'LOCAL_MONGODB_URI', // only for local dev
						'ALLOW_PRODUCTION_DB_IN_DEV', // only for local dev
						'BYPASS_MICROFRONTEND', // used as workaround for bypassing withMicrofrontends in next.config
					],
				},
			],
		},
	},
	// {
	// 	plugins: {
	// 		onlyWarn,
	// 	},
	// },
	{
		rules: {
			'@typescript-eslint/no-unused-vars': [
				'error',
				{
					argsIgnorePattern: '^_',
					varsIgnorePattern: '^_',
					caughtErrorsIgnorePattern: '^_',
					ignoreRestSiblings: true,
				},
			],
			'@typescript-eslint/no-empty-object-type': [
				'error',
				{
					allowInterfaces: 'with-single-extends',
					allowObjectTypes: 'never',
					allowWithName: 'Props$',
				},
			],
		},
	},
	{
		files: ['**/*.mjs'],
		languageOptions: {
			globals: {
				...globals.node,
			},
		},
	},
	{
		files: ['**/*.test.ts', '**/*.test.tsx', '**/*.spec.ts'],
		rules: {
			'@typescript-eslint/no-explicit-any': 'off',
			'@typescript-eslint/no-unused-vars': 'off',
		},
	},
];
