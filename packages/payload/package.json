{"name": "@repo/payload", "version": "0.0.1", "type": "module", "sideEffects": false, "private": true, "scripts": {"lint": "eslint --max-warnings=0 .", "check-types": "tsc --noEmit", "test": "vitest run", "generate:types": "PAYLOAD_CONFIG_PATH=src/payload-config.ts payload generate:types"}, "exports": {"./payload-types": "./src/payload-types.ts", "./payload-config": "./src/payload-config.ts", "./blocks": "./src/blocks/index.ts", "./blocks/admin": "./src/blocks/index.admin.ts", "./blocks/*": "./src/blocks/*/index.ts", "./fields/components": "./src/fields/components.ts", "./fields/utility": "./src/fields/utility/index.ts", "./fields/defaultLexical": "./src/fields/defaultLexical.ts", "./fields/*/hooks/*": "./src/fields/*/hooks/*.ts", "./configs/access": "./src/configs/access.ts", "./configs/locales": "./src/configs/locales.ts", "./contextParameters/*": "./src/contextParameters/*.ts", "./lexical/features/WxNodeConverterFeature": "./src/lexical/features/WxNodeConverterFeature/index.ts", "./lexical/features/WxNodeConverterFeature/*": "./src/lexical/features/WxNodeConverterFeature/*.ts", "./lexical/features/WeatherLocationTicker/*": "./src/lexical/features/WeatherLocationTicker/*.tsx", "./collections/Articles/utils/articleData": "./src/collections/Articles/utils/articleData.ts", "./collections/Pages/utils/pageData": "./src/collections/Pages/utils/pageData.ts", "./components/Blocks/renderBlock": "./src/components/Blocks/renderBlock.tsx", "./components/*": "./src/components/*/index.tsx", "./components/FrontendAdminHeader/AdminHeaderWrapper": "./src/components/FrontendAdminHeader/AdminHeaderWrapper.tsx", "./components/FrontendAdminHeader/collectors/DebugCollector": "./src/components/FrontendAdminHeader/collectors/DebugCollector.tsx", "./utils/*": "./src/utils/*.ts"}, "devDependencies": {"@playwright/test": "catalog:dev", "@repo/eslint-config": "workspace:*", "@repo/mocks": "workspace:*", "@repo/typescript-config": "workspace:*", "@repo/vitest-config": "workspace:*", "@storybook/react": "catalog:storybook", "@testing-library/react": "catalog:dev", "@types/node": "catalog:node", "@types/react": "catalog:web", "@types/react-dom": "catalog:web", "eslint": "catalog:dev", "typescript": "catalog:dev", "vitest": "catalog:dev"}, "peerDependencies": {"class-variance-authority": "catalog:web", "next": "catalog:web", "next-intl": "catalog:web", "react": "catalog:web", "react-dom": "catalog:web"}, "dependencies": {"@ai-sdk/openai-compatible": "catalog:ai", "@payloadcms/db-mongodb": "catalog:payload", "@payloadcms/email-nodemailer": "catalog:payload", "@payloadcms/live-preview-react": "catalog:payload", "@payloadcms/next": "catalog:payload", "@payloadcms/payload-cloud": "catalog:payload", "@payloadcms/plugin-cloud-storage": "catalog:payload", "@payloadcms/plugin-multi-tenant": "catalog:payload", "@payloadcms/plugin-oauth2": "catalog:payload", "@payloadcms/plugin-seo": "catalog:payload", "@payloadcms/richtext-lexical": "catalog:payload", "@payloadcms/storage-s3": "catalog:payload", "@payloadcms/ui": "catalog:payload", "@repo/analytics": "workspace:*", "@repo/dal": "workspace:*", "@repo/dpr-sdk": "workspace:*", "@repo/experiments": "workspace:*", "@repo/flags": "workspace:*", "@repo/helios": "workspace:*", "@repo/icons": "workspace:*", "@repo/jw-player": "workspace:*", "@repo/location": "workspace:*", "@repo/logger": "workspace:*", "@repo/navigation": "workspace:*", "@repo/newrelic": "workspace:*", "@repo/privacy": "workspace:*", "@repo/ui": "workspace:*", "@repo/units": "workspace:*", "@repo/user": "workspace:*", "@repo/utils": "workspace:*", "@vercel/edge-config": "^1.4.0", "ai": "catalog:ai", "bloom-filters": "3.0.4", "cookies-next": "catalog:web", "date-fns": "^4.1.0", "fast-xml-parser": "^5.2.5", "jotai": "catalog:web", "lucide-react": "catalog:web", "nodemailer": "^7.0.0", "payload": "catalog:payload", "server-only": "^0.0.1", "sharp": "^0.34.0", "swr": "catalog:web", "zod": "catalog:ai"}}