{
	"extends": "@repo/typescript-config/nextjs.json",
	"compilerOptions": {
		"baseUrl": ".",
		"paths": {
			// leave this alone since (payload) files were generated using this import
			"@repo/payload/blocks/*": ["./src/blocks/*"],
			"@repo/payload/collections/*": ["./src/collections/*"],
			"@repo/payload/configs/*": ["./src/configs/*"],
			"@repo/payload/components/*": ["./src/components/*"],
			"@repo/payload/contextParameters/*": ["./src/contextParameters/*"],
			"@repo/payload/fields/*": ["./src/fields/*"],
			"@repo/payload/hooks/*": ["./src/hooks/*"],
			"@repo/payload/jobs/*": ["./src/jobs/*"],
			"@repo/payload/lexical/*": ["./src/lexical/*"],
			"@repo/payload/plugins": ["./src/plugins/index.ts"],
			"@repo/payload/plugins/*": ["./src/plugins/*"],
			"@repo/payload/utils/*": ["./src/utils/*"],
			"@repo/payload/payload-config": ["src/payload-config.ts"],
			"@repo/payload/payload-types": ["./src/payload-types.ts"]
		}
	},
	"include": [
		"next-env.d.ts",
		"assets.d.ts",
		"../../packages/dpr-sdk/index.d.ts",
		"../../packages/helios/src/helios.d.ts",
		"../../packages/jw-player/src/types/jwplayer.d.ts",
		"**/*.ts",
		"**/*.tsx"
	],
	"exclude": ["node_modules", "src/payload-types.ts"]
}
