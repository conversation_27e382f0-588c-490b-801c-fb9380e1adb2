import { getCurrentDeploymentURL } from '@repo/payload/utils/getURL';
import { hasLength } from '@repo/utils/hasLength';
import { oAuth2Plugin } from '@payloadcms/plugin-oauth2';
import { createLogger } from '@repo/logger';
import type {
	PluginOptions,
	VerifyFunction,
} from '@payloadcms/plugin-oauth2/types';
import type { TypedUser } from 'payload';

const logger = createLogger('payload-oauth-plugin');

type OktaData = {
	sub?: string | null;
	exp?: number | null;
	iss?: string | null;
	iat?: number | null;
	cid?: string | null;
};

type OAuthUser = {
	_strategy?: string;
	exp?: number;
	_okta?: OktaData;
} & TypedUser;

export const isLocalDevelopment = (): boolean => {
	return process.env.NODE_ENV === 'development';
};

/**
 * BUG: This function will be needed once, Payload releases an update to work with Vercel.
 * Right now it doesnt hurt to leave this in place, it just doesnt do anything.
 */
export const generateAuthorizationUrlParams = (): Record<string, string> => {
	// NOTE: in the future we will add response_mode=code and response_type=query
	if (process.env.VERCEL_ENV === 'preview') {
		return {
			response_type: 'id_token%20code',
		};
	}
	return {
		response_type: 'id_token%20code',
	};
};

// Helper function to compare values for token property changes
function compareValues(userVal: unknown, tokenVal: unknown): boolean {
	if (Array.isArray(userVal) && Array.isArray(tokenVal)) {
		return (
			userVal.some((val) => !tokenVal.includes(val)) ||
			tokenVal.some((val) => !userVal.includes(val))
		);
	}
	return userVal !== tokenVal;
}

// Helper function to validate email format
export function isValidEmail(email: string): boolean {
	const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
	return emailRegex.test(email);
}

// Helper function to determine the best username from token
export function getUserDisplayName(token: Record<string, unknown>): string {
	// Priority order: email (if valid) > preferred_username (if valid email) > sub
	if (
		token.email &&
		typeof token.email === 'string' &&
		isValidEmail(token.email)
	) {
		return token.email;
	}

	if (
		token.preferred_username &&
		typeof token.preferred_username === 'string' &&
		isValidEmail(token.preferred_username)
	) {
		return token.preferred_username;
	}

	// Fallback to sub if no valid email found
	return token.sub as string;
}

// Helper function to check if user Okta properties have changed
export const hasUserOktaPropsChanged = (
	user: OAuthUser,
	token: Record<string, unknown>,
): boolean => {
	// List of valuable token properties we want to store in _okta field
	const oktaPropsToStore: (keyof OktaData)[] = [
		'sub',
		'exp',
		'iss',
		'iat',
		'cid',
	];

	// Get current _okta data from user
	const currentOktaData = user._okta || {};

	for (const key of oktaPropsToStore) {
		if (token[key] !== undefined) {
			const userValue = currentOktaData[key];
			const tokenValue = token[key];

			// If user doesn't have this property, or if values are different, update is needed
			if (userValue == null || compareValues(userValue, tokenValue)) {
				return true;
			}
		}
	}

	return false;
};

/**
 * Custom verify function for Okta OAuth2 integration
 * Uses 'sub' for user matching (immutable ID) and validated email for username display
 */
const customVerify: VerifyFunction = async ({ payload, token }) => {
	// Use sub for finding users (immutable unique identifier)
	const userIdentifier = token.sub as string;
	// Use validated email for the username field (human-readable)
	const userDisplayName = getUserDisplayName(token);

	if (!userIdentifier) {
		payload.logger.error({
			msg: 'OAuth verification failed: No user identifier found in token',
			token: { sub: token.sub, email: token.email },
		});
		return { user: null };
	}

	let user: null | OAuthUser = null;

	try {
		// Find user by _okta.sub (preferred) or fallback to username for backward compatibility
		const results = await payload.find({
			collection: 'users',
			depth: 0,
			limit: 1,
			pagination: false,
			where: {
				or: [
					{ '_okta.sub': { equals: userIdentifier } }, // Primary lookup by _okta.sub
					{ username: { equals: userDisplayName } }, // New approach (email as username)
				],
			},
		});

		if (hasLength(results.docs)) {
			user = results.docs[0] as OAuthUser;

			// Update user with new Okta data if properties have changed
			if (hasUserOktaPropsChanged(user, token)) {
				const oktaData = {
					sub: token.sub,
					exp: token.exp,
					iss: token.iss,
					iat: token.iat,
					cid: token.cid,
				};

				user = (await payload.update({
					id: user.id,
					collection: 'users',
					data: {
						_okta: oktaData,
					},
				})) as OAuthUser;
			}
		} else {
			// Create new user
			const oktaData = {
				sub: token.sub,
				exp: token.exp,
				iss: token.iss,
				iat: token.iat,
				cid: token.cid,
			};

			const newUser: Omit<OAuthUser, 'id' | 'createdAt' | 'updatedAt'> = {
				username: userDisplayName,
				collection: 'users',
				role: isLocalDevelopment() ? ['admin'] : ['authenticated'],
				_okta: oktaData,
			};

			user = (await payload.create({
				collection: 'users',
				data: newUser,
			})) as OAuthUser;
		}
	} catch (err) {
		payload.logger.error({
			err,
			msg: 'OAuth verification failed: Error processing user',
			username: userDisplayName,
		});
		return { user: null };
	}

	if (user) {
		// Ensure all required properties are present for the User type
		const authenticatedUser: OAuthUser & { collection: 'users' } = {
			...user,
			collection: 'users',
			_strategy: 'okta-oauth',
			// Ensure required User properties are present
			role: user.role || (isLocalDevelopment() ? ['admin'] : ['authenticated']),
			updatedAt: user.updatedAt || new Date().toISOString(),
			createdAt: user.createdAt || new Date().toISOString(),
		};

		return { user: authenticatedUser };
	}

	payload.logger.error({
		msg: 'OAuth verification failed: No user to return',
		username: userDisplayName,
	});
	return { user: null };
};

const redirectServerURL = getCurrentDeploymentURL();

logger.info(`🌐 Using ${redirectServerURL} for oauth redirect URI`);

const isDisabled = !!(
	process.env.BYPASS_SSO?.toLowerCase() === 'true' &&
	process.env.VERCEL_ENV !== 'production'
);

const shouldDebugOAuth = process.env.LOG_LEVEL === 'debug';
const cookieOptions: PluginOptions['createCookieOptions'] = ({
	cookieOptions,
}) => ({
	...cookieOptions,
	sameSite: 'None',
	secure: true,
});

export const oAuth2 = oAuth2Plugin({
	disabled: isDisabled,
	strategyName: 'okta-oauth',
	redirectServerURL,
	debug: shouldDebugOAuth,
	createCookieOptions: cookieOptions,
	collections: [
		{
			slug: 'users',
			token: 'id_token',
			identityMetadata: process.env.PAYLOAD_OAUTH_IDENTITY_METADATA || '',
			clientID: process.env.PAYLOAD_OAUTH_CLIENT_ID || '',
			scope: ['openid', 'profile', 'email', 'offline_access'],
			endOAuthSessionOnLogout: false,
			usernameField: 'username',
			verify: customVerify,
			authorizationURLParams: generateAuthorizationUrlParams(),
		},
	],
});
