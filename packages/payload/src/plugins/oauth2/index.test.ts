import { describe, it, expect, vi } from 'vitest';
import {
	hasUserOktaPropsChanged,
	isLocalDevelopment,
	generateAuthorizationUrlParams,
	isValidEmail,
	getUserDisplayName,
} from './index';

// Mock the logger to avoid import issues in tests
vi.mock('@repo/logger', () => ({
	createLogger: () => ({
		info: vi.fn(),
		error: vi.fn(),
		warn: vi.fn(),
	}),
}));

// Mock other dependencies
vi.mock('@repo/payload/utils/getURL', () => ({
	getCurrentDeploymentURL: () => 'http://localhost:3000',
}));

vi.mock('@repo/utils/hasLength', () => ({
	hasLength: (arr: any[]) => arr && arr.length > 0,
}));

vi.mock('@payloadcms/plugin-oauth2', () => ({
	oAuth2Plugin: vi.fn(),
}));

describe('OAuth2 Plugin Helper Functions', () => {
	describe('isValidEmail', () => {
		it('should validate correct email addresses', () => {
			const validEmails = [
				'<EMAIL>',
				'<EMAIL>',
				'<EMAIL>',
				'<EMAIL>',
				'<EMAIL>',
			];

			validEmails.forEach((email) => {
				expect(isValidEmail(email)).toBe(true);
			});
		});

		it('should reject invalid email addresses', () => {
			const invalidEmails = [
				'',
				'not-an-email',
				'@domain.com',
				'user@',
				'user@domain',
				'user.domain.com',
				'user @domain.com',
				'user@domain .com',
				'user@@domain.com',
			];

			invalidEmails.forEach((email) => {
				expect(isValidEmail(email)).toBe(false);
			});
		});
	});

	describe('getUserDisplayName', () => {
		it('should return email when it is valid', () => {
			const token = {
				email: '<EMAIL>',
				preferred_username: 'username',
				sub: 'user-id-123',
			};

			expect(getUserDisplayName(token)).toBe('<EMAIL>');
		});

		it('should return preferred_username when email is invalid but preferred_username is valid email', () => {
			const token = {
				email: 'invalid-email',
				preferred_username: '<EMAIL>',
				sub: 'user-id-123',
			};

			expect(getUserDisplayName(token)).toBe('<EMAIL>');
		});

		it('should return sub when both email and preferred_username are invalid', () => {
			const token = {
				email: 'invalid-email',
				preferred_username: 'not-an-email',
				sub: 'user-id-123',
			};

			expect(getUserDisplayName(token)).toBe('user-id-123');
		});

		it('should return sub when email is missing', () => {
			const token = {
				preferred_username: 'not-an-email',
				sub: 'user-id-123',
			};

			expect(getUserDisplayName(token)).toBe('user-id-123');
		});

		it('should return sub when preferred_username is missing', () => {
			const token = {
				email: 'invalid-email',
				sub: 'user-id-123',
			};

			expect(getUserDisplayName(token)).toBe('user-id-123');
		});

		it('should handle empty strings', () => {
			const token = {
				email: '',
				preferred_username: '',
				sub: 'user-id-123',
			};

			expect(getUserDisplayName(token)).toBe('user-id-123');
		});

		it('should handle non-string values', () => {
			const token = {
				email: 123,
				preferred_username: null,
				sub: 'user-id-123',
			};

			expect(getUserDisplayName(token)).toBe('user-id-123');
		});

		it('should prioritize email over preferred_username even if both are valid', () => {
			const token = {
				email: '<EMAIL>',
				preferred_username: '<EMAIL>',
				sub: 'user-id-123',
			};

			expect(getUserDisplayName(token)).toBe('<EMAIL>');
		});

		it('should handle getUserDisplayName with undefined token properties', () => {
			const token = {
				sub: 'user-id-123',
			};

			expect(getUserDisplayName(token)).toBe('user-id-123');
		});

		it('should handle getUserDisplayName with all empty values', () => {
			const token = {
				email: '',
				preferred_username: '',
				sub: '',
			};

			expect(getUserDisplayName(token)).toBe('');
		});
	});

	describe('hasUserOktaPropsChanged', () => {
		it('should return true when user has no _okta data', () => {
			const user = {
				id: '1',
				username: '<EMAIL>',
				role: ['authenticated'],
			} as any;

			const token = {
				sub: 'user-id-123',
				exp: 1234567890,
				iss: 'https://okta.example.com',
				iat: 1234567800,
				cid: 'client-id-123',
			};

			expect(hasUserOktaPropsChanged(user, token)).toBe(true);
		});

		it('should return false when all Okta properties are unchanged', () => {
			const user = {
				id: '1',
				username: '<EMAIL>',
				role: ['authenticated'],
				_okta: {
					sub: 'user-id-123',
					exp: 1234567890,
					iss: 'https://okta.example.com',
					iat: 1234567800,
					cid: 'client-id-123',
				},
			} as any;

			const token = {
				sub: 'user-id-123',
				exp: 1234567890,
				iss: 'https://okta.example.com',
				iat: 1234567800,
				cid: 'client-id-123',
			};

			expect(hasUserOktaPropsChanged(user, token)).toBe(false);
		});

		it('should return true when sub has changed', () => {
			const user = {
				id: '1',
				username: '<EMAIL>',
				role: ['authenticated'],
				_okta: {
					sub: 'old-user-id',
					exp: 1234567890,
					iss: 'https://okta.example.com',
					iat: 1234567800,
					cid: 'client-id-123',
				},
			} as any;

			const token = {
				sub: 'new-user-id',
				exp: 1234567890,
				iss: 'https://okta.example.com',
				iat: 1234567800,
				cid: 'client-id-123',
			};

			expect(hasUserOktaPropsChanged(user, token)).toBe(true);
		});

		it('should return true when exp has changed', () => {
			const user = {
				id: '1',
				username: '<EMAIL>',
				role: ['authenticated'],
				_okta: {
					sub: 'user-id-123',
					exp: 1234567890,
					iss: 'https://okta.example.com',
					iat: 1234567800,
					cid: 'client-id-123',
				},
			} as any;

			const token = {
				sub: 'user-id-123',
				exp: 9876543210, // Changed
				iss: 'https://okta.example.com',
				iat: 1234567800,
				cid: 'client-id-123',
			};

			expect(hasUserOktaPropsChanged(user, token)).toBe(true);
		});

		it('should handle missing token properties', () => {
			const user = {
				id: '1',
				username: '<EMAIL>',
				role: ['authenticated'],
				_okta: {
					sub: 'user-id-123',
				},
			} as any;

			const token = {
				sub: 'user-id-123',
				// exp, iss, iat, cid are missing
			};

			expect(hasUserOktaPropsChanged(user, token)).toBe(false);
		});

		it('should handle array values in tracked properties', () => {
			const user = {
				id: '1',
				username: '<EMAIL>',
				role: ['authenticated'],
				_okta: {
					sub: 'user-id-123',
					exp: ['value1', 'value2'], // Using exp as array for testing
				},
			} as any;

			const token = {
				sub: 'user-id-123',
				exp: ['value1', 'value3'], // Different array
			};

			// This tests the compareValues function for arrays on tracked properties
			expect(hasUserOktaPropsChanged(user, token)).toBe(true);
		});

		it('should ignore non-tracked properties', () => {
			const user = {
				id: '1',
				username: '<EMAIL>',
				role: ['authenticated'],
				_okta: {
					sub: 'user-id-123',
					customArray: ['value1', 'value2'], // Non-tracked property
				},
			} as any;

			const token = {
				sub: 'user-id-123',
				customArray: ['value1', 'value3'], // Different array but not tracked
			};

			// Should return false because customArray is not in oktaPropsToStore
			expect(hasUserOktaPropsChanged(user, token)).toBe(false);
		});

		it('should handle null and undefined values', () => {
			const user = {
				id: '1',
				username: '<EMAIL>',
				role: ['authenticated'],
				_okta: {
					sub: 'user-id-123',
					exp: null,
				},
			} as any;

			const token = {
				sub: 'user-id-123',
				exp: 1234567890, // Changed from null
			};

			expect(hasUserOktaPropsChanged(user, token)).toBe(true);
		});

		it('should handle user with empty _okta object', () => {
			const user = {
				id: '1',
				username: '<EMAIL>',
				role: ['authenticated'],
				_okta: {},
			} as any;

			const token = {
				sub: 'user-id-123',
			};

			expect(hasUserOktaPropsChanged(user, token)).toBe(true);
		});

		it('should handle token with undefined values', () => {
			const user = {
				id: '1',
				username: '<EMAIL>',
				role: ['authenticated'],
				_okta: {
					sub: 'user-id-123',
					exp: 1234567890,
				},
			} as any;

			const token = {
				sub: 'user-id-123',
				exp: undefined,
			};

			expect(hasUserOktaPropsChanged(user, token)).toBe(false);
		});
	});
});

describe('OAuth2 Plugin Integration', () => {
	describe('isLocalDevelopment', () => {
		it('should return true when NODE_ENV is development', () => {
			// Use vi.stubEnv to properly mock environment variables
			vi.stubEnv('NODE_ENV', 'development');

			expect(isLocalDevelopment()).toBe(true);

			// Restore original environment
			vi.unstubAllEnvs();
		});

		it('should return false when NODE_ENV is not development', () => {
			// Use vi.stubEnv to properly mock environment variables
			vi.stubEnv('NODE_ENV', 'production');

			expect(isLocalDevelopment()).toBe(false);

			// Restore original environment
			vi.unstubAllEnvs();
		});
	});

	describe('generateAuthorizationUrlParams', () => {
		it('should return correct params for preview environment', () => {
			// Use vi.stubEnv to properly mock environment variables
			vi.stubEnv('VERCEL_ENV', 'preview');

			const params = generateAuthorizationUrlParams();

			expect(params).toEqual({
				response_type: 'id_token%20code',
			});

			// Restore original environment
			vi.unstubAllEnvs();
		});

		it('should return correct params for non-preview environment', () => {
			// Use vi.stubEnv to properly mock environment variables
			vi.stubEnv('VERCEL_ENV', 'production');

			const params = generateAuthorizationUrlParams();

			expect(params).toEqual({
				response_type: 'id_token%20code',
			});

			// Restore original environment
			vi.unstubAllEnvs();
		});
	});
});
