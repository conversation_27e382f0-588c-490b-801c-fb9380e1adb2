import { multiTenantPlugin } from '@payloadcms/plugin-multi-tenant';
import type { Config } from '@repo/payload/payload-types';
import { isAdmin } from '@repo/payload/configs/access';
import { getUserTenantIDs } from '@repo/payload/utils/getUserTenantIDs';

export const multiTenant = multiTenantPlugin<Config>({
	collections: {
		pages: {},
		articles: {},
		images: {},
		liveblogs: {},
		videos: {},
	},
	enabled: true,
	tenantField: {
		access: {
			read: () => true,
			update: ({ req }) => {
				if (isAdmin(req.user)) {
					return true;
				}
				return getUserTenantIDs(req.user).length > 0;
			},
		},
	},
	tenantsArrayField: {
		includeDefaultField: false,
	},
	userHasAccessToAllTenants: (user) => isAdmin(user),
});
