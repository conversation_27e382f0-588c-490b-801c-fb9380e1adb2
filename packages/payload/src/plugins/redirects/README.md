# Redirects Plugin

This plugin provides automatic redirect management for PayloadCMS collections when `assetName` fields are changed.

## Features

- **Redirects Collection**: Manages URL redirects with support for permanent (301) and temporary (302) redirects
- **Automatic Redirect Creation**: Creates redirects when `assetName` fields change on published documents
- **Bloom Filter Integration**: Efficient redirect lookups using bloom filters stored in Vercel Edge Config
- **Path Revalidation**: Automatically revalidates paths when redirects are created
- **Collection Agnostic**: Works with any collection that has an `assetName` field

## Architecture

The plugin uses a clean, utility-based architecture:

1. **Redirects Collection**: Provides the core redirects functionality
2. **Asset Name Change Hook**: A reusable hook that collections can import and use
3. **Utility Functions**: Helper functions for creating redirects and managing bloom filters

## Usage

### Plugin Installation

The plugin is automatically installed in `apps/web/plugins/index.ts`:

```typescript
import { redirectsPlugin } from './redirects';

export const plugins: Plugin[] = [
  // ... other plugins
  redirectsPlugin(),
];
```

### Adding to Collections

Collections with `assetName` fields can import and use the asset name change hook:

```typescript
import { createAssetNameChangeHook } from '@/plugins/redirects';

const MyCollection: CollectionConfig = {
  slug: 'my-collection',
  hooks: {
    afterChange: [
      // ... other hooks
      createAssetNameChangeHook({
        minimumTimeThreshold: 60 * 60 * 1000, // 1 hour in milliseconds
        isLocalized: true // Set to true if assetName field has localized: true
      })
    ]
  },
  // ... rest of collection config
};
```

### Hook Options

The `createAssetNameChangeHook` function accepts an options object:

- `minimumTimeThreshold` (optional): Minimum time in milliseconds that must pass between updates before creating a redirect. Default: 0 (no threshold)
- `isLocalized` (optional): Whether the assetName field is localized. Default: false

## How It Works

### Asset Name Change Detection

1. The hook monitors `afterChange` events for collections
2. Only processes `update` operations on published documents
3. Compares current and previous `assetName` values
4. Creates redirects when changes are detected

### Redirect Creation Logic

When an `assetName` change is detected:

1. **Check for Existing Redirect**: Looks for an existing redirect with the old `assetName` as the source
2. **Update or Create**:
   - If a redirect exists, updates its destination to the new `assetName`
   - If no redirect exists, creates a new one
3. **Metadata Tracking**: Records the original collection and document ID for reference

### Bloom Filter Management

The plugin maintains a bloom filter for efficient redirect lookups:

- **Automatic Updates**: Filter is updated when redirects are created or modified
- **Edge Config Storage**: Stored in Vercel Edge Config for fast access
- **Regeneration**: Filter is regenerated when redirects are deleted

## Collections Using This Plugin

Currently implemented in:

- **Articles** (`apps/web/collections/Articles/index.ts`)
- **Pages** (`apps/web/collections/Pages/index.ts`)

## Database Schema

### Redirects Collection

| Field | Type | Description |
|-------|------|-------------|
| `source` | string | The original URL path (e.g., `/old-path`) |
| `destination` | string | The new URL path (e.g., `/new-path`) |
| `permanent` | boolean | Whether to use 301 (permanent) or 302 (temporary) redirect |
| `notes` | textarea | Optional notes about the redirect |
| `createdByAssetNameChange` | boolean | Whether this redirect was automatically created |
| `originalCollection` | string | The collection that contained the changed document |
| `originalDocumentId` | string | The ID of the document that changed |

## Environment Variables

The plugin integrates with Vercel Edge Config for bloom filter storage:

- `EDGE_CONFIG`: Vercel Edge Config connection string
- `EDGE_CONFIG_ITEM_KEY`: Key for storing the bloom filter (default: 'redirects-bloom-filter')

## Logging

The plugin provides detailed logging for debugging:

- `[REDIRECTS_PLUGIN]` prefix for all log messages
- Debug logs for threshold checks and filter operations
- Info logs for successful redirect creation/updates
- Error logs for failures (non-blocking)

## Error Handling

The plugin is designed to be non-blocking:

- Errors in redirect creation don't prevent document saves
- Failed bloom filter updates are logged but don't break functionality
- Graceful fallbacks when Edge Config is unavailable

## Development

### Testing the Hook

To test the asset name change functionality:

1. Create or edit an article/page in the admin interface
2. Publish the document with an initial `assetName`
3. Update the `assetName` and save
4. Check the Redirects collection for the automatically created redirect

### Debugging

Enable debug logging by checking the PayloadCMS logs when documents are updated. Look for `[REDIRECTS_PLUGIN]` prefixed messages.

## Localization Support

The plugin supports localized `assetName` fields through explicit configuration:

### Configuration

Set `isLocalized: true` when creating the hook for collections with localized assetName fields:

```typescript
// For Articles collection (has localized: true on assetName)
createAssetNameChangeHook({ isLocalized: true })

// For Pages collection (non-localized assetName)
createAssetNameChangeHook({ isLocalized: false }) // or omit since false is default
```

### How Localization Works

- **Non-localized fields** (`isLocalized: false`): Creates redirects as before when assetName changes
- **Localized fields** (`isLocalized: true`): Uses the current request locale to create locale-specific redirects
- **PayloadCMS normalization**: Since PayloadCMS normalizes localized fields to strings based on the current locale, the hook receives the locale-specific value as a string

### Example Behavior

When editing an article in English locale:
- Previous assetName: `/old-english-slug`
- New assetName: `/new-english-slug`
- Result: Creates redirect `/old-english-slug` → `/new-english-slug` with locale context

When editing the same article in Spanish locale:
- Previous assetName: `/viejo-slug-espanol`
- New assetName: `/nuevo-slug-espanol`
- Result: Creates redirect `/viejo-slug-espanol` → `/nuevo-slug-espanol` with locale context

### Benefits

- **Locale isolation**: Changes in one locale only create redirects for that specific locale
- **Explicit configuration**: Clear intent through the `isLocalized` parameter
- **Request context awareness**: Uses the current request locale for proper redirect creation
- **Backward compatibility**: Non-localized fields continue to work as before

## Future Enhancements

- Bulk redirect management utilities
- Redirect analytics and usage tracking
- Integration with external redirect services
