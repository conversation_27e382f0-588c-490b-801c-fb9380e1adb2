import type { CollectionAfterChangeHook, PayloadRequest } from 'payload';
import type { RedirectsSelect } from '@repo/payload/payload-types';
import { createRedirect } from '../utils/createRedirect';
import {
	generateStandardPreviewPath,
	type SupportedCollection,
} from '@repo/payload/utils/generatePreviewPath';

interface AssetNameChangeHookOptions {
	minimumTimeThreshold?: number; // in milliseconds, default 0 (no threshold)
	isLocalized?: boolean; // whether the assetName field is localized, default false
}

// Type for documents with assetName field
interface DocumentWithAssetName {
	id: string;
	assetName: string;
	_status?: 'draft' | 'published';
	updatedAt?: string;
	[key: string]: unknown;
}

/**
 * Creates an assetName change detection hook that can be used by any collection
 * with an assetName field.
 *
 * Usage:
 * ```typescript
 * import { createAssetNameChangeHook } from '@/plugins/redirects';
 *
 * const MyCollection: CollectionConfig = {
 *   hooks: {
 *     afterChange: [
 *       createAssetNameChangeHook({ minimumTimeThreshold: 60 * 60 * 1000 })
 *     ]
 *   }
 * };
 * ```
 */
export function createAssetNameChangeHook(
	options: AssetNameChangeHookOptions = {},
): CollectionAfterChangeHook {
	const { minimumTimeThreshold = 0, isLocalized = false } = options;

	const hook: CollectionAfterChangeHook = async (args) => {
		const { operation, req, doc, collection } = args;

		// Only process update operations
		if (operation !== 'update') {
			return doc;
		}

		// Ensure we have current document
		if (!doc) {
			return doc;
		}

		// Only process documents with assetName field
		if (!('assetName' in doc)) {
			return doc;
		}

		const currentDoc = doc as DocumentWithAssetName;

		// Only process published documents
		if (currentDoc._status !== 'published') {
			return doc;
		}

		try {
			// Query for the most recent published version of this document
			const publishedVersions = await req.payload.findVersions({
				collection: collection.slug,
				where: {
					parent: { equals: currentDoc.id },
					'version._status': { equals: 'published' },
				},
				sort: '-updatedAt',
				limit: 2, // Get current and previous published versions
			});

			// If there's no previous published version, this is the first publication
			if (publishedVersions.totalDocs < 2) {
				req.payload.logger.debug(
					`[REDIRECTS_PLUGIN] Skipping redirect creation for ${collection.slug} ${currentDoc.id} - first publication or no previous published version`,
				);
				return doc;
			}

			// Get the previous published version (second in the sorted list)
			const previousPublishedVersion = publishedVersions.docs[1];
			if (
				!previousPublishedVersion?.version ||
				!('assetName' in previousPublishedVersion.version)
			) {
				req.payload.logger.debug(
					`[REDIRECTS_PLUGIN] Skipping redirect creation for ${collection.slug} ${currentDoc.id} - previous published version has no assetName`,
				);
				return doc;
			}

			const prevPublishedDoc =
				previousPublishedVersion.version as unknown as DocumentWithAssetName;

			// Check time threshold - skip if updated too recently
			if (minimumTimeThreshold > 0 && prevPublishedDoc.updatedAt) {
				const prevUpdatedTime = new Date(prevPublishedDoc.updatedAt).getTime();
				const currentTime = Date.now();
				const timeSinceLastUpdate = currentTime - prevUpdatedTime;

				if (timeSinceLastUpdate < minimumTimeThreshold) {
					req.payload.logger.debug(
						`[REDIRECTS_PLUGIN] Skipping redirect creation for ${collection.slug} ${currentDoc.id} - updated too recently (${Math.round(timeSinceLastUpdate / 1000 / 60)} minutes ago)`,
					);
					return doc;
				}
			}

			await processAssetNameChanges({
				currentDoc,
				prevDoc: prevPublishedDoc,
				req,
				collectionSlug: collection.slug,
				isLocalized,
			});
		} catch (error) {
			req.payload.logger.error(
				`[REDIRECTS_PLUGIN] Error processing assetName changes for ${collection.slug} ${currentDoc.id}: ${error instanceof Error ? error.message : 'Unknown error'}`,
			);
			// Don't throw - we don't want to break the main operation
		}

		return doc;
	};

	return hook;
}

/**
 * Process assetName changes and create redirects as needed
 */
async function processAssetNameChanges({
	currentDoc,
	prevDoc,
	req,
	collectionSlug,
	isLocalized,
}: {
	currentDoc: DocumentWithAssetName;
	prevDoc: DocumentWithAssetName;
	req: PayloadRequest;
	collectionSlug: string;
	isLocalized: boolean;
}) {
	const currentAssetName = currentDoc.assetName;
	const prevAssetName = prevDoc.assetName;

	if (isLocalized) {
		// For localized fields, we get the current locale's value as a string
		// We need to get the current locale from the request context
		const currentLocale = req.locale || 'en-US'; // fallback to default locale

		// Since PayloadCMS normalizes localized fields, both values should be strings
		// representing the current locale's value
		if (
			typeof currentAssetName === 'string' &&
			typeof prevAssetName === 'string'
		) {
			if (currentAssetName !== prevAssetName) {
				await handleAssetNameChange({
					oldAssetName: prevAssetName,
					newAssetName: currentAssetName,
					locale: currentLocale,
					req,
					collectionSlug,
					documentId: currentDoc.id,
				});
			}
		} else {
			req.payload.logger.warn(
				`[REDIRECTS_PLUGIN] Expected string assetName for localized field but got different types for ${collectionSlug} ${currentDoc.id}`,
			);
		}
	} else {
		// Non-localized field - handle as before
		if (
			typeof currentAssetName === 'string' &&
			typeof prevAssetName === 'string'
		) {
			if (currentAssetName !== prevAssetName) {
				await handleAssetNameChange({
					oldAssetName: prevAssetName,
					newAssetName: currentAssetName,
					locale: null,
					req,
					collectionSlug,
					documentId: currentDoc.id,
				});
			}
		} else {
			req.payload.logger.warn(
				`[REDIRECTS_PLUGIN] Expected string assetName for non-localized field but got different types for ${collectionSlug} ${currentDoc.id}`,
			);
		}
	}
}

/**
 * Handle a single assetName change by creating or updating a redirect
 */
async function handleAssetNameChange({
	oldAssetName,
	newAssetName,
	locale,
	req,
	collectionSlug,
	documentId,
}: {
	oldAssetName: string;
	newAssetName: string;
	locale: string | null;
	req: PayloadRequest;
	collectionSlug: string;
	documentId: string;
}) {
	const { payload } = req;

	// Check if the collection is supported by generateStandardPreviewPath
	const supportedCollections: Record<string, SupportedCollection> = {
		articles: 'articles',
		pages: 'pages',
	};

	const mappedCollection = supportedCollections[collectionSlug];

	if (!mappedCollection) {
		payload.logger.warn(
			`[REDIRECTS_PLUGIN] Collection '${collectionSlug}' is not supported by generateStandardPreviewPath. Using raw assetName values.`,
		);
	}

	// Generate proper paths using generateStandardPreviewPath if collection is supported
	const sourcePath = mappedCollection
		? generateStandardPreviewPath(mappedCollection, {
				assetName: oldAssetName,
			}) || oldAssetName
		: oldAssetName;

	const destinationPath = mappedCollection
		? generateStandardPreviewPath(mappedCollection, {
				assetName: newAssetName,
			}) || newAssetName
		: newAssetName;

	try {
		// Check if a redirect already exists for this source path
		const existingRedirect = await payload.find<'redirects', RedirectsSelect>({
			collection: 'redirects',
			where: {
				source: { equals: sourcePath },
			},
			limit: 1,
		});

		if (existingRedirect.totalDocs > 0 && existingRedirect.docs[0]) {
			// Update existing redirect
			const redirect = existingRedirect.docs[0];

			await payload.update<'redirects', RedirectsSelect>({
				collection: 'redirects',
				id: redirect.id,
				data: {
					destination: destinationPath,
					notes: redirect.notes
						? `${redirect.notes}\n\nUpdated due to assetName change on ${new Date().toISOString()}`
						: `Updated due to assetName change on ${new Date().toISOString()}`,
				},
			});

			payload.logger.info(
				`[REDIRECTS_PLUGIN] Updated existing redirect: ${sourcePath} → ${destinationPath} (${collectionSlug} ${documentId}${locale ? `, locale: ${locale}` : ''})`,
			);
		} else {
			// Create new redirect
			const createResult = await createRedirect({
				req,
				source: sourcePath,
				destination: destinationPath,
				originalCollection: collectionSlug,
				originalDocumentId: documentId,
			});

			if (createResult.success) {
				payload.logger.info(
					`[REDIRECTS_PLUGIN] Created redirect for assetName change: ${sourcePath} → ${destinationPath} (${collectionSlug} ${documentId}${locale ? `, locale: ${locale}` : ''})`,
				);
			} else {
				payload.logger.warn(
					`[REDIRECTS_PLUGIN] Failed to create redirect for assetName change: ${createResult.error} (${collectionSlug} ${documentId}${locale ? `, locale: ${locale}` : ''})`,
				);
			}
		}
	} catch (error) {
		payload.logger.error(
			`[REDIRECTS_PLUGIN] Error handling assetName change for ${sourcePath}: ${error instanceof Error ? error.message : 'Unknown error'}`,
		);
	}
}
