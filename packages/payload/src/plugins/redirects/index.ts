import type { Plugin, Config } from 'payload';
import Redirects from './collections';

/**
 * Redirects Plugin
 *
 * This plugin adds a Redirects collection to Payload CMS and provides utilities for
 * managing URL redirects.
 *
 * Features:
 * - Redirects collection for managing URL redirects
 * - Support for both permanent (301) and temporary (302) redirects
 * - Path revalidation when redirects are created
 * - Custom endpoint for redirect lookups
 * - Bloom filter integration for efficient redirect lookups
 * - Utility hooks for automatic redirect creation on assetName changes
 *
 * Usage:
 * Collections can import and use the assetName change hook:
 * ```typescript
 * import { createAssetNameChangeHook } from '@/plugins/redirects';
 *
 * const MyCollection: CollectionConfig = {
 *   // ... other config
 *   hooks: {
 *     afterChange: [
 *       createAssetNameChangeHook({ minimumTimeThreshold: 60 * 60 * 1000 })
 *     ]
 *   }
 * };
 * ```
 */
export const redirectsPlugin: Plugin = (incomingConfig: Config): Config => {
	// Create a copy of the config to modify
	const config = { ...incomingConfig };

	// Add the Redirects collection to the config
	config.collections = [...(config.collections || []), Redirects];

	// Return the modified config
	return config;
};

// Export utilities for collections to use
export { createAssetNameChangeHook } from './hooks/assetNameChangeHook';
export { createRedirect } from './utils/createRedirect';

export default redirectsPlugin;
