import type { Page, Article } from '@repo/payload/payload-types';
import type { Plugin } from 'payload';

import { seoPlugin } from '@payloadcms/plugin-seo';
import { GenerateTitle, GenerateURL } from '@payloadcms/plugin-seo/types';
import { s3SdotConnector } from './s3-connector';
import { multiTenant } from './multiTenant';
import { oAuth2 } from './oauth2';
import { getClientSideURL } from '@repo/payload/utils/getURL';
import { redirectsPlugin } from './redirects';
import { aiPlugin } from './ai';

const generateTitle: GenerateTitle<Article | Page> = ({ doc }) => {
	return doc?.title
		? `${doc.title} | the weather company foo bar`
		: 'the weather company foo bar';
};

const generateURL: GenerateURL<Article | Page> = ({ doc, collectionSlug }) => {
	let path = '/';

	switch (collectionSlug) {
		case 'articles':
			path = `/${(doc as Article).assetName}`;
			break;
		case 'pages':
			path = `/${doc.assetName}`;
			break;
		default:
	}

	return `${getClientSideURL()}/${path}`;
};

export const plugins: Plugin[] = [
	seoPlugin({
		generateTitle,
		generateURL,
	}),
	s3SdotConnector,
	multiTenant,
	oAuth2,
	redirectsPlugin,
	aiPlugin,
];
