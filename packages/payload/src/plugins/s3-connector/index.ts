import { s3Storage } from '@payloadcms/storage-s3';
import { Plugin } from 'payload';

import type { GenerateFileURL } from '@payloadcms/plugin-cloud-storage/types';

const generateFileURL: GenerateFileURL = (doc) => {
	const prefix = doc.prefix ? `${doc.prefix}` : 'wxnext';
	const filename = doc.filename;
	return `https://${process.env.S3_BUCKET}/${prefix}/${filename}`;
};

export const s3SdotConnector: Plugin = s3Storage({
	collections: {
		images: {
			prefix: 'wxnext/img',
			generateFileURL,
		},
	},
	bucket: process.env.S3_BUCKET || '',
	config: {
		credentials: {
			accessKeyId: process.env.S3_ACCESS_KEY_ID || '',
			secretAccessKey: process.env.S3_SECRET_ACCESS_KEY || '',
		},
		region: process.env.S3_REGION || '',
	},
});
