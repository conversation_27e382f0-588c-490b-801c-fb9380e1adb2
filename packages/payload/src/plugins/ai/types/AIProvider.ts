export interface TranslationData {
	translatedText: string;
	detectedSourceLanguage?: string;
}

export interface TranslationResponse {
	translation: TranslationData;
}

export interface BatchTranslationData {
	translations: {
		targetLanguage: string;
		translatedText: string;
	}[];
}

export interface BatchTranslationResponse {
	batchTranslation: BatchTranslationData;
}

export interface AltTextRequest {
	imageUrl?: string;
	imageBase64?: string;
	context?: string;
}

export interface AltTextResponse {
	altText: string;
}

export interface ArticleRecommendationRequest {
	articleContent: string;
	count: number;
}

export interface ArticleRecommendationData {
	recommendations: {
		articleId: string;
		title: string;
		relevanceScore: number;
	}[];
}

export interface ArticleRecommendationResponse {
	articleRecommendations: ArticleRecommendationData;
}

export type SupportedLanguage =
	| 'en'
	| 'es'
	| 'fr'
	| 'de'
	| 'it'
	| 'pt'
	| 'nl'
	| 'pl'
	| 'ru'
	| 'ja'
	| 'ko'
	| 'zh'
	| 'ar'
	| 'hi'
	| 'th'
	| 'vi';

/**
 * AI Provider Interface
 *
 * This interface defines the contract for AI service providers that can perform
 * various AI operations like translation, alt text generation, and article recommendations.
 *
 * Each provider implementation (LiteLLM, Mercury, etc.) must implement all methods
 * defined in this interface to ensure consistent behavior across different AI services.
 */
export interface AIProvider {
	/**
	 * Translates text from one language to another
	 *
	 * @param text - The text to be translated
	 * @param sourceLanguage - The language code of the source text (e.g., 'en', 'es')
	 * @param targetLanguage - The language code to translate to (e.g., 'fr', 'de')
	 * @param options - Optional configuration parameters for the translation (e.g., formality level, domain-specific terms)
	 * @returns Promise<TranslationResponse> - The translated text and optionally the detected source language
	 *
	 * Use case: Translating article content, user interface text, or any content that needs
	 * to be available in multiple languages for international audiences.
	 */
	translate(
		text: string,
		sourceLanguage: SupportedLanguage,
		targetLanguage: SupportedLanguage,
		options?: Record<string, unknown>,
	): Promise<TranslationResponse>;

	/**
	 * Translates text into multiple target languages simultaneously
	 *
	 * @param text - The text to be translated
	 * @param sourceLanguage - The language code of the source text
	 * @param targetLanguages - Array of language codes to translate to
	 * @param options - Optional configuration parameters for the translation
	 * @returns Promise<BatchTranslationResponse> - Array of translations for each target language
	 *
	 * Use case: Efficiently translating content into multiple languages at once, such as
	 * translating a news article into all supported languages for a global audience.
	 */
	batchTranslate(
		text: string,
		sourceLanguage: SupportedLanguage,
		targetLanguages: SupportedLanguage[],
		options?: Record<string, unknown>,
	): Promise<BatchTranslationResponse>;

	/**
	 * Generates descriptive alt text for images using AI
	 *
	 * @param request - Contains the image URL and optional context about the image
	 * @returns Promise<AltTextResponse> - The generated alt text description
	 *
	 * Use case: Automatically generating accessible alt text for images in articles,
	 * improving accessibility for users with screen readers and SEO for search engines.
	 */
	generateAltText(request: AltTextRequest): Promise<AltTextResponse>;

	/**
	 * Recommends related articles based on the content of a given article
	 *
	 * @param request - Contains the article content and number of recommendations to return
	 * @returns Promise<ArticleRecommendationResponse> - Array of recommended articles with relevance scores
	 *
	 * Use case: Suggesting related content to readers to increase engagement and time spent
	 * on the site, helping users discover more relevant content based on what they're reading.
	 */
	recommendArticles(
		request: ArticleRecommendationRequest,
	): Promise<ArticleRecommendationResponse>;
}
