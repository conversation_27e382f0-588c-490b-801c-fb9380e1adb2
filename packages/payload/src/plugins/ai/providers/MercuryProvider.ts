import { Payload } from 'payload';
import { createLogger } from '@repo/logger';
import {
	AIProvider,
	TranslationResponse,
	BatchTranslationResponse,
	AltTextRequest,
	AltTextResponse,
	ArticleRecommendationRequest,
	ArticleRecommendationResponse,
	SupportedLanguage,
} from '../types/AIProvider';

const logger = createLogger('mercury-provider');

interface MercuryTokenResponse {
	access_token: string;
	token_type: string;
	expires_in: number;
}

interface MercuryTranslationRequest {
	input: {
		project: string;
		prompt: string;
		responseFormat: string;
		source_language: string;
		target_language: string;
		text: string;
	};
}

interface MercuryTranslationItem {
	language: string;
	translation: string;
	source: string;
	mqm_score: string;
	explanation: string;
	error: string | null;
}

interface MercuryTranslationResponse {
	output: {
		translations: MercuryTranslationItem[];
	};
}

/**
 * Mercury Provider Implementation
 *
 * This provider uses Mercury (a custom AI service) for AI operations.
 * Mercury is a proprietary or internal AI service that provides
 * specialized translation and content analysis capabilities.
 *
 * This provider is currently used for:
 * - Text translation (single and batch)
 * - Content analysis and processing
 *
 * Note: Only translate and batchTranslate are implemented.
 * Other methods throw "Unimplemented" errors.
 */
export class MercuryProvider implements AIProvider {
	private accessToken: string | null = null;
	private tokenExpiry: number = 0;
	private readonly TOKEN_URL = 'https://authz.mercury.weather.com/oauth2/token';
	private readonly API_URL = 'https://translator.mercury.weather.com/v2';

	constructor(private payload: Payload) {}

	/**
	 * Gets an access token from the Mercury OAuth2 server
	 * @returns Promise with the access token
	 */
	private async getAccessToken(): Promise<string> {
		const clientId = process.env.MERCURY_CLIENT_ID;
		const clientSecret = process.env.MERCURY_CLIENT_SECRET;

		if (!clientId || !clientSecret) {
			throw new Error(
				'MERCURY_CLIENT_ID and MERCURY_CLIENT_SECRET must be set',
			);
		}

		// Check if we have a valid cached token
		if (this.accessToken && Date.now() < this.tokenExpiry) {
			return this.accessToken;
		}

		logger.info('Getting new Mercury access token');

		try {
			const credentials = Buffer.from(`${clientId}:${clientSecret}`).toString(
				'base64',
			);
			const response = await fetch(this.TOKEN_URL, {
				method: 'POST',
				headers: {
					Authorization: `Basic ${credentials}`,
					'Content-Type': 'application/x-www-form-urlencoded',
				},
				body: 'grant_type=client_credentials',
			});

			if (!response.ok) {
				throw new Error(`Failed to get access token: ${response.statusText}`);
			}

			const tokenData: MercuryTokenResponse = await response.json();

			// Cache the token with expiry (subtract 60 seconds for safety)
			this.accessToken = tokenData.access_token;
			this.tokenExpiry = Date.now() + (tokenData.expires_in - 60) * 1000;

			logger.info('Successfully obtained Mercury access token');
			return this.accessToken;
		} catch (error) {
			logger.error('Failed to get Mercury access token', { error });
			throw error;
		}
	}

	/**
	 * Translates text using Mercury AI service
	 *
	 * This method calls Mercury's translation API endpoint. Mercury
	 * has specialized translation models optimized for specific domains or content types.
	 *
	 * @param text - Text to translate
	 * @param sourceLanguage - Source language code
	 * @param targetLanguage - Target language code
	 * @param _options - Translation options (currently unused, prefixed with _)
	 * @returns Promise with translated text and detected source language
	 */
	async translate(
		text: string,
		sourceLanguage: SupportedLanguage,
		targetLanguage: SupportedLanguage,
		_options?: Record<string, unknown>,
	): Promise<TranslationResponse> {
		logger.info('Translating text with Mercury', {
			sourceLanguage,
			targetLanguage,
			textLength: text.length,
		});

		try {
			const accessToken = await this.getAccessToken();

			const payload: MercuryTranslationRequest = {
				input: {
					project: 'translator',
					prompt: 'translate_json',
					responseFormat: 'text',
					source_language: sourceLanguage,
					target_language: targetLanguage,
					text: text,
				},
			};

			const response = await fetch(this.API_URL, {
				method: 'POST',
				headers: {
					Authorization: `Bearer ${accessToken}`,
					Accept: 'application/json',
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(payload),
			});

			if (!response.ok) {
				throw new Error(`Mercury translation failed: ${response.statusText}`);
			}

			const result: MercuryTranslationResponse = await response.json();

			// For single translation, we expect one translation item
			const translationItem = result.output.translations[0];
			if (!translationItem) {
				throw new Error('No translation received from Mercury API');
			}

			return {
				translation: {
					translatedText: translationItem.translation,
					detectedSourceLanguage: sourceLanguage,
				},
			};
		} catch (error) {
			logger.error('Mercury translation failed', { error });
			// Fallback to original text if translation fails
			return {
				translation: {
					translatedText: text,
					detectedSourceLanguage: sourceLanguage,
				},
			};
		}
	}

	/**
	 * Batch translates text into multiple languages using Mercury
	 *
	 * This method calls Mercury's translation endpoint for each target language, which can
	 * efficiently process multiple language pairs in a single request.
	 *
	 * @param text - Text to translate
	 * @param sourceLanguage - Source language code
	 * @param targetLanguages - Array of target language codes
	 * @param _options - Translation options (currently unused, prefixed with _)
	 * @returns Promise with array of translations for each target language
	 */
	async batchTranslate(
		text: string,
		sourceLanguage: SupportedLanguage,
		targetLanguages: SupportedLanguage[],
		_options?: Record<string, unknown>,
	): Promise<BatchTranslationResponse> {
		logger.info('Batch translating text with Mercury', {
			sourceLanguage,
			targetLanguages,
			textLength: text.length,
		});

		try {
			// Get access token once for all translations
			const accessToken = await this.getAccessToken();

			// For batch translation, we send all target languages in one request
			const payload: MercuryTranslationRequest = {
				input: {
					project: 'translator',
					prompt: 'translate_json',
					responseFormat: 'text',
					source_language: sourceLanguage,
					target_language: targetLanguages.join(','), // Join multiple languages with comma
					text: text,
				},
			};

			const response = await fetch(this.API_URL, {
				method: 'POST',
				headers: {
					Authorization: `Bearer ${accessToken}`,
					Accept: 'application/json',
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(payload),
			});

			if (!response.ok) {
				throw new Error(
					`Mercury batch translation failed: ${response.statusText}`,
				);
			}

			const result: MercuryTranslationResponse = await response.json();

			// Transform the response to match our expected format
			const translations = result.output.translations.map((item) => ({
				targetLanguage: item.language,
				translatedText: item.translation,
			}));

			return {
				batchTranslation: { translations },
			};
		} catch (error) {
			logger.error('Mercury batch translation failed', { error });
			// Fallback to original text for all languages if batch translation fails
			const translations = targetLanguages.map((targetLanguage) => ({
				targetLanguage,
				translatedText: text,
			}));
			return {
				batchTranslation: { translations },
			};
		}
	}

	/**
	 * Generates alt text for images using Mercury vision capabilities
	 *
	 * This method would use Mercury's image analysis capabilities to generate
	 * descriptive alt text. Mercury may have specialized models for different
	 * types of content (news, weather, sports, etc.).
	 *
	 * @param request - Contains image URL and optional context
	 * @returns Promise with generated alt text
	 */
	async generateAltText(request: AltTextRequest): Promise<AltTextResponse> {
		logger.info('Generating alt text with Mercury', {
			hasImageUrl: !!request.imageUrl,
			hasImageBase64: !!request.imageBase64,
			hasContext: !!request.context,
		});
		throw new Error('MercuryProvider.generateAltText is not implemented');
	}

	/**
	 * Recommends articles using Mercury content analysis
	 *
	 * This method would use Mercury's content analysis capabilities to find
	 * similar articles. Mercury may have domain-specific models trained on
	 * news and weather content for better relevance.
	 *
	 * @param request - Contains article content and number of recommendations
	 * @returns Promise with array of recommended articles and relevance scores
	 */
	async recommendArticles(
		request: ArticleRecommendationRequest,
	): Promise<ArticleRecommendationResponse> {
		logger.info('Recommending articles with Mercury', {
			contentLength: request.articleContent.length,
			count: request.count,
		});
		throw new Error('MercuryProvider.recommendArticles is not implemented');
	}
}
