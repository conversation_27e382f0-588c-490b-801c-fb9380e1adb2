import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { LiteLLMProvider } from './LiteLLMProvider';
import { SupportedLanguage } from '../types/AIProvider';

// Mock the logger
vi.mock('@repo/logger', () => ({
	createLogger: vi.fn(() => ({
		info: vi.fn(),
		error: vi.fn(),
		warn: vi.fn(),
		debug: vi.fn(),
	})),
}));

// Mock the Vercel AI SDK
vi.mock('@ai-sdk/openai-compatible', () => ({
	createOpenAICompatible: vi.fn(() => {
		const mockProvider = vi.fn(() => 'mocked-language-model') as any;
		mockProvider.textEmbeddingModel = vi.fn(() => 'mocked-embedding-model');
		return mockProvider;
	}),
}));

// Mock the AI SDK functions
vi.mock('ai', () => ({
	generateText: vi.fn(),
	embedMany: vi.fn(),
	cosineSimilarity: vi.fn(),
}));

// Mock fetch globally
global.fetch = vi.fn();

describe('LiteLLMProvider', () => {
	let provider: LiteLLMProvider;
	let mockPayload: any;
	let mockGenerateText: any;
	let mockEmbedMany: any;
	let mockCosineSimilarity: any;
	let mockFetch: any;

	beforeEach(async () => {
		// Clear all mocks
		vi.clearAllMocks();

		// Set required environment variables for tests
		process.env.LITELLM_BASE_URL = 'https://test-api.litellm.com';
		process.env.LITELLM_API_KEY = 'test-api-key';

		// Setup mock Payload
		mockPayload = {
			find: vi.fn(),
		};

		// Setup AI SDK mocks
		const aiModule = await import('ai');
		mockGenerateText = vi.mocked(aiModule.generateText);
		mockEmbedMany = vi.mocked(aiModule.embedMany);
		mockCosineSimilarity = vi.mocked(aiModule.cosineSimilarity);
		mockFetch = vi.mocked(global.fetch);

		// Create provider instance
		provider = new LiteLLMProvider(mockPayload);
	});

	afterEach(() => {
		vi.restoreAllMocks();
		// Clean up environment variables
		delete process.env.LITELLM_BASE_URL;
		delete process.env.LITELLM_API_KEY;
		delete process.env.LITELLM_TRANSLATION_MODEL;
		delete process.env.LITELLM_VISION_MODEL;
		delete process.env.LITELLM_EMBEDDING_MODEL;
	});

	describe('constructor', () => {
		it('should create a LiteLLMProvider instance', () => {
			expect(provider).toBeInstanceOf(LiteLLMProvider);
		});

		it('should initialize the openai-compatible provider', async () => {
			const { createOpenAICompatible } = await import(
				'@ai-sdk/openai-compatible'
			);
			expect(createOpenAICompatible).toHaveBeenCalledWith({
				baseURL: process.env.LITELLM_BASE_URL || '',
				apiKey: process.env.LITELLM_API_KEY || '',
				name: 'openai-compatible',
			});
		});
	});

	describe('translate', () => {
		const mockText = 'Hello world';
		const mockSourceLanguage: SupportedLanguage = 'en';
		const mockTargetLanguage: SupportedLanguage = 'es';

		it('should translate text successfully', async () => {
			// Set required environment variable
			process.env.LITELLM_TRANSLATION_MODEL = 'gpt-3.5-turbo';

			mockGenerateText.mockResolvedValue({
				text: 'Hola mundo',
			});

			const result = await provider.translate(
				mockText,
				mockSourceLanguage,
				mockTargetLanguage,
			);

			expect(mockGenerateText).toHaveBeenCalledWith({
				model: 'mocked-language-model',
				system: `You are a professional translator. Translate the following text from ${mockSourceLanguage} to ${mockTargetLanguage}. It is text for a website article. Return only the translated text without any additional formatting or explanations.`,
				prompt: mockText,
			});
			expect(result).toEqual({
				translation: {
					translatedText: 'Hola mundo',
					detectedSourceLanguage: mockSourceLanguage,
				},
			});

			// Clean up environment variable
			delete process.env.LITELLM_TRANSLATION_MODEL;
		});

		it('should throw error when LITELLM_TRANSLATION_MODEL is missing', async () => {
			// Clear environment variable
			delete process.env.LITELLM_TRANSLATION_MODEL;

			await expect(
				provider.translate(mockText, mockSourceLanguage, mockTargetLanguage),
			).rejects.toThrow('LITELLM_TRANSLATION_MODEL must be set');
		});

		it('should fallback to original text on translation failure', async () => {
			// Set required environment variable
			process.env.LITELLM_TRANSLATION_MODEL = 'gpt-3.5-turbo';

			mockGenerateText.mockRejectedValue(new Error('Translation failed'));

			const result = await provider.translate(
				mockText,
				mockSourceLanguage,
				mockTargetLanguage,
			);

			expect(result).toEqual({
				translation: {
					translatedText: mockText,
					detectedSourceLanguage: mockSourceLanguage,
				},
			});

			// Clean up environment variable
			delete process.env.LITELLM_TRANSLATION_MODEL;
		});

		it('should use environment variable for translation model', async () => {
			const originalEnv = process.env.LITELLM_TRANSLATION_MODEL;
			process.env.LITELLM_TRANSLATION_MODEL = 'custom-model';

			mockGenerateText.mockResolvedValue({ text: 'Translated' });

			await provider.translate(
				mockText,
				mockSourceLanguage,
				mockTargetLanguage,
			);

			expect(mockGenerateText).toHaveBeenCalledWith(
				expect.objectContaining({
					model: 'mocked-language-model',
				}),
			);

			// Restore environment
			if (originalEnv) {
				process.env.LITELLM_TRANSLATION_MODEL = originalEnv;
			} else {
				delete process.env.LITELLM_TRANSLATION_MODEL;
			}
		});
	});

	describe('batchTranslate', () => {
		const mockText = 'Hello world';
		const mockSourceLanguage: SupportedLanguage = 'en';
		const mockTargetLanguages: SupportedLanguage[] = ['es', 'fr', 'de'];

		it('should batch translate text successfully', async () => {
			// Set required environment variable
			process.env.LITELLM_TRANSLATION_MODEL = 'gpt-3.5-turbo';

			mockGenerateText
				.mockResolvedValueOnce({ text: 'Hola mundo' })
				.mockResolvedValueOnce({ text: 'Bonjour le monde' })
				.mockResolvedValueOnce({ text: 'Hallo Welt' });

			const result = await provider.batchTranslate(
				mockText,
				mockSourceLanguage,
				mockTargetLanguages,
			);

			expect(mockGenerateText).toHaveBeenCalledTimes(3);
			expect(result).toEqual({
				batchTranslation: {
					translations: [
						{ targetLanguage: 'es', translatedText: 'Hola mundo' },
						{ targetLanguage: 'fr', translatedText: 'Bonjour le monde' },
						{ targetLanguage: 'de', translatedText: 'Hallo Welt' },
					],
				},
			});

			// Clean up environment variable
			delete process.env.LITELLM_TRANSLATION_MODEL;
		});

		it('should throw error when LITELLM_TRANSLATION_MODEL is missing', async () => {
			// Clear environment variable
			delete process.env.LITELLM_TRANSLATION_MODEL;

			await expect(
				provider.batchTranslate(
					mockText,
					mockSourceLanguage,
					mockTargetLanguages,
				),
			).rejects.toThrow('LITELLM_TRANSLATION_MODEL must be set');
		});

		it('should handle partial failures in batch translation', async () => {
			// Set required environment variable
			process.env.LITELLM_TRANSLATION_MODEL = 'gpt-3.5-turbo';

			mockGenerateText
				.mockResolvedValueOnce({ text: 'Hola mundo' })
				.mockRejectedValueOnce(new Error('Translation failed'))
				.mockResolvedValueOnce({ text: 'Hallo Welt' });

			const result = await provider.batchTranslate(
				mockText,
				mockSourceLanguage,
				mockTargetLanguages,
			);

			// The actual implementation falls back to original text for all translations when any fails
			expect(result.batchTranslation.translations).toEqual([
				{ targetLanguage: 'es', translatedText: mockText }, // Fallback due to error
				{ targetLanguage: 'fr', translatedText: mockText }, // Fallback due to error
				{ targetLanguage: 'de', translatedText: mockText }, // Fallback due to error
			]);

			// Clean up environment variable
			delete process.env.LITELLM_TRANSLATION_MODEL;
		});

		it('should fallback to original text for all languages on complete failure', async () => {
			// Set required environment variable
			process.env.LITELLM_TRANSLATION_MODEL = 'gpt-3.5-turbo';

			mockGenerateText.mockRejectedValue(new Error('All translations failed'));

			const result = await provider.batchTranslate(
				mockText,
				mockSourceLanguage,
				mockTargetLanguages,
			);

			expect(result.batchTranslation.translations).toEqual([
				{ targetLanguage: 'es', translatedText: mockText },
				{ targetLanguage: 'fr', translatedText: mockText },
				{ targetLanguage: 'de', translatedText: mockText },
			]);

			// Clean up environment variable
			delete process.env.LITELLM_TRANSLATION_MODEL;
		});
	});

	describe('generateAltText', () => {
		const mockImageUrl = 'https://example.com/image.jpg';
		const mockImageBase64 = 'data:image/jpeg;base64,SGVsbG8gV29ybGQ='; // Valid base64 for "Hello World"
		const mockContext = 'Weather forecast image';

		it('should generate alt text with imageUrl successfully', async () => {
			// Set required environment variable
			process.env.LITELLM_VISION_MODEL = 'gpt-4-vision-preview';

			mockGenerateText.mockResolvedValue({
				text: 'A weather map showing precipitation patterns',
			});

			const result = await provider.generateAltText({
				imageUrl: mockImageUrl,
				context: mockContext,
			});

			expect(mockFetch).not.toHaveBeenCalled();
			expect(mockGenerateText).toHaveBeenCalledWith({
				model: 'mocked-language-model',
				system:
					'You are an accessibility expert. Generate concise, descriptive alt text for images. Focus on accessibility and describe the key visual elements.',
				messages: [
					{
						role: 'user',
						content: [
							{
								type: 'text',
								text: 'Generate alt text for this image with context: Weather forecast image',
							},
							{
								type: 'image',
								image: mockImageUrl,
							},
						],
					},
				],
			});
			expect(result).toEqual({
				altText: 'A weather map showing precipitation patterns',
			});

			// Clean up environment variable
			delete process.env.LITELLM_VISION_MODEL;
		});

		it('should generate alt text with imageBase64 successfully', async () => {
			// Set required environment variable
			process.env.LITELLM_VISION_MODEL = 'gpt-4-vision-preview';

			mockGenerateText.mockResolvedValue({
				text: 'A weather map showing precipitation patterns',
			});

			const result = await provider.generateAltText({
				imageBase64: mockImageBase64,
				context: mockContext,
			});

			expect(mockFetch).not.toHaveBeenCalled();
			expect(mockGenerateText).toHaveBeenCalledWith({
				model: 'mocked-language-model',
				system:
					'You are an accessibility expert. Generate concise, descriptive alt text for images. Focus on accessibility and describe the key visual elements.',
				messages: [
					{
						role: 'user',
						content: [
							{
								type: 'text',
								text: 'Generate alt text for this image with context: Weather forecast image',
							},
							{
								type: 'image',
								image: mockImageBase64,
							},
						],
					},
				],
			});
			expect(result).toEqual({
				altText: 'A weather map showing precipitation patterns',
			});

			// Clean up environment variable
			delete process.env.LITELLM_VISION_MODEL;
		});

		it('should throw error when LITELLM_VISION_MODEL is missing', async () => {
			// Clear environment variable
			delete process.env.LITELLM_VISION_MODEL;

			await expect(
				provider.generateAltText({ imageUrl: mockImageUrl }),
			).rejects.toThrow('LITELLM_VISION_MODEL must be set');
		});

		it('should handle missing image source', async () => {
			// Set required environment variable
			process.env.LITELLM_VISION_MODEL = 'gpt-4-vision-preview';

			// The actual implementation has a try-catch that returns fallback value
			const result = await provider.generateAltText({});
			expect(result).toEqual({ altText: 'An Image' });

			// Clean up environment variable
			delete process.env.LITELLM_VISION_MODEL;
		});

		it('should handle invalid base64 data', async () => {
			// Set required environment variable
			process.env.LITELLM_VISION_MODEL = 'gpt-4-vision-preview';

			// The actual implementation has a try-catch that returns fallback value
			const result = await provider.generateAltText({
				imageBase64: 'invalid-base64-data',
			});
			expect(result).toEqual({ altText: 'An Image' });

			// Clean up environment variable
			delete process.env.LITELLM_VISION_MODEL;
		});

		it('should handle invalid image URL', async () => {
			// Set required environment variable
			process.env.LITELLM_VISION_MODEL = 'gpt-4-vision-preview';

			// The actual implementation has a try-catch that returns fallback value
			const result = await provider.generateAltText({
				imageUrl: 'not-a-valid-url',
			});
			expect(result).toEqual({ altText: 'An Image' });

			// Clean up environment variable
			delete process.env.LITELLM_VISION_MODEL;
		});

		it('should handle alt text generation failure', async () => {
			// Set required environment variable
			process.env.LITELLM_VISION_MODEL = 'gpt-4-vision-preview';

			mockGenerateText.mockRejectedValue(
				new Error('Alt text generation failed'),
			);

			const result = await provider.generateAltText({
				imageUrl: mockImageUrl,
			});

			expect(result).toEqual({ altText: 'An Image' });

			// Clean up environment variable
			delete process.env.LITELLM_VISION_MODEL;
		});

		it('should use environment variable for vision model', async () => {
			const originalEnv = process.env.LITELLM_VISION_MODEL;
			process.env.LITELLM_VISION_MODEL = 'custom-vision-model';

			mockGenerateText.mockResolvedValue({ text: 'Alt text' });

			await provider.generateAltText({ imageUrl: mockImageUrl });

			expect(mockGenerateText).toHaveBeenCalledWith(
				expect.objectContaining({
					model: 'mocked-language-model',
				}),
			);

			// Restore environment
			if (originalEnv) {
				process.env.LITELLM_VISION_MODEL = originalEnv;
			} else {
				delete process.env.LITELLM_VISION_MODEL;
			}
		});
	});

	describe('recommendArticles', () => {
		const mockArticleContent = 'This is a weather article about storms';
		const mockCount = 3;

		it('should recommend articles successfully with plain text content', async () => {
			// Set required environment variable
			process.env.LITELLM_EMBEDDING_MODEL = 'text-embedding-3-small';

			const mockArticles = {
				docs: [
					{ id: '1', title: 'Weather Article 1', subHeadline: 'About rain' },
					{ id: '2', title: 'Weather Article 2', subHeadline: 'About snow' },
					{ id: '3', title: 'Weather Article 3', subHeadline: 'About wind' },
				],
			};

			mockPayload.find.mockResolvedValue(mockArticles);
			mockEmbedMany.mockResolvedValue({
				embeddings: [
					[0.1, 0.2, 0.3], // Input article embedding
					[0.4, 0.5, 0.6], // Article 1 embedding
					[0.7, 0.8, 0.9], // Article 2 embedding
					[0.2, 0.3, 0.4], // Article 3 embedding
				],
			});
			mockCosineSimilarity
				.mockReturnValueOnce(0.8) // Article 1 similarity
				.mockReturnValueOnce(0.6) // Article 2 similarity
				.mockReturnValueOnce(0.9); // Article 3 similarity

			const result = await provider.recommendArticles({
				articleContent: mockArticleContent,
				count: mockCount,
			});

			expect(mockPayload.find).toHaveBeenCalledWith({
				collection: 'articles',
				limit: 50,
			});
			expect(mockEmbedMany).toHaveBeenCalledWith({
				model: 'mocked-embedding-model',
				values: expect.arrayContaining([
					mockArticleContent.substring(0, 1000),
					'Weather Article 1 About rain',
					'Weather Article 2 About snow',
					'Weather Article 3 About wind',
				]),
			});
			expect(result.articleRecommendations.recommendations).toHaveLength(3);
			expect(
				result.articleRecommendations.recommendations[0]?.relevanceScore,
			).toBe(0.9); // Highest similarity

			// Clean up environment variable
			delete process.env.LITELLM_EMBEDDING_MODEL;
		});

		it('should recommend articles successfully with JSON content', async () => {
			// Set required environment variable
			process.env.LITELLM_EMBEDDING_MODEL = 'text-embedding-3-small';

			const mockArticles = {
				docs: [
					{ id: '1', title: 'Weather Article 1', subHeadline: 'About rain' },
					{ id: '2', title: 'Weather Article 2', subHeadline: 'About snow' },
				],
			};

			// Mock JSON content from Lexical rich text editor
			const mockJsonContent = JSON.stringify({
				body: {
					root: {
						type: 'root',
						children: [
							{
								type: 'paragraph',
								children: [
									{
										type: 'text',
										text: 'This is a weather article about storms and precipitation patterns.',
										version: 1,
									},
								],
								version: 1,
							},
						],
						version: 1,
					},
				},
			});

			mockPayload.find.mockResolvedValue(mockArticles);
			mockEmbedMany.mockResolvedValue({
				embeddings: [
					[0.1, 0.2, 0.3], // Input article embedding (extracted text)
					[0.4, 0.5, 0.6], // Article 1 embedding
					[0.7, 0.8, 0.9], // Article 2 embedding
				],
			});
			mockCosineSimilarity
				.mockReturnValueOnce(0.8) // Article 1 similarity
				.mockReturnValueOnce(0.9); // Article 2 similarity

			const result = await provider.recommendArticles({
				articleContent: mockJsonContent,
				count: mockCount,
			});

			expect(mockPayload.find).toHaveBeenCalledWith({
				collection: 'articles',
				limit: 50,
			});
			expect(mockEmbedMany).toHaveBeenCalledWith({
				model: 'mocked-embedding-model',
				values: expect.arrayContaining([
					'This is a weather article about storms and precipitation patterns.', // Extracted text from JSON
					'Weather Article 1 About rain',
					'Weather Article 2 About snow',
				]),
			});
			expect(result.articleRecommendations.recommendations).toHaveLength(2);
			expect(
				result.articleRecommendations.recommendations[0]?.relevanceScore,
			).toBe(0.9); // Highest similarity

			// Clean up environment variable
			delete process.env.LITELLM_EMBEDDING_MODEL;
		});

		it('should throw error when LITELLM_EMBEDDING_MODEL is missing', async () => {
			// Clear environment variable
			delete process.env.LITELLM_EMBEDDING_MODEL;

			await expect(
				provider.recommendArticles({
					articleContent: mockArticleContent,
					count: mockCount,
				}),
			).rejects.toThrow('LITELLM_EMBEDDING_MODEL must be set');
		});

		it('should handle empty articles list', async () => {
			// Set required environment variable
			process.env.LITELLM_EMBEDDING_MODEL = 'text-embedding-3-small';

			mockPayload.find.mockResolvedValue({ docs: [] });

			const result = await provider.recommendArticles({
				articleContent: mockArticleContent,
				count: mockCount,
			});

			expect(result.articleRecommendations.recommendations).toEqual([]);

			// Clean up environment variable
			delete process.env.LITELLM_EMBEDDING_MODEL;
		});

		it('should handle embedding generation failure', async () => {
			// Set required environment variable
			process.env.LITELLM_EMBEDDING_MODEL = 'text-embedding-3-small';

			mockPayload.find.mockResolvedValue({
				docs: [{ id: '1', title: 'Test Article' }],
			});
			mockEmbedMany.mockRejectedValue(new Error('Embedding failed'));

			const result = await provider.recommendArticles({
				articleContent: mockArticleContent,
				count: mockCount,
			});

			expect(result.articleRecommendations.recommendations).toEqual([]);

			// Clean up environment variable
			delete process.env.LITELLM_EMBEDDING_MODEL;
		});

		it('should handle missing input embedding', async () => {
			// Set required environment variable
			process.env.LITELLM_EMBEDDING_MODEL = 'text-embedding-3-small';

			mockPayload.find.mockResolvedValue({
				docs: [{ id: '1', title: 'Test Article' }],
			});
			mockEmbedMany.mockResolvedValue({
				embeddings: [null, [0.1, 0.2, 0.3]], // Missing input embedding
			});

			// The actual implementation has a try-catch that returns empty recommendations
			const result = await provider.recommendArticles({
				articleContent: mockArticleContent,
				count: mockCount,
			});
			expect(result.articleRecommendations.recommendations).toEqual([]);

			// Clean up environment variable
			delete process.env.LITELLM_EMBEDDING_MODEL;
		});

		it('should use environment variable for embedding model', async () => {
			const originalEnv = process.env.LITELLM_EMBEDDING_MODEL;
			process.env.LITELLM_EMBEDDING_MODEL = 'custom-embedding-model';

			mockPayload.find.mockResolvedValue({ docs: [] });

			await provider.recommendArticles({
				articleContent: mockArticleContent,
				count: mockCount,
			});

			// The embedding model is created but not called when docs is empty
			expect(mockPayload.find).toHaveBeenCalledWith({
				collection: 'articles',
				limit: 50,
			});

			// Restore environment
			if (originalEnv) {
				process.env.LITELLM_EMBEDDING_MODEL = originalEnv;
			} else {
				delete process.env.LITELLM_EMBEDDING_MODEL;
			}
		});
	});

	describe('error handling', () => {
		it('should handle logger errors gracefully', async () => {
			// Set required environment variable
			process.env.LITELLM_TRANSLATION_MODEL = 'gpt-3.5-turbo';

			const loggerModule = await import('@repo/logger');
			const mockLogger = vi.mocked(loggerModule.createLogger)('test-logger');
			vi.mocked(mockLogger.error).mockImplementation(() => {
				throw new Error('Logger error');
			});

			// Should not throw even if logger fails
			await expect(
				provider.translate('test', 'en', 'es'),
			).resolves.toBeDefined();

			// Clean up environment variable
			delete process.env.LITELLM_TRANSLATION_MODEL;
		});

		it('should handle null embeddings in similarity calculation', async () => {
			// Set required environment variable
			process.env.LITELLM_EMBEDDING_MODEL = 'text-embedding-3-small';

			mockPayload.find.mockResolvedValue({
				docs: [
					{ id: '1', title: 'Article 1' },
					{ id: '2', title: 'Article 2' },
				],
			});
			mockEmbedMany.mockResolvedValue({
				embeddings: [
					[0.1, 0.2, 0.3], // Input embedding
					null, // Null embedding for first article
					[0.4, 0.5, 0.6], // Valid embedding for second article
				],
			});
			mockCosineSimilarity.mockReturnValue(0.7);

			const result = await provider.recommendArticles({
				articleContent: 'test content',
				count: 2,
			});

			// Should only include articles with valid embeddings
			expect(result.articleRecommendations.recommendations).toHaveLength(1);

			// Clean up environment variable
			delete process.env.LITELLM_EMBEDDING_MODEL;
		});
	});

	describe('interface compliance', () => {
		it('should implement all AIProvider interface methods', () => {
			expect(typeof provider.translate).toBe('function');
			expect(typeof provider.batchTranslate).toBe('function');
			expect(typeof provider.generateAltText).toBe('function');
			expect(typeof provider.recommendArticles).toBe('function');
		});

		it('should return correct response types', async () => {
			// Set required environment variables
			process.env.LITELLM_TRANSLATION_MODEL = 'gpt-3.5-turbo';
			process.env.LITELLM_VISION_MODEL = 'gpt-4-vision-preview';
			process.env.LITELLM_EMBEDDING_MODEL = 'text-embedding-3-small';

			mockGenerateText.mockResolvedValue({ text: 'Translated' });
			mockPayload.find.mockResolvedValue({ docs: [] });

			const translateResult = await provider.translate('test', 'en', 'es');
			expect(translateResult).toHaveProperty('translation');
			expect(translateResult.translation).toHaveProperty('translatedText');
			expect(translateResult.translation).toHaveProperty(
				'detectedSourceLanguage',
			);

			const batchResult = await provider.batchTranslate('test', 'en', ['es']);
			expect(batchResult).toHaveProperty('batchTranslation');
			expect(batchResult.batchTranslation).toHaveProperty('translations');
			expect(Array.isArray(batchResult.batchTranslation.translations)).toBe(
				true,
			);

			mockGenerateText.mockResolvedValue({ text: 'Alt text' });

			const altTextResult = await provider.generateAltText({
				imageUrl: 'test.jpg',
			});
			expect(altTextResult).toHaveProperty('altText');

			const recommendResult = await provider.recommendArticles({
				articleContent: 'test',
				count: 1,
			});
			expect(recommendResult).toHaveProperty('articleRecommendations');
			expect(recommendResult.articleRecommendations).toHaveProperty(
				'recommendations',
			);
			expect(
				Array.isArray(recommendResult.articleRecommendations.recommendations),
			).toBe(true);

			// Clean up environment variables
			delete process.env.LITELLM_TRANSLATION_MODEL;
			delete process.env.LITELLM_VISION_MODEL;
			delete process.env.LITELLM_EMBEDDING_MODEL;
		});
	});
});
