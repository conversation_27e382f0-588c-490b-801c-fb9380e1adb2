// NOTE: The following custom AI job task slugs must be registered in payload.config.ts under jobs.tasks:
// 'aiTranslate', 'aiBatchTranslate', 'aiGenerateAltText', 'aiRecommendArticles'
import { Payload } from 'payload';
import { createLogger } from '@repo/logger';
import {
	createOpenAICompatible,
	OpenAICompatibleProvider,
} from '@ai-sdk/openai-compatible';
import { generateText, embedMany, cosineSimilarity } from 'ai';
import { hasLength } from '@repo/utils/hasLength';
import { isValidBase64 } from '../../../utils/isValidBase64';
import { isValidUrl } from '../../../utils/isValidUrl';
import { extractTextFromJson } from '../../../utils/extractTextFromJson';
import type { Article } from '../../../payload-types';
import {
	AIProvider,
	TranslationResponse,
	BatchTranslationResponse,
	AltTextRequest,
	AltTextResponse,
	ArticleRecommendationRequest,
	ArticleRecommendationResponse,
	SupportedLanguage,
} from '../types/AIProvider';

const logger = createLogger('litellm-provider');

/**
 * LiteLLM Provider Implementation
 *
 * This provider uses LiteLLM (Lightweight Large Language Model) for AI operations.
 * LiteLLM is a library that provides a unified interface to various LLM providers
 * like OpenAI, Anthropic, Cohere, etc.
 *
 * This provider is currently used for:
 * - Alt text generation (using vision models)
 * - Article recommendations (using text analysis models)
 *
 * Implementation uses Vercel AI SDK 5 with openai-compatible provider
 * for actual AI operations.
 */
export class LiteLLMProvider implements AIProvider {
	private provider: OpenAICompatibleProvider;

	constructor(private payload: Payload) {
		// Initialize the openai-compatible client for LiteLLM
		// Configure with your LiteLLM endpoint and API key
		const baseURL = process.env.LITELLM_BASE_URL;
		const apiKey = process.env.LITELLM_API_KEY;

		if (!baseURL || !apiKey) {
			throw new Error('LITELLM_BASE_URL and LITELLM_API_KEY must be set');
		}

		this.provider = createOpenAICompatible({
			baseURL,
			apiKey,
			name: 'openai-compatible',
		});
	}

	/**
	 * Translates text using LiteLLM
	 *
	 * This method uses the openai-compatible client to translate text
	 * through LiteLLM, which can route to different providers based on the model specified.
	 *
	 * @param text - Text to translate
	 * @param sourceLanguage - Source language code
	 * @param targetLanguage - Target language code
	 * @param _options - Translation options (currently unused, prefixed with _)
	 * @returns Promise with translated text and detected source language
	 */
	async translate(
		text: string,
		sourceLanguage: SupportedLanguage,
		targetLanguage: SupportedLanguage,
		_options?: Record<string, unknown>,
	): Promise<TranslationResponse> {
		logger.info('Translating text with LiteLLM', {
			sourceLanguage,
			targetLanguage,
			textLength: text.length,
		});

		// Validate required environment variables
		const model = process.env.LITELLM_TRANSLATION_MODEL;
		if (!model) {
			throw new Error('LITELLM_TRANSLATION_MODEL must be set');
		}

		try {
			const { text: translatedText } = await generateText({
				model: this.provider(model),
				// TODO: Get system prompt from Langfuse instead of hardcoding
				system: `You are a professional translator. Translate the following text from ${sourceLanguage} to ${targetLanguage}. It is text for a website article. Return only the translated text without any additional formatting or explanations.`,
				prompt: text,
			});

			return {
				translation: {
					translatedText,
					detectedSourceLanguage: sourceLanguage,
				},
			};
		} catch (error) {
			logger.error('Translation failed', { error });
			// Fallback to original text if translation fails
			return {
				translation: {
					translatedText: text,
					detectedSourceLanguage: sourceLanguage,
				},
			};
		}
	}

	/**
	 * Batch translates text into multiple languages using LiteLLM
	 *
	 * This method makes parallel translation requests for each target language
	 * using the openai-compatible client.
	 *
	 * @param text - Text to translate
	 * @param sourceLanguage - Source language code
	 * @param targetLanguages - Array of target language codes
	 * @param _options - Translation options (currently unused, prefixed with _)
	 * @returns Promise with array of translations for each target language
	 */
	async batchTranslate(
		text: string,
		sourceLanguage: SupportedLanguage,
		targetLanguages: SupportedLanguage[],
		_options?: Record<string, unknown>,
	): Promise<BatchTranslationResponse> {
		logger.info('Batch translating text with LiteLLM', {
			sourceLanguage,
			targetLanguages,
			textLength: text.length,
		});

		// Validate required environment variables
		const model = process.env.LITELLM_TRANSLATION_MODEL;
		if (!model) {
			throw new Error('LITELLM_TRANSLATION_MODEL must be set');
		}

		try {
			// Create parallel translation requests
			const translationPromises = targetLanguages.map(
				async (targetLanguage) => {
					const { text: translatedText } = await generateText({
						model: this.provider(model),
						// TODO: Get system prompt from Langfuse instead of hardcoding
						system: `You are a professional translator. Translate the following text from ${sourceLanguage} to ${targetLanguage}. It is text for a website article. Return only the translated text without any additional formatting or explanations.`,
						prompt: text,
					});

					return {
						targetLanguage,
						translatedText: translatedText || text,
					};
				},
			);

			const translations = await Promise.all(translationPromises);
			return {
				batchTranslation: { translations },
			};
		} catch (error) {
			logger.error('Batch translation failed', { error });
			// Fallback to original text for all languages if batch translation fails
			const translations = targetLanguages.map((targetLanguage) => ({
				targetLanguage,
				translatedText: text,
			}));
			return {
				batchTranslation: { translations },
			};
		}
	}

	/**
	 * Generates alt text for images using LiteLLM vision models
	 *
	 * This method uses a vision-capable model to analyze the image and generate
	 * descriptive alt text. The context parameter can provide additional information
	 * to improve the quality of the description.
	 *
	 * @param request - Contains image URL and optional context
	 * @returns Promise with generated alt text
	 */
	async generateAltText(request: AltTextRequest): Promise<AltTextResponse> {
		logger.info('Generating alt text with LiteLLM', {
			hasImageUrl: !!request.imageUrl,
			hasImageBase64: !!request.imageBase64,
			hasContext: !!request.context,
		});

		// Validate required environment variables
		const model = process.env.LITELLM_VISION_MODEL;
		if (!model) {
			throw new Error('LITELLM_VISION_MODEL must be set');
		}

		try {
			let imageData: string;

			if (request.imageBase64) {
				// Validate base64 before using it
				if (!isValidBase64(request.imageBase64)) {
					throw new Error('Invalid base64 data provided');
				}
				// Use provided base64 directly
				imageData = request.imageBase64;
			} else if (request.imageUrl) {
				// Validate URL before using it
				if (!isValidUrl(request.imageUrl)) {
					throw new Error('Invalid image URL provided');
				}
				// Use URL directly for the image parameter
				imageData = request.imageUrl;
			} else {
				throw new Error(
					'No image source provided (imageUrl or imageBase64 required)',
				);
			}

			// Use the language model with image for vision tasks
			const { text: altText } = await generateText({
				model: this.provider(model),
				// TODO: Get system prompt from Langfuse instead of hardcoding
				system:
					'You are an accessibility expert. Generate concise, descriptive alt text for images. Focus on accessibility and describe the key visual elements.',
				messages: [
					{
						role: 'user',
						content: [
							{
								type: 'text',
								text: `Generate alt text for this image${request.context ? ` with context: ${request.context}` : ''}`,
							},
							{
								type: 'image',
								image: imageData,
							},
						],
					},
				],
			});

			return {
				altText,
			};
		} catch (error) {
			logger.error('Alt text generation failed', { error });
			// Fallback to generic alt text if generation fails
			return {
				altText: 'An Image',
			};
		}
	}

	/**
	 * Recommends articles using LiteLLM text analysis
	 *
	 * This method analyzes the content of an article and finds similar articles
	 * in the database. It uses the LLM's embedding model to analyze content and generate recommendations.
	 *
	 * @param request - Contains article content and number of recommendations
	 * @returns Promise with array of recommended articles and relevance scores
	 */
	async recommendArticles(
		request: ArticleRecommendationRequest,
	): Promise<ArticleRecommendationResponse> {
		logger.info('Recommending articles with LiteLLM', {
			contentLength: request.articleContent.length,
			count: request.count,
		});

		// Validate required environment variables
		const model = process.env.LITELLM_EMBEDDING_MODEL;
		if (!model) {
			throw new Error('LITELLM_EMBEDDING_MODEL must be set');
		}

		try {
			// First, get articles from the database
			const articles = await this.payload.find({
				collection: 'articles',
				limit: 50, // Get a reasonable sample for analysis
			});

			if (!hasLength(articles.docs)) {
				return {
					articleRecommendations: { recommendations: [] },
				};
			}

			// Prepare text content for embedding
			// Handle potential JSON content from rich text editors
			let inputText: string;
			try {
				// Try to parse as JSON first
				const parsedContent = JSON.parse(request.articleContent);
				// Extract text content from JSON structure (common in rich text editors)
				const textContent = extractTextFromJson(parsedContent);
				inputText = textContent.substring(0, 1000); // Limit input text
			} catch {
				// If not JSON, treat as plain text
				inputText = request.articleContent.substring(0, 1000); // Limit input text
			}
			const articleTexts = articles.docs.map(
				(article: Article) =>
					`${article.title || 'Untitled'} ${article.subHeadline || ''}`,
			);

			// Create embedding model
			const embeddingModel = this.provider.textEmbeddingModel(model);

			// Generate embeddings for input article and all articles
			const { embeddings } = await embedMany({
				model: embeddingModel,
				values: [inputText, ...articleTexts],
			});

			// Calculate similarities between input article and all other articles
			const inputEmbedding = embeddings[0];
			if (!inputEmbedding) {
				throw new Error('Failed to generate embedding for input article');
			}

			const similarities = embeddings
				.slice(1)
				.map((embedding, index) => {
					if (!embedding) return null;
					return {
						article: articles.docs[index],
						similarity: cosineSimilarity(inputEmbedding, embedding),
						index,
					};
				})
				.filter((item) => item !== null);

			// Sort by similarity (highest first) and take top recommendations
			const topSimilar = similarities
				.sort((a, b) => (b?.similarity || 0) - (a?.similarity || 0))
				.slice(0, request.count);

			// Convert to recommendations format
			const recommendations = topSimilar
				.map((item) => {
					if (!item?.article) return null;
					return {
						articleId: item.article.id,
						title: item.article.title || 'Untitled',
						relevanceScore: item.similarity,
					};
				})
				.filter((item) => item !== null);

			return {
				articleRecommendations: { recommendations },
			};
		} catch (error) {
			logger.error('Article recommendation failed', { error });
			// Fallback to empty recommendations if analysis fails
			return {
				articleRecommendations: { recommendations: [] },
			};
		}
	}
}
