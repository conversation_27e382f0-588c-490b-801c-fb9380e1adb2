import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { MercuryProvider } from './MercuryProvider';
import { SupportedLanguage } from '../types/AIProvider';

// Mock the logger
vi.mock('@repo/logger', () => ({
	createLogger: vi.fn(() => ({
		info: vi.fn(),
		error: vi.fn(),
		warn: vi.fn(),
		debug: vi.fn(),
	})),
}));

// Mock fetch globally
global.fetch = vi.fn();

describe('MercuryProvider', () => {
	let provider: MercuryProvider;
	let mockPayload: any;
	let mockFetch: any;

	beforeEach(() => {
		// Clear all mocks
		vi.clearAllMocks();

		// Setup mock Payload
		mockPayload = {
			find: vi.fn(),
		};

		// Setup fetch mock
		mockFetch = vi.mocked(global.fetch);

		// Create provider instance
		provider = new MercuryProvider(mockPayload);
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	describe('constructor', () => {
		it('should create a MercuryProvider instance', () => {
			expect(provider).toBeInstanceOf(MercuryProvider);
		});

		it('should store the payload instance', () => {
			expect(provider).toHaveProperty('payload', mockPayload);
		});
	});

	describe('getAccessToken', () => {
		it('should get access token successfully', async () => {
			const mockTokenResponse = {
				access_token: 'mock-access-token',
				token_type: 'Bearer',
				expires_in: 3600,
			};

			mockFetch.mockResolvedValue({
				ok: true,
				json: vi.fn().mockResolvedValue(mockTokenResponse),
			});

			// Set environment variables
			process.env.MERCURY_CLIENT_ID = 'test-client-id';
			process.env.MERCURY_CLIENT_SECRET = 'test-client-secret';

			// Access the private method through reflection or test the public methods
			const result = await provider.translate('test', 'en', 'es');

			expect(mockFetch).toHaveBeenCalledWith(
				'https://authz.mercury.weather.com/oauth2/token',
				expect.objectContaining({
					method: 'POST',
					headers: expect.objectContaining({
						Authorization: expect.stringContaining('Basic '),
						'Content-Type': 'application/x-www-form-urlencoded',
					}),
					body: 'grant_type=client_credentials',
				}),
			);

			// Clean up environment variables
			delete process.env.MERCURY_CLIENT_ID;
			delete process.env.MERCURY_CLIENT_SECRET;
		});

		it('should fallback to original text when credentials are missing', async () => {
			// Clear environment variables
			delete process.env.MERCURY_CLIENT_ID;
			delete process.env.MERCURY_CLIENT_SECRET;

			// The actual implementation falls back to original text when getAccessToken fails
			const result = await provider.translate('test', 'en', 'es');
			expect(result).toEqual({
				translation: {
					translatedText: 'test',
					detectedSourceLanguage: 'en',
				},
			});
		});

		it('should fallback to original text when token request fails', async () => {
			process.env.MERCURY_CLIENT_ID = 'test-client-id';
			process.env.MERCURY_CLIENT_SECRET = 'test-client-secret';

			mockFetch.mockResolvedValue({
				ok: false,
				statusText: 'Unauthorized',
			});

			// The actual implementation falls back to original text when getAccessToken fails
			const result = await provider.translate('test', 'en', 'es');
			expect(result).toEqual({
				translation: {
					translatedText: 'test',
					detectedSourceLanguage: 'en',
				},
			});

			// Clean up environment variables
			delete process.env.MERCURY_CLIENT_ID;
			delete process.env.MERCURY_CLIENT_SECRET;
		});
	});

	describe('translate', () => {
		const mockText = 'Hello world';
		const mockSourceLanguage: SupportedLanguage = 'en';
		const mockTargetLanguage: SupportedLanguage = 'es';

		beforeEach(() => {
			// Set up environment variables
			process.env.MERCURY_CLIENT_ID = 'test-client-id';
			process.env.MERCURY_CLIENT_SECRET = 'test-client-secret';
		});

		afterEach(() => {
			// Clean up environment variables
			delete process.env.MERCURY_CLIENT_ID;
			delete process.env.MERCURY_CLIENT_SECRET;
		});

		it('should translate text successfully', async () => {
			// Mock token response
			mockFetch
				.mockResolvedValueOnce({
					ok: true,
					json: vi.fn().mockResolvedValue({
						access_token: 'mock-access-token',
						token_type: 'Bearer',
						expires_in: 3600,
					}),
				})
				.mockResolvedValueOnce({
					ok: true,
					json: vi.fn().mockResolvedValue({
						output: {
							translations: [
								{
									language: 'es',
									translation: 'Hola mundo',
									source: 'en',
									mqm_score: '0.95',
									explanation: 'Good translation',
									error: null,
								},
							],
						},
					}),
				});

			const result = await provider.translate(
				mockText,
				mockSourceLanguage,
				mockTargetLanguage,
			);

			expect(mockFetch).toHaveBeenCalledTimes(2);
			expect(mockFetch).toHaveBeenNthCalledWith(
				2,
				'https://translator.mercury.weather.com/v2',
				expect.objectContaining({
					method: 'POST',
					headers: expect.objectContaining({
						Authorization: 'Bearer mock-access-token',
						Accept: 'application/json',
						'Content-Type': 'application/json',
					}),
					body: JSON.stringify({
						input: {
							project: 'translator',
							prompt: 'translate_json',
							responseFormat: 'text',
							source_language: mockSourceLanguage,
							target_language: mockTargetLanguage,
							text: mockText,
						},
					}),
				}),
			);

			expect(result).toEqual({
				translation: {
					translatedText: 'Hola mundo',
					detectedSourceLanguage: mockSourceLanguage,
				},
			});
		});

		it('should fallback to original text on translation failure', async () => {
			// Mock token response
			mockFetch
				.mockResolvedValueOnce({
					ok: true,
					json: vi.fn().mockResolvedValue({
						access_token: 'mock-access-token',
						token_type: 'Bearer',
						expires_in: 3600,
					}),
				})
				.mockResolvedValueOnce({
					ok: false,
					statusText: 'Translation failed',
				});

			const result = await provider.translate(
				mockText,
				mockSourceLanguage,
				mockTargetLanguage,
			);

			expect(result).toEqual({
				translation: {
					translatedText: mockText,
					detectedSourceLanguage: mockSourceLanguage,
				},
			});
		});

		it('should handle token request failure in translate', async () => {
			mockFetch.mockResolvedValue({
				ok: false,
				statusText: 'Unauthorized',
			});

			const result = await provider.translate(
				mockText,
				mockSourceLanguage,
				mockTargetLanguage,
			);

			expect(result).toEqual({
				translation: {
					translatedText: mockText,
					detectedSourceLanguage: mockSourceLanguage,
				},
			});
		});
	});

	describe('batchTranslate', () => {
		const mockText = 'Hello world';
		const mockSourceLanguage: SupportedLanguage = 'en';
		const mockTargetLanguages: SupportedLanguage[] = ['es', 'fr', 'de'];

		beforeEach(() => {
			// Set up environment variables
			process.env.MERCURY_CLIENT_ID = 'test-client-id';
			process.env.MERCURY_CLIENT_SECRET = 'test-client-secret';
		});

		afterEach(() => {
			// Clean up environment variables
			delete process.env.MERCURY_CLIENT_ID;
			delete process.env.MERCURY_CLIENT_SECRET;
		});

		it('should batch translate text successfully', async () => {
			// Mock token response
			mockFetch
				.mockResolvedValueOnce({
					ok: true,
					json: vi.fn().mockResolvedValue({
						access_token: 'mock-access-token',
						token_type: 'Bearer',
						expires_in: 3600,
					}),
				})
				.mockResolvedValueOnce({
					ok: true,
					json: vi.fn().mockResolvedValue({
						output: {
							translations: [
								{
									language: 'es',
									translation: 'Hola mundo',
									source: 'en',
									mqm_score: '0.95',
									explanation: 'Good translation',
									error: null,
								},
								{
									language: 'fr',
									translation: 'Bonjour le monde',
									source: 'en',
									mqm_score: '0.92',
									explanation: 'Good translation',
									error: null,
								},
								{
									language: 'de',
									translation: 'Hallo Welt',
									source: 'en',
									mqm_score: '0.94',
									explanation: 'Good translation',
									error: null,
								},
							],
						},
					}),
				});

			const result = await provider.batchTranslate(
				mockText,
				mockSourceLanguage,
				mockTargetLanguages,
			);

			expect(mockFetch).toHaveBeenCalledTimes(2); // 1 token + 1 batch translation
			expect(result).toEqual({
				batchTranslation: {
					translations: [
						{ targetLanguage: 'es', translatedText: 'Hola mundo' },
						{ targetLanguage: 'fr', translatedText: 'Bonjour le monde' },
						{ targetLanguage: 'de', translatedText: 'Hallo Welt' },
					],
				},
			});
		});

		it('should handle partial failures in batch translation', async () => {
			// Mock token response
			mockFetch
				.mockResolvedValueOnce({
					ok: true,
					json: vi.fn().mockResolvedValue({
						access_token: 'mock-access-token',
						token_type: 'Bearer',
						expires_in: 3600,
					}),
				})
				.mockResolvedValueOnce({
					ok: false,
					statusText: 'Translation failed',
				});

			const result = await provider.batchTranslate(
				mockText,
				mockSourceLanguage,
				mockTargetLanguages,
			);

			// The actual implementation falls back to original text for all languages when any fails
			expect(result.batchTranslation.translations).toEqual([
				{ targetLanguage: 'es', translatedText: mockText }, // Fallback
				{ targetLanguage: 'fr', translatedText: mockText }, // Fallback
				{ targetLanguage: 'de', translatedText: mockText }, // Fallback
			]);
		});

		it('should fallback to original text for all languages on complete failure', async () => {
			// Mock token response
			mockFetch
				.mockResolvedValueOnce({
					ok: true,
					json: vi.fn().mockResolvedValue({
						access_token: 'mock-access-token',
						token_type: 'Bearer',
						expires_in: 3600,
					}),
				})
				.mockResolvedValue({
					ok: false,
					statusText: 'All translations failed',
				});

			const result = await provider.batchTranslate(
				mockText,
				mockSourceLanguage,
				mockTargetLanguages,
			);

			expect(result.batchTranslation.translations).toEqual([
				{ targetLanguage: 'es', translatedText: mockText },
				{ targetLanguage: 'fr', translatedText: mockText },
				{ targetLanguage: 'de', translatedText: mockText },
			]);
		});
	});

	describe('generateAltText', () => {
		it('should throw unimplemented error', async () => {
			await expect(
				provider.generateAltText({ imageUrl: 'test.jpg' }),
			).rejects.toThrow('MercuryProvider.generateAltText is not implemented');
		});

		it('should throw unimplemented error with imageBase64', async () => {
			await expect(
				provider.generateAltText({ imageBase64: 'test-base64' }),
			).rejects.toThrow('MercuryProvider.generateAltText is not implemented');
		});

		it('should throw unimplemented error with context', async () => {
			await expect(
				provider.generateAltText({
					imageUrl: 'test.jpg',
					context: 'test context',
				}),
			).rejects.toThrow('MercuryProvider.generateAltText is not implemented');
		});
	});

	describe('recommendArticles', () => {
		it('should throw unimplemented error', async () => {
			await expect(
				provider.recommendArticles({
					articleContent: 'test content',
					count: 3,
				}),
			).rejects.toThrow('MercuryProvider.recommendArticles is not implemented');
		});

		it('should throw unimplemented error with different parameters', async () => {
			await expect(
				provider.recommendArticles({
					articleContent: 'Another test article',
					count: 1,
				}),
			).rejects.toThrow('MercuryProvider.recommendArticles is not implemented');
		});
	});

	describe('error handling', () => {
		it('should handle logger errors gracefully', async () => {
			const loggerModule = await import('@repo/logger');
			const mockLogger = vi.mocked(loggerModule.createLogger)('test-logger');
			vi.mocked(mockLogger.error).mockImplementation(() => {
				throw new Error('Logger error');
			});

			// Should not throw even if logger fails
			await expect(
				provider.translate('test', 'en', 'es'),
			).resolves.toBeDefined();
		});
	});

	describe('interface compliance', () => {
		it('should implement all AIProvider interface methods', () => {
			expect(typeof provider.translate).toBe('function');
			expect(typeof provider.batchTranslate).toBe('function');
			expect(typeof provider.generateAltText).toBe('function');
			expect(typeof provider.recommendArticles).toBe('function');
		});

		it('should return correct response types', async () => {
			// Set up environment for successful test
			process.env.MERCURY_CLIENT_ID = 'test-client-id';
			process.env.MERCURY_CLIENT_SECRET = 'test-client-secret';

			mockFetch
				.mockResolvedValueOnce({
					ok: true,
					json: vi.fn().mockResolvedValue({
						access_token: 'mock-access-token',
						token_type: 'Bearer',
						expires_in: 3600,
					}),
				})
				.mockResolvedValueOnce({
					ok: true,
					json: vi.fn().mockResolvedValue({
						output: {
							translations: [
								{
									language: 'es',
									translation: 'Translated',
									source: 'en',
									mqm_score: '0.95',
									explanation: 'Good translation',
									error: null,
								},
							],
						},
					}),
				});

			const translateResult = await provider.translate('test', 'en', 'es');
			expect(translateResult).toHaveProperty('translation');
			expect(translateResult.translation).toHaveProperty('translatedText');
			expect(translateResult.translation).toHaveProperty(
				'detectedSourceLanguage',
			);

			const batchResult = await provider.batchTranslate('test', 'en', ['es']);
			expect(batchResult).toHaveProperty('batchTranslation');
			expect(batchResult.batchTranslation).toHaveProperty('translations');
			expect(Array.isArray(batchResult.batchTranslation.translations)).toBe(
				true,
			);

			// Clean up environment variables
			delete process.env.MERCURY_CLIENT_ID;
			delete process.env.MERCURY_CLIENT_SECRET;
		});
	});
});
