import type { Plugin, Config } from 'payload';
import { Payload } from 'payload';
import { createLogger } from '@repo/logger';

const logger = createLogger('ai-plugin');

/**
 * AI Plugin
 *
 * This plugin provides AI functionality for the Payload CMS including translation,
 * alt text generation, and article recommendations.
 *
 * Features:
 * - Translation support for multiple languages
 * - Batch translation capabilities
 * - AI-powered alt text generation for images
 * - Article recommendation system
 * - Support for multiple AI providers (LiteLLM, Mercury)
 *
 * Usage:
 * ```typescript
 * import { aiPlugin } from '@/plugins/ai';
 *
 * const result = await aiPlugin.translate('Hello world', 'en', 'es');
 * ```
 */
export const aiPlugin: Plugin = (incomingConfig: Config): Config => {
	// Create a copy of the config to modify
	const config = { ...incomingConfig };

	// Add AI jobs to the config
	if (!config.jobs) {
		config.jobs = {};
	}

	// Initialize AI provider on startup
	const originalOnInit = config.onInit;
	config.onInit = async (payload: Payload) => {
		if (originalOnInit) {
			await originalOnInit(payload);
		}

		logger.info('AI Plugin initialized');
	};

	// Return the modified config
	return config;
};

// Export types for external use
export type {
	AIProvider,
	TranslationResponse,
	BatchTranslationResponse,
	AltTextRequest,
	AltTextResponse,
	ArticleRecommendationRequest,
	ArticleRecommendationResponse,
} from './types/AIProvider';

export default aiPlugin;
