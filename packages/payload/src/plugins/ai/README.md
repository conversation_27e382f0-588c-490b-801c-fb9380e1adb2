# AI Plugin

This plugin provides AI functionality for the Payload CMS including translation, alt text generation, and article recommendations.

## Features

- **Translation**: Translate text between multiple languages
- **Batch Translation**: Translate text into multiple target languages simultaneously
- **Alt Text Generation**: Generate descriptive alt text for images using AI
- **Article Recommendations**: Recommend related articles based on content
- **Multi-Provider Support**: Support for multiple AI providers (LiteLLM, Mercury)

## Structure

The plugin follows the established patterns in the codebase:

```
plugins/ai/
├── index.ts              # Plugin entry point
├── types/
│   └── AIProvider.ts     # Type definitions
├── providers/
│   ├── LiteLLMProvider.ts
│   └── MercuryProvider.ts
└── constants.ts          # Supported languages
```

Jobs are organized in the `jobs/` directory following the established pattern:

```
jobs/
├── aitranslate/
│   ├── config.ts         # Job configuration with input/output schemas
│   └── task.ts           # Task handler implementation
├── aibatchtranslate/
├── aigeneratealttext/
└── airecommendarticles/
```

## Usage

### Using Jobs

Jobs can be queued for background processing. Each job uses a specific provider hardcoded in the task handler:

```typescript
// Queue a translation job (uses Mercury provider)
await payload.jobs.queue({
	task: "aiTranslate",
	input: {
		text: "Hello world",
		sourceLanguage: "en",
		targetLanguage: "es",
		options: {},
	},
	queue: "ai",
});

// Queue a batch translation job (uses Mercury provider)
await payload.jobs.queue({
	task: "aiBatchTranslate",
	input: {
		text: "Hello world",
		sourceLanguage: "en",
		targetLanguages: [{ value: "es" }, { value: "fr" }],
		options: {},
	},
	queue: "ai",
});

// Queue an alt text generation job (uses LiteLLM provider)
await payload.jobs.queue({
	task: "aiGenerateAltText",
	input: {
		imageUrl: "https://example.com/image.jpg",
		context: "Weather forecast image",
	},
	queue: "ai",
});

// Queue an article recommendation job (uses LiteLLM provider)
await payload.jobs.queue({
	task: "aiRecommendArticles",
	input: {
		articleContent: "Article content here...",
		count: 5,
	},
	queue: "ai",
});
```

## Configuration

The plugin is automatically initialized when added to the plugins array in `payload.config.ts`. No additional configuration is needed since providers are hardcoded in the task handlers.

## Providers

### LiteLLM Provider

Provider that uses LiteLLM for AI operations. Used by alt text generation and article recommendation jobs.

### Mercury Provider

Provider that uses Mercury for AI operations. Used by translation and batch translation jobs.

## Job Schemas

### aiTranslate

**Input Schema:**

- `text` (required): Text to translate
- `sourceLanguage` (required): Source language code
- `targetLanguage` (required): Target language code
- `options` (optional): Additional options

**Output Schema:**

- `translatedText` (required): Translated text
- `detectedSourceLanguage` (optional): Detected source language

### aiBatchTranslate

**Input Schema:**

- `text` (required): Text to translate
- `sourceLanguage` (required): Source language code
- `targetLanguages` (required): Array of target language codes
- `options` (optional): Additional options

**Output Schema:**

- `translations` (required): Array of translations with target language and translated text

### aiGenerateAltText

**Input Schema:**

- `imageUrl` (required): URL of the image
- `context` (optional): Context about the image

**Output Schema:**

- `altText` (required): Generated alt text

### aiRecommendArticles

**Input Schema:**

- `articleContent` (required): Content of the article
- `count` (required): Number of recommendations to return

**Output Schema:**

- `recommendations` (required): Array of recommended articles with ID, title, and relevance score

## Development

To add a new AI provider:

1. Create a new provider class implementing the `AIProvider` interface
2. Implement the actual AI logic in the provider methods
3. Update the task handlers to use the new provider

To add a new AI operation:

1. Create a new job directory in `jobs/` with config and task files
2. Add the job configuration to the payload config
3. Update the `AIProvider` interface and provider implementations
4. Update the plugin to register the new job
