import { Locale } from 'payload';

export const locales: Locale[] = [
	// {
	// 	label: 'Ar',
	// 	code: 'ar',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (AE)',
	// 	code: 'ar-AE',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (AR)',
	// 	code: 'ar-AR',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (BH)',
	// 	code: 'ar-BH',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (DJ)',
	// 	code: 'ar-DJ',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (DZ)',
	// 	code: 'ar-DZ',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (EG)',
	// 	code: 'ar-EG',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (ER)',
	// 	code: 'ar-ER',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (IL)',
	// 	code: 'ar-IL',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (IQ)',
	// 	code: 'ar-IQ',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (JO)',
	// 	code: 'ar-JO',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (KM)',
	// 	code: 'ar-KM',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (KW)',
	// 	code: 'ar-KW',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (LB)',
	// 	code: 'ar-LB',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (LY)',
	// 	code: 'ar-LY',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (MA)',
	// 	code: 'ar-MA',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (MR)',
	// 	code: 'ar-MR',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (OM)',
	// 	code: 'ar-OM',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (QA)',
	// 	code: 'ar-QA',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (SA)',
	// 	code: 'ar-SA',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (SD)',
	// 	code: 'ar-SD',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (SO)',
	// 	code: 'ar-SO',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (SY)',
	// 	code: 'ar-SY',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (TD)',
	// 	code: 'ar-TD',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (TN)',
	// 	code: 'ar-TN',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ar (YE)',
	// 	code: 'ar-YE',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Az (AZ)',
	// 	code: 'az-AZ',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Bg (BG)',
	// 	code: 'bg-BG',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Bn (BD)',
	// 	code: 'bn-BD',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Bn (IN)',
	// 	code: 'bn-IN',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Bs (BA)',
	// 	code: 'bs-BA',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Ca (AD)',
	// 	code: 'ca-AD',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Ca (ES)',
	// 	code: 'ca-ES',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Cs (CZ)',
	// 	code: 'cs-CZ',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Da (DK)',
	// 	code: 'da-DK',
	// 	rtl: false,
	// },
	// {
	// 	label: 'De (AT)',
	// 	code: 'de-AT',
	// 	rtl: false,
	// },
	// {
	// 	label: 'De (CH)',
	// 	code: 'de-CH',
	// 	rtl: false,
	// },
	{
		label: 'De (DE)',
		code: 'de-DE',
		rtl: false,
	},
	// {
	// 	label: 'De (LI)',
	// 	code: 'de-LI',
	// 	rtl: false,
	// },
	// {
	// 	label: 'El (CY)',
	// 	code: 'el-CY',
	// 	rtl: false,
	// },
	// {
	// 	label: 'El (GR)',
	// 	code: 'el-GR',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English',
	// 	code: 'en',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (AG)',
	// 	code: 'en-AG',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (AS)',
	// 	code: 'en-AS',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (AU)',
	// 	code: 'en-AU',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (BB)',
	// 	code: 'en-BB',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (BS)',
	// 	code: 'en-BS',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (BZ)',
	// 	code: 'en-BZ',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (CA)',
	// 	code: 'en-CA',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (CM)',
	// 	code: 'en-CM',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (DM)',
	// 	code: 'en-DM',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (FJ)',
	// 	code: 'en-FJ',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (FM)',
	// 	code: 'en-FM',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (GB)',
	// 	code: 'en-GB',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (GD)',
	// 	code: 'en-GD',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (GH)',
	// 	code: 'en-GH',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (GM)',
	// 	code: 'en-GM',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (GY)',
	// 	code: 'en-GY',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (IE)',
	// 	code: 'en-IE',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (IN)',
	// 	code: 'en-IN',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (JM)',
	// 	code: 'en-JM',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (KE)',
	// 	code: 'en-KE',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (KI)',
	// 	code: 'en-KI',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (KN)',
	// 	code: 'en-KN',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (LC)',
	// 	code: 'en-LC',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (LR)',
	// 	code: 'en-LR',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (LS)',
	// 	code: 'en-LS',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (MH)',
	// 	code: 'en-MH',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (MT)',
	// 	code: 'en-MT',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (MU)',
	// 	code: 'en-MU',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (NA)',
	// 	code: 'en-NA',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (NG)',
	// 	code: 'en-NG',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (NZ)',
	// 	code: 'en-NZ',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (PA)',
	// 	code: 'en-PA',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (PH)',
	// 	code: 'en-PH',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (PK)',
	// 	code: 'en-PK',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (PW)',
	// 	code: 'en-PW',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (RW)',
	// 	code: 'en-RW',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (SB)',
	// 	code: 'en-SB',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (SG)',
	// 	code: 'en-SG',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (SL)',
	// 	code: 'en-SL',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (SS)',
	// 	code: 'en-SS',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (SZ)',
	// 	code: 'en-SZ',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (TO)',
	// 	code: 'en-TO',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (TT)',
	// 	code: 'en-TT',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (TV)',
	// 	code: 'en-TV',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (TZ)',
	// 	code: 'en-TZ',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (UG)',
	// 	code: 'en-UG',
	// 	rtl: false,
	// },
	{
		label: 'English (US)',
		code: 'en-US',
		rtl: false,
	},
	// {
	// 	label: 'English (VC)',
	// 	code: 'en-VC',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (VU)',
	// 	code: 'en-VU',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (ZA)',
	// 	code: 'en-ZA',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (ZM)',
	// 	code: 'en-ZM',
	// 	rtl: false,
	// },
	// {
	// 	label: 'English (ZW)',
	// 	code: 'en-ZW',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Es',
	// 	code: 'es',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Es (AR)',
	// 	code: 'es-AR',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Es (BO)',
	// 	code: 'es-BO',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Es (CL)',
	// 	code: 'es-CL',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Es (CO)',
	// 	code: 'es-CO',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Es (CR)',
	// 	code: 'es-CR',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Es (DO)',
	// 	code: 'es-DO',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Es (EC)',
	// 	code: 'es-EC',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Es (ES)',
	// 	code: 'es-ES',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Es (GQ)',
	// 	code: 'es-GQ',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Es (GT)',
	// 	code: 'es-GT',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Es (HN)',
	// 	code: 'es-HN',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Es (LA)',
	// 	code: 'es-LA',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Es (MX)',
	// 	code: 'es-MX',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Es (NI)',
	// 	code: 'es-NI',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Es (PA)',
	// 	code: 'es-PA',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Es (PE)',
	// 	code: 'es-PE',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Es (PY)',
	// 	code: 'es-PY',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Es (SV)',
	// 	code: 'es-SV',
	// 	rtl: false,
	// },
	{
		label: 'Es (US)',
		code: 'es-US',
		rtl: false,
	},
	// {
	// 	label: 'Es (UY)',
	// 	code: 'es-UY',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Es (VE)',
	// 	code: 'es-VE',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Et (EE)',
	// 	code: 'et-EE',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Persian (IR)',
	// 	code: 'fa-IR',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Finnish (FI)',
	// 	code: 'fi-FI',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French',
	// 	code: 'fr',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (AD)',
	// 	code: 'fr-AD',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (BE)',
	// 	code: 'fr-BE',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (BF)',
	// 	code: 'fr-BF',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (BI)',
	// 	code: 'fr-BI',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (BJ)',
	// 	code: 'fr-BJ',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (CA)',
	// 	code: 'fr-CA',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (CD)',
	// 	code: 'fr-CD',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (CF)',
	// 	code: 'fr-CF',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (CG)',
	// 	code: 'fr-CG',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (CI)',
	// 	code: 'fr-CI',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (CM)',
	// 	code: 'fr-CM',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (DJ)',
	// 	code: 'fr-DJ',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (DZ)',
	// 	code: 'fr-DZ',
	// 	rtl: false,
	// },
	{
		label: 'French (FR)',
		code: 'fr-FR',
		rtl: false,
	},
	// {
	// 	label: 'French (GA)',
	// 	code: 'fr-GA',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (GN)',
	// 	code: 'fr-GN',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (HT)',
	// 	code: 'fr-HT',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (KM)',
	// 	code: 'fr-KM',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (LU)',
	// 	code: 'fr-LU',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (MA)',
	// 	code: 'fr-MA',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (MC)',
	// 	code: 'fr-MC',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (MG)',
	// 	code: 'fr-MG',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (ML)',
	// 	code: 'fr-ML',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (MU)',
	// 	code: 'fr-MU',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (NE)',
	// 	code: 'fr-NE',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (RW)',
	// 	code: 'fr-RW',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (SN)',
	// 	code: 'fr-SN',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (TD)',
	// 	code: 'fr-TD',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (TG)',
	// 	code: 'fr-TG',
	// 	rtl: false,
	// },
	// {
	// 	label: 'French (VU)',
	// 	code: 'fr-VU',
	// 	rtl: false,
	// },
	{
		label: 'Gls',
		code: 'gls',
		rtl: false,
	},
	// {
	// 	label: 'Gu (IN)',
	// 	code: 'gu-IN',
	// 	rtl: false,
	// },
	// {
	// 	label: 'He (IL)',
	// 	code: 'he-IL',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Hi (IN)',
	// 	code: 'hi-IN',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Hr (BA)',
	// 	code: 'hr-BA',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Hr (HR)',
	// 	code: 'hr-HR',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Hu (HU)',
	// 	code: 'hu-HU',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Id (ID)',
	// 	code: 'id-ID',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Is (IS)',
	// 	code: 'is-IS',
	// 	rtl: false,
	// },
	// {
	// 	label: 'It',
	// 	code: 'it',
	// 	rtl: false,
	// },
	// {
	// 	label: 'It (IT)',
	// 	code: 'it-IT',
	// 	rtl: false,
	// },
	// {
	// 	label: 'It (SM)',
	// 	code: 'it-SM',
	// 	rtl: false,
	// },
	// {
	// 	label: 'It (VA)',
	// 	code: 'it-VA',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Ja (JP)',
	// 	code: 'ja-JP',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Jv',
	// 	code: 'jv',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Jv (ID)',
	// 	code: 'jv-ID',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Ka (GE)',
	// 	code: 'ka-GE',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Kk (KZ)',
	// 	code: 'kk-KZ',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Kn (IN)',
	// 	code: 'kn-IN',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Ko (KP)',
	// 	code: 'ko-KP',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Ko (KR)',
	// 	code: 'ko-KR',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Lt (LT)',
	// 	code: 'lt-LT',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Lv (LV)',
	// 	code: 'lv-LV',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Mk (MK)',
	// 	code: 'mk-MK',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Mn (MN)',
	// 	code: 'mn-MN',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Ms (BN)',
	// 	code: 'ms-BN',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Ms (MY)',
	// 	code: 'ms-MY',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Nl (BE)',
	// 	code: 'nl-BE',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Nl (NL)',
	// 	code: 'nl-NL',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Nl (SR)',
	// 	code: 'nl-SR',
	// 	rtl: false,
	// },
	// {
	// 	label: 'No (NO)',
	// 	code: 'no-NO',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Pl',
	// 	code: 'pl',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Pl (PL)',
	// 	code: 'pl-PL',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Pt',
	// 	code: 'pt',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Pt (AO)',
	// 	code: 'pt-AO',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Pt (BR)',
	// 	code: 'pt-BR',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Pt (CV)',
	// 	code: 'pt-CV',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Pt (GW)',
	// 	code: 'pt-GW',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Pt (MZ)',
	// 	code: 'pt-MZ',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Pt (PT)',
	// 	code: 'pt-PT',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Pt (ST)',
	// 	code: 'pt-ST',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Pt (TP)',
	// 	code: 'pt-TP',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Ro (RO)',
	// 	code: 'ro-RO',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Ru (BY)',
	// 	code: 'ru-BY',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Ru (EE)',
	// 	code: 'ru-EE',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Ru (KG)',
	// 	code: 'ru-KG',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Ru (RU)',
	// 	code: 'ru-RU',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Si (LK)',
	// 	code: 'si-LK',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Sk (SK)',
	// 	code: 'sk-SK',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Sl (SI)',
	// 	code: 'sl-SI',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Sq (AL)',
	// 	code: 'sq-AL',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Sr (BA)',
	// 	code: 'sr-BA',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Sr (ME)',
	// 	code: 'sr-ME',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Sr (RS)',
	// 	code: 'sr-RS',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Sv (SE)',
	// 	code: 'sv-SE',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Sw',
	// 	code: 'sw',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Sw (CD)',
	// 	code: 'sw-CD',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Sw (KE)',
	// 	code: 'sw-KE',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Sw (TZ)',
	// 	code: 'sw-TZ',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Sw (UG)',
	// 	code: 'sw-UG',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Ta (IN)',
	// 	code: 'ta-IN',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Ta (LK)',
	// 	code: 'ta-LK',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Te (IN)',
	// 	code: 'te-IN',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Tg (TJ)',
	// 	code: 'tg-TJ',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Th (TH)',
	// 	code: 'th-TH',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Tk (TM)',
	// 	code: 'tk-TM',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Tl (PH)',
	// 	code: 'tl-PH',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Tr (TR)',
	// 	code: 'tr-TR',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Uk (UA)',
	// 	code: 'uk-UA',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Ur',
	// 	code: 'ur',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Ur (PK)',
	// 	code: 'ur-PK',
	// 	rtl: true,
	// },
	// {
	// 	label: 'Uz (UZ)',
	// 	code: 'uz-UZ',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Vi (VN)',
	// 	code: 'vi-VN',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Xc (MN)',
	// 	code: 'xc-MN',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Zh (CN)',
	// 	code: 'zh-CN',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Zh (HK)',
	// 	code: 'zh-HK',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Zh (SG)',
	// 	code: 'zh-SG',
	// 	rtl: false,
	// },
	// {
	// 	label: 'Zh (TW)',
	// 	code: 'zh-TW',
	// 	rtl: false,
	// },
];
