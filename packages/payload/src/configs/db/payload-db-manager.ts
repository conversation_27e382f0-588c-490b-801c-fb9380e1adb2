// configs/db/payload-db-manager.ts
import fs from 'fs';
import path from 'path';
import os from 'os';
import type { ConnectOptions } from 'mongoose';

// Enhanced local development detection
export const isLocalDevelopment = (): boolean => {
	return process.env.NODE_ENV === 'development';
};

// Helper function to create temp certificate files if needed
const createTempCertFile = (
	certData: string,
	certType: string,
): string | undefined => {
	if (!certData) {
		console.warn(`No ${certType} certificate data provided`);
		return undefined;
	}

	try {
		// For Vercel serverless environment, always use /tmp directory
		const isServerless =
			process.env.VERCEL_TARGET_ENV === 'preview' ||
			process.env.VERCEL_TARGET_ENV === 'production';

		// If it looks like a file path and we're not in serverless, use it directly
		if (
			!isServerless &&
			(certData.startsWith('/') || certData.startsWith('./'))
		) {
			const resolvedPath = path.resolve(certData);
			if (fs.existsSync(resolvedPath)) {
				console.debug(
					`Using ${certType} certificate from file: ${resolvedPath}`,
				);
				return resolvedPath;
			} else {
				console.warn(`${certType} certificate file not found: ${resolvedPath}`);
			}
		}

		// Handle base64 encoded certificate
		let certContent = certData;
		// Check if it's base64 encoded (doesn't start with BEGIN CERTIFICATE)
		if (!certData.includes('-----BEGIN')) {
			try {
				certContent = Buffer.from(certData, 'base64').toString('utf-8');
				console.debug(`Decoded ${certType} certificate from base64`);

				// Verify the decoded content has the expected format
				if (!certContent.includes('-----BEGIN')) {
					console.error(
						`Decoded ${certType} certificate doesn't have valid PEM format`,
					);
					return undefined;
				}
			} catch (error) {
				console.error(
					`Failed to decode ${certType} certificate as base64: ${(error as Error).message}`,
				);
				return undefined;
			}
		}

		// Create a temporary file in the appropriate location
		const tmpDir = isServerless ? '/tmp' : os.tmpdir();
		const tempFilePath = path.join(
			tmpDir,
			`mongodb-${certType}-${Date.now()}.pem`,
		);

		try {
			fs.writeFileSync(tempFilePath, certContent, { mode: 0o600 }); // Secure permissions
			console.debug(
				`Created temporary ${certType} certificate file at: ${tempFilePath}`,
			);

			// In non-serverless, attempt to register cleanup
			if (!isServerless) {
				process.on('exit', () => {
					try {
						if (fs.existsSync(tempFilePath)) {
							fs.unlinkSync(tempFilePath);
						}
					} catch (error) {
						console.warn(
							`Failed to remove temp cert file: ${(error as Error).message}`,
						);
					}
				});
			}

			return tempFilePath;
		} catch (error) {
			console.error(
				`Failed to write ${certType} certificate file: ${(error as Error).message}`,
			);
			return undefined;
		}
	} catch (error) {
		console.error(
			`Error handling ${certType} certificate: ${(error as Error).message}`,
		);
		return undefined;
	}
};

// Get MongoDB URI with development safety checks
export const getMongoDBUri = (): string => {
	// If local development and LOCAL_MONGODB_URI is set, use that first
	// This needs to happen before we do anything else
	if (isLocalDevelopment() && process.env.LOCAL_MONGODB_URI) {
		console.debug('🔧 Using local development database');
		return process.env.LOCAL_MONGODB_URI;
	}

	// Default to the main MongoDB URI
	const dbUri = process.env.MONGODB_URI || '';

	// If development environment and we detect a production URI (mongodb+srv),
	// show warning and block access unless override flag is set
	if (
		isLocalDevelopment() &&
		dbUri.includes('mongodb+srv') &&
		process.env.ALLOW_PRODUCTION_DB_IN_DEV !== 'true'
	) {
		console.warn(
			'\n⚠️  WARNING: You appear to be using a production database in development mode!',
		);
		console.warn('🛑 To protect your production data, development is blocked.');
		console.warn('👉 Please set LOCAL_MONGODB_URI in your .env file');
		console.warn(
			'💡 If you really want to use production DB in development (not recommended),',
		);
		console.warn('   set ALLOW_PRODUCTION_DB_IN_DEV=true in your .env file\n');

		// Return a deliberately invalid URI to prevent connection
		return 'mongodb://localhost:27017/blocked-production-db-access';
	}

	return dbUri;
};

// Get MongoDB connection options
export const getMongoConnectionOptions = (): ConnectOptions => {
	// Determine if we're in a serverless environment
	//const isServerless = process.env.VERCEL === '1';

	// Base connection options - optimized for MongoDB Atlas on serverless
	const connectionOptions: ConnectOptions = {
		// For serverless, use smaller pool with faster timeout
		//minPoolSize: isServerless ? 0 : 1,
		//maxPoolSize: isServerless ? 5 : 10,
		//maxIdleTimeMS: isServerless ? 10000 : 270000,
		//serverSelectionTimeoutMS: 5000,
		//socketTimeoutMS: 30000,
		//heartbeatFrequencyMS: 10000,
		//waitQueueTimeoutMS: 5000,
		// compressors: 'zstd',
		// readPreference: 'primaryPreferred',
	};

	// Get the appropriate URI
	const dbUri = getMongoDBUri();

	// X509 Authentication setup if needed
	if (
		process.env.MONGODB_USE_TLS === 'true' ||
		dbUri.includes('authMechanism=MONGODB-X509')
	) {
		console.debug('X.509 authentication detected in connection string');

		// Always set tls to true if MONGODB_USE_TLS is true, regardless of certificate status
		if (process.env.MONGODB_USE_TLS === 'true') {
			connectionOptions.tls = true;
		}

		// Always set tlsAllowInvalidCertificates if configured, regardless of certificate status
		if (process.env.MONGODB_REJECT_UNAUTHORIZED === 'false') {
			connectionOptions.tlsAllowInvalidCertificates = true;
		}

		// Client certificate (with private key)
		const clientCertPath = createTempCertFile(
			process.env.MONGODB_CERT || '',
			'client-cert',
		);

		// CA certificate (if provided)
		const caPath = process.env.MONGODB_CA
			? createTempCertFile(process.env.MONGODB_CA, 'ca')
			: undefined;

		if (clientCertPath) {
			console.debug('🔐 Using X509 certificate authentication');
			connectionOptions.tls = true; // Ensure TLS is enabled with cert
			connectionOptions.tlsCertificateKeyFile = clientCertPath;

			// Only set CA file if provided and successfully created
			if (caPath) {
				connectionOptions.tlsCAFile = caPath;
				console.debug('Using custom CA certificate');
			}

			// Add explicit auth mechanism to options (belt and suspenders)
			connectionOptions.authMechanism = 'MONGODB-X509';
			connectionOptions.authSource = '$external';
		} else {
			console.error(
				'❌ X509 authentication requested but client certificate creation failed!',
			);
			// We don't unset tls here anymore, as it should be set if MONGODB_USE_TLS is true
		}
	}

	return connectionOptions;
};

// Create a MongoDB connection configuration for PayloadCMS
export const createMongooseAdapterConfig = () => {
	const uri = getMongoDBUri();
	const options = getMongoConnectionOptions();
	return {
		url: uri,
		connectOptions: options,
	};
};
