// apps/web/configs/db/payload-db-manager.test.ts

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import fs from 'fs';
import path from 'path';
import os from 'os';
import {
	getMongoDBUri,
	getMongoConnectionOptions,
	createMongooseAdapterConfig,
	isLocalDevelopment,
} from './payload-db-manager';
import type { PathLike } from 'fs';

// Define the mock function types
type MockedFunction<T extends (...args: any[]) => any> = T & {
	mockImplementation: (
		implementation: (...args: Parameters<T>) => ReturnType<T>,
	) => MockedFunction<T>;
	mockReturnValue: (val: ReturnType<T>) => MockedFunction<T>;
};

// Save original environment
const originalEnv = { ...process.env };

// Mock the fs module with a more direct approach that works with Vitest
vi.mock('fs', () => {
	const writeFileSync = vi.fn() as MockedFunction<typeof fs.writeFileSync>;
	const existsSync = vi.fn() as MockedFunction<typeof fs.existsSync>;
	const unlinkSync = vi.fn() as MockedFunction<typeof fs.unlinkSync>;

	return {
		default: {
			existsSync,
			writeFileSync,
			unlinkSync,
		},
		existsSync,
		writeFileSync,
		unlinkSync,
	};
});

// Mock path with a more direct approach
vi.mock('path', () => {
	const resolve = vi.fn((p) => p) as MockedFunction<typeof path.resolve>;
	const join = vi.fn((dir, file) => `${dir}/${file}`) as MockedFunction<
		typeof path.join
	>;

	return {
		default: {
			resolve,
			join,
		},
		resolve,
		join,
	};
});

// Type the mocked modules for usage in tests
const mockedFs = fs as unknown as {
	existsSync: MockedFunction<typeof fs.existsSync>;
	writeFileSync: MockedFunction<typeof fs.writeFileSync>;
	unlinkSync: MockedFunction<typeof fs.unlinkSync>;
};

const mockedPath = path as unknown as {
	resolve: MockedFunction<typeof path.resolve>;
	join: MockedFunction<typeof path.join>;
};

// Mock the createTempCertFile function to return a path for tests
const mockTempFilePath = '/tmp/mock-cert-path.pem';

// Create a helper function for mocking certificate creation
function mockCertificateCreation() {
	// Mock writeFileSync to simulate successful file creation
	mockedFs.writeFileSync.mockImplementation(() => undefined);

	// Mock path operations
	mockedPath.join.mockImplementation(() => mockTempFilePath);

	// Ensure existsSync returns false for most paths but true for our mock path
	mockedFs.existsSync.mockImplementation(
		(p: PathLike) => p === mockTempFilePath || p === './cert.pem',
	);
}

describe('payload-db-manager', () => {
	// Reset mocks and environment before each test
	beforeEach(() => {
		vi.resetModules();
		vi.resetAllMocks();
		// Set test environment variables
		vi.stubEnv('NODE_ENV', 'development');
		process.env.MONGODB_URI = 'mongodb://server:27017/test';
	});

	// Restore environment after each test
	afterEach(() => {
		vi.unstubAllEnvs();
		process.env = { ...originalEnv };
	});

	describe('isLocalDevelopment', () => {
		it('should return true in development environment', () => {
			vi.stubEnv('NODE_ENV', 'development');
			expect(isLocalDevelopment()).toBe(true);
		});

		it('should return false in production environment', () => {
			vi.stubEnv('NODE_ENV', 'production');
			expect(isLocalDevelopment()).toBe(false);
		});
	});

	describe('getMongoDBUri', () => {
		it('should return the MongoDB URI from MONGODB_URI by default', () => {
			expect(getMongoDBUri()).toBe('mongodb://server:27017/test');
		});

		it('should prioritize LOCAL_MONGODB_URI in development', () => {
			vi.stubEnv('NODE_ENV', 'development');
			vi.stubEnv('LOCAL_MONGODB_URI', 'mongodb://localhost:27017/local-db');
			expect(getMongoDBUri()).toBe('mongodb://localhost:27017/local-db');
		});

		it('should block production database in development without override', () => {
			vi.stubEnv('NODE_ENV', 'development');
			vi.stubEnv(
				'MONGODB_URI',
				'mongodb+srv://user:<EMAIL>/db',
			);

			// Should return a blocked URI
			expect(getMongoDBUri()).toBe(
				'mongodb://localhost:27017/blocked-production-db-access',
			);
		});

		it('should allow production database in development when override flag is set', () => {
			vi.stubEnv('NODE_ENV', 'development');
			vi.stubEnv(
				'MONGODB_URI',
				'mongodb+srv://user:<EMAIL>/db',
			);
			vi.stubEnv('ALLOW_PRODUCTION_DB_IN_DEV', 'true');

			expect(getMongoDBUri()).toBe(
				'mongodb+srv://user:<EMAIL>/db',
			);
		});
	});

	describe('getMongoConnectionOptions', () => {
		it('should return an empty options object by default', () => {
			const options = getMongoConnectionOptions();
			expect(Object.keys(options).length).toBe(0);
		});

		it('should add X.509 certificate options when detected', () => {
			// Set env vars for certificate authentication
			vi.stubEnv('MONGODB_USE_TLS', 'true');
			vi.stubEnv(
				'MONGODB_CERT',
				'-----BEGIN CERTIFICATE-----\nMockCertContent\n-----END CERTIFICATE-----',
			);

			// Set up mocks to make cert creation succeed
			mockCertificateCreation();

			// Get connection options
			const options = getMongoConnectionOptions();

			// Verify certificate options are correctly set
			expect(options.tls).toBe(true);
			expect(options.tlsCertificateKeyFile).toBe(mockTempFilePath);
			expect(options.authMechanism).toBe('MONGODB-X509');
			expect(options.authSource).toBe('$external');
		});

		it('should handle base64 encoded certificates', () => {
			// Base64 encoded version of "-----BEGIN CERTIFICATE-----\nTest\n-----END CERTIFICATE-----"
			const base64Cert =
				'LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tClRlc3QKLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=';
			vi.stubEnv('MONGODB_USE_TLS', 'true');
			vi.stubEnv('MONGODB_CERT', base64Cert);

			// Set up mocks to make cert creation succeed
			mockCertificateCreation();

			// Get connection options
			const options = getMongoConnectionOptions();

			// Verify options are set
			expect(options.tls).toBe(true);
			expect(options.tlsCertificateKeyFile).toBe(mockTempFilePath);
		});

		it('should set CA file when MONGODB_CA is provided', () => {
			vi.stubEnv('MONGODB_USE_TLS', 'true');
			vi.stubEnv(
				'MONGODB_CERT',
				'-----BEGIN CERTIFICATE-----\nMockCertContent\n-----END CERTIFICATE-----',
			);
			vi.stubEnv(
				'MONGODB_CA',
				'-----BEGIN CERTIFICATE-----\nMockCAContent\n-----END CERTIFICATE-----',
			);

			// Set up mocks to make cert creation succeed
			mockCertificateCreation();

			// Get connection options
			const options = getMongoConnectionOptions();

			// Verify both cert and CA are set
			expect(options.tls).toBe(true);
			expect(options.tlsCertificateKeyFile).toBe(mockTempFilePath);
			expect(options.tlsCAFile).toBe(mockTempFilePath);
		});

		it('should use existing file path for certificates in non-serverless environment', () => {
			vi.stubEnv('MONGODB_USE_TLS', 'true');
			vi.stubEnv('MONGODB_CERT', './cert.pem');

			// Mock fs.existsSync to return true and path.resolve
			mockedFs.existsSync.mockReturnValue(true);
			mockedPath.resolve.mockReturnValue('/abs/path/to/cert.pem');

			// Get connection options
			const options = getMongoConnectionOptions();

			// Verify cert is set without creating temp file
			expect(options.tls).toBe(true);
			expect(options.tlsCertificateKeyFile).toBe('/abs/path/to/cert.pem');

			// Verify writeFileSync was not called (using existing file)
			expect(mockedFs.writeFileSync).not.toHaveBeenCalled();
		});

		it('should detect X.509 auth from connection string', () => {
			vi.stubEnv(
				'MONGODB_URI',
				'mongodb://server:27017/test?authMechanism=MONGODB-X509',
			);
			vi.stubEnv(
				'MONGODB_CERT',
				'-----BEGIN CERTIFICATE-----\nMockCertContent\n-----END CERTIFICATE-----',
			);

			// Set up mocks to make cert creation succeed
			mockCertificateCreation();

			// Get connection options
			const options = getMongoConnectionOptions();

			// Verify X.509 options are set
			expect(options.tls).toBe(true);
			expect(options.tlsCertificateKeyFile).toBe(mockTempFilePath);
			expect(options.authMechanism).toBe('MONGODB-X509');
		});

		it('should not set X.509 options when certificate creation fails', () => {
			vi.stubEnv('MONGODB_USE_TLS', 'true');
			// No certificate data provided

			// Mock fs.writeFileSync to simulate failure
			mockedFs.writeFileSync.mockImplementation(() => {
				throw new Error('Mock file write failure');
			});

			// Get connection options
			const options = getMongoConnectionOptions();

			// TLS should be true because MONGODB_USE_TLS is true
			expect(options.tls).toBe(true);
			// But certificate options should be undefined
			expect(options.tlsCertificateKeyFile).toBeUndefined();
		});

		it('should allow invalid certificates when configured', () => {
			vi.stubEnv('MONGODB_USE_TLS', 'true');
			vi.stubEnv(
				'MONGODB_CERT',
				'-----BEGIN CERTIFICATE-----\nMockCertContent\n-----END CERTIFICATE-----',
			);
			vi.stubEnv('MONGODB_REJECT_UNAUTHORIZED', 'false');

			// Set up mocks to make cert creation succeed
			mockCertificateCreation();

			// Get connection options
			const options = getMongoConnectionOptions();

			// Verify option is set
			expect(options.tlsAllowInvalidCertificates).toBe(true);
		});
	});

	describe('createMongooseAdapterConfig', () => {
		it('should create config with the correct URL and options', () => {
			const config = createMongooseAdapterConfig();

			expect(config.url).toBe('mongodb://server:27017/test');
			expect(config.connectOptions).toBeDefined();
			expect(Object.keys(config.connectOptions).length).toBe(0);
		});

		it('should create config with X.509 options when configured', () => {
			vi.stubEnv('MONGODB_USE_TLS', 'true');
			vi.stubEnv(
				'MONGODB_CERT',
				'-----BEGIN CERTIFICATE-----\nMockCertContent\n-----END CERTIFICATE-----',
			);
			mockCertificateCreation();

			const config = createMongooseAdapterConfig();

			expect(config.connectOptions.tls).toBe(true);
			expect(config.connectOptions.tlsCertificateKeyFile).toBe(
				mockTempFilePath,
			);
		});

		it('should return the full URI in the config object', () => {
			// We can't directly test console output, but we can verify the config itself
			vi.stubEnv('MONGODB_URI', '*****************************************');
			const config = createMongooseAdapterConfig();

			// Just verify the config has the correct fields
			expect(config.url).toBe('*****************************************');
			expect(config.connectOptions).toBeDefined();
		});
	});
});
