# MongoDB Connection Manager for wx-next

## Overview

This directory contains a MongoDB connection management system specifically optimized for PayloadCMS running on Vercel's serverless architecture. The system provides robust connection handling with optimizations for the specific challenges of serverless environments.

## Key Features

- **Serverless optimization**: Connection pooling optimized for Vercel functions
- **Development safety**: Production database protection in development
- **X.509 certificate support**: Secure authentication when required
- **Vercel environment awareness**: Intelligent configuration based on VERCEL_ENV

## File Structure

- `db-connection-manager.ts`: Core connection management functionality (server-only)
- `payload-db-manager.ts`: Specialized version for PayloadCMS integration
- `*.test.ts` files: Unit tests for the connection manager

## Connection Management

The connection manager provides a single, optimized MongoDB connection with safety features to prevent accidental production database use in development environments.

## PayloadCMS Integration

The system includes specialized integration with PayloadCMS:

- `payload-db-manager.ts` provides connection functionality specifically for PayloadCMS
- Connection options are optimized for PayloadCMS's needs in serverless environments
- Safety features prevent accidental use of production databases during development

## Usage Examples

### Basic Usage with PayloadCMS

```typescript
import { getPayload } from "payload";
import { createMongooseAdapterConfig } from "./configs/db/payload-db-manager";

const payload = await getPayload({
	mongooseAdapter: createMongooseAdapterConfig(),
});

// Execute queries
const results = await payload.find({
	collection: "articles",
	// query parameters
});
```

## Implementation Details

### Connection Manager Architecture

The connection manager uses a modular design with three main components:

1. **URI Management** - The `getMongoDBUri` function selects the appropriate connection URI based on the current environment. It includes safety features to prevent accidental production database use in development.

2. **Connection Options** - The `getMongoConnectionOptions` function provides optimized connection parameters based on the environment (serverless vs. traditional) and handles certificate authentication when required.

3. **PayloadCMS Integration** - The `createMongooseAdapterConfig` function creates configuration objects for PayloadCMS's mongoose adapter, ensuring proper connection setup.

4. **Connection Monitoring** - The `setupConnectionMonitoring` function adds event listeners to track connection status.

## Vercel Environment Handling

The connection manager intelligently adapts to different Vercel environments:

### Production Environment (VERCEL_ENV=production)

- Uses serverless-optimized connection settings with smaller connection pools
- Optimizes settings for efficient resource usage in serverless functions

### Preview Environment (VERCEL_ENV=preview)

- Similar to production settings
- Can use a different database URI if needed (via environment variables)

### Development Environment (VERCEL_ENV=development)

- Uses standard connection settings, not serverless optimizations
- Can connect to local MongoDB if configured

### Local Development

- Uses MONGODB_URI pointing to a local MongoDB instance
- Prevents accidental production database use with a safety check
- Can be overridden with ALLOW_PRODUCTION_DB_IN_DEV=true if needed

### Test Environment

- Properly detects NODE_ENV=test as a local development environment
- Uses test-specific configurations when needed

## Development Safety

To prevent accidental use of production databases in development:

1. The system automatically blocks connections to MongoDB Atlas URIs in development
2. Set up a local MongoDB instance and configure `MONGODB_URI` to point to your local instance
3. If you need to use a production database in development (not recommended), set `ALLOW_PRODUCTION_DB_IN_DEV=true`

## Connection Optimization

The connection manager automatically optimizes connection parameters based on the environment:

- **Development**: Standard connection parameters focused on developer experience
- **Production**: Optimized connection pooling for serverless functions with:
  - Smaller connection pools
  - Shorter idle timeouts
  - Compression enabled
  - Retry capabilities for improved resilience

## Best Practices

1. **Configure appropriate environment variables**:

   - Use `MONGODB_URI` for all environments, pointing to a local MongoDB instance for development
   - Only use `ALLOW_PRODUCTION_DB_IN_DEV=true` when absolutely necessary

2. **Monitor connection usage** in MongoDB Atlas to ensure you're not exceeding connection limits

3. **Consider connection pooling implications** in serverless environments:

   - Vercel functions benefit from smaller connection pools
   - Development environments can use larger, longer-lived pools

4. **Optimize connection URIs**:

   ```text
   MONGODB_URI=mongodb+srv://user:<EMAIL>/database?appName=wx-next
   ```

5. **Consider query impact**:
   - Use indexes appropriately
   - Limit result sizes for large queries
   - Use projection to return only needed fields

## Testing

Run the connection manager tests with:

```bash
pnpm --filter web test configs/db/db-connection-manager.test.ts
```

## Future Enhancements

1. **Dynamic connection pool sizing** based on function memory limits
2. **Enhanced monitoring and telemetry** for connection usage
3. **Automatic failover** between node types when needed
