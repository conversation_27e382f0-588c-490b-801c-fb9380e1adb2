// below import causes an error:
// > payload generate:types

// ~/projects/wx-next/apps/web/configs/global/SiteMode/hooks/fetchDsxSitemode.ts:2
// import { getLinkList } from '@repo/dal/content/links/linkList';
//          ^
// SyntaxError: The requested module '@repo/dal/content/links/linkList' does not provide an export named 'getLinkList'
//     at ModuleJob._instantiate (node:internal/modules/esm/module_job:180:21)
//     at async ModuleJob.run (node:internal/modules/esm/module_job:263:5)
//     at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)
//     at async bin (/Users/<USER>/projects/wx-next/node_modules/.pnpm/payload@3.33.0_graphql@16.10.0_typescript@5.8.3/node_modules/payload/dist/bin/index.js:58:27)
//     at async start (file:///Users/<USER>/projects/wx-next/node_modules/.pnpm/payload@3.33.0_graphql@16.10.0_typescript@5.8.3/node_modules/payload/bin.js:30:7)
// the reason this errors is due to payload generate:types
// https://github.com/payloadcms/payload/issues/5868
// this has forced us to compile the dal package instead of transpiling it with web app
// import { getLinkList } from '@repo/dal/content/links/linkList';
import { GlobalBeforeValidateHook } from 'payload';

/** TODO: committing this asa WIP, need to finish this
 * But ultimately this will need to be a task and a job not a hook
 * or at minimum a client side component.
 */
export const fetchDsxSitemode: GlobalBeforeValidateHook = async ({
	global,
}) => {
	if (global.slug !== 'site-mode') return;
	try {
		// if (process.env.DEBUG) {
		// 	const lists = await getLinkList('798ce33c-729e-4ec4-b726-8d9a5c3ab6eb');
		// 	// Extract and validate attributes (mode and locale)
		// 	lists?.links.map((link: any) => {
		// 		const mode = link?.attributes?.mode;
		// 		const locale = link?.attributes?.locale;
		// 		if (!mode || !locale) {
		// 			console.warn('Invalid DSX sitemode link attributes', link);
		// 			return; // Skip invalid entries
		// 		}
		// 		console.debug('DSX sitemode link attributes', mode, locale);
		// 	});
		// }
	} catch (error) {
		console.error('Error during fetch:', error);
	}
};
