import { createGroup } from '@repo/payload/fields/utility';
import { SelectField, ArrayField, TextField } from 'payload';

export const source: SelectField = {
	name: 'source',
	label: 'Source',
	type: 'select',
	options: [
		{ label: 'DSX', value: 'kalliope' },
		{ label: 'Orpheus', value: 'orpheus' },
	],
};

export const configName: TextField = {
	name: 'configName',
	label: 'Config Name',
	type: 'text',
	required: true,
};

// TODO: Technically this is what I want but no time right now
//type SiteModeLocales = Record<Extract<Locale["label"], string>, Locale["code"]> & Option;

export const configLocale: SelectField = {
	name: 'configLocale',
	label: 'Config Locale',
	type: 'select',
	options: [
		{ label: 'en-US', value: 'en-US' },
		{ label: 'es-US', value: 'es-US' },
	],
};

export const configMode: SelectField = {
	name: 'configMode',
	label: 'Config Mode',
	type: 'select',
	options: [
		{ label: 'severe', value: 'severe' },
		{ label: 'hybrid', value: 'hybrid' },
		{ label: 'normal', value: 'normal' },
	],
};

const attributesFields = [configName, configMode, configLocale];
const attributesGroup = createGroup(
	attributesFields,
	'attributes',
	'Attributes',
);
const configFields = [attributesGroup];

export const siteModeConfigs: ArrayField = {
	name: 'siteModeConfigs',
	label: 'Site Mode Configs',
	type: 'array',
	fields: configFields,
	admin: {
		disabled: false,
	},
};
