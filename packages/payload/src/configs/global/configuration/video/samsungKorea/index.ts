import { GlobalConfig } from 'payload';

const kalliopeCollectionId = 'ea8758a3-6fd5-471f-87e7-05e814ff6f67'; // samsung-global
const kalliopePlaylistId = '87475b5a-d463-42ea-aba8-661ccc4be7da'; // pl-samsung-korea
const kalliopeInterestsId = '0c041bf3-bf98-46ed-9f36-764f294d792d'; // Forecasts
const kalliopeProviderId = 'aee1b774-e0cf-11e1-9ee2-001d092f5a10'; // YouTube-07 Days-No Distro
const kalliopeForecastKeywordId = '09fbb703-d57c-4383-b9e4-48b030010f18'; //samsung-kr-forecast
const kalliopeWeatherKeywordId = '58876cb0-a0a5-401f-9b38-4dbdd2b75f78'; //samsung-kr-weather

export const INCLUSION_KEYWORDS_KO = [
	'기후', // Climate
	'대기', // Atmosphere
	'폭풍', // Storm
	'태풍', // Typhoon
	'홍수', // Flood
	'가뭄', // Drought
	'눈', // Snow
	'비', // Rain
	'번개', // Lightning
	'천둥', // Thunder
	'한파', // Cold snap/Severe cold wave
	'폭염', // Heatwave
	'장마', // Monsoon/Rainy season
	'구름', // Cloud
	'바람', // Wind
	'기온', // Temperature
	'습도', // Humidity
];

export const sources = [
	{
		type: 'forecast',
		name: 'KBS',
		inclusionKeywords: ['날씨'],
		exclusionKeywords: [],
		channelId: 'UCcQTRi69dsVYHN3exePtZ1A',
	},
	{
		type: 'weather',
		name: 'KMA',
		inclusionKeywords: [],
		exclusionKeywords: ['날씨'],
		channelId: 'UCI110BywhJpYRC1XY_rSMiQ',
	},
	{
		type: 'weather',
		name: 'YTN (Major 24-hour news channel)',
		channelId: 'UChlgI3UHCOnwUGzWzbJ3H5w',
		inclusionKeywords: INCLUSION_KEYWORDS_KO,
		exclusionKeywords: [],
	},
	{
		type: 'weather',
		name: 'SBS',
		channelId: 'UCkinYTS9IHqOEwR1Sze2JTw',
		inclusionKeywords: INCLUSION_KEYWORDS_KO,
		exclusionKeywords: [],
	},
];

export const SamsungKorea: GlobalConfig = {
	slug: 'samsung-korea',
	label: 'SamsungKorea',
	admin: {
		description: 'Configuration for YouTube rss for samsung korea.',
		group: 'Configuration',
	},
	versions: true,
	fields: [
		{
			name: 'configs',
			type: 'group',
			fields: [
				{
					name: 'kalliopeCollectionId',
					type: 'text',
					defaultValue: kalliopeCollectionId,
					required: true,
				},
				{
					name: 'kalliopePlaylistId',
					type: 'text',
					defaultValue: kalliopePlaylistId,
					required: true,
				},
				{
					name: 'kalliopeInterestsId',
					type: 'text',
					defaultValue: kalliopeInterestsId,
					required: true,
				},
				{
					name: 'kalliopeProviderId',
					type: 'text',
					defaultValue: kalliopeProviderId,
					required: true,
				},
				{
					name: 'kalliopeForecastKeywordId',
					type: 'text',
					defaultValue: kalliopeForecastKeywordId,
					required: true,
				},
				{
					name: 'kalliopeWeatherKeywordId',
					type: 'text',
					defaultValue: kalliopeWeatherKeywordId,
					required: true,
				},
			],
			required: true,
		},
		{
			name: 'keywordLimits',
			type: 'group',
			label: 'Keyword Limits',
			admin: {
				description: 'Set content limits for each keyword type',
			},
			fields: [
				{
					name: 'forecastLimit',
					type: 'number',
					label: 'Forecast Keyword Limit',
					admin: {
						description:
							'Maximum number of items for samsung-kr-forecast keyword',
					},
					defaultValue: 1,
					required: true,
					min: 0,
				},
				{
					name: 'weatherLimit',
					type: 'number',
					label: 'Weather Keyword Limit',
					admin: {
						description:
							'Maximum number of items for samsung-kr-weather keyword',
					},
					defaultValue: 1,
					required: true,
					min: 0,
				},
			],
			required: true,
		},
		{
			required: true,
			name: 'sources',
			type: 'array',
			fields: [
				{ name: 'name', type: 'text', required: true },
				{ name: 'channelId', type: 'text', required: true },
				{
					name: 'type',
					type: 'select',
					options: ['forecast', 'weather'],
					required: true,
				},
				{ name: 'inclusionKeywords', type: 'json' },
				{ name: 'exclusionKeywords', type: 'json' },
			],
			defaultValue: sources,
		},
	],
};
