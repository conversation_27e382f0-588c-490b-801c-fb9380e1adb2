import type { User, Access, Where, TypedUser, RunJobAccess } from 'payload';
export type SingleRole = User['role'][number];
import { getTenantFromCookie } from '@payloadcms/plugin-multi-tenant/utilities';
import { getUserTenantIDs } from '@repo/payload/utils/getUserTenantIDs';
import { getCollectionIDType } from '@repo/payload/utils/getCollectionIDType';

/**
 * Each role either has { read: boolean; update: boolean } for a field,
 * or we omit the role entirely if that role shouldn't have any access.
 */
interface RoleFieldAccess {
	read: boolean;
	update: boolean;
}

interface RoleCollectionAccess {
	create: boolean;
	read: boolean;
	update: boolean;
	delete: boolean;
}

/**
 * A FieldPermissionsMap is:
 *   key = field name
 *   value = an object with role-based access
 */
export type FieldPermissionsMap = Record<
	string,
	Partial<Record<SingleRole, RoleFieldAccess>>
>;

/**
 * CollectionPermissionsMap is:
 *   key = field name
 *   value = an object with role-based access
 */
export type CollectionPermissionsMap = Record<
	string,
	Partial<Record<SingleRole, RoleCollectionAccess>>
>;

/**
 * This is the source of truth for field permissions.
 * */
// export const articleFieldPermissions: FieldPermissionsMap = {
//   title: {
//     admin: { read: true, update: true },
//     editor: { read: true, update: true },
//     authenticated: { read: true, update: false },
//   },
// };
// TODO: Copy paste from payload example Refactor to use our helpers
export const isAdminAccess: Access = ({ req }): boolean => {
	return isAdmin(req.user);
};

export const isAdmin = (user: TypedUser | null): boolean => {
	return Boolean(user?.role.includes('admin'));
};

export const isAuthor = (_data: Partial<User> | null): boolean => {
	return _data?.isAuthor;
};

export const isAccessingSelf = ({
	id,
	user,
}: {
	user?: TypedUser;
	id?: string | number;
}): boolean => {
	return user ? Boolean(user.id === id) : false;
};

export const createAccess: Access<User> = ({ req }) => {
	if (!req.user) {
		return false;
	}

	if (isAdmin(req.user)) {
		return true;
	}

	const adminTenantAccessIDs = getUserTenantIDs(req.user, 'tenant-admin');

	if (adminTenantAccessIDs.length) {
		return true;
	}

	return false;
};

export const readAccess: Access<User> = ({ req, id }) => {
	if (!req?.user) {
		return false;
	}

	if (isAccessingSelf({ id, user: req.user })) {
		return true;
	}

	const admin = isAdmin(req.user);
	const selectedTenant = getTenantFromCookie(
		req.headers,
		getCollectionIDType({ payload: req.payload, collectionSlug: 'tenants' }),
	);
	const adminTenantAccessIDs = getUserTenantIDs(req.user, 'tenant-admin');

	if (selectedTenant) {
		// If it's a super admin, or they have access to the tenant ID set in cookie
		const hasTenantAccess = adminTenantAccessIDs.some(
			(id) => id === selectedTenant,
		);
		if (admin || hasTenantAccess) {
			return {
				'tenants.tenant': {
					equals: selectedTenant,
				},
			};
		}
	}

	if (admin) {
		return true;
	}

	return {
		or: [
			{
				id: {
					equals: req.user.id,
				},
			},
			{
				'tenants.tenant': {
					in: adminTenantAccessIDs,
				},
			},
		],
	} as Where;
};

export const updateAndDeleteAccess: Access = ({ req, id }) => {
	const { user } = req;

	if (!user) {
		return false;
	}

	if (isAdmin(user) || isAccessingSelf({ user, id })) {
		return true;
	}

	/**
	 * Constrains update and delete access to users that belong
	 * to the same tenant as the tenant-admin making the request
	 *
	 * You may want to take this a step further with a beforeChange
	 * hook to ensure that the a tenant-admin can only remove users
	 * from their own tenant in the tenants array.
	 */
	return {
		'tenants.tenant': {
			in: getUserTenantIDs(user, 'tenant-admin'),
		},
	};
};

/**
 * Tenant admins and super admins can will be allowed access
 */
export const superAdminOrTenantAdminAccess: Access = ({
	req: { payload, user },
}) => {
	if (!user) {
		return false;
	}

	if (isAdmin(user)) {
		return true;
	}

	const tenantIds = getUserTenantIDs(user, 'tenant-admin');

	if (!tenantIds.length) {
		payload.logger.error('No tenants found for the user');
		return false;
	}

	return {
		tenant: {
			in: tenantIds,
		},
	};
};

export const canRunCron: RunJobAccess = ({ req }) => {
	if (req.user && isAdmin(req.user)) return true;

	const authHeader = req.headers.get('authorization');
	return authHeader === 'Bearer ' + process.env.CRON_SECRET;
};
