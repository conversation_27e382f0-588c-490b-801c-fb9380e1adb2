import type { FieldHook } from 'payload';

export const formatSlug = (text: string) =>
	text
		.toLowerCase()
		.replace(/\s+/g, '-')
		.replace(/[^a-zA-Z-]+/g, '')
		.replace(/--+/g, '-') // Replace multiple - with single -
		.replace(/^-+/, '') // Trim - from start of text
		.replace(/-+$/, '');

export const formatSlugHook =
	(fallback: string): FieldHook =>
	({ data, operation, value }) => {
		if (typeof value === 'string') {
			return formatSlug(value);
		}

		if (operation === 'create' || !data?.slug) {
			const fallbackData = data?.[fallback] || data?.[fallback];

			if (fallbackData && typeof fallbackData === 'string') {
				return formatSlug(fallbackData);
			}
		}

		return value;
	};
