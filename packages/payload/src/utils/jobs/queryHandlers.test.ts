import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import {
	findQueryAndLock,
	findQueryByKalliopeIdAndLock,
	updateQueryWithContent,
	unlockAndCompleteQuery,
	buildWhereConditions,
	handleTaskError,
} from './queryHandlers';
import { ContentQuery } from '@repo/payload/payload-types';

// Mock Payload
const mockPayload = {
	findByID: vi.fn(),
	update: vi.fn(),
	logger: {
		error: vi.fn(),
		warn: vi.fn(),
		debug: vi.fn(),
	},
};

// Mock request
const mockReq = {} as any;

describe('queryHandlers', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	afterEach(() => {
		vi.resetAllMocks();
	});

	describe('findQueryAndLock', () => {
		it('should find and lock a query successfully', async () => {
			// Mock query document
			const mockQueryDoc = {
				id: 'query-doc-123',
				queries: [
					{
						id: 'query-123',
						_locked: false,
						_lockedByTaskID: null,
						syncStatus: 'pending',
					},
				],
				mergedContent: [{ id: 'content-1' }],
			};

			// Store the original query object for later comparison
			const originalQueryToUpdate = { ...mockQueryDoc.queries[0] };

			mockPayload.findByID.mockResolvedValueOnce(mockQueryDoc);
			mockPayload.update.mockResolvedValueOnce({ ...mockQueryDoc });

			const result = await findQueryAndLock({
				payload: mockPayload as any,
				req: mockReq,
				queryDocID: 'query-doc-123',
				queryId: 'query-123',
				jobId: 'job-123',
			});

			expect(mockPayload.findByID).toHaveBeenCalledWith({
				collection: 'content-queries',
				id: 'query-doc-123',
				depth: 0,
				req: mockReq,
			});

			expect(mockPayload.update).toHaveBeenCalledWith({
				collection: 'content-queries',
				id: 'query-doc-123',
				data: {
					queries: [
						{
							id: 'query-123',
							_locked: true,
							_lockedByTaskID: 'job-123',
							syncStatus: 'in-progress',
						},
					],
				},
				context: { skipQueueJobs: true },
				req: mockReq,
			});

			// The original queryToUpdate is returned, not the updated one
			expect(result).toEqual({
				queryDoc: mockQueryDoc,
				queryToUpdate: originalQueryToUpdate,
				existingContent: mockQueryDoc.mergedContent,
			});
		});

		it('should return null when query document is not found', async () => {
			mockPayload.findByID.mockResolvedValueOnce(null);

			const result = await findQueryAndLock({
				payload: mockPayload as any,
				req: mockReq,
				queryDocID: 'query-doc-123',
				queryId: 'query-123',
				jobId: 'job-123',
			});

			expect(mockPayload.logger.error).toHaveBeenCalled();
			expect(result).toEqual({
				queryDoc: null,
				queryToUpdate: null,
				existingContent: [],
			});
		});

		it('should return null when query is not found in document', async () => {
			const mockQueryDoc = {
				id: 'query-doc-123',
				queries: [
					{
						id: 'different-query-id',
						_locked: false,
						_lockedByTaskID: null,
					},
				],
			};

			mockPayload.findByID.mockResolvedValueOnce(mockQueryDoc);

			const result = await findQueryAndLock({
				payload: mockPayload as any,
				req: mockReq,
				queryDocID: 'query-doc-123',
				queryId: 'query-123',
				jobId: 'job-123',
			});

			expect(mockPayload.logger.error).toHaveBeenCalled();
			expect(result).toEqual({
				queryDoc: mockQueryDoc,
				queryToUpdate: null,
				existingContent: [],
			});
		});

		it('should not lock query if already locked by another job', async () => {
			const mockQueryDoc = {
				id: 'query-doc-123',
				queries: [
					{
						id: 'query-123',
						_locked: true,
						_lockedByTaskID: 'other-job-id',
					},
				],
			};

			mockPayload.findByID.mockResolvedValueOnce(mockQueryDoc);

			const result = await findQueryAndLock({
				payload: mockPayload as any,
				req: mockReq,
				queryDocID: 'query-doc-123',
				queryId: 'query-123',
				jobId: 'job-123',
			});

			expect(mockPayload.logger.warn).toHaveBeenCalled();
			expect(result).toEqual({
				queryDoc: mockQueryDoc,
				queryToUpdate: null,
				existingContent: [],
			});
		});
	});

	describe('findQueryByKalliopeIdAndLock', () => {
		it('should find and lock a query by Kalliope ID successfully', async () => {
			const mockQueryDoc = {
				id: 'query-doc-123',
				queries: [
					{
						id: 'query-123',
						kalliopeCMQSID: 'kalliope-123',
						_locked: false,
						_lockedByTaskID: null,
						syncStatus: 'pending',
					},
				],
				mergedContent: [{ id: 'content-1' }],
			};

			// Store the original query object for later comparison
			const originalQueryToUpdate = { ...mockQueryDoc.queries[0] };

			mockPayload.findByID.mockResolvedValueOnce(mockQueryDoc);
			mockPayload.update.mockResolvedValueOnce({ ...mockQueryDoc });

			const result = await findQueryByKalliopeIdAndLock({
				payload: mockPayload as any,
				req: mockReq,
				queryDocID: 'query-doc-123',
				kalliopeCMQID: 'kalliope-123',
				jobId: 'job-123',
			});

			expect(mockPayload.update).toHaveBeenCalledWith({
				collection: 'content-queries',
				id: 'query-doc-123',
				data: {
					queries: [
						{
							id: 'query-123',
							kalliopeCMQSID: 'kalliope-123',
							_locked: true,
							_lockedByTaskID: 'job-123',
							syncStatus: 'in-progress',
						},
					],
				},
				context: { skipQueueJobs: true },
				req: mockReq,
			});

			// The original queryToUpdate is returned, not the updated one
			expect(result).toEqual({
				queryDoc: mockQueryDoc,
				queryToUpdate: originalQueryToUpdate,
				existingContent: mockQueryDoc.mergedContent,
			});
		});
	});

	describe('updateQueryWithContent', () => {
		it('should update a query with new content', async () => {
			const mockQueryDoc = {
				id: 'query-doc-123',
				queries: [
					{
						id: 'query-123',
						_locked: true,
						_lockedByTaskID: 'job-123',
					},
				],
			} as ContentQuery;

			const mockQueryToUpdate = {
				id: 'query-123',
				_locked: true,
				_lockedByTaskID: 'job-123',
			};

			const newContent = [{ id: 'new-content-1' }] as any;

			await updateQueryWithContent({
				payload: mockPayload as any,
				queryDoc: mockQueryDoc,
				queryToUpdate: mockQueryToUpdate,
				newContent,
				existingContent: [],
				queryId: 'query-123',
				req: mockReq,
			});

			expect(mockPayload.update).toHaveBeenCalledWith({
				collection: 'content-queries',
				id: 'query-doc-123',
				data: {
					queries: [
						{
							id: 'query-123',
							_locked: false,
							_lockedByTaskID: null,
							syncStatus: 'completed',
							lastSynced: expect.any(String),
						},
					],
					mergedContent: newContent,
				},
				context: { skipQueueJobs: true },
				req: mockReq,
			});
		});

		it('should handle errors when query document has no queries', async () => {
			const mockQueryDoc = {
				id: 'query-doc-123',
				queries: [],
			};

			const result = await updateQueryWithContent({
				payload: mockPayload as any,
				//@ts-expect-error too much work for a test
				queryDoc: mockQueryDoc,
				queryToUpdate: { id: 'query-123' },
				newContent: [],
				existingContent: [],
				queryId: 'query-123',
				req: mockReq,
			});

			expect(mockPayload.logger.error).toHaveBeenCalled();
			expect(result).toEqual({
				queryDoc: null,
				queryToUpdate: null,
				existingContent: [],
			});
		});
	});

	describe('unlockAndCompleteQuery', () => {
		it('should unlock and complete a query', async () => {
			const mockQueryDoc = {
				id: 'query-doc-123',
			} as ContentQuery;

			const mockQueryToUpdate = {
				id: 'query-123',
				_locked: true,
				_lockedByTaskID: 'job-123',
			};

			const mockQueries = [mockQueryToUpdate];

			await unlockAndCompleteQuery({
				payload: mockPayload as any,
				queryDoc: mockQueryDoc,
				queryToUpdate: mockQueryToUpdate,
				//@ts-expect-error to much to make types work in test
				queries: mockQueries,
				req: mockReq,
			});

			expect(mockPayload.update).toHaveBeenCalledWith({
				collection: 'content-queries',
				id: 'query-doc-123',
				data: {
					queries: [
						{
							id: 'query-123',
							_locked: false,
							_lockedByTaskID: null,
							syncStatus: 'completed',
							lastSynced: expect.any(String),
						},
					],
				},
				context: { skipQueueJobs: true },
				req: mockReq,
			});
		});

		it('should handle errors when queries array is empty', async () => {
			const mockQueryDoc = {
				id: 'query-doc-123',
			} as ContentQuery;

			await unlockAndCompleteQuery({
				payload: mockPayload as any,
				queryDoc: mockQueryDoc,
				queryToUpdate: { id: 'query-123' },
				queries: [],
				req: mockReq,
			});

			expect(mockPayload.logger.error).toHaveBeenCalled();
			expect(mockPayload.update).not.toHaveBeenCalled();
		});
	});

	describe('buildWhereConditions', () => {
		it('should build where conditions with tags using OR operator', () => {
			const result = buildWhereConditions({
				tags: ['tag1', 'tag2'],
				tagsOperator: 'OR',
				locale: 'en-US',
			});

			expect(result).toEqual([
				{
					'tags.en-US.value': {
						in: ['tag1', 'tag2'],
					},
				},
			]);
		});

		it('should build where conditions with tags using AND operator', () => {
			const result = buildWhereConditions({
				tags: ['tag1', 'tag2'],
				tagsOperator: 'AND',
				locale: 'en-US',
			});

			expect(result).toEqual([
				{
					'tags.en-US.value': {
						all: ['tag1', 'tag2'],
					},
				},
			]);
		});

		it('should build where conditions with multiple criteria', () => {
			const result = buildWhereConditions({
				tags: ['tag1', 'tag2'],
				tagsOperator: 'OR',
				category: ['cat1', 'cat2'],
				categoryOperator: 'AND', // Note: AND is specified but 'in' will be used for category
				topic: ['topic1'],
				topicOperator: 'OR',
				locale: 'es-US',
			});

			expect(result).toEqual([
				{
					'tags.es-US.value': {
						in: ['tag1', 'tag2'],
					},
				},
				{
					'category.es-US.value': {
						in: ['cat1', 'cat2'], // Always uses 'in' for category
					},
				},
				{
					'topic.es-US.value': {
						in: ['topic1'], // Always uses 'in' for topic
					},
				},
			]);
		});

		it('should always use "in" operator for category regardless of specified operator', () => {
			const result = buildWhereConditions({
				category: ['cat1', 'cat2'],
				categoryOperator: 'AND', // This should be ignored
				locale: 'en-US',
			});

			expect(result).toEqual([
				{
					'category.en-US.value': {
						in: ['cat1', 'cat2'], // Always uses 'in' for category
					},
				},
			]);
		});

		it('should always use "in" operator for topic regardless of specified operator', () => {
			const result = buildWhereConditions({
				topic: ['topic1', 'topic2'],
				topicOperator: 'AND', // This should be ignored
				locale: 'en-US',
			});

			expect(result).toEqual([
				{
					'topic.en-US.value': {
						in: ['topic1', 'topic2'], // Always uses 'in' for topic
					},
				},
			]);
		});

		it('should return empty array when no criteria provided', () => {
			const result = buildWhereConditions({});
			expect(result).toEqual([]);
		});
	});

	describe('handleTaskError', () => {
		it('should unlock a query after an error', async () => {
			const mockQueryDoc = {
				id: 'query-doc-123',
				queries: [
					{
						id: 'query-123',
						_locked: true,
						_lockedByTaskID: 'job-123',
					},
				],
			};

			mockPayload.findByID.mockResolvedValueOnce(mockQueryDoc);

			await handleTaskError({
				payload: mockPayload as any,
				queryDocID: 'query-doc-123',
				queryId: 'query-123',
				jobId: 'job-123',
				error: new Error('Test error'),
				req: mockReq,
			});

			expect(mockPayload.update).toHaveBeenCalledWith({
				collection: 'content-queries',
				id: 'query-doc-123',
				data: {
					queries: [
						{
							id: 'query-123',
							_locked: false,
							_lockedByTaskID: null,
							syncStatus: 'failed',
						},
					],
				},
				context: { skipQueueJobs: true },
				req: mockReq,
			});
		});

		it('should unlock a query by Kalliope ID after an error', async () => {
			const mockQueryDoc = {
				id: 'query-doc-123',
				queries: [
					{
						id: 'query-123',
						kalliopeCMQSID: 'kalliope-123',
						_locked: true,
						_lockedByTaskID: 'job-123',
					},
				],
			};

			mockPayload.findByID.mockResolvedValueOnce(mockQueryDoc);

			await handleTaskError({
				payload: mockPayload as any,
				queryDocID: 'query-doc-123',
				queryId: 'kalliope-123',
				jobId: 'job-123',
				error: new Error('Test error'),
				isKalliopeId: true,
				req: mockReq,
			});

			expect(mockPayload.update).toHaveBeenCalledWith({
				collection: 'content-queries',
				id: 'query-doc-123',
				data: {
					queries: [
						{
							id: 'query-123',
							kalliopeCMQSID: 'kalliope-123',
							_locked: false,
							_lockedByTaskID: null,
							syncStatus: 'failed',
						},
					],
				},
				context: { skipQueueJobs: true },
				req: mockReq,
			});
		});

		it('should not unlock a query if locked by another job', async () => {
			const mockQueryDoc = {
				id: 'query-doc-123',
				queries: [
					{
						id: 'query-123',
						_locked: true,
						_lockedByTaskID: 'other-job-id',
					},
				],
			};

			mockPayload.findByID.mockResolvedValueOnce(mockQueryDoc);

			await handleTaskError({
				payload: mockPayload as any,
				queryDocID: 'query-doc-123',
				queryId: 'query-123',
				jobId: 'job-123',
				error: new Error('Test error'),
				req: mockReq,
			});

			expect(mockPayload.update).not.toHaveBeenCalled();
		});

		it('should handle errors when query document is not found', async () => {
			mockPayload.findByID.mockResolvedValueOnce(null);

			await handleTaskError({
				payload: mockPayload as any,
				queryDocID: 'query-doc-123',
				queryId: 'query-123',
				jobId: 'job-123',
				error: new Error('Test error'),
				req: mockReq,
			});

			expect(mockPayload.logger.error).toHaveBeenCalled();
			expect(mockPayload.update).not.toHaveBeenCalled();
		});
	});
});
