import { Payload, PayloadRequest, Where } from 'payload';
import { ContentQuery } from '@repo/payload/payload-types';
import { hasLength } from '@repo/utils/hasLength';

/**
 * Find a query document and lock it for processing
 */
export async function findQueryAndLock({
	payload,
	req,
	queryDocID,
	queryId,
	jobId,
}: {
	payload: Payload;
	req: PayloadRequest;
	queryDocID: string;
	queryId: string;
	jobId: string;
}) {
	// Get the query document
	const queryDoc = await payload.findByID({
		collection: 'content-queries',
		id: queryDocID,
		depth: 0,
		req,
	});

	if (!queryDoc || !hasLength(queryDoc.queries)) {
		payload.logger.error(`Could not find query document with ID ${queryDocID}`);
		return { queryDoc: null, queryToUpdate: null, existingContent: [] };
	}

	// Find the query to update
	const queryToUpdate = queryDoc.queries?.find((q) => q.id === queryId);

	if (!queryToUpdate) {
		payload.logger.error(
			`Could not find query with ID ${queryId} in document ${queryDocID}`,
		);
		return { queryDoc, queryToUpdate: null, existingContent: [] };
	}

	// Check if the query is locked by another job
	if (
		queryToUpdate._locked &&
		queryToUpdate._lockedByTaskID &&
		queryToUpdate._lockedByTaskID !== jobId
	) {
		payload.logger.warn(
			`Query with ID ${queryId} is locked by job ${queryToUpdate._lockedByTaskID}`,
		);
		return { queryDoc, queryToUpdate: null, existingContent: [] };
	}

	// Lock the query for processing
	const queryIndex = queryDoc.queries.findIndex((q) => q.id === queryId);
	if (queryIndex === -1) {
		payload.logger.error(
			`Could not find query with ID ${queryId} in queries array`,
		);
		return { queryDoc, queryToUpdate: null, existingContent: [] };
	}

	// Update the query in the queries array
	queryDoc.queries[queryIndex] = {
		...queryToUpdate,
		_locked: true,
		_lockedByTaskID: jobId,
		syncStatus: 'in-progress',
	};

	// Create a consistent context object for all query updates
	const updateContext = {
		skipQueueJobs: true, // Set context flag to skip queuing jobs
	};

	// Update the query doc with the locked query
	await payload.update({
		collection: 'content-queries',
		id: queryDoc.id,
		data: {
			queries: queryDoc.queries,
		},
		context: updateContext,
		req,
	});

	// Get the existing content
	const existingContent = queryDoc.mergedContent || [];

	return { queryDoc, queryToUpdate, existingContent };
}

/**
 * Find a query document by Kalliope ID and lock it for processing
 */
export async function findQueryByKalliopeIdAndLock({
	payload,
	req,
	queryDocID,
	kalliopeCMQID,
	jobId,
}: {
	payload: Payload;
	req: PayloadRequest;
	queryDocID: string;
	kalliopeCMQID: string;
	jobId: string;
}) {
	// Get the query document
	const queryDoc = await payload.findByID({
		collection: 'content-queries',
		id: queryDocID,
		depth: 0,
		req,
	});

	if (!queryDoc || !hasLength(queryDoc.queries)) {
		payload.logger.error(`Could not find query document with ID ${queryDocID}`);
		return { queryDoc: null, queryToUpdate: null, existingContent: [] };
	}

	// Find the query to update by Kalliope ID
	const queryToUpdate = queryDoc.queries?.find(
		(q) => q.kalliopeCMQSID === kalliopeCMQID,
	);

	if (!queryToUpdate) {
		payload.logger.error(
			`Could not find query with kalliopeCMQSID ${kalliopeCMQID} in document ${queryDocID}`,
		);
		return { queryDoc, queryToUpdate: null, existingContent: [] };
	}

	// Check if the query is locked by another job
	if (
		queryToUpdate._locked &&
		queryToUpdate._lockedByTaskID &&
		queryToUpdate._lockedByTaskID !== jobId
	) {
		payload.logger.warn(
			`Query with kalliopeCMQSID ${kalliopeCMQID} is locked by job ${queryToUpdate._lockedByTaskID}`,
		);
		return { queryDoc, queryToUpdate: null, existingContent: [] };
	}

	// Lock the query for processing
	const queryIndex = queryDoc.queries.findIndex(
		(q) => q.kalliopeCMQSID === kalliopeCMQID,
	);
	if (queryIndex === -1) {
		payload.logger.error(
			`Could not find query with kalliopeCMQSID ${kalliopeCMQID} in queries array`,
		);
		return { queryDoc, queryToUpdate: null, existingContent: [] };
	}

	// Update the query in the queries array
	queryDoc.queries[queryIndex] = {
		...queryToUpdate,
		_locked: true,
		_lockedByTaskID: jobId,
		syncStatus: 'in-progress',
	};

	// Create a consistent context object for all query updates
	const updateContext = {
		skipQueueJobs: true, // Set context flag to skip queuing jobs
	};

	// Update the query doc with the locked query
	await payload.update({
		collection: 'content-queries',
		id: queryDoc.id,
		data: {
			queries: queryDoc.queries,
		},
		context: updateContext,
		req,
	});

	// Get the existing content
	const existingContent = queryDoc.mergedContent || [];

	return { queryDoc, queryToUpdate, existingContent };
}

/**
 * Update a query with new content
 */
export async function updateQueryWithContent({
	payload,
	queryDoc,
	queryToUpdate,
	newContent,
	queryId,
	req,
}: {
	payload: Payload;
	queryDoc: ContentQuery;
	// @ts-expect-error - TypeScript is having trouble inferring the type here
	queryToUpdate: ContentQuery['queries'][number];
	newContent: ContentQuery['mergedContent'];
	existingContent: ContentQuery['mergedContent'];
	queryId: string;
	req: PayloadRequest;
}) {
	if (!hasLength(queryDoc.queries)) {
		payload.logger.error(
			`Could not find query document with ID ${queryDoc.id}`,
		);
		return { queryDoc: null, queryToUpdate: null, existingContent: [] };
	}
	// Find the index of the query to update
	const queryIndex = queryDoc.queries.findIndex(
		(q) => q.id === queryToUpdate.id,
	);

	if (queryIndex === -1) {
		payload.logger.error(
			`Could not find query with ID ${queryId} in queries array`,
		);
		return;
	}

	// Update the query in the queries array
	queryDoc.queries[queryIndex] = {
		...queryToUpdate,
		_locked: false,
		_lockedByTaskID: null,
		syncStatus: 'completed',
		lastSynced: new Date().toISOString(),
	};

	// Create a consistent context object for all query updates
	const updateContext = {
		skipQueueJobs: true, // Set context flag to skip queuing jobs
	};

	// Update the query doc with the new content
	await payload.update({
		collection: 'content-queries',
		id: queryDoc.id,
		data: {
			queries: queryDoc.queries,
			mergedContent: newContent,
		},
		context: updateContext,
		req,
	});

	payload.logger.debug(
		`Updated query with ID ${queryId} with ${newContent?.length || 0} content items`,
	);
}

/**
 * Unlock a query and mark it as completed
 */
export async function unlockAndCompleteQuery({
	payload,
	queryDoc,
	queryToUpdate,
	queries,
	req,
}: {
	payload: Payload;
	queryDoc: ContentQuery;
	// @ts-expect-error - TypeScript is having trouble inferring the type here
	queryToUpdate: ContentQuery['queries'][number];
	queries: ContentQuery['queries'];
	req: PayloadRequest;
}) {
	if (!hasLength(queries) || !queryToUpdate) {
		payload.logger.error(
			`No queries found in document ${queryDoc.id} when unlocking`,
		);
		return;
	}
	// Find the index of the query to update
	const queryIndex = queries.findIndex((q) => q.id === queryToUpdate.id);

	if (queryIndex === -1) {
		payload.logger.error(
			`Could not find query with ID ${queryToUpdate.id} in queries array`,
		);
		return;
	}

	// Update the query in the queries array
	queries[queryIndex] = {
		...queryToUpdate,
		_locked: false,
		_lockedByTaskID: null,
		syncStatus: 'completed',
		lastSynced: new Date().toISOString(),
	};

	// Create a consistent context object for all query updates
	const updateContext = {
		skipQueueJobs: true, // Set context flag to skip queuing jobs
	};

	// Update the query doc with the unlocked query
	await payload.update({
		collection: 'content-queries',
		id: queryDoc.id,
		data: {
			queries,
		},
		context: updateContext,
		req,
	});

	payload.logger.debug(
		`Unlocked and completed query with ID ${queryToUpdate.id}`,
	);
}

/**
 * Build where conditions for content queries
 */
export function buildWhereConditions({
	tags,
	tagsOperator,
	category,
	categoryOperator,
	topic,
	topicOperator,
	locale = 'en-US',
}: {
	tags?: string[];
	tagsOperator?: string;
	category?: string[];
	categoryOperator?: string;
	topic?: string[];
	topicOperator?: string;
	locale?: string;
}): Where[] {
	const whereConditions = [];

	// Add tags condition if tags are provided
	if (hasLength(tags)) {
		const tagsCondition = {
			[`tags.${locale}.value`]: {
				[tagsOperator === 'AND' ? 'all' : 'in']: tags,
			},
		};
		whereConditions.push(tagsCondition);
	}

	// Add category condition if categories are provided
	if (hasLength(category)) {
		// Warning: Articles can only have one category, so using AND with multiple values won't match anything
		if (categoryOperator === 'AND' && category.length > 1) {
			console.warn(
				'Warning: Using AND operator with multiple categories will not match any articles since an article can only have one category.',
			);
		}

		const categoryCondition = {
			[`category.${locale}.value`]: {
				// Always use 'in' for category since an article can only have one category
				in: category,
			},
		};
		whereConditions.push(categoryCondition);
	}

	// Add topic condition if topics are provided
	if (hasLength(topic)) {
		// Warning: Articles can only have one topic, so using AND with multiple values won't match anything
		if (topicOperator === 'AND' && topic.length > 1) {
			console.warn(
				'Warning: Using AND operator with multiple topics will not match any articles since an article can only have one topic.',
			);
		}

		const topicCondition = {
			[`topic.${locale}.value`]: {
				// Always use 'in' for topic since an article can only have one topic
				in: topic,
			},
		};
		whereConditions.push(topicCondition);
	}

	return whereConditions;
}

/**
 * Handle task errors and unlock queries
 *
 * This function handles errors that occur during task execution and unlocks any queries
 * that were locked by the task. It uses a direct approach to update the query object
 * to avoid TypeScript errors with array indexing.
 */
export async function handleTaskError({
	payload,
	queryDocID,
	queryId,
	jobId,
	error,
	isKalliopeId = false,
	req,
}: {
	payload: Payload;
	queryDocID: string;
	queryId: string;
	jobId: string;
	error: Error;
	isKalliopeId?: boolean;
	req: PayloadRequest;
}) {
	try {
		// Get the query document
		const queryDoc = await payload.findByID({
			collection: 'content-queries',
			id: queryDocID,
			depth: 0,
			req,
		});

		if (!queryDoc || !hasLength(queryDoc.queries)) {
			payload.logger.error(
				`Could not find query document with ID ${queryDocID} to handle error`,
			);
			return;
		}

		// Find the query to update
		const queryToUpdate = isKalliopeId
			? queryDoc.queries?.find((q) => q.kalliopeCMQSID === queryId)
			: queryDoc.queries?.find((q) => q.id === queryId);

		if (!queryToUpdate) {
			payload.logger.error(
				`Could not find query with ${isKalliopeId ? 'kalliopeCMQSID' : 'ID'} ${queryId} in document ${queryDocID} to handle error`,
			);
			return;
		}

		// Only unlock if this job locked it
		if (
			queryToUpdate._locked &&
			queryToUpdate._lockedByTaskID &&
			queryToUpdate._lockedByTaskID === jobId
		) {
			// Find the index of the query to update
			const queryIndex = queryDoc.queries.findIndex((q) =>
				isKalliopeId ? q.kalliopeCMQSID === queryId : q.id === queryId,
			);

			if (queryIndex === -1) {
				payload.logger.error(
					`Could not find query with ${isKalliopeId ? 'kalliopeCMQSID' : 'ID'} ${queryId} in queries array to handle error`,
				);
				return;
			}

			// We've already verified that queryDoc.queries exists and queryIndex is valid,
			// but TypeScript is still having trouble with the type inference.
			// Since we've done all the necessary runtime checks, we'll use @ts-expect-error
			// to bypass the TypeScript error.

			queryDoc.queries[queryIndex] = {
				...queryToUpdate,
				_locked: false,
				_lockedByTaskID: null,
				syncStatus: 'failed',
			};

			// Create a consistent context object for all query updates
			const updateContext = {
				skipQueueJobs: true, // Set context flag to skip queuing jobs
			};

			// Update the query doc with the unlocked query
			await payload.update({
				collection: 'content-queries',
				id: queryDoc.id,
				data: {
					queries: queryDoc.queries,
				},
				context: updateContext,
				req,
			});

			payload.logger.debug(
				`Unlocked query with ${isKalliopeId ? 'kalliopeCMQSID' : 'ID'} ${queryId} after error: ${error.message}`,
			);
		}
	} catch (unlockError) {
		payload.logger.error(
			`Error unlocking query with ${isKalliopeId ? 'kalliopeCMQSID' : 'ID'} ${queryId} after error: ${unlockError}`,
		);
	}
}
