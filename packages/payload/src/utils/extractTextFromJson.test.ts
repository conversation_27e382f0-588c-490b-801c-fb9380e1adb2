import { describe, it, expect } from 'vitest';
import { extractTextFromJson } from './extractTextFromJson';

describe('extractTextFromJson', () => {
	it('should extract text from Lexical rich text editor structure', () => {
		const lexicalContent = {
			body: {
				root: {
					type: 'root',
					children: [
						{
							type: 'paragraph',
							children: [
								{
									type: 'text',
									text: 'This is a weather article about storms.',
									version: 1,
								},
							],
							version: 1,
						},
						{
							type: 'paragraph',
							children: [
								{
									type: 'text',
									text: 'Residents should prepare for heavy rainfall.',
									version: 1,
								},
							],
							version: 1,
						},
					],
					version: 1,
				},
			},
		};

		const result = extractTextFromJson(lexicalContent);
		expect(result).toBe(
			'This is a weather article about storms. Residents should prepare for heavy rainfall.',
		);
	});

	it('should handle plain text input', () => {
		const plainText = 'This is plain text content';
		const result = extractTextFromJson(plainText);
		expect(result).toBe(plainText);
	});

	it('should handle arrays of content', () => {
		const arrayContent = [
			{ type: 'text', text: 'First paragraph.' },
			{ type: 'text', text: 'Second paragraph.' },
		];
		const result = extractTextFromJson(arrayContent);
		expect(result).toBe('First paragraph. Second paragraph.');
	});

	it('should handle other rich text editor structures', () => {
		const otherEditorContent = {
			type: 'doc',
			content: [
				{
					type: 'paragraph',
					content: [
						{
							type: 'text',
							text: 'Content from another editor.',
						},
					],
				},
			],
		};
		const result = extractTextFromJson(otherEditorContent);
		expect(result).toBe('Content from another editor.');
	});

	it('should handle objects with text property', () => {
		const textObject = { text: 'Simple text object' };
		const result = extractTextFromJson(textObject);
		expect(result).toBe('Simple text object');
	});

	it('should handle nested objects', () => {
		const nestedContent = {
			metadata: {
				title: 'Article Title',
			},
			content: {
				body: {
					root: {
						type: 'root',
						children: [
							{
								type: 'paragraph',
								children: [
									{
										type: 'text',
										text: 'Article content here.',
										version: 1,
									},
								],
								version: 1,
							},
						],
						version: 1,
					},
				},
			},
		};
		const result = extractTextFromJson(nestedContent);
		expect(result).toBe('Article Title Article content here.');
	});

	it('should return empty string for empty objects', () => {
		const emptyObject = {};
		const result = extractTextFromJson(emptyObject);
		expect(result).toBe('');
	});

	it('should return empty string for null input', () => {
		const result = extractTextFromJson(null);
		expect(result).toBe('');
	});

	it('should handle mixed content types', () => {
		const mixedContent = {
			title: 'Article Title',
			body: {
				root: {
					type: 'root',
					children: [
						{
							type: 'paragraph',
							children: [
								{
									type: 'text',
									text: 'Article body content.',
									version: 1,
								},
							],
							version: 1,
						},
					],
					version: 1,
				},
			},
			summary: 'Article summary',
		};
		const result = extractTextFromJson(mixedContent);
		expect(result).toBe('Article body content.');
	});
});
