import { describe, it, expect } from 'vitest';
import { isValidUrl } from '@repo/payload/utils/isValidUrl';

describe('isValidUrl', () => {
	// Valid URLs with default protocols (http, https)
	it.each([
		'http://example.com',
		'https://example.com',
		'https://subdomain.example.com',
		'https://example.com/path/to/resource',
		'https://example.com/search?q=test&page=1',
		'https://example.com/page#section',
		'https://example.com:8080',
		'https://one.two.three.example.com',
		'https://example.com/',
		'https://example.com/path-with_special~chars',
	])('should validate valid URL: %s', (url) => {
		expect(isValidUrl(url)).toBe(true);
	});

	// Invalid URLs
	it.each([
		'',
		'example.com',
		'http:/example.com',
		'just a string',
		'ftp://example.com',
		'sftp://example.com',
		'mailto:<EMAIL>',
		'tel:+1234567890',
		'https://例子.测试',
	])('should reject invalid URL: %s', (url) => {
		expect(isValidUrl(url)).toBe(false);
	});

	// Edge cases with null/undefined
	it.each([undefined as unknown as string, null as unknown as string])(
		'should reject invalid value: %s',
		(value) => {
			expect(isValidUrl(value)).toBe(false);
		},
	);

	// Custom protocol options
	describe.each([
		{ url: 'ftp://example.com', protocols: ['ftp'], expected: true },
		{ url: 'http://example.com', protocols: ['ftp'], expected: false },
		{
			url: 'ftp://example.com',
			protocols: ['http', 'https', 'ftp'],
			expected: true,
		},
		{
			url: 'sftp://example.com',
			protocols: ['http', 'https', 'ftp'],
			expected: false,
		},
		{ url: 'http://example.com', protocols: [], expected: false },
	])(
		'testing URL: $url with protocols: $protocols',
		({ url, protocols, expected }) => {
			it(`should ${expected ? 'accept' : 'reject'} the URL`, () => {
				expect(isValidUrl(url, { protocols })).toBe(expected);
			});
		},
	);
});
