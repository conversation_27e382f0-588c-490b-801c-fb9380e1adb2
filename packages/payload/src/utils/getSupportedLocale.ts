import { Config } from '@repo/payload/payload-types';

// Define the type from the existing Config type
type ValidLocale = Config['locale'];

// Create a value array with all possible locales
export const validLocales: ValidLocale[] = [
	'de-DE',
	'en-US',
	'es-US',
	'fr-FR',
	'gls',
];

// Function to validate and convert a string to the typed locale
export function getLocale(locale: string | undefined): ValidLocale {
	return locale && validLocales.includes(locale as ValidLocale)
		? (locale as ValidLocale)
		: 'en-US';
}
