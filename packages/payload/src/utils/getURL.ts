import canUseDOM from '@repo/utils/canUseDOM';

export const getServerSideURL = () => {
	let url = process.env.NEXT_PUBLIC_SERVER_URL;

	if (!url && process.env.VERCEL_PROJECT_PRODUCTION_URL) {
		// return weather.com for production.
		// we can remove this once we have weather.com custom domain
		// on vercel for production environment
		if (process.env.VERCEL_ENV === 'production') {
			return `https://weather.com`;
		}

		return `https://${process.env.VERCEL_PROJECT_PRODUCTION_URL}`;
	}

	if (!url) {
		url = 'https://local.weather.com';
	}

	return url;
};

export const getClientSideURL = () => {
	if (canUseDOM) {
		const protocol = window.location.protocol;
		const domain = window.location.hostname;
		const port = window.location.port;

		return `${protocol}//${domain}${port ? `:${port}` : ''}`;
	}

	// Server-side URL detection
	return getServerSideURL();
};

/**
 * Get the current deployment URL for server-side usage
 * This handles different environments with proper domain mapping
 *
 * Environment mapping:
 * - Local development: localhost:3001
 * - Preview environments: VERCEL_BRANCH_URL
 * - Custom environments via VERCEL_TARGET_ENV:
 *   - qat -> qat.weather.com
 *   - dev -> dev.weather.com
 *   - staging -> stage.weather.com
 * - Production: weather.com
 */
export const getCurrentDeploymentURL = () => {
	// 1. Local development
	if (
		!process.env.VERCEL_BRANCH_URL &&
		process.env.NODE_ENV === 'development'
	) {
		const url = 'https://local.weather.com';
		return url;
	}

	// 2. Preview Vercel environments - use VERCEL_BRANCH_URL
	if (process.env.VERCEL_BRANCH_URL && process.env.VERCEL_ENV === 'preview') {
		const url = `https://${process.env.VERCEL_BRANCH_URL}`;
		return url;
	}

	// 3. Production environment
	if (process.env.VERCEL_ENV === 'production') {
		const url = 'https://weather.com';
		return url;
	}

	// 4. Custom environments using VERCEL_TARGET_ENV mapping
	if (process.env.VERCEL_TARGET_ENV) {
		const targetEnv = process.env.VERCEL_TARGET_ENV.toLowerCase();
		const envMapping: Record<string, string> = {
			qat: 'https://qat.weather.com',
			dev: 'https://dev.weather.com',
			staging: 'https://stage.weather.com',
		};

		if (envMapping[targetEnv]) {
			const url = envMapping[targetEnv];
			return url;
		} else {
			// Unknown environment - redirect to not-found
			const url = 'https://weather.com/not-found';
			return url;
		}
	}

	// 5. Fallback to client-side URL detection for any other cases
	const fallbackUrl = getClientSideURL();
	return fallbackUrl;
};
