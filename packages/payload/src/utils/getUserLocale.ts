import { PayloadRequest } from 'payload';
import { getLocale } from '@repo/payload/utils/getSupportedLocale';

/**
 * Extracts the user's locale from a PayloadCMS request
 *
 * PayloadCMS has a specific set of supported locales that we need to match.
 * This function attempts to map user locales to one of the supported PayloadCMS locales.
 *
 * @param req - The PayloadCMS request
 * @param defaultLocale - Default locale to use if none can be determined
 * @returns A locale value compatible with PayloadCMS
 */
export const getUserLocale = (
	req: PayloadRequest | undefined,
	defaultLocale: PayloadRequest['locale'] = 'en-US',
): PayloadRequest['locale'] => {
	if (!req) return defaultLocale;

	// Use PayloadCMS locale if available
	if (req.locale) {
		if (getLocale(req.locale)) return req.locale;
	}

	// Return default locale if no match found
	return defaultLocale;
};
