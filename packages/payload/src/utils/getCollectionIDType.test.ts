import { describe, test, expect, vi } from 'vitest';
import { getCollectionIDType } from './getCollectionIDType';
import type { Payload, CollectionSlug } from 'payload';

describe('getCollectionIDType', () => {
	test('returns collection customIDType when available', () => {
		// Mock payload with a collection that has customIDType
		const mockPayload = {
			collections: {
				users: {
					customIDType: 'text',
				},
				products: {
					customIDType: 'number',
				},
			},
			db: {
				defaultIDType: 'number',
			},
		} as unknown as Payload;

		// Test with collection that has text ID type
		expect(
			getCollectionIDType({
				collectionSlug: 'users' as CollectionSlug,
				payload: mockPayload,
			}),
		).toBe('text');

		// Test with collection that has number ID type
		expect(
			getCollectionIDType({
				collectionSlug: 'products' as CollectionSlug,
				payload: mockPayload,
			}),
		).toBe('number');
	});

	test('returns database defaultIDType when collection customIDType is not available', () => {
		// Mock payload with a collection that doesn't have customIDType
		const mockPayload = {
			collections: {
				posts: {},
			},
			db: {
				defaultIDType: 'number',
			},
		} as unknown as Payload;

		expect(
			getCollectionIDType({
				collectionSlug: 'posts' as CollectionSlug,
				payload: mockPayload,
			}),
		).toBe('number');
	});

	test('handles different database defaultIDType values', () => {
		// Mock payload with text as defaultIDType
		const mockPayloadWithTextDefault = {
			collections: {
				comments: {},
			},
			db: {
				defaultIDType: 'text',
			},
		} as unknown as Payload;

		expect(
			getCollectionIDType({
				collectionSlug: 'comments' as CollectionSlug,
				payload: mockPayloadWithTextDefault,
			}),
		).toBe('text');
	});

	test('handles non-existent collection', () => {
		// Mock payload with no matching collection
		const mockPayload = {
			collections: {},
			db: {
				defaultIDType: 'number',
			},
		} as unknown as Payload;

		expect(
			getCollectionIDType({
				collectionSlug: 'nonexistent' as CollectionSlug,
				payload: mockPayload,
			}),
		).toBe('number');
	});
});
