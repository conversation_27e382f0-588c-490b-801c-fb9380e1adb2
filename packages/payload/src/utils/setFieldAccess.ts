import type { FieldBase } from 'payload';
import type { FieldPermissionsMap } from '@repo/payload/configs/access';
import getRolesFromUser from './getRolesFromUser';

/**
 * Creates field-level read/update access based on a provided
 * `permissionsMap` object and the field's `name` string.
 *
 * Usage:
 *   access: createFieldAccess('myFieldName', myPermissionsMap)
 */
const setFieldAccess = (
	fieldName: string,
	permissionsMap: FieldPermissionsMap,
): FieldBase['access'] => {
	return {
		read: ({ req: { user } }) => {
			if (!user) return false;
			const roles = getRolesFromUser(user);
			const table = permissionsMap[fieldName];
			if (!table) return false;
			return roles.some((role) => table[role]?.read === true);
		},
		update: ({ req: { user } }) => {
			if (!user) return false;
			const roles = getRolesFromUser(user);
			const table = permissionsMap[fieldName];
			if (!table) return false;
			return roles.some((role) => table[role]?.update === true);
		},
	};
};

export default setFieldAccess;
