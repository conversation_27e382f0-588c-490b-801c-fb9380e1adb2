import { describe, it, expect } from 'vitest';
import { isValidBase64 } from './isValidBase64';

describe('isValidBase64', () => {
	describe('valid base64 strings', () => {
		it('should return true for valid base64 strings', () => {
			expect(isValidBase64('SGVsbG8gV29ybGQ=')).toBe(true); // "Hello World"
			expect(isValidBase64('VGVzdCBzdHJpbmc=')).toBe(true); // "Test string"
			expect(
				isValidBase64('QSBsb25nZXIgdGV4dCB0aGF0IG5lZWRzIHRvIGJlIGVuY29kZWQ='),
			).toBe(true); // "A longer text that needs to be encoded"
		});

		it('should return true for base64 strings with padding', () => {
			expect(isValidBase64('dGVzdA==')).toBe(true); // "test"
			expect(isValidBase64('dGVzdGVy')).toBe(true); // "tester"
		});

		it('should return true for data URLs with base64', () => {
			expect(isValidBase64('data:image/jpeg;base64,SGVsbG8gV29ybGQ=')).toBe(
				true,
			);
			expect(isValidBase64('data:image/png;base64,VGVzdCBzdHJpbmc=')).toBe(
				true,
			);
			expect(
				isValidBase64('data:application/json;base64,eyJ0ZXN0IjoidmFsdWUifQ=='),
			).toBe(true);
		});
	});

	describe('invalid base64 strings', () => {
		it('should return false for non-base64 strings', () => {
			expect(isValidBase64('Not base64')).toBe(false);
			expect(isValidBase64('Hello World')).toBe(false);
			expect(isValidBase64('This is not base64 encoded')).toBe(false);
		});

		it('should return false for strings with invalid characters', () => {
			expect(isValidBase64('SGVsbG8gV29ybGQ!')).toBe(false); // Invalid character '!'
			expect(isValidBase64('SGVsbG8gV29ybGQ@')).toBe(false); // Invalid character '@'
			expect(isValidBase64('SGVsbG8gV29ybGQ#')).toBe(false); // Invalid character '#'
		});

		it('should return false for malformed base64', () => {
			expect(isValidBase64('SGVsbG8gV29ybGQ')).toBe(false); // Missing padding
			expect(isValidBase64('SGVsbG8gV29ybGQ===')).toBe(false); // Too much padding
			expect(isValidBase64('SGVsbG8gV29ybGQ!=')).toBe(false); // Invalid character with padding
		});

		it('should return false for empty or null values', () => {
			expect(isValidBase64('')).toBe(false);
			expect(isValidBase64(null as any)).toBe(false);
			expect(isValidBase64(undefined as any)).toBe(false);
		});

		it('should return false for non-string values', () => {
			expect(isValidBase64(123 as any)).toBe(false);
			expect(isValidBase64({} as any)).toBe(false);
			expect(isValidBase64([] as any)).toBe(false);
		});
	});

	describe('edge cases', () => {
		it('should handle single character base64', () => {
			expect(isValidBase64('QQ==')).toBe(true); // "A"
			expect(isValidBase64('Qg==')).toBe(true); // "B"
		});

		it('should handle data URLs with various MIME types', () => {
			expect(isValidBase64('data:text/plain;base64,SGVsbG8=')).toBe(true);
			expect(isValidBase64('data:application/pdf;base64,JVBERi0xLjQK')).toBe(
				true,
			);
			expect(
				isValidBase64(
					'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCI+PC9zdmc+',
				),
			).toBe(true);
		});

		it('should handle base64 strings with different lengths', () => {
			expect(isValidBase64('QQ==')).toBe(true); // 1 character
			expect(isValidBase64('QkM=')).toBe(true); // 2 characters
			expect(isValidBase64('QkNE')).toBe(true); // 3 characters
			expect(isValidBase64('QkNERQ==')).toBe(true); // 4 characters
		});
	});
});
