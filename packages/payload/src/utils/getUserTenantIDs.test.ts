import { describe, test, expect } from 'vitest';
import { getUserTenantIDs } from './getUserTenantIDs';
import type { TypedUser } from 'payload';

describe('getUserTenantIDs', () => {
	test('returns empty array when user is null', () => {
		expect(getUserTenantIDs(null)).toEqual([]);
	});

	test('returns empty array when user has no tenants', () => {
		const userWithoutTenants = {
			id: '123',
			tenants: undefined,
		} as TypedUser;

		expect(getUserTenantIDs(userWithoutTenants)).toEqual([]);
	});

	test('returns tenant IDs for user with tenants', () => {
		const userWithTenants = {
			id: '123',
			tenants: [
				{ tenant: '1', roles: ['admin'] },
				{ tenant: '2', roles: ['editor'] },
				{ tenant: '3', roles: ['viewer'] },
			],
		} as unknown as TypedUser;

		expect(getUserTenantIDs(userWithTenants)).toEqual(['1', '2', '3']);
	});

	test('returns tenant IDs for user with tenant objects', () => {
		const userWithTenantObjects = {
			id: '123',
			tenants: [
				{ tenant: { id: '1' }, roles: ['admin'] },
				{ tenant: { id: '2' }, roles: ['editor'] },
				{ tenant: { id: '3' }, roles: ['viewer'] },
			],
		} as unknown as TypedUser;

		expect(getUserTenantIDs(userWithTenantObjects)).toEqual(['1', '2', '3']);
	});

	test('filters tenant IDs by role', () => {
		const userWithMixedRoles = {
			id: '123',
			tenants: [
				{ tenant: '1', roles: ['admin'] },
				{ tenant: '2', roles: ['editor'] },
				{ tenant: '3', roles: ['admin', 'editor'] },
				{ tenant: '4', roles: ['viewer'] },
			],
		} as unknown as TypedUser;

		expect(getUserTenantIDs(userWithMixedRoles, 'admin')).toEqual(['1', '3']);
		expect(getUserTenantIDs(userWithMixedRoles, 'editor')).toEqual(['2', '3']);
		expect(getUserTenantIDs(userWithMixedRoles, 'viewer')).toEqual(['4']);
	});

	test('handles tenants without valid tenant reference', () => {
		const userWithInvalidTenants = {
			id: '123',
			tenants: [
				{ tenant: '1', roles: ['admin'] },
				{ tenant: null, roles: ['editor'] },
				{ tenant: undefined, roles: ['viewer'] },
			],
		} as unknown as TypedUser;

		expect(getUserTenantIDs(userWithInvalidTenants)).toEqual(['1']);
	});
});
