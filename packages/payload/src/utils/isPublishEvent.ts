import { Article, Liveblog } from '@repo/payload/payload-types';
import { Operation } from 'payload';

type ValidStatus = Article['_status'] | Liveblog['_status'];

export const isPublishEvent = (
	operation: Operation | undefined,
	status: ValidStatus | undefined,
	originalStatus: ValidStatus | undefined,
): boolean => {
	// Handle create operation with published status
	if (operation === 'create' && status === 'published') {
		return true;
	}

	// Handle update operation from draft to published
	if (operation === 'update') {
		if (status === 'published' && originalStatus === 'draft') {
			return true;
		}
	}

	return false;
};
