/**
 * Validates if a string is valid base64 encoded
 *
 * This function checks if the input string is properly base64 encoded by:
 * 1. Attempting to decode it as base64
 * 2. Re-encoding the decoded result and comparing with the original
 * 3. Catching any errors from invalid base64 characters
 *
 * @param str - The string to validate as base64
 * @returns true if the string is valid base64, false otherwise
 *
 * @example
 * ```typescript
 * isValidBase64('SGVsbG8gV29ybGQ='); // true
 * isValidBase64('Not base64'); // false
 * isValidBase64(''); // false
 * ```
 */
export function isValidBase64(str: string): boolean {
	if (!str || typeof str !== 'string') {
		return false;
	}

	try {
		// Remove data URL prefix if present
		const cleanStr = str.replace(/^data:[^;]+;base64,/, '');

		// Check if the string contains only valid base64 characters
		if (!/^[A-Za-z0-9+/]*={0,2}$/.test(cleanStr)) {
			return false;
		}

		// Verify by decoding and re-encoding
		const decoded = Buffer.from(cleanStr, 'base64');
		const reEncoded = decoded.toString('base64');

		return reEncoded === cleanStr;
	} catch (_e) {
		return false; // Catches errors for invalid Base64 characters
	}
}
