import { describe, test, expect } from 'vitest';
import { PayloadRequest } from 'payload';
import { getUserLocale } from './getUserLocale';

describe('getUserLocale', () => {
	// Test with undefined request
	test('returns default locale when request is undefined', () => {
		expect(getUserLocale(undefined)).toBe('en-US');
		expect(getUserLocale(undefined, 'fr-FR')).toBe('fr-FR');
	});

	// Test with undefined locale in request
	test('returns default locale when request locale is undefined', () => {
		const mockReq = {} as PayloadRequest;
		expect(getUserLocale(mockReq)).toBe('en-US');
	});
});
