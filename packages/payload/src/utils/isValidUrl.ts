type IsValidUrlOptions = {
	protocols?: string[];
};

export const isValidUrl = (
	url: string,
	options?: IsValidUrlOptions,
): boolean => {
	const protocols = options?.protocols ?? ['http', 'https'];

	// Regex to validate URLs
	const urlRegex = new RegExp(
		`^(${protocols.join('|')})://` + // protocol
			`(?:[a-zA-Z0-9-]+\\.)*` + // optional subdomains
			`[a-zA-Z0-9-]+\\.[a-zA-Z]{2,}` + // domain and TLD
			`(?::\\d{1,5})?` + // optional port (e.g., :80, :443)
			`(?:/(?:[^\\s%]|%[0-9A-Fa-f]{2})*)?$`, // optional path with percent-encoding
	);

	if (urlRegex.test(url)) {
		return true;
	}

	return false;
};
