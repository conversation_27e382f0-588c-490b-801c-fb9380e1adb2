import { Config } from '@repo/payload/payload-types';

export const getLocalizedValue = (
	field: string | Record<string, string>,
	locale: Config['locale'],
	fallbackLocale = 'en-US',
): string | null => {
	if (!field) return null;

	// If it's already a string, return it directly
	if (typeof field === 'string') return field;

	// If it's an object with locale keys, try to get the requested locale
	if (typeof field === 'object') {
		return field[locale] || field[fallbackLocale] || null;
	}

	return null;
};
