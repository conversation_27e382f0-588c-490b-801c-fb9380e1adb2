import { describe, it, expect } from 'vitest';
import { getLocalizedValue } from './getLocalizedValue';

describe('getLocalizedValue', () => {
	it('should return null when field is null or undefined', () => {
		expect(getLocalizedValue(null as any, 'en-US')).toBeNull();
		expect(getLocalizedValue(undefined as any, 'en-US')).toBeNull();
	});

	it('should return the string directly if field is a string', () => {
		expect(getLocalizedValue('test string', 'en-US')).toBe('test string');
		expect(getLocalizedValue('another test', 'fr-FR')).toBe('another test');
	});

	it('should return the value for the requested locale if available', () => {
		const field = {
			'en-US': 'Hello',
			'fr-FR': 'Bonjour',
			'es-ES': 'Hola',
		};

		expect(getLocalizedValue(field, 'fr-FR')).toBe('Bonjour');
		expect(getLocalizedValue(field, 'es-ES' as any)).toBe('Hola');
	});

	it('should fall back to the fallback locale if requested locale is not available', () => {
		const field = {
			'en-US': 'Hello',
			'fr-FR': 'Bonjour',
		};

		expect(getLocalizedValue(field, 'de-DE')).toBe('Hello');
	});

	it('should use the provided fallback locale', () => {
		const field = {
			'en-US': 'Hello',
			'fr-FR': 'Bonjour',
			'es-ES': 'Hola',
		};

		expect(getLocalizedValue(field, 'de-DE', 'fr-FR')).toBe('Bonjour');
	});

	it('should return null if neither requested locale nor fallback locale exists', () => {
		const field = {
			'fr-FR': 'Bonjour',
			'es-ES': 'Hola',
		};

		expect(getLocalizedValue(field, 'de-DE')).toBeNull();
	});
});
