import 'server-only';
import { getPayload } from 'payload';
import { cache } from 'react';
import config from '@repo/payload/payload-config';
import { headers as nextHeaders } from 'next/headers';

export const getUserFromHeaders = cache(async (headersList: Headers) => {
	try {
		const payload = await getPayload({ config });
		const response = await payload.auth({ headers: headersList });
		return response.user;
	} catch {
		return null;
	}
});

/**
 * Utility function that gets the current user and their roles from headers
 * This consolidates the common pattern used across the application
 * @returns Object containing user and userRoles
 */
export const getCurrentUser = cache(async () => {
	const headers = await nextHeaders();
	const user = await getUserFromHeaders(headers);
	const userRoles = user?.role || [];

	return {
		user,
		userRoles,
	};
});
