/**
 * Extracts text content from JSON structures commonly used by rich text editors
 * @param jsonContent - The parsed JSON content
 * @returns The extracted text content
 */
export function extractTextFromJson(jsonContent: unknown): string {
	if (typeof jsonContent === 'string') {
		return jsonContent;
	}

	if (Array.isArray(jsonContent)) {
		return jsonContent.map((item) => extractTextFromJson(item)).join(' ');
	}

	if (typeof jsonContent === 'object' && jsonContent !== null) {
		const obj = jsonContent as Record<string, unknown>;

		// Handle Lexical rich text editor structure
		if (obj.body && typeof obj.body === 'object' && obj.body !== null) {
			const body = obj.body as Record<string, unknown>;
			if (body.root) {
				return extractTextFromJson(body.root);
			}
		}

		// Handle text nodes directly (highest priority)
		if (obj.type === 'text' && obj.text) {
			return String(obj.text);
		}

		// Handle blocks with text property
		if (obj.text) {
			return String(obj.text);
		}

		// Handle nodes with children arrays
		if (obj.children && Array.isArray(obj.children)) {
			return obj.children.map((item) => extractTextFromJson(item)).join(' ');
		}

		// Handle blocks with content arrays (for other rich text editors)
		if (obj.content && Array.isArray(obj.content)) {
			return obj.content.map((item) => extractTextFromJson(item)).join(' ');
		}

		// For other objects, try to extract text from all properties
		const textParts: string[] = [];
		for (const [_key, value] of Object.entries(obj)) {
			if (typeof value === 'string') {
				textParts.push(value);
			} else if (Array.isArray(value)) {
				const arrayText = extractTextFromJson(value);
				if (arrayText) textParts.push(arrayText);
			} else if (typeof value === 'object' && value !== null) {
				const objectText = extractTextFromJson(value);
				if (objectText) textParts.push(objectText);
			}
		}
		return textParts.join(' ');
	}

	return '';
}
