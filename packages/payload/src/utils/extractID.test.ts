import { describe, it, expect } from 'vitest';
import { extractID } from './extractID';

describe('extractID', () => {
	it('should return the ID when given an object with an ID property', () => {
		const obj = { id: '123', name: 'Test Object' };
		// @ts-expect-error - obj not expected type
		expect(extractID(obj)).toBe('123');
	});

	it('should return the value directly when given a string ID', () => {
		const id = '456';
		expect(extractID(id)).toBe('456');
	});

	it('should return the value directly when given a number ID', () => {
		const id = 789;
		// @ts-expect-error - obj not expected type
		expect(extractID(id)).toBe(789);
	});

	it('should handle objects with numeric IDs', () => {
		const obj = { id: 123, name: 'Test Object' };
		// @ts-expect-error - obj not expected type
		expect(extractID(obj)).toBe(123);
	});

	it('should handle undefined input by returning undefined', () => {
		// @ts-expect-error - obj not expected type
		expect(extractID(undefined)).toBeUndefined();
	});

	it('should handle null input by returning null', () => {
		// @ts-expect-error - obj not expected type
		expect(extractID(null)).toBeNull();
	});
});
