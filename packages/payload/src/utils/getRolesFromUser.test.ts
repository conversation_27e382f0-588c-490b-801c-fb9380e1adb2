import { describe, test, expect } from 'vitest';
import getRolesFromUser from './getRolesFromUser';
import type { Config, User } from '@repo/payload/payload-types';

describe('getRolesFromUser', () => {
	// Test with undefined user
	test('returns empty array when user is undefined', () => {
		expect(getRolesFromUser(undefined)).toEqual([]);
	});

	// Test with user from a different collection
	test('returns empty array when user is not from users collection', () => {
		const nonUserCollectionUser = {
			collection: 'admins',
			role: ['admin', 'editor'],
		} as unknown as Config['user'];

		expect(getRolesFromUser(nonUserCollectionUser)).toEqual([]);
	});

	// Test with valid user with roles
	test('returns roles when user is from users collection and has roles', () => {
		const userWithRoles = {
			collection: 'users',
			role: ['editor', 'author'],
		} as Config['user'];

		expect(getRolesFromUser(userWithRoles)).toEqual(['editor', 'author']);
	});

	// Test with valid user without roles
	test('returns empty array when user is from users collection but has no roles', () => {
		// @ts-expect-error incomplete type const
		const userWithoutRoles = {
			collection: 'users',
			role: undefined,
		} as Config['user'];

		expect(getRolesFromUser(userWithoutRoles)).toEqual([]);
	});

	// Test with valid user with empty roles array
	test('returns empty array when user is from users collection but has empty roles array', () => {
		// @ts-expect-error incomplete type const
		const userWithEmptyRoles = {
			collection: 'users',
			role: [],
		} as Config['user'];

		expect(getRolesFromUser(userWithEmptyRoles)).toEqual([]);
	});
});
