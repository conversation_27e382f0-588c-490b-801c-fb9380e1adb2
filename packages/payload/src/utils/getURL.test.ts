import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { getCurrentDeploymentURL } from './getURL';

describe('getCurrentDeploymentURL', () => {
	let originalEnv: NodeJS.ProcessEnv;

	beforeEach(() => {
		originalEnv = { ...process.env };
		vi.clearAllMocks();
	});

	afterEach(() => {
		process.env = originalEnv;
		vi.restoreAllMocks();
	});

	describe('Local Development Environment', () => {
		it('should return localhost URL for local development', () => {
			vi.stubEnv('NODE_ENV', 'development');
			vi.stubEnv('VERCEL_BRANCH_URL', undefined);

			const result = getCurrentDeploymentURL();
			expect(result).toBe('https://local.weather.com');
		});

		it('should fallback to client-side URL when not in development and no Vercel environment', () => {
			vi.stubEnv('NODE_ENV', 'test');
			vi.stubEnv('VERCEL_ENV', undefined);
			vi.stubEnv('VERCEL_BRANCH_URL', undefined);
			vi.stubEnv('VERCEL_TARGET_ENV', undefined);

			const result = getCurrentDeploymentURL();
			// Should fallback to getClientSideURL which returns localhost:3000 in test environment
			expect(result).toBe('http://localhost:3000');
		});
	});

	describe('Vercel Preview Environment', () => {
		it('should return branch URL for preview deployments', () => {
			vi.stubEnv('VERCEL_ENV', 'preview');
			vi.stubEnv('VERCEL_BRANCH_URL', 'wx-next-zi4cnxl5x.vercel.weather.com');

			const result = getCurrentDeploymentURL();
			expect(result).toBe('https://wx-next-zi4cnxl5x.vercel.weather.com');
		});

		it('should handle branch-based preview URLs', () => {
			vi.stubEnv('VERCEL_ENV', 'preview');
			vi.stubEnv(
				'VERCEL_BRANCH_URL',
				'wx-next-web-git-feat-branch.vercel.weather.com',
			);

			const result = getCurrentDeploymentURL();
			expect(result).toBe(
				'https://wx-next-web-git-feat-branch.vercel.weather.com',
			);
		});

		it('should fallback to client-side URL if VERCEL_BRANCH_URL is missing in preview', () => {
			vi.stubEnv('VERCEL_ENV', 'preview');
			vi.stubEnv('VERCEL_TARGET_ENV', undefined);
			vi.stubEnv('VERCEL_BRANCH_URL', undefined);

			const result = getCurrentDeploymentURL();
			// Should fallback to getClientSideURL which returns localhost:3000 in test environment
			expect(result).toBe('http://localhost:3000');
		});
	});

	describe('Vercel Production Environment', () => {
		it('should return production URL for production environment', () => {
			vi.stubEnv('VERCEL_ENV', 'production');
			vi.stubEnv('VERCEL_TARGET_ENV', undefined);

			const result = getCurrentDeploymentURL();
			expect(result).toBe('https://weather.com');
		});

		it('should handle QAT environment', () => {
			vi.stubEnv('VERCEL_TARGET_ENV', 'qat');

			const result = getCurrentDeploymentURL();
			expect(result).toBe('https://qat.weather.com');
		});

		it('should handle DEV environment', () => {
			vi.stubEnv('VERCEL_TARGET_ENV', 'dev');

			const result = getCurrentDeploymentURL();
			expect(result).toBe('https://dev.weather.com');
		});

		it('should handle STAGING environment', () => {
			vi.stubEnv('VERCEL_TARGET_ENV', 'staging');

			const result = getCurrentDeploymentURL();
			expect(result).toBe('https://stage.weather.com');
		});

		it('should return not-found URL for unknown target environments', () => {
			vi.stubEnv('VERCEL_TARGET_ENV', 'unknown-env');

			const result = getCurrentDeploymentURL();
			expect(result).toBe('https://weather.com/not-found');
		});
	});

	describe('Environment Variable Edge Cases', () => {
		it('should handle empty string environment variables', () => {
			vi.stubEnv('VERCEL_ENV', '');
			vi.stubEnv('VERCEL_BRANCH_URL', '');
			vi.stubEnv('VERCEL_TARGET_ENV', '');

			const result = getCurrentDeploymentURL();
			expect(result).toBe('http://localhost:3000');
		});

		it('should handle whitespace in environment variables', () => {
			vi.stubEnv('VERCEL_ENV', ' production ');
			vi.stubEnv('VERCEL_TARGET_ENV', ' qat ');

			const result = getCurrentDeploymentURL();
			// The function doesn't trim whitespace, so ' qat ' becomes an unknown environment
			expect(result).toBe('https://weather.com/not-found');
		});

		it('should be case sensitive for environment variables', () => {
			vi.stubEnv('VERCEL_ENV', 'PRODUCTION'); // Uppercase
			vi.stubEnv('VERCEL_TARGET_ENV', 'QAT'); // Uppercase

			const result = getCurrentDeploymentURL();
			// The function converts to lowercase, so 'QAT' becomes 'qat' and matches
			expect(result).toBe('https://qat.weather.com');
		});
	});

	describe('OAuth2 Plugin Integration', () => {
		it('should provide consistent URLs for OAuth redirect URIs', () => {
			const testCases = [
				{
					env: { NODE_ENV: 'development', VERCEL_BRANCH_URL: undefined },
					expected: 'https://local.weather.com',
					description: 'local development',
				},
				{
					env: {
						VERCEL_ENV: 'preview',
						VERCEL_BRANCH_URL: 'wx-next-test.vercel.weather.com',
					},
					expected: 'https://wx-next-test.vercel.weather.com',
					description: 'preview deployment',
				},
				{
					env: { VERCEL_ENV: 'production' },
					expected: 'https://weather.com',
					description: 'production',
				},
				{
					env: { VERCEL_TARGET_ENV: 'qat' },
					expected: 'https://qat.weather.com',
					description: 'QAT environment',
				},
			];

			testCases.forEach(({ env, expected, description }) => {
				// Clear all environment variables first
				vi.stubEnv('NODE_ENV', undefined);
				vi.stubEnv('VERCEL_ENV', undefined);
				vi.stubEnv('VERCEL_BRANCH_URL', undefined);
				vi.stubEnv('VERCEL_TARGET_ENV', undefined);

				// Set up environment
				Object.entries(env).forEach(([key, value]) => {
					vi.stubEnv(key, value);
				});

				const baseUrl = getCurrentDeploymentURL();
				expect(baseUrl).toBe(expected);

				// Verify OAuth endpoints would be constructed correctly
				const loginEndpoint = `${baseUrl}/api/users/okta-oauth/login`;
				const logoutEndpoint = `${baseUrl}/api/users/okta-oauth/logout`;

				expect(loginEndpoint).toBe(`${expected}/api/users/okta-oauth/login`);
				expect(logoutEndpoint).toBe(`${expected}/api/users/okta-oauth/logout`);
			});
		});

		it('should support all required OAuth2 redirect URI patterns', () => {
			// Test that our function can generate the base URLs for these patterns
			const testEnvironments = [
				{
					env: { NODE_ENV: 'development', VERCEL_BRANCH_URL: undefined },
					expectedBase: 'https://local.weather.com',
				},
				{
					env: { VERCEL_ENV: 'production' },
					expectedBase: 'https://weather.com',
				},
				{
					env: { VERCEL_TARGET_ENV: 'qat' },
					expectedBase: 'https://qat.weather.com',
				},
				{
					env: { VERCEL_TARGET_ENV: 'dev' },
					expectedBase: 'https://dev.weather.com',
				},
				{
					env: { VERCEL_TARGET_ENV: 'staging' },
					expectedBase: 'https://stage.weather.com',
				},
				{
					env: {
						VERCEL_ENV: 'preview',
						VERCEL_BRANCH_URL: 'test.vercel.weather.com',
					},
					expectedBase: 'https://test.vercel.weather.com',
				},
			];

			testEnvironments.forEach(({ env, expectedBase }) => {
				// Clear all environment variables first
				vi.stubEnv('NODE_ENV', undefined);
				vi.stubEnv('VERCEL_ENV', undefined);
				vi.stubEnv('VERCEL_BRANCH_URL', undefined);
				vi.stubEnv('VERCEL_TARGET_ENV', undefined);

				// Set up environment
				Object.entries(env).forEach(([key, value]) => {
					vi.stubEnv(key, value);
				});

				const result = getCurrentDeploymentURL();
				expect(result).toBe(expectedBase);

				// Verify the OAuth endpoint construction
				const oauthEndpoint = `${result}/api/users/okta-oauth/login`;
				expect(oauthEndpoint).toMatch(
					/^https?:\/\/[^/]+\/api\/users\/okta-oauth\/login$/,
				);
			});
		});
	});

	describe('Function Behavior and Performance', () => {
		it('should be deterministic for the same environment', () => {
			vi.stubEnv('VERCEL_TARGET_ENV', 'qat');

			const result1 = getCurrentDeploymentURL();
			const result2 = getCurrentDeploymentURL();
			const result3 = getCurrentDeploymentURL();

			expect(result1).toBe(result2);
			expect(result2).toBe(result3);
			expect(result1).toBe('https://qat.weather.com');
		});

		it('should respond to environment changes', () => {
			// First call with one environment
			vi.stubEnv('VERCEL_TARGET_ENV', 'qat');
			const result1 = getCurrentDeploymentURL();
			expect(result1).toBe('https://qat.weather.com');

			// Change environment
			vi.stubEnv('VERCEL_TARGET_ENV', 'dev');
			const result2 = getCurrentDeploymentURL();
			expect(result2).toBe('https://dev.weather.com');

			// Verify they're different
			expect(result1).not.toBe(result2);
		});

		it('should handle rapid successive calls', () => {
			vi.stubEnv('VERCEL_ENV', 'preview');
			vi.stubEnv('VERCEL_BRANCH_URL', 'rapid-test.vercel.weather.com');

			const results = Array.from({ length: 10 }, () =>
				getCurrentDeploymentURL(),
			);

			// All results should be identical
			results.forEach((result) => {
				expect(result).toBe('https://rapid-test.vercel.weather.com');
			});
		});
	});
});
