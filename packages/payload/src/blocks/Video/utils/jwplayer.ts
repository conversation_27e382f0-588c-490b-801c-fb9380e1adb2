import { Playlist, PlaylistItem } from '@repo/jw-player/types/playlist';

export const buildJwPlaylist = (
	playlist: unknown,
	video: PlaylistItem,
): Playlist => {
	if (!video?.file) return [];

	// TODO: Update type check once we support playlists as a pointer to a collection
	// TODO: If it is a pointer, generate jwplaylist from the collection
	if (typeof playlist === 'string') {
		if (!video?.file) return []; // if there is no valid video
		return [video] as Playlist;
	}

	// TODO: Remove this once we remove dsx support
	// TODO: Once we have collection pointer support,
	// TODO: this only gets used if playlist is not a pointer
	// Cast playlist to array type
	const existingPlaylist = (
		Array.isArray(playlist) ? playlist : []
	) as Playlist;

	// if there is no valid video
	if (!video?.file) return existingPlaylist;

	// Check if video is already first in playlist
	if (
		existingPlaylist.length > 0 &&
		existingPlaylist[0]?.file === video?.file
	) {
		return existingPlaylist;
	}

	// Create new playlist with video as first item
	return [
		video,
		...existingPlaylist.filter((item) => item.file !== video.file),
	];
};
