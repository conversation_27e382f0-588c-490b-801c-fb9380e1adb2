import { describe, test, expect, vi } from 'vitest';

// Mock the entire module
vi.mock('./thumbnail', () => ({
	PLACEHOLDER_IMAGE: '/mock-placeholder-video-thumbnail.jpg',
	getVideoThumbnail: (videoUrl: string): string => {
		if (!videoUrl) return '/mock-placeholder-video-thumbnail.jpg';

		// Extract video ID from common JW Player URL patterns
		const patterns = [
			/(?:cdn\.jwplayer\.com\/(?:videos|manifests|previews)\/)([a-zA-Z0-9]+)(?:-[a-zA-Z0-9]+)?/,
			/(?:content\.jwplatform\.com\/(?:videos|manifests)\/)([a-zA-Z0-9]+)(?:-[a-zA-Z0-9]+)?/,
			/(?:players\.jwplayer\.com\/preview\/)([a-zA-Z0-9]+)(?:-[a-zA-Z0-9]+)?/,
		];

		for (const pattern of patterns) {
			const match = videoUrl.match(pattern);
			if (match && match[1]) {
				return `https://cdn.jwplayer.com/v2/media/${match[1]}/poster.jpg`;
			}
		}

		return '/mock-placeholder-video-thumbnail.jpg';
	},
}));

import { getVideoThumbnail, PLACEHOLDER_IMAGE } from './thumbnail';

describe('getVideoThumbnail', () => {
	test('returns placeholder image when no video URL is provided', () => {
		expect(getVideoThumbnail('')).toBe(PLACEHOLDER_IMAGE);
		expect(getVideoThumbnail(undefined as unknown as string)).toBe(
			PLACEHOLDER_IMAGE,
		);
	});

	test('extracts video ID from JW Player CDN URL', () => {
		const urls = [
			'https://cdn.jwplayer.com/videos/abc123',
			'https://cdn.jwplayer.com/manifests/abc123',
			'https://cdn.jwplayer.com/previews/abc123',
			'https://cdn.jwplayer.com/videos/abc123-someHash',
		];

		urls.forEach((url) => {
			expect(getVideoThumbnail(url)).toBe(
				'https://cdn.jwplayer.com/v2/media/abc123/poster.jpg',
			);
		});
	});

	test('extracts video ID from JW Platform content URL', () => {
		const urls = [
			'https://content.jwplatform.com/videos/xyz789',
			'https://content.jwplatform.com/manifests/xyz789',
			'https://content.jwplatform.com/videos/xyz789-someHash',
		];

		urls.forEach((url) => {
			expect(getVideoThumbnail(url)).toBe(
				'https://cdn.jwplayer.com/v2/media/xyz789/poster.jpg',
			);
		});
	});

	test('extracts video ID from JW Player preview URL', () => {
		const urls = [
			'https://players.jwplayer.com/preview/def456',
			'https://players.jwplayer.com/preview/def456-someHash',
		];

		urls.forEach((url) => {
			expect(getVideoThumbnail(url)).toBe(
				'https://cdn.jwplayer.com/v2/media/def456/poster.jpg',
			);
		});
	});

	test('returns placeholder image for unsupported URL patterns', () => {
		const urls = [
			'https://example.com/video/123',
			'https://jwplayer.com/something/abc',
			'https://cdn.jwplayer.com/invalid/format',
			'not-a-url',
		];

		urls.forEach((url) => {
			expect(getVideoThumbnail(url)).toBe(PLACEHOLDER_IMAGE);
		});
	});

	test('handles complex URL patterns with query parameters', () => {
		const urls = [
			'https://cdn.jwplayer.com/videos/abc123?foo=bar',
			'https://content.jwplatform.com/manifests/xyz789?token=123',
			'https://players.jwplayer.com/preview/def456#fragment',
		];

		urls.forEach((url) => {
			const videoId = url.split('/').pop()?.split(/[?#]/)[0]?.split('-')[0];
			expect(getVideoThumbnail(url)).toBe(
				`https://cdn.jwplayer.com/v2/media/${videoId}/poster.jpg`,
			);
		});
	});

	test('handles URLs with hyphens in video ID', () => {
		const url = 'https://cdn.jwplayer.com/videos/abc123-def456';
		expect(getVideoThumbnail(url)).toBe(
			'https://cdn.jwplayer.com/v2/media/abc123/poster.jpg',
		);
	});
});
