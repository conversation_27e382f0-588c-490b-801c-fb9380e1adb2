import { describe, test, expect } from 'vitest';
import { buildJwPlaylist } from './jwplayer';
import type { PlaylistItem, Playlist } from '@repo/jw-player/types/playlist';

describe('buildJwPlaylist', () => {
	const mockVideo: PlaylistItem = {
		file: 'test.mp4',
		title: 'Test Video',
		description: 'Test Description',
		image: 'thumbnail.jpg',
	};

	test('returns array with single video when playlist is string', () => {
		const result = buildJwPlaylist('some-string', mockVideo);
		expect(result).toEqual([mockVideo]);
	});

	test('returns empty array when an empty video object is provided', () => {
		const result = buildJwPlaylist([], {} as PlaylistItem);
		expect(result).toEqual([]);
	});

	test('returns existing playlist when video is already first item', () => {
		const existingPlaylist: Playlist = [
			mockVideo,
			{ file: 'second.mp4', title: 'Second Video' },
		];

		const result = buildJwPlaylist(existingPlaylist, mockVideo);
		expect(result).toBe(existingPlaylist);
	});

	test('adds video to start of playlist and removes duplicates', () => {
		const existingPlaylist: Playlist = [
			{ file: 'first.mp4', title: 'First Video' },
			{ file: 'test.mp4', title: 'Duplicate Video' },
			{ file: 'second.mp4', title: 'Second Video' },
		];

		const result = buildJwPlaylist(existingPlaylist, mockVideo);

		expect(result).toHaveLength(3);
		expect(result[0]).toBe(mockVideo);
		expect(result.filter((item) => item.file === mockVideo.file)).toHaveLength(
			1,
		);
	});

	test('handles non-array playlist input', () => {
		const result = buildJwPlaylist({}, mockVideo);
		expect(result).toEqual([mockVideo]);
	});
});
