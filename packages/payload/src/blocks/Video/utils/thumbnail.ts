export const PLACEHOLDER_IMAGE = '/placeholder-video-thumbnail.jpg';

/**
 * Attempts to get a thumbnail image for a video URL
 */
export const getVideoThumbnail = (videoUrl: string): string => {
	if (!videoUrl) return PLACEHOLDER_IMAGE;

	// Extract video ID from common JW Player URL patterns
	const patterns = [
		/(?:cdn\.jwplayer\.com\/(?:videos|manifests|previews)\/)([a-zA-Z0-9]+)(?:-[a-zA-Z0-9]+)?/,
		/(?:content\.jwplatform\.com\/(?:videos|manifests)\/)([a-zA-Z0-9]+)(?:-[a-zA-Z0-9]+)?/,
		/(?:players\.jwplayer\.com\/preview\/)([a-zA-Z0-9]+)(?:-[a-zA-Z0-9]+)?/,
	];

	for (const pattern of patterns) {
		const match = videoUrl.match(pattern);
		if (match && match[1]) {
			return `https://cdn.jwplayer.com/v2/media/${match[1]}/poster.jpg`;
		}
	}

	return PLACEHOLDER_IMAGE;
};
