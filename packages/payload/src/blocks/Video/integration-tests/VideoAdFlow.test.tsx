import React from 'react';
import { describe, test, expect, vi, beforeEach, afterEach } from 'vitest';
import { render } from '@testing-library/react';
import { VideoBlock } from '../VideoBlock';
import { JWPlayer } from '@repo/jw-player/JWPlayer';
import * as adUtils from '../ads/ad-utils';
import type { VideoBlockConfig } from '@repo/payload/payload-types';

// Mock server-only modules
vi.mock('server-only', () => ({}));

// Mock any other server components or modules that might be using server-only
vi.mock('@repo/payload/components/Helios/utils/iabConfig', () => ({
	getIabConfig: vi.fn(() => ({
		iabSiteCategories: {
			v1: ['IAB15-10_Weather'],
			v3: ['390_Weather'],
		},
		additionalConfig: {},
		iabCategoriesByPageCode: {},
	})),
}));

// Import JWPlayerInstance type
import type { JWPlayerInstance } from '@repo/jw-player/types/player';
import { User } from '@repo/user/hooks/useUser';

// Define event callback types to match the actual implementation
type EventCallback = ({
	event,
	player,
}: {
	event: unknown;
	player: JWPlayerInstance;
}) => void;

// Define types for JWPlayer mock
interface JWPlayerEvent {
	eventName: string;
	callback: EventCallback;
}

// Define types for JWPlayer props
interface JWPlayerProps {
	playlist: unknown;
	events: JWPlayerEvent[];
}

// Mock JWPlayer events storage
let mockJWPlayerEvents: JWPlayerEvent[] = [];

// Shared isFirstPlayRef for tests
const mockIsFirstPlayRef = { current: true };

// Mock user for tests
const mockUser: User = {
	userID: '',
	isUserLoggedIn: false,
	subscriptionTier: 0,
	isUserPremium: false,
};

// Mock the useUser hook
vi.mock('@repo/user/hooks/useUser', () => ({
	useUser: vi.fn(() => ({ user: mockUser })),
}));

// Mock the useMobileMedia hook
vi.mock('@repo/ui/hooks/useMobileMedia', () => ({
	useMobileMedia: vi.fn(() => false),
}));

// Mock the JWPlayer component (only mock the component, not the logic)
vi.mock('@repo/jw-player/JWPlayer', () => {
	const mockJWPlayer = vi.fn(({ playlist, events }: JWPlayerProps) => {
		// Store the events for testing
		mockJWPlayerEvents = events;

		return (
			<div
				data-testid="mock-jwplayer"
				data-playlist={JSON.stringify(playlist)}
			/>
		);
	});

	return { JWPlayer: mockJWPlayer };
});

// Mock the DebugCollector component
vi.mock(
	'@repo/payload/components/FrontendAdminHeader/collectors/DebugCollector',
	() => ({
		DebugCollector: vi.fn(({ children }: { children: React.ReactNode }) => (
			<div data-testid="mock-debug-collector">{children}</div>
		)),
	}),
);

// Mock the thumbnail utility
vi.mock('../utils/thumbnail', () => ({
	getVideoThumbnail: vi.fn((file: string) => `${file}-thumbnail`),
}));

// Mock the jwplayer utility
vi.mock('../utils/jwplayer', () => ({
	buildJwPlaylist: vi.fn((playlist: unknown[], video: unknown) => {
		if (playlist && playlist.length > 0) {
			return [...playlist, video];
		}
		return [video];
	}),
}));

// Mock the debugLogger
const mockDebugLogger = vi.hoisted(() => ({
	error: vi.fn(),
	lifecycle: vi.fn(),
}));

vi.mock(
	'@repo/payload/components/FrontendAdminHeader/utils/debugLogger',
	() => ({
		debugLogger: mockDebugLogger,
	}),
);

// Mock Helios (external dependency that can't run in tests)
const mockHelios = {
	emit: vi.fn(),
	video: {
		requestVideoBids: vi.fn().mockResolvedValue(['bid1', 'bid2']),
	},
	modules: {
		iabc: {
			update: vi.fn(),
		},
		videoTag: {
			createAdTag: vi.fn().mockResolvedValue('https://example.com/ad-tag'),
		},
	},
};

describe('Video Ad Flow Integration', () => {
	// Mock props for VideoBlock
	const mockProps: VideoBlockConfig = {
		blockType: 'Video',
		file: 'https://example.com/video.mp4',
		image: 'https://example.com/thumbnail.jpg',
		title: 'Test Video with Ads',
		description: 'A video with ads for testing',
		custom: {
			ctx: {
				jwplayer: 'test-player-id',
				adzone: 'weather/video/ads',
				iab: {
					v1: ['IAB15-10_Weather'],
					v2: ['390_Weather'],
				},
			},
		},
	};

	beforeEach(() => {
		vi.clearAllMocks();
		mockJWPlayerEvents = [];
		mockIsFirstPlayRef.current = true;

		// Setup global Helios object
		Object.defineProperty(window, '__Helios', {
			value: mockHelios,
			writable: true,
			configurable: true,
		});
	});

	afterEach(() => {
		vi.restoreAllMocks();

		// Clean up global Helios object
		if ('__Helios' in window) {
			delete (window as { __Helios?: unknown }).__Helios;
		}
	});

	test('VideoBlock correctly initializes JWPlayer with ad configuration', () => {
		render(<VideoBlock {...mockProps} />);

		// Verify JWPlayer was called with the correct props
		expect(JWPlayer).toHaveBeenCalled();

		// Get the props passed to JWPlayer
		const mockCalls = vi.mocked(JWPlayer).mock.calls;
		expect(mockCalls.length).toBeGreaterThan(0);

		// Type assertion for the props
		const jwPlayerProps = mockCalls[0]?.[0] as JWPlayerProps;

		// Verify playlist contains the video with custom fields
		expect(jwPlayerProps.playlist).toBeDefined();

		// Verify playlist is an array
		expect(Array.isArray(jwPlayerProps.playlist)).toBe(true);

		// Directly cast the playlist to a known type with a non-empty array
		const playlist = jwPlayerProps.playlist as [
			{
				file: string;
				title: string;
				custom: {
					ctx: {
						jwplayer: string;
						adzone: string;
					};
				};
			},
		];

		// Verify playlist has items
		expect(playlist.length).toBeGreaterThan(0);

		// Access the first item directly - TypeScript now knows it exists
		expect(playlist[0].file).toBe('https://example.com/video.mp4');
		expect(playlist[0].title).toBe('Test Video with Ads');
		expect(playlist[0].custom.ctx.jwplayer).toBe('test-player-id');
		expect(playlist[0].custom.ctx.adzone).toBe('weather/video/ads');

		// Verify events are passed
		expect(jwPlayerProps.events).toBeDefined();

		// Verify ad-related events are included
		const eventNames = jwPlayerProps.events.map((e) => e.eventName);
		expect(eventNames).toContain('beforePlay');
		expect(eventNames).toContain('playlistItem');
		expect(eventNames).toContain('adComplete');
		expect(eventNames).toContain('adError');
	});

	test('Desktop environment uses desktop ad unit path', async () => {
		render(<VideoBlock {...mockProps} />);

		// Get the events passed to JWPlayer
		const events = mockJWPlayerEvents;

		// Find the beforePlay event
		const beforePlayEvent = events.find((e) => e.eventName === 'beforePlay');
		expect(beforePlayEvent).toBeDefined();

		// Create a mock player with all required methods
		const mockPlayer: JWPlayerInstance = {
			setup: vi.fn(),
			on: vi.fn(),
			off: vi.fn(),
			remove: vi.fn(),
			play: vi.fn(),
			pause: vi.fn(),
			stop: vi.fn(),
			playAd: vi.fn(),
			setConfig: vi.fn(),
			getConfig: vi.fn(),
			getPosition: vi.fn(),
			getAdBlock: vi.fn(),
			getDuration: vi.fn(),
			getMute: vi.fn(),
			getPlaylistItem: vi.fn().mockReturnValue({
				custom: {
					ctx: {
						jwplayer: 'test-player-id',
						adzone: 'weather/video/ads',
					},
				},
			}),
			getPlaylistIndex: vi.fn().mockReturnValue(0),
		};

		// Call the beforePlay callback
		beforePlayEvent!.callback({
			event: {},
			player: mockPlayer,
		});

		// Verify player.stop was called (to pause for ad)
		expect(mockPlayer.stop).toHaveBeenCalled();

		// Verify the ad request uses desktop ad unit path
		expect(mockHelios.emit).toHaveBeenCalledWith(
			'UPDATE_AD_UNIT_PATH',
			'/7646/web_weather_us/weather/video/ads',
		);
	});

	test('Mobile environment uses mobile ad unit path', async () => {
		// Mock useMobileMedia to return true for mobile environment
		const { useMobileMedia } = await import('@repo/ui/hooks/useMobileMedia');
		vi.mocked(useMobileMedia).mockReturnValue(true);

		// Re-render the component with mobile environment
		render(<VideoBlock {...mockProps} />);

		// Get the events passed to JWPlayer
		const events = mockJWPlayerEvents;

		// Find the beforePlay event
		const beforePlayEvent = events.find((e) => e.eventName === 'beforePlay');
		expect(beforePlayEvent).toBeDefined();

		// Create a mock player with all required methods
		const mockPlayer: JWPlayerInstance = {
			setup: vi.fn(),
			on: vi.fn(),
			off: vi.fn(),
			remove: vi.fn(),
			play: vi.fn(),
			pause: vi.fn(),
			stop: vi.fn(),
			playAd: vi.fn(),
			setConfig: vi.fn(),
			getConfig: vi.fn(),
			getPosition: vi.fn(),
			getAdBlock: vi.fn(),
			getDuration: vi.fn(),
			getMute: vi.fn(),
			getPlaylistItem: vi.fn().mockReturnValue({
				custom: {
					ctx: {
						jwplayer: 'test-player-id',
						adzone: 'weather/video/ads',
					},
				},
			}),
			getPlaylistIndex: vi.fn().mockReturnValue(0),
		};

		// Call the beforePlay callback
		beforePlayEvent!.callback({
			event: {},
			player: mockPlayer,
		});

		// Verify player.stop was called (to pause for ad)
		expect(mockPlayer.stop).toHaveBeenCalled();

		// Verify the ad request uses mobile ad unit path
		expect(mockHelios.emit).toHaveBeenCalledWith(
			'UPDATE_AD_UNIT_PATH',
			'/7646/mobile_smart_us/weather/video/ads',
		);
	});

	test('Custom fields from wxNode are properly passed through to ad system', async () => {
		// Create a mock player with all required methods
		const mockPlayer: JWPlayerInstance = {
			setup: vi.fn(),
			on: vi.fn(),
			off: vi.fn(),
			remove: vi.fn(),
			play: vi.fn(),
			pause: vi.fn(),
			stop: vi.fn(),
			playAd: vi.fn(),
			setConfig: vi.fn(),
			getConfig: vi.fn(),
			getPosition: vi.fn(),
			getAdBlock: vi.fn(),
			getDuration: vi.fn(),
			getMute: vi.fn(),
			getPlaylistItem: vi.fn(),
			getPlaylistIndex: vi.fn(),
		};

		// Create a mock context with the custom fields from wxNode
		const ctx = {
			jwplayer: 'test-player-id',
			adzone: 'weather/video/ads',
			iab: {
				v1: ['IAB15-10_Weather'],
				v2: ['390_Weather'],
			},
		};

		// Call prerollAd directly with the mock player and context (desktop by default)
		await adUtils.prerollAd({
			player: mockPlayer,
			user: mockUser,
			ctx,
		});

		// Verify Helios emit was called with the correct desktop ad unit path
		expect(mockHelios.emit).toHaveBeenCalledWith(
			'UPDATE_AD_UNIT_PATH',
			'/7646/web_weather_us/weather/video/ads',
		);

		// Verify IAB categories were updated
		expect(mockHelios.modules.iabc.update).toHaveBeenCalledWith({
			page: {
				v1: ['IAB15-10_Weather'],
				v2: ['390_Weather'],
			},
			site: {
				v1: ['IAB15-10_Weather'],
				v3: ['390_Weather'],
			},
		});

		// Verify bids were requested
		expect(mockHelios.video.requestVideoBids).toHaveBeenCalled();

		// Verify ad tag was created
		expect(mockHelios.modules.videoTag.createAdTag).toHaveBeenCalled();
	});
});
