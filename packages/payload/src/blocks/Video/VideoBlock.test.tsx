import React, { JSX } from 'react';
import { describe, test, expect, vi, beforeEach } from 'vitest';
import { render } from '@testing-library/react';
import { VideoBlock } from './VideoBlock';
import { JWPlayer } from '@repo/jw-player/JWPlayer';
import { adEvents } from './ads/ad-events';
import { metricEvents } from './metrics/metric-events';
import { DebugCollector } from '@repo/payload/components/FrontendAdminHeader/collectors/DebugCollector';
import type { CallbackProps, VideoEvent } from '@repo/jw-player/types/events';
import type { PlaylistItem } from '@repo/jw-player/types/playlist';
import type { VideoBlockConfig } from '@repo/payload/payload-types';
import type {
	OptionalSetupOptions,
	SetupParams,
} from '@repo/jw-player/types/setup';
import type { JWPlayerInstance } from '@repo/jw-player/types/player';

// Mock the useUser hook
vi.mock('@repo/user/hooks/useUser', () => ({
	useUser: vi.fn(() => ({ user: { isUserPremium: false } })),
}));

// Shared isFirstPlayRef for tests
const mockIsFirstPlayRef = { current: true };

// Define a type for the mocked JWPlayer
interface MockedJWPlayer {
	mockEvents?: VideoEvent[];
	(props: {
		events: VideoEvent[];
		playlist: PlaylistItem[];
		options?: OptionalSetupOptions;
		setupParams?: SetupParams;
	}): JSX.Element;
}

// Mock the JWPlayer component
vi.mock('@repo/jw-player/JWPlayer', () => ({
	JWPlayer: vi.fn(({ events }) => {
		// Store the events for testing
		(JWPlayer as MockedJWPlayer).mockEvents = events;
		return <div data-testid="mock-jwplayer" />;
	}),
}));

// Mock the DebugCollector component
vi.mock(
	'@repo/payload/components/FrontendAdminHeader/collectors/DebugCollector',
	() => {
		return {
			DebugCollector: vi.fn(({ children }) => (
				<div data-testid="mock-debug-collector">{children}</div>
			)),
		};
	},
);

// Mock the hasLength utility
vi.mock('@repo/utils/hasLength', () => ({
	hasLength: vi.fn((arr) => (Array.isArray(arr) ? arr.length : arr ? 1 : 0)),
}));

// Mock the utility functions
vi.mock('./utils/thumbnail', () => ({
	getVideoThumbnail: vi.fn((file) => `${file}-thumbnail`),
}));

vi.mock('./utils/jwplayer', () => ({
	buildJwPlaylist: vi.fn((playlist, video) => [video]),
}));

// Mock the ad-events module
vi.mock('./ads/ad-events', () => ({
	adEvents: [
		{
			eventName: 'beforePlay',
			callback: (context: CallbackProps) => {
				return ({ player }: CallbackProps) => {
					const { isFirstPlayRef } = context;
					if (!isFirstPlayRef?.current) return;

					// We get the playlist item but don't use it in this mock
					player.getPlaylistItem();

					player.stop();
					// Ensure isFirstPlayRef.current is set to false
					if (isFirstPlayRef) {
						isFirstPlayRef.current = false;
					}
				};
			},
		},
	],
}));

// Mock the metric-events module
vi.mock('./metrics/metric-events', () => ({
	metricEvents: [
		{
			eventName: 'play',
			callback: () => {
				return ({ player }: CallbackProps) => {
					// Mock implementation
					console.log(`Video played: ${player.getPlaylistItem()?.title}`);
				};
			},
		},
	],
}));

describe('VideoBlock', () => {
	const mockProps: VideoBlockConfig = {
		blockType: 'Video',
		file: 'test-video.mp4',
		image: 'test-image.jpg',
		title: 'Test Video',
		description: 'Test Description',
		custom: { ctx: { someContext: 'value' } },
	};

	beforeEach(() => {
		vi.clearAllMocks();
		mockIsFirstPlayRef.current = true;
		(JWPlayer as MockedJWPlayer).mockEvents = [];
	});

	test('should pass events to JWPlayer component', () => {
		// Render VideoBlock with mock props
		render(<VideoBlock {...mockProps} />);

		// Verify JWPlayer was called with events prop
		expect(JWPlayer).toHaveBeenCalledTimes(1);

		const jwPlayerProps = (JWPlayer as ReturnType<typeof vi.fn>).mock
			.calls[0]?.[0];

		// Verify playlist is passed
		expect(jwPlayerProps?.playlist).toBeDefined();

		// Verify events are passed
		expect(jwPlayerProps?.events).toBeDefined();

		// Verify events include all ad events
		adEvents.forEach((adEvent) => {
			const matchingEvent = jwPlayerProps?.events.find(
				(event: VideoEvent) => event.eventName === adEvent.eventName,
			);
			expect(matchingEvent).toBeDefined();
		});

		// Verify events include all metric events
		metricEvents.forEach((metricEvent) => {
			const matchingEvent = jwPlayerProps?.events.find(
				(event: VideoEvent) => event.eventName === metricEvent.eventName,
			);
			expect(matchingEvent).toBeDefined();
		});
	});

	test('should reset isFirstPlayRef when playlist changes', () => {
		// First render
		const { rerender } = render(<VideoBlock {...mockProps} />);

		// Get the events from the mock
		const events = (JWPlayer as MockedJWPlayer).mockEvents || [];

		// Find the beforePlay event handler
		const beforePlayEvent = events.find(
			(event: VideoEvent) => event.eventName === 'beforePlay',
		);
		expect(beforePlayEvent).toBeDefined();

		if (!beforePlayEvent) return; // Early return for TypeScript

		// Reset isFirstPlayRef to ensure it's true
		mockIsFirstPlayRef.current = true;

		// Create a mock player that satisfies the minimum requirements
		const mockPlayer: JWPlayerInstance = {
			stop: vi.fn(),
			play: vi.fn(),
			getPlaylistItem: vi.fn().mockReturnValue({
				custom: { ctx: { someContext: 'value' } },
			}),
			getDuration: vi.fn(),
			// Add required methods from JWPlayerInstance
			on: vi.fn(),
			off: vi.fn(),
			remove: vi.fn(),
			setup: vi.fn(),
			setConfig: vi.fn(),
			pause: vi.fn(),
			playAd: vi.fn(),
			getConfig: vi.fn(),
			getPosition: vi.fn(),
			getAdBlock: vi.fn(),
			getMute: vi.fn(),
			getPlaylistIndex: function (): number {
				throw new Error('Function not implemented.');
			},
		};

		// Call the callback with the new structure
		beforePlayEvent.callback({
			event: { type: 'beforePlay' },
			player: mockPlayer as JWPlayerInstance,
			isFirstPlayRef: mockIsFirstPlayRef,
		} as unknown as CallbackProps);

		// Rerender with different playlist to trigger useEffect
		rerender(<VideoBlock {...mockProps} playlist="another-playlist" />);

		// isFirstPlayRef should be reset to true by the useEffect
		expect(mockIsFirstPlayRef.current).toBe(true);
	});

	test('should not render when file is not provided', () => {
		// Render VideoBlock without file prop
		const { container } = render(<VideoBlock {...mockProps} file="" />);

		// Verify component returns null
		expect(container.firstChild).toBeNull();

		// Verify JWPlayer was not called
		expect(JWPlayer).not.toHaveBeenCalled();
	});

	// New tests for tracks handling
	test('should handle single track object correctly', () => {
		const singleTrack = {
			file: 'captions.vtt',
			kind: 'captions' as const,
			label: 'English',
		};
		render(<VideoBlock {...mockProps} tracks={singleTrack} />);

		// Get JWPlayer props
		const jwPlayerProps = (JWPlayer as ReturnType<typeof vi.fn>).mock
			.calls[0]?.[0];

		// Verify playlist item has the track
		expect(jwPlayerProps?.playlist[0].tracks).toHaveLength(1);
		expect(jwPlayerProps?.playlist[0].tracks[0]).toMatchObject({
			file: 'captions.vtt',
			kind: 'captions',
			label: 'English',
		});
	});

	test('should convert tracks to array internally', () => {
		// VideoBlockConfig.tracks is a single object, but VideoBlock converts it to an array
		const singleTrack = {
			file: 'captions.vtt',
			kind: 'captions' as const,
			label: 'English',
		};

		render(<VideoBlock {...mockProps} tracks={singleTrack} />);

		// Get JWPlayer props
		const jwPlayerProps = (JWPlayer as ReturnType<typeof vi.fn>).mock
			.calls[0]?.[0];

		// Verify playlist item has tracks as an array
		expect(Array.isArray(jwPlayerProps?.playlist[0].tracks)).toBe(true);
		expect(jwPlayerProps?.playlist[0].tracks).toHaveLength(1);
	});

	test('should handle undefined tracks gracefully', () => {
		// Test with undefined tracks
		render(<VideoBlock {...mockProps} tracks={undefined} />);

		const jwPlayerProps = (JWPlayer as ReturnType<typeof vi.fn>).mock
			.calls[0]?.[0];
		expect(jwPlayerProps?.playlist[0].tracks).toEqual([]);
	});

	// Tests for video object memoization
	test('should memoize video object when props remain the same', () => {
		// First render
		const { rerender } = render(<VideoBlock {...mockProps} />);

		// Get first video object
		const firstCall = (JWPlayer as ReturnType<typeof vi.fn>).mock.calls[0]?.[0];
		const firstVideoObject = firstCall?.playlist[0];

		// Re-render with same props
		rerender(<VideoBlock {...mockProps} />);

		// Get second video object
		const secondCall = (JWPlayer as ReturnType<typeof vi.fn>).mock
			.calls[1]?.[0];
		const secondVideoObject = secondCall?.playlist[0];

		// Should be the same object (memoized)
		expect(secondVideoObject).toBe(firstVideoObject);
	});

	test('should create new video object when props change', () => {
		// First render
		const { rerender } = render(<VideoBlock {...mockProps} />);

		// Get first video object
		const firstCall = (JWPlayer as ReturnType<typeof vi.fn>).mock.calls[0]?.[0];
		const firstVideoObject = firstCall?.playlist[0];

		// Re-render with different props
		const updatedProps = {
			...mockProps,
			title: 'Updated Title',
		};

		rerender(<VideoBlock {...updatedProps} />);

		// Get second video object
		const secondCall = (JWPlayer as ReturnType<typeof vi.fn>).mock
			.calls[1]?.[0];
		const secondVideoObject = secondCall?.playlist[0];

		// Should be different objects
		expect(secondVideoObject).not.toBe(firstVideoObject);
		expect(secondVideoObject.title).toBe('Updated Title');
	});

	// Tests for DebugCollector integration
	test('should pass correct data to DebugCollector', () => {
		// Access the mocked DebugCollector
		const mockDebugCollector = vi.mocked(DebugCollector);

		const props = {
			...mockProps,
			tracks: { file: 'captions.vtt', kind: 'captions' as const },
		};

		render(<VideoBlock {...props} />);

		// Verify DebugCollector was called
		expect(mockDebugCollector).toHaveBeenCalledTimes(1);

		const debugCollectorProps = mockDebugCollector.mock.calls[0]?.[0];

		// Ensure debugCollectorProps is defined
		expect(debugCollectorProps).toBeDefined();

		if (debugCollectorProps) {
			// Check component name
			expect(debugCollectorProps.componentName).toBe('VideoBlock');

			// Ensure data is defined
			expect(debugCollectorProps.data).toBeDefined();

			if (debugCollectorProps.data) {
				// Check props data
				expect(debugCollectorProps.data.props).toMatchObject({
					file: 'test-video.mp4',
					image: 'test-image.jpg',
					title: 'Test Video',
					description: 'Test Description',
					tracksCount: 1,
					hasPlaylist: false,
					custom: { ctx: { someContext: 'value' } },
				});

				// Check state data
				expect(debugCollectorProps.data.state).toMatchObject({
					isFirstPlay: true,
					playlistItems: expect.any(Number),
					events: expect.arrayContaining(['beforePlay']),
				});
			}
		}
	});

	// Tests for custom properties handling
	test('should pass custom properties through to video object', () => {
		const customProps = {
			ctx: {
				someContext: 'value',
				analytics: {
					contentId: '12345',
					category: 'weather',
				},
			},
			advertising: {
				client: 'vast',
				tag: 'https://example.com/ad',
			},
		};

		render(<VideoBlock {...mockProps} custom={customProps} />);

		// Get JWPlayer props
		const jwPlayerProps = (JWPlayer as ReturnType<typeof vi.fn>).mock
			.calls[0]?.[0];

		// Verify custom properties are passed through
		expect(jwPlayerProps?.playlist[0].custom).toEqual(customProps);
	});

	test('should handle undefined custom properties gracefully', () => {
		const propsWithoutCustom = { ...mockProps };
		delete propsWithoutCustom.custom;

		render(<VideoBlock {...propsWithoutCustom} />);

		// Get JWPlayer props
		const jwPlayerProps = (JWPlayer as ReturnType<typeof vi.fn>).mock
			.calls[0]?.[0];

		// Verify custom is an empty object by default
		expect(jwPlayerProps?.playlist[0].custom).toEqual({});
	});
});
