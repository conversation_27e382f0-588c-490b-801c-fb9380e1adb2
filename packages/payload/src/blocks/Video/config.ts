import type {
	Block,
	TextField,
	SelectField,
	GroupField,
	CheckboxField,
	JSONField,
	RelationshipField,
	ArrayField,
} from 'payload';

const fileField: TextField = {
	name: 'file',
	type: 'text',
	label: 'Video Source URL',
	required: false,
};

const playlistField: TextField = {
	name: 'playlist',
	type: 'text',
	label: 'Playlist (optional)',
	required: false,
};

const imageField: TextField = {
	name: 'image',
	type: 'text',
	label: 'Thumbnail Image URL (optional)',
	required: false,
};

const titleField: TextField = {
	name: 'title',
	type: 'text',
	label: 'Video Title',
	required: false,
};

const descriptionField: TextField = {
	name: 'description',
	type: 'text',
	label: 'Video Description',
	required: false,
};

const tracksField: GroupField = {
	name: 'tracks',
	type: 'group',
	label: 'Captions',
	fields: [
		{
			name: 'file',
			type: 'text',
			label: 'Captions URL',
		},
		{
			name: 'label',
			type: 'text',
			label: 'Language',
			defaultValue: 'English',
		},
		{
			name: 'kind',
			type: 'select',
			label: 'Kind',
			defaultValue: 'captions',
			options: [
				{ label: 'Captions', value: 'captions' },
				{ label: 'Chapters', value: 'chapters' },
				{ label: 'Thumbnails', value: 'thumbnails' },
			],
		},
		{
			name: 'default',
			type: 'checkbox',
			label: 'Start Enabled',
			defaultValue: false,
		},
	],
};

const playerSettingsField: GroupField = {
	name: 'playerSettings',
	type: 'group',
	label: 'Player Settings',
	fields: [
		{
			name: 'aspectRatio',
			type: 'select',
			label: 'Aspect Ratio',
			defaultValue: '16:9',
			options: [
				{ label: '16:9', value: '16:9' },
				{ label: '4:3', value: '4:3' },
				{ label: '1:1', value: '1:1' },
			],
		} as SelectField,
		{
			name: 'width',
			type: 'text',
			label: 'Width',
			defaultValue: '100%',
		} as TextField,
		{
			name: 'showDescriptions',
			type: 'checkbox',
			label: 'Show Descriptions',
			defaultValue: true,
		} as CheckboxField,
	],
};

// Video reference field
const videoReferenceField: RelationshipField = {
	name: 'videoReference',
	type: 'relationship',
	label: 'Video Reference',
	relationTo: 'videos',
	hasMany: false,
	required: false,
	admin: {
		description:
			'Select a video from the video collection. If selected, this will override the direct video fields.',
	},
};

// Playlists reference field
const playlistsReferenceField: ArrayField = {
	name: 'playlistsReference',
	type: 'array',
	label: 'Playlists Reference',
	admin: {
		description: 'Select content queries to use as playlists',
	},
	fields: [
		{
			name: 'contentQuery',
			type: 'relationship',
			label: 'Content Query',
			relationTo: 'content-queries',
			required: true,
			hasMany: false,
		},
	],
};

export const VideoBlockConfig: Block = {
	slug: 'Video',
	interfaceName: 'VideoBlockConfig',
	labels: {
		singular: 'Video Block',
		plural: 'Video Blocks',
	},
	fields: [
		videoReferenceField,
		fileField,
		playlistField,
		imageField,
		titleField,
		descriptionField,
		tracksField,
		playerSettingsField,
		playlistsReferenceField,
		{
			name: 'custom',
			type: 'json',
			label: 'Custom Properties',
			admin: {
				hidden: true, // Hide from UI since it's only for DSX support
			},
			defaultValue: {},
		} as JSONField,
	],
	// Component to render in the admin interface
	admin: {
		components: {
			Block: {
				path: '@repo/payload/blocks/admin#VideoAdminBlock',
			},
		},
	},
};

export default VideoBlockConfig;
