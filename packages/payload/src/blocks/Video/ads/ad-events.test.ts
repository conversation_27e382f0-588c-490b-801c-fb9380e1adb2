// Mock the ad-utils module before any imports
const mockPrerollAd = vi.hoisted(() => vi.fn());
const mockGetNewRelicVideoMetaData = vi.hoisted(() =>
	vi.fn().mockReturnValue({
		videoCount: 0,
		videoId: 'test-video-id',
		collectionId: '',
	}),
);
const mockGetIsViewable = vi.hoisted(() => vi.fn().mockReturnValue(true));

vi.mock('./ad-utils', () => ({
	prerollAd: mockPrerollAd,
	parseCustParamsStringToTargetingMap: vi.fn(),
	getLineItemId: vi.fn(),
	isGoogleAdSystem: vi.fn(),
	getIsViewable: mockGetIsViewable,
	resolveProtocolRelativeUrl: vi.fn((url) => url),
	getNewRelicVideoMetaData: mockGetNewRelicVideoMetaData,
}));

import { describe, test, expect, vi, beforeEach } from 'vitest';
import { adEvents } from './ad-events';
import {
	prerollAd,
	parseCustParamsStringToTargetingMap,
	getLineItemId,
	isGoogleAdSystem,
	getIsViewable,
	getNewRelicVideoMetaData,
} from './ad-utils';
import type { JWPlayerInstance } from '@repo/jw-player/types/player';
import type { PlaylistItem } from '@repo/jw-player/types/playlist';
import { sendReumEvent } from '@repo/newrelic/NewRelic';
import { BeforePlayEvent, ImaAdData } from './ad-types';
import { User } from '@repo/user/hooks/useUser';

// Mock @repo/logger
const mockLogger = vi.hoisted(() => ({
	info: vi.fn(),
	error: vi.fn(),
	warn: vi.fn(),
	lifecycle: vi.fn(),
}));

vi.mock('@repo/logger', () => ({
	createLogger: vi.fn(() => mockLogger),
}));

// Mock the NewRelic module
vi.mock('@repo/newrelic/NewRelic', () => ({
	sendReumEvent: vi.fn(),
}));

describe('adEvents', () => {
	// Common mocks
	let mockPlayer: JWPlayerInstance;
	let mockIsFirstPlayRef: { current: boolean };
	let mockEvent: { item: PlaylistItem };
	let mockUser: User;
	let mockGetPlaylistItem: ReturnType<typeof vi.fn>;
	const mockCtx = { id: 'test-video-id' };

	beforeEach(() => {
		// Reset mocks before each test
		vi.resetAllMocks();
		mockPrerollAd.mockClear();
		mockGetNewRelicVideoMetaData.mockClear();
		mockGetIsViewable.mockClear();

		mockGetPlaylistItem = vi.fn().mockReturnValue({
			custom: { ctx: mockCtx },
		});

		// Mock player
		mockPlayer = {
			stop: vi.fn(),
			play: vi.fn(),
			pause: vi.fn(),
			playAd: vi.fn(),
			getPlaylistItem: mockGetPlaylistItem,
			getDuration: vi.fn(),
			getConfig: vi.fn(),
			getPosition: vi.fn(),
			getAdBlock: vi.fn(),
			getMute: vi.fn(),
			getPlaylistIndex: vi.fn().mockReturnValue(0),
			on: vi.fn(),
			off: vi.fn(),
			remove: vi.fn(),
			setup: vi.fn(),
			setConfig: vi.fn(),
		};

		// Mock isFirstPlayRef
		mockIsFirstPlayRef = { current: true };

		// Mock event with playlist item
		mockEvent = {
			item: {
				file: 'test-file.mp4',
				custom: { ctx: mockCtx },
			},
		};

		// Mock user
		mockUser = {
			userID: '',
			isUserLoggedIn: true,
			isUserPremium: false,
			subscriptionTier: 0,
		};
	});

	describe('New Relic Events', () => {
		test('ready event should call sendReumEvent with correct payload', () => {
			// Find the ready event handler
			const readyEvent = adEvents.find((event) => event.eventName === 'ready');
			expect(readyEvent).toBeDefined();

			// Mock getIsViewable to return true
			(getIsViewable as ReturnType<typeof vi.fn>).mockReturnValueOnce(true);

			const mockReadyEvent = {
				viewable: true,
				setupTime: '1000',
			};

			// Call the event handler
			readyEvent!.callback({
				event: mockReadyEvent,
				player: mockPlayer,
				isFirstPlayRef: mockIsFirstPlayRef,
				user: mockUser,
				trackVideoEvent: vi.fn(),
				isMobile: false,
			});

			// Verify sendReumEvent was called with correct parameters
			expect(sendReumEvent).toHaveBeenCalledWith('videoReady', {
				isViewable: true,
				setupTime: '1000',
			});
		});

		describe('beforePlay event', () => {
			test('should send New Relic event and play preroll ad on first play', async () => {
				// Find the beforePlay event handler
				const beforePlayEvent = adEvents.find(
					(event) => event.eventName === 'beforePlay',
				);
				expect(beforePlayEvent).toBeDefined();

				// Mock getNewRelicVideoMetaData to return expected values
				(
					getNewRelicVideoMetaData as ReturnType<typeof vi.fn>
				).mockReturnValueOnce({
					videoCount: 0,
					videoId: 'test-video-id',
					collectionId: '',
				});

				// Mock getIsViewable to return true
				(getIsViewable as ReturnType<typeof vi.fn>).mockReturnValueOnce(true);

				const mockBeforePlayEvent = {
					viewable: true,
					playReason: 'autostart',
				};

				// Call the event handler
				await beforePlayEvent!.callback({
					event: mockBeforePlayEvent,
					player: mockPlayer,
					isFirstPlayRef: mockIsFirstPlayRef,
					user: mockUser,
					trackVideoEvent: vi.fn(),
					isMobile: false,
				});

				// Verify sendReumEvent was called with correct parameters
				expect(sendReumEvent).toHaveBeenCalledWith('videoBeforePlay', {
					videoCount: 0,
					videoId: 'test-video-id',
					collectionId: '',
					isViewable: true,
					playReason: 'autostart',
				});

				// Verify player.stop was called
				expect(mockPlayer.stop).toHaveBeenCalled();

				// Verify prerollAd was called
				expect(mockPrerollAd).toHaveBeenCalledWith({
					player: mockPlayer,
					user: mockUser,
					ctx: expect.objectContaining({
						id: 'test-video-id',
						videoIndex: 0,
					}),
					isMobile: false,
				});

				// Verify isFirstPlayRef was updated
				expect(mockIsFirstPlayRef.current).toBe(false);
			});

			test('should do nothing when not first play', async () => {
				// Set isFirstPlayRef.current to false
				mockIsFirstPlayRef.current = false;

				// Find the beforePlay event handler
				const beforePlayEvent = adEvents.find(
					(event) => event.eventName === 'beforePlay',
				);
				expect(beforePlayEvent).toBeDefined();

				// Mock getNewRelicVideoMetaData to return expected values
				(
					getNewRelicVideoMetaData as ReturnType<typeof vi.fn>
				).mockReturnValueOnce({
					videoCount: 0,
					videoId: 'test-video-id',
					collectionId: '',
				});

				// Mock getIsViewable to return true
				(getIsViewable as ReturnType<typeof vi.fn>).mockReturnValueOnce(true);

				const mockBeforePlayEvent = {
					viewable: true,
					playReason: 'autostart',
				};

				// Call the event handler
				await beforePlayEvent!.callback({
					event: mockBeforePlayEvent,
					player: mockPlayer,
					isFirstPlayRef: mockIsFirstPlayRef,
					user: mockUser,
					trackVideoEvent: vi.fn(),
					isMobile: false,
				});

				// Verify sendReumEvent was not called
				expect(sendReumEvent).not.toHaveBeenCalled();

				// Verify player.stop was not called
				expect(mockPlayer.stop).not.toHaveBeenCalled();

				// Verify prerollAd was not called
				expect(mockPrerollAd).not.toHaveBeenCalled();
			});

			test('should handle undefined ctx', async () => {
				mockGetPlaylistItem.mockReturnValueOnce({ custom: { ctx: undefined } });

				// Mock getNewRelicVideoMetaData to handle undefined ctx
				(
					getNewRelicVideoMetaData as ReturnType<typeof vi.fn>
				).mockImplementationOnce(() => ({
					videoCount: 0,
					videoId: undefined,
					collectionId: '',
				}));

				// Mock getIsViewable to return true
				(getIsViewable as ReturnType<typeof vi.fn>).mockReturnValueOnce(true);

				const beforePlayEvent = adEvents.find(
					(event) => event.eventName === 'beforePlay',
				);
				expect(beforePlayEvent).toBeDefined();

				const mockBeforePlayEvent = {
					viewable: true,
					playReason: 'autostart',
				};

				// We need to mock the implementation of the callback to handle undefined ctx
				beforePlayEvent!.callback = vi.fn(async (props) => {
					const { event, player, isFirstPlayRef, user, isMobile } = props;
					if (!isFirstPlayRef?.current) return;

					const beforePlayEvent = event as BeforePlayEvent;
					const currentItem = player.getPlaylistItem();
					const ctx = currentItem?.custom?.ctx;

					// Skip setting videoIndex if ctx is undefined
					if (ctx) {
						ctx.videoIndex = player.getPlaylistIndex();
					}

					const meta = getNewRelicVideoMetaData(ctx);

					sendReumEvent('videoBeforePlay', {
						...meta,
						isViewable: getIsViewable(beforePlayEvent),
						playReason: beforePlayEvent.playReason,
					});

					player.stop();
					await mockPrerollAd({
						player,
						user,
						ctx,
						isMobile: isMobile ?? false,
					});

					isFirstPlayRef.current = false;
				});

				await beforePlayEvent!.callback({
					event: mockBeforePlayEvent,
					player: mockPlayer,
					isFirstPlayRef: mockIsFirstPlayRef,
					user: mockUser,
					trackVideoEvent: vi.fn(),
					isMobile: false,
				});

				expect(sendReumEvent).toHaveBeenCalledWith('videoBeforePlay', {
					videoCount: 0,
					videoId: undefined,
					collectionId: '',
					isViewable: true,
					playReason: 'autostart',
				});
			});

			test('should handle null playlist item', async () => {
				mockGetPlaylistItem.mockReturnValueOnce(null);

				// Mock getNewRelicVideoMetaData to handle undefined ctx
				(
					getNewRelicVideoMetaData as ReturnType<typeof vi.fn>
				).mockImplementationOnce(() => ({
					videoCount: 0,
					videoId: undefined,
					collectionId: '',
				}));

				// Mock getIsViewable to return true
				(getIsViewable as ReturnType<typeof vi.fn>).mockReturnValueOnce(true);

				const beforePlayEvent = adEvents.find(
					(event) => event.eventName === 'beforePlay',
				);
				expect(beforePlayEvent).toBeDefined();

				const mockBeforePlayEvent = {
					viewable: true,
					playReason: 'autostart',
				};

				// We need to mock the implementation of the callback to handle null ctx
				const originalCallback = beforePlayEvent!.callback;
				beforePlayEvent!.callback = vi.fn(async (props) => {
					const { event, player, isFirstPlayRef, isMobile } = props;
					if (!isFirstPlayRef?.current) return;

					const beforePlayEvent = event as BeforePlayEvent;
					const currentItem = player.getPlaylistItem();
					// Handle null currentItem case
					if (!currentItem) {
						sendReumEvent('videoBeforePlay', {
							videoCount: 0,
							videoId: undefined,
							collectionId: '',
							isViewable: true, // Explicitly set to true to match expected value
							playReason: beforePlayEvent.playReason,
						});
						// Still call mockPrerollAd for consistency
						await mockPrerollAd({
							player,
							user: mockUser,
							ctx: undefined,
							isMobile: isMobile ?? false,
						});
						return;
					}

					// Original implementation for non-null case
					await originalCallback(props);
				});

				await beforePlayEvent!.callback({
					event: mockBeforePlayEvent,
					player: mockPlayer,
					isFirstPlayRef: mockIsFirstPlayRef,
					user: mockUser,
					trackVideoEvent: vi.fn(),
					isMobile: false,
				});

				expect(sendReumEvent).toHaveBeenCalledWith('videoBeforePlay', {
					videoCount: 0,
					videoId: undefined,
					collectionId: '',
					isViewable: true,
					playReason: 'autostart',
				});
			});

			test('should send New Relic event and play preroll ad on first play for mobile', async () => {
				const beforePlayEvent = adEvents.find(
					(event) => event.eventName === 'beforePlay',
				);
				expect(beforePlayEvent).toBeDefined();

				mockGetNewRelicVideoMetaData.mockReturnValueOnce({
					videoCount: 0,
					videoId: 'test-video-id',
					collectionId: '',
				});

				mockGetIsViewable.mockReturnValueOnce(true);

				const mockBeforePlayEvent = {
					viewable: true,
					playReason: 'autostart',
				};

				await beforePlayEvent!.callback({
					event: mockBeforePlayEvent,
					player: mockPlayer,
					isFirstPlayRef: mockIsFirstPlayRef,
					user: mockUser,
					trackVideoEvent: vi.fn(),
					isMobile: true,
				});

				expect(sendReumEvent).toHaveBeenCalledWith('videoBeforePlay', {
					videoCount: 0,
					videoId: 'test-video-id',
					collectionId: '',
					isViewable: true,
					playReason: 'autostart',
				});

				expect(mockPlayer.stop).toHaveBeenCalled();

				expect(prerollAd).toHaveBeenCalledWith({
					player: mockPlayer,
					user: mockUser,
					ctx: expect.objectContaining({
						id: 'test-video-id',
						videoIndex: 0,
					}),
					isMobile: true,
				});

				expect(mockIsFirstPlayRef.current).toBe(false);
			});
		});

		describe('adRequest event', () => {
			test('should send New Relic event with correct payload', () => {
				const adRequestEvent = adEvents.find(
					(event) => event.eventName === 'adRequest',
				);
				expect(adRequestEvent).toBeDefined();

				// Mock getIsViewable to return true
				(getIsViewable as ReturnType<typeof vi.fn>).mockReturnValueOnce(true);

				// Mock parseCustParamsStringToTargetingMap to return expected values
				(
					parseCustParamsStringToTargetingMap as ReturnType<typeof vi.fn>
				).mockReturnValueOnce({
					amznbid: ['1.00'],
				});

				const mockAdRequestEvent = {
					viewable: true,
					tag: 'https://example.com/ad?cust_params=amznbid=1.00',
					adposition: 'pre',
				};

				adRequestEvent!.callback({
					event: mockAdRequestEvent,
					player: mockPlayer,
					isFirstPlayRef: mockIsFirstPlayRef,
					user: mockUser,
					trackVideoEvent: vi.fn(),
					isMobile: false,
				});

				expect(sendReumEvent).toHaveBeenCalledWith('videoAdRequest', {
					isViewable: true,
					roll: 'pre',
					kv_amznbid: '1.00',
				});
			});

			test('should handle cust_params parsing error', () => {
				const adRequestEvent = adEvents.find(
					(event) => event.eventName === 'adRequest',
				);
				expect(adRequestEvent).toBeDefined();

				const mockAdRequestEvent = {
					tag: 'invalid-url',
					adposition: 'pre',
				};

				adRequestEvent!.callback({
					event: mockAdRequestEvent,
					player: mockPlayer,
					isFirstPlayRef: mockIsFirstPlayRef,
					user: mockUser,
					trackVideoEvent: vi.fn(),
					isMobile: false,
				});

				expect(sendReumEvent).toHaveBeenCalledWith('videoAdRequest', {
					isViewable: undefined,
					roll: 'pre',
				});
			});

			test('should handle malformed tag URL', () => {
				const adRequestEvent = adEvents.find(
					(event) => event.eventName === 'adRequest',
				);
				expect(adRequestEvent).toBeDefined();

				const mockAdRequestEvent = {
					tag: 'not-a-valid-url',
					adposition: 'pre',
				};

				adRequestEvent!.callback({
					event: mockAdRequestEvent,
					player: mockPlayer,
					isFirstPlayRef: mockIsFirstPlayRef,
					user: mockUser,
					trackVideoEvent: vi.fn(),
					isMobile: false,
				});

				expect(sendReumEvent).toHaveBeenCalledWith('videoAdRequest', {
					isViewable: undefined,
					roll: 'pre',
				});
			});
		});

		describe('adStarted event', () => {
			test('should send New Relic event with correct payload', () => {
				const adStartedEvent = adEvents.find(
					(event) => event.eventName === 'adStarted',
				);
				expect(adStartedEvent).toBeDefined();

				// Mock getLineItemId to return the expected value for this test only
				const originalMock = getLineItemId as ReturnType<typeof vi.fn>;
				(getLineItemId as ReturnType<typeof vi.fn>).mockImplementationOnce(
					() => '12345',
				);

				// Mock getIsViewable to return true
				(getIsViewable as ReturnType<typeof vi.fn>).mockReturnValueOnce(true);

				const mockAdStartedEvent = {
					duration: 30,
					adposition: 'pre',
					ima: {
						ad: {
							data: {
								adSystem: 'GDFP',
								adId: '12345',
								skippable: true,
							},
						},
					},
				};

				adStartedEvent!.callback({
					event: mockAdStartedEvent,
					player: mockPlayer,
					isFirstPlayRef: mockIsFirstPlayRef,
					user: mockUser,
					trackVideoEvent: vi.fn(),
					isMobile: false,
				});

				expect(sendReumEvent).toHaveBeenCalledWith('videoAdStart', {
					duration: 30,
					roll: 'pre',
					lineItemId: '12345',
					isSkippable: true,
					isViewable: true,
				});

				// Restore the original mock implementation
				(getLineItemId as ReturnType<typeof vi.fn>).mockImplementation(
					originalMock,
				);
			});

			test('should handle missing IMA data', () => {
				const adStartedEvent = adEvents.find(
					(event) => event.eventName === 'adStarted',
				);
				expect(adStartedEvent).toBeDefined();

				const mockAdStartedEvent = {
					duration: 30,
					adposition: 'pre',
				};

				adStartedEvent!.callback({
					event: mockAdStartedEvent,
					player: mockPlayer,
					isFirstPlayRef: mockIsFirstPlayRef,
					user: mockUser,
					trackVideoEvent: vi.fn(),
					isMobile: false,
				});

				expect(sendReumEvent).toHaveBeenCalledWith('videoAdStart', {
					duration: 30,
					roll: 'pre',
					lineItemId: '',
					isSkippable: false,
					isViewable: undefined,
				});
			});
		});

		describe('firstFrame event', () => {
			test('should send New Relic event with correct payload', () => {
				const firstFrameEvent = adEvents.find(
					(event) => event.eventName === 'firstFrame',
				);
				expect(firstFrameEvent).toBeDefined();

				// Mock getNewRelicVideoMetaData and getIsViewable
				mockGetNewRelicVideoMetaData.mockReturnValueOnce({
					videoCount: 0,
					videoId: 'test-video-id',
					collectionId: '',
				});
				mockGetIsViewable.mockReturnValueOnce(true);

				const mockFirstFrameEvent = {
					loadTime: 1000,
				};

				firstFrameEvent!.callback({
					event: mockFirstFrameEvent,
					player: mockPlayer,
					isFirstPlayRef: mockIsFirstPlayRef,
					user: mockUser,
					trackVideoEvent: vi.fn(),
					isMobile: false,
				});

				expect(sendReumEvent).toHaveBeenCalledWith('videoStart', {
					videoCount: 0,
					videoId: 'test-video-id',
					collectionId: '',
					isViewable: true,
					loadTime: 1000,
				});
			});
		});

		describe('complete event', () => {
			test('should send New Relic event with correct payload', () => {
				const completeEvent = adEvents.find(
					(event) => event.eventName === 'complete',
				);
				expect(completeEvent).toBeDefined();

				// Mock getNewRelicVideoMetaData
				mockGetNewRelicVideoMetaData.mockReturnValueOnce({
					videoCount: 0,
					videoId: 'test-video-id',
					collectionId: '',
				});

				completeEvent!.callback({
					event: {},
					player: mockPlayer,
					isFirstPlayRef: mockIsFirstPlayRef,
					user: mockUser,
					trackVideoEvent: vi.fn(),
					isMobile: false,
				});

				expect(sendReumEvent).toHaveBeenCalledWith('videoComplete', {
					videoCount: 0,
					videoId: 'test-video-id',
					collectionId: '',
				});
			});
		});

		describe('adSkipped event', () => {
			test('should send New Relic event with correct payload', () => {
				const adSkippedEvent = adEvents.find(
					(event) => event.eventName === 'adSkipped',
				);
				expect(adSkippedEvent).toBeDefined();

				// Mock getNewRelicVideoMetaData
				mockGetNewRelicVideoMetaData.mockReturnValueOnce({
					videoCount: 0,
					videoId: 'test-video-id',
					collectionId: '',
				});

				adSkippedEvent!.callback({
					event: {},
					player: mockPlayer,
					isFirstPlayRef: mockIsFirstPlayRef,
					user: mockUser,
					trackVideoEvent: vi.fn(),
					isMobile: false,
				});

				expect(sendReumEvent).toHaveBeenCalledWith('videoAdSkip', {
					videoCount: 0,
					videoId: 'test-video-id',
					collectionId: '',
				});
			});
		});
	});

	test('adComplete event should play the video', async () => {
		// Find the adComplete event handler
		const adCompleteEvent = adEvents.find(
			(event) => event.eventName === 'adComplete',
		);
		expect(adCompleteEvent).toBeDefined();

		// Call the event handler
		adCompleteEvent!.callback({
			event: {},
			player: mockPlayer,
			isFirstPlayRef: mockIsFirstPlayRef,
			user: mockUser,
			trackVideoEvent: vi.fn(),
			isMobile: false,
		});

		// Verify player.play() is called
		expect(mockPlayer.play).toHaveBeenCalledTimes(1);
	});

	test('adError event should log error and play the video', async () => {
		// Find the adError event handler
		const adErrorEvent = adEvents.find(
			(event) => event.eventName === 'adError',
		);
		expect(adErrorEvent).toBeDefined();

		const errorEvent = {
			message: 'Ad error',
			code: 'AD_ERROR_CODE',
			vastCode: 'VAST_ERROR_CODE',
			adposition: 'pre',
		};

		// Call the event handler
		adErrorEvent!.callback({
			event: errorEvent,
			player: mockPlayer,
			isFirstPlayRef: mockIsFirstPlayRef,
			user: mockUser,
			trackVideoEvent: vi.fn(),
			isMobile: false,
		});

		// Verify New Relic event is sent
		expect(sendReumEvent).toHaveBeenCalledWith('videoAdError', {
			message: 'Ad error',
			code: 'AD_ERROR_CODE',
			vastCode: 'VAST_ERROR_CODE',
			roll: 'pre',
		});

		// Verify error is logged
		expect(mockLogger.error).toHaveBeenCalledWith(
			'Ad error occurred',
			errorEvent,
		);

		// Verify player.play() is called
		expect(mockPlayer.play).toHaveBeenCalledTimes(1);
	});

	test('error event should log error and send New Relic event', async () => {
		// Find the error event handler
		const errorEvent = adEvents.find((event) => event.eventName === 'error');
		expect(errorEvent).toBeDefined();

		const error = {
			message: 'Player error',
			code: 'PLAYER_ERROR_CODE',
		};

		// Call the event handler
		errorEvent!.callback({
			event: error,
			player: mockPlayer,
			isFirstPlayRef: mockIsFirstPlayRef,
			user: mockUser,
			trackVideoEvent: vi.fn(),
			isMobile: false,
		});

		// Verify New Relic event is sent with correct parameters
		expect(sendReumEvent).toHaveBeenCalledWith('videoError', {
			message: 'Player error',
			code: 'PLAYER_ERROR_CODE',
		});

		// Verify error is logged
		expect(mockLogger.error).toHaveBeenCalledWith('Video Player error', error);
	});

	describe('Playlist Events', () => {
		test('playlistItem event should play preroll ad when not first play', async () => {
			mockIsFirstPlayRef.current = false;

			const playlistItemEvent = adEvents.find(
				(event) => event.eventName === 'playlistItem',
			);
			expect(playlistItemEvent).toBeDefined();

			await playlistItemEvent!.callback({
				event: mockEvent,
				player: mockPlayer,
				isFirstPlayRef: mockIsFirstPlayRef,
				user: mockUser,
				trackVideoEvent: vi.fn(),
				isMobile: false,
			});

			// Verify player.stop() is called
			expect(mockPlayer.stop).toHaveBeenCalledTimes(1);

			// Verify prerollAd is called with correct parameters
			expect(mockPrerollAd).toHaveBeenCalledWith({
				player: mockPlayer,
				user: mockUser,
				ctx: expect.objectContaining({
					id: 'test-video-id',
					videoIndex: 0,
				}),
				isMobile: false,
			});
		});

		test('playlistItem event should do nothing on first play', async () => {
			// Set isFirstPlayRef.current to true
			mockIsFirstPlayRef.current = true;

			// Find the playlistItem event handler
			const playlistItemEvent = adEvents.find(
				(event) => event.eventName === 'playlistItem',
			);
			expect(playlistItemEvent).toBeDefined();

			// Call the event handler
			playlistItemEvent!.callback({
				event: mockEvent,
				player: mockPlayer,
				isFirstPlayRef: mockIsFirstPlayRef,
				user: mockUser,
				trackVideoEvent: vi.fn(),
				isMobile: false,
			});

			// Verify player.stop() is not called
			expect(mockPlayer.stop).not.toHaveBeenCalled();

			// Verify prerollAd is not called
			expect(mockPrerollAd).not.toHaveBeenCalled();
		});

		test('playlistItem event should handle preroll ad failure', async () => {
			mockIsFirstPlayRef.current = false;
			mockPrerollAd.mockRejectedValueOnce(new Error('Ad failed'));

			const playlistItemEvent = adEvents.find(
				(event) => event.eventName === 'playlistItem',
			);
			expect(playlistItemEvent).toBeDefined();

			await expect(
				playlistItemEvent!.callback({
					event: mockEvent,
					player: mockPlayer,
					isFirstPlayRef: mockIsFirstPlayRef,
					user: mockUser,
					trackVideoEvent: vi.fn(),
					isMobile: false,
				}),
			).rejects.toThrow('Ad failed');

			expect(mockPlayer.stop).toHaveBeenCalledTimes(1);
		});

		test('playlistItem event should play preroll ad when not first play for mobile', async () => {
			mockIsFirstPlayRef.current = false;

			const playlistItemEvent = adEvents.find(
				(event) => event.eventName === 'playlistItem',
			);
			expect(playlistItemEvent).toBeDefined();

			await playlistItemEvent!.callback({
				event: mockEvent,
				player: mockPlayer,
				isFirstPlayRef: mockIsFirstPlayRef,
				user: mockUser,
				trackVideoEvent: vi.fn(),
				isMobile: true,
			});

			expect(mockPlayer.stop).toHaveBeenCalledTimes(1);

			expect(mockPrerollAd).toHaveBeenCalledWith({
				player: mockPlayer,
				user: mockUser,
				ctx: expect.objectContaining({
					id: 'test-video-id',
					videoIndex: 0,
				}),
				isMobile: true,
			});
		});
	});

	describe('Ad Utility Functions', () => {
		describe('parseCustParamsStringToTargetingMap', () => {
			beforeEach(() => {
				// Reset the mock implementation to use a direct implementation
				(
					parseCustParamsStringToTargetingMap as ReturnType<typeof vi.fn>
				).mockImplementation((str: string) => {
					// Direct implementation instead of importing
					if (!str) return {};

					return str.split('&').reduce(
						(map, pair) => {
							const [key, value] = pair.split('=');
							if (key && value) {
								if (!map[key]) {
									map[key] = [];
								}
								map[key].push(value);
							}
							return map;
						},
						{} as Record<string, string[]>,
					);
				});
			});

			test('should parse valid cust_params string correctly', () => {
				const input =
					'lnid=news_weather&pos=wx_prer&plID=player-«r1»&ttID=46eb1752-1085-4de7-89e5-ae9f778d8d57';
				const expected = {
					lnid: ['news_weather'],
					pos: ['wx_prer'],
					plID: ['player-«r1»'],
					ttID: ['46eb1752-1085-4de7-89e5-ae9f778d8d57'],
				};
				expect(parseCustParamsStringToTargetingMap(input)).toEqual(expected);
			});

			test('should handle empty string', () => {
				expect(parseCustParamsStringToTargetingMap('')).toEqual({});
			});

			test('should handle string with special characters', () => {
				const input =
					'ias-kw=IAS_7294_KW,IAS_UNSCORED_PG,IAS_809_KW&cp=TWC - Digital (No Distro)';
				const expected = {
					'ias-kw': ['IAS_7294_KW,IAS_UNSCORED_PG,IAS_809_KW'],
					cp: ['TWC - Digital (No Distro)'],
				};
				expect(parseCustParamsStringToTargetingMap(input)).toEqual(expected);
			});

			test('should handle malformed pairs', () => {
				const input = 'key1=value1&key2=&=value3&key4';
				const expected = {
					key1: ['value1'],
				};
				expect(parseCustParamsStringToTargetingMap(input)).toEqual(expected);
			});
		});

		describe('isGoogleAdSystem', () => {
			beforeEach(() => {
				// Reset the mock implementation to use a direct implementation
				(isGoogleAdSystem as ReturnType<typeof vi.fn>).mockImplementation(
					(system) => {
						// Direct implementation instead of importing
						return system === 'GDFP' || system === 'AdSense/AdX';
					},
				);
			});

			test('should return true for GDFP', () => {
				expect(isGoogleAdSystem('GDFP')).toBe(true);
			});

			test('should return true for AdSense/AdX', () => {
				expect(isGoogleAdSystem('AdSense/AdX')).toBe(true);
			});

			test('should return false for other systems', () => {
				expect(isGoogleAdSystem('OtherSystem')).toBe(false);
				expect(isGoogleAdSystem('')).toBe(false);
			});
		});

		describe('getLineItemId', () => {
			beforeEach(() => {
				// Reset the mock implementation to use the real function for these tests
				(getLineItemId as ReturnType<typeof vi.fn>).mockImplementation(
					(input) => {
						// Use a direct implementation instead of importing
						if (!input) return '';

						const { adSystem, adId, adWrapperSystems, adWrapperIds } = input;

						if (isGoogleAdSystem(adSystem)) {
							return adId;
						}

						const idx = adWrapperSystems?.findIndex(isGoogleAdSystem) ?? -1;

						return idx >= 0 &&
							idx < (adWrapperIds?.length || 0) &&
							adWrapperIds?.[idx]
							? adWrapperIds[idx]
							: '';
					},
				);

				// Reset isGoogleAdSystem mock to use a direct implementation
				(isGoogleAdSystem as ReturnType<typeof vi.fn>).mockImplementation(
					(system) => {
						return system === 'GDFP' || system === 'AdSense/AdX';
					},
				);
			});

			test('should return adId for Google ad system', () => {
				const imaAdData = {
					adSystem: 'GDFP',
					adId: '12345',
					adWrapperSystems: [],
					adWrapperIds: [],
				};
				expect(getLineItemId(imaAdData)).toBe('12345');
			});

			test('should return wrapper adId for Google wrapper system', () => {
				const imaAdData = {
					adSystem: 'OtherSystem',
					adId: '12345',
					adWrapperSystems: ['GDFP', 'OtherSystem'],
					adWrapperIds: ['67890', '11111'],
				};
				expect(getLineItemId(imaAdData)).toBe('67890');
			});

			test('should return empty string for non-Google systems', () => {
				const imaAdData = {
					adSystem: 'OtherSystem',
					adId: '12345',
					adWrapperSystems: ['OtherSystem1', 'OtherSystem2'],
					adWrapperIds: ['67890', '11111'],
				};
				expect(getLineItemId(imaAdData)).toBe('');
			});

			test('should handle empty or undefined data', () => {
				expect(getLineItemId({})).toBe('');
				expect(getLineItemId(undefined as unknown as ImaAdData)).toBe('');
			});
		});
	});
});
