import type { PlaylistItem } from '@repo/jw-player/types/playlist';
import type { VideoEvent } from '@repo/jw-player/types/events';
import {
	prerollAd,
	getLineItemId,
	getIsViewable,
	resolveProtocolRelativeUrl,
	parseCustParamsStringToTargetingMap,
	getNewRelicVideoMetaData,
} from './ad-utils';
import { type CTX } from '@repo/jw-player/types/ctx';
import { createLogger } from '@repo/logger';
import { sendReumEvent } from '@repo/newrelic/NewRelic';

const logger = createLogger('AdEvents');
import {
	type AdErrorEvent,
	type AdRequestEvent,
	type AdStartedEvent,
	type BeforePlayEvent,
	type ErrorEvent,
	type FirstFrameEvent,
	type ReadyEvent,
	type SetupErrorEvent,
} from './ad-types';

// Only keep the constants that are specific to ad-events.ts
const metaKvPrefix = 'kv_';

const newRelicMetaKvs = new Set([
	'amznbid', // amazon cpm (encoded)
	'crt_pb', // criteo cpm
	'hb_pb', // prebid cpm
]);

export const adEvents: VideoEvent[] = [
	{
		eventName: 'ready',
		callback: ({ event }) => {
			const readyEvent = event as ReadyEvent;
			sendReumEvent('videoReady', {
				isViewable: getIsViewable(readyEvent),
				setupTime: readyEvent.setupTime,
			});
		},
	},
	{
		eventName: 'beforePlay',
		callback: async ({ event, player, isFirstPlayRef, user, isMobile }) => {
			if (!isFirstPlayRef?.current) return;

			const beforePlayEvent = event as BeforePlayEvent;
			const currentItem = player.getPlaylistItem();
			const ctx = currentItem?.custom?.ctx as CTX;

			if (ctx) ctx.videoIndex = player.getPlaylistIndex();

			const meta = getNewRelicVideoMetaData(ctx);

			sendReumEvent('videoBeforePlay', {
				...meta,
				isViewable: getIsViewable(beforePlayEvent),
				playReason: beforePlayEvent.playReason,
			});

			player.stop();
			await prerollAd({ player, user, ctx, isMobile });

			isFirstPlayRef.current = false;
		},
	},
	{
		eventName: 'adRequest',
		callback: ({ event, player }) => {
			const adRequestEvent = event as AdRequestEvent;
			const currentItem = player.getPlaylistItem();
			const ctx = currentItem?.custom?.ctx as CTX;

			const metaKvs = {};
			const meta = getNewRelicVideoMetaData(ctx);
			try {
				const tagWithProtocol = resolveProtocolRelativeUrl(
					adRequestEvent.tag || '',
				);
				const custParams = new URL(tagWithProtocol).searchParams.get(
					'cust_params',
				);
				const targetingMap = parseCustParamsStringToTargetingMap(
					custParams as string,
				);

				for (const key of Object.keys(targetingMap)) {
					if (newRelicMetaKvs.has(key)) {
						(metaKvs as Record<string, string>)[`${metaKvPrefix}${key}`] =
							`${targetingMap[key]}`;
					}
				}
			} catch (err) {
				logger.error('Error parsing cust_params', err);
			}

			sendReumEvent('videoAdRequest', {
				...meta,
				isViewable: getIsViewable(adRequestEvent),
				roll: adRequestEvent.adposition,
				...metaKvs,
			});
		},
	},
	{
		eventName: 'adStarted',
		callback: ({ event, player }) => {
			const adStartedEvent = event as AdStartedEvent;
			const currentItem = player.getPlaylistItem();
			const ctx = currentItem?.custom?.ctx as CTX;

			const { duration, adposition: roll } = adStartedEvent;

			const meta = getNewRelicVideoMetaData(ctx);
			let lineItemId = '';

			try {
				const imaData = adStartedEvent.ima?.ad?.data;
				if (imaData) {
					lineItemId = getLineItemId(imaData);
				}
			} catch (err) {
				logger.error('Error parsing ima ad data', err);
			}

			const isSkippable = !!adStartedEvent.ima?.ad?.data?.skippable;

			sendReumEvent('videoAdStart', {
				...meta,
				isViewable: getIsViewable(adStartedEvent),
				duration,
				roll,
				lineItemId,
				isSkippable,
			});
		},
	},
	{
		eventName: 'firstFrame',
		callback: ({ event, player }) => {
			const firstFrameEvent = event as FirstFrameEvent;
			const currentItem = player.getPlaylistItem();
			const ctx = currentItem?.custom?.ctx as CTX;

			const meta = getNewRelicVideoMetaData(ctx);

			sendReumEvent('videoStart', {
				...meta,
				isViewable: getIsViewable(firstFrameEvent),
				loadTime: firstFrameEvent.loadTime,
			});
		},
	},
	{
		eventName: 'complete',
		callback: ({ player }) => {
			const currentItem = player.getPlaylistItem();
			const ctx = currentItem?.custom?.ctx as CTX;

			const meta = getNewRelicVideoMetaData(ctx);

			sendReumEvent('videoComplete', {
				...meta,
			});
		},
	},
	{
		eventName: 'adSkipped',
		callback: ({ player }) => {
			const currentItem = player.getPlaylistItem();
			const ctx = currentItem?.custom?.ctx as CTX;

			const meta = getNewRelicVideoMetaData(ctx);

			sendReumEvent('videoAdSkip', {
				...meta,
			});
		},
	},
	{
		eventName: 'playlistItem',
		callback: async ({ event, player, isFirstPlayRef, user, isMobile }) => {
			if (isFirstPlayRef?.current) return;

			const { item } = event as { item: PlaylistItem };
			const ctx = item?.custom?.ctx as CTX;
			ctx.videoIndex = player.getPlaylistIndex();

			player.stop();
			await prerollAd({ player, user, ctx, isMobile });
		},
	},
	{
		eventName: 'adComplete',
		callback: async ({ player }) => {
			player.play();
		},
	},
	{
		eventName: 'adError',
		callback: async ({ event, player }) => {
			const adErrorEvent = event as AdErrorEvent;
			const currentItem = player.getPlaylistItem();
			const ctx = currentItem?.custom?.ctx as CTX;

			const meta = getNewRelicVideoMetaData(ctx);

			sendReumEvent('videoAdError', {
				...meta,
				message: adErrorEvent.message,
				code: adErrorEvent.code,
				vastCode: adErrorEvent.vastCode,
				roll: adErrorEvent.adposition,
			});

			logger.error('Ad error occurred', event);
			player.play();
		},
	},
	{
		eventName: 'error',
		callback: async ({ event, player }) => {
			const errorEvent = event as ErrorEvent;
			const currentItem = player.getPlaylistItem();
			const ctx = currentItem?.custom?.ctx as CTX;

			const meta = getNewRelicVideoMetaData(ctx);

			sendReumEvent('videoError', {
				...meta,
				message: errorEvent.message,
				code: errorEvent.code,
			});
			logger.error('Video Player error', event);
		},
	},
	{
		eventName: 'setupError',
		callback: ({ event }) => {
			const setupErrorEvent = event as SetupErrorEvent;

			sendReumEvent('videoSetupError', {
				message: setupErrorEvent.message,
				code: setupErrorEvent.code,
			});
		},
	},
];
