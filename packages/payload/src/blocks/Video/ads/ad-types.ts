/**
 * Type definitions for ad-related events and data
 */

import { type CTX } from '@repo/jw-player/types/ctx';
import { JWPlayerInstance } from '@repo/jw-player/types/player';
import { User } from '@repo/user/hooks/useUser';

export interface PrerollAdProps {
	player: JWPlayerInstance;
	user: User;
	ctx?: CTX;
	isMobile?: boolean;
}

/**
 * IMA Ad Data structure
 */
export type ImaAdData = {
	adSystem?: string;
	adId?: string;
	adWrapperSystems?: string[];
	adWrapperIds?: string[];
};

/**
 * Base event type with common properties
 */
export type BaseEvent = {
	viewable?: boolean;
};

/**
 * Ready event type
 */
export type ReadyEvent = BaseEvent & {
	setupTime?: string;
};

/**
 * BeforePlay event type
 */
export type BeforePlayEvent = BaseEvent & {
	playReason?: string;
};

/**
 * AdRequest event type
 */
export type AdRequestEvent = BaseEvent & {
	tag?: string;
	adposition?: string;
};

/**
 * AdStarted event type
 */
export type AdStartedEvent = BaseEvent & {
	duration?: number;
	adposition?: string;
	ima?: {
		ad?: {
			data?: ImaAdData & {
				skippable?: boolean;
			};
		};
	};
};

/**
 * FirstFrame event type
 */
export type FirstFrameEvent = BaseEvent & {
	loadTime?: number;
};

/**
 * AdError event type
 */
export type AdErrorEvent = BaseEvent & {
	message?: string;
	code?: string;
	vastCode?: string;
	adposition?: string;
};

/**
 * Error event type
 */
export type ErrorEvent = BaseEvent & {
	message?: string;
	code?: string;
};

/**
 * SetupError event type
 */
export type SetupErrorEvent = {
	message?: string;
	code?: string;
};

// IMA ad system names for parsing line item
export const adSystemDfp = 'GDFP';
export const adSystemAdSense = 'AdSense/AdX';
