'use client';

import React, { FC, useId, useMemo, useRef, useEffect } from 'react';
import useSWR from 'swr';
import type { VideoBlockConfig } from '@repo/payload/payload-types';
import type { Video } from '@repo/payload/payload-types';
import { JWPlayer } from '@repo/jw-player/JWPlayer';
import { getVideoThumbnail } from './utils/thumbnail';
import type {
	PlaylistItem,
	Track,
	Playlist,
} from '@repo/jw-player/types/playlist';
import { buildJwPlaylist } from './utils/jwplayer';
import { adEvents } from './ads/ad-events';
import type { VideoEvent } from '@repo/jw-player/types/events';
import { metricEvents } from './metrics/metric-events';
import { useTrackVideoEvent } from '@repo/analytics/mparticle/hooks/useTrackVideoEvent';
import { DebugCollector } from '@repo/payload/components/FrontendAdminHeader/collectors/DebugCollector';
import { hasLength } from '@repo/utils/hasLength';
import { cn } from '@repo/ui/lib/utils';
import { useUser } from '@repo/user/hooks/useUser';
import { useMobileMedia } from '@repo/ui/hooks/useMobileMedia';
import placeholderImage from './placeholder.jpg';

export interface VideoBlockInjectedFields {
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	descriptionAttributes?: Record<string, any>;
}

// Fetcher function for video data
const videoFetcher = async (url: string) => {
	const response = await fetch(url);
	if (!response.ok) {
		throw new Error(`Failed to fetch video data from ${url}`);
	}
	return response.json();
};

export const VideoBlock: FC<VideoBlockConfig & VideoBlockInjectedFields> = ({
	file,
	image,
	title,
	description,
	tracks = [],
	playlist,
	custom = {},
	playerSettings,
	descriptionAttributes,
	videoReference,
	playlistsReference = [],
}) => {
	const playerKey = `video-player-${useId()}`;
	const isFirstPlayRef = useRef(true);

	// Use ref to store previous video data and prevent unnecessary updates
	const prevVideoRef = useRef<PlaylistItem>(null);

	const user = useUser()?.user || {};
	const isMobile = useMobileMedia();

	// Determine the API URL based on videoReference type
	let videoApiUrl = null;

	if (videoReference) {
		if (typeof videoReference === 'string') {
			// if the id is passed in directly
			videoApiUrl = `/api/payload/videos/${videoReference}`;
		} else if (typeof videoReference === 'object') {
			// if the reference object is passed in
			videoApiUrl = videoReference?.id
				? `/api/payload/videos/${videoReference.id}`
				: null;
		}
	}

	// Use the determined URL in useSWR
	const { data: videoData, error: videoError } = useSWR(
		videoApiUrl,
		videoFetcher,
		{
			revalidateOnFocus: false,
			dedupingInterval: 300000, // 5 minutes
		},
	);

	// mParticle metrics callback to track various video events
	const trackVideoEvent = useTrackVideoEvent();

	// Create tracks array from either the array or a single track
	// Tracks are used for Captions
	const tracksArray = useMemo(() => {
		const result: Track[] = [];
		if (!Array.isArray(tracks) && tracks?.file) {
			result.push(tracks as Track);
		} else if (Array.isArray(tracks)) {
			result.push(...tracks);
		}
		return result;
	}, [tracks]);

	// Check if we have video data from the API or if videoReference is already an object
	const isVideoObject = useMemo(() => {
		return (
			(videoData && Object.keys(videoData).length > 0) ||
			(videoReference && typeof videoReference !== 'string')
		);
	}, [videoData, videoReference]);

	// Get the actual video object to use
	const effectiveVideoObject = useMemo(() => {
		if (videoData && Object.keys(videoData).length > 0) {
			return videoData;
		}
		if (videoReference && typeof videoReference !== 'string') {
			return videoReference as Video;
		}
		return null;
	}, [videoData, videoReference]);

	// Use video reference data if available
	const effectiveFile = useMemo(() => {
		if (
			isVideoObject &&
			effectiveVideoObject?.content?.videoFormatUrls?.length > 0
		) {
			// Use the default format if specified, otherwise use the first format
			if (effectiveVideoObject?.content?.defaultFormat?.url) {
				return effectiveVideoObject?.content?.defaultFormat?.url;
			}
			return effectiveVideoObject?.content?.videoFormatUrls?.[0]?.url;
		}
		return file;
	}, [isVideoObject, effectiveVideoObject, file]);

	const effectiveImage = useMemo(() => {
		if (isVideoObject) {
			return (
				effectiveVideoObject?.content?.keyFrameImage ||
				effectiveVideoObject?.seo?.image?.url ||
				image
			);
		}
		return image;
	}, [isVideoObject, effectiveVideoObject, image]);

	const effectiveTitle = useMemo(() => {
		if (isVideoObject) {
			return effectiveVideoObject?.title || title;
		}
		return title;
	}, [isVideoObject, effectiveVideoObject, title]);

	const effectiveDescription = useMemo(() => {
		if (isVideoObject && effectiveVideoObject?.description) {
			// This is a rich text field, so we'd need to extract plain text
			// For now, use the first paragraph and fallback to the provided description

			return (
				effectiveVideoObject.description.root?.children[0]?.children[0]?.text ||
				description
			);
		}
		return description;
	}, [isVideoObject, effectiveVideoObject, description]);

	// Memoize the video object creation
	const video = useMemo(() => {
		// Check if relevant props have changed
		const currentVideo = {
			file: effectiveFile,
			image: effectiveImage,
			title: effectiveTitle,
			description: effectiveDescription,
			tracks: tracksArray,
			playlist,
			custom,
		};

		if (
			prevVideoRef.current &&
			JSON.stringify(currentVideo) === JSON.stringify(prevVideoRef.current)
		)
			return prevVideoRef.current;

		const thumbnail =
			effectiveImage ||
			(effectiveFile ? getVideoThumbnail(effectiveFile) : null) ||
			placeholderImage.src;

		const newVideo = {
			file: effectiveFile,
			image: thumbnail,
			title: effectiveTitle,
			description: effectiveDescription,
			tracks: tracksArray,
			custom,
		} as PlaylistItem;

		prevVideoRef.current = newVideo;
		return newVideo;
	}, [
		effectiveFile,
		effectiveImage,
		effectiveTitle,
		effectiveDescription,
		tracksArray,
		playlist,
		custom,
	]);

	// Memoize the playlist with ref check
	const jwplaylist = useMemo(() => {
		let basePlaylist: Playlist = [];

		// First, handle the direct playlist field
		if (playlist) {
			basePlaylist = buildJwPlaylist(playlist, video) || [];
		}

		// Then, handle playlist references if available
		if (playlistsReference && playlistsReference.length > 0) {
			// For each content query reference, we would ideally fetch the content
			// and add it to the playlist. For now, we'll just use what we have.
			// In a real implementation, this would involve fetching the content queries
			// and processing their content.
			// This is a placeholder for future implementation
			// The actual implementation would depend on how content queries are structured
			// and how they should be converted to playlist items
		}

		// If videoReference has playlists, add those too
		if (
			isVideoObject &&
			effectiveVideoObject?.content?.playlists &&
			Array.isArray(effectiveVideoObject?.content?.playlists) &&
			effectiveVideoObject?.content?.playlists?.length > 0
		) {
			// Similar to above, this would involve processing the playlists from the video reference
			// This is a placeholder for future implementation
		}

		return basePlaylist.length > 0 ? basePlaylist : [video];
	}, [
		playlist,
		video,
		playlistsReference,
		effectiveVideoObject,
		isVideoObject,
	]);

	// Reset first play flag when playlist changes
	useEffect(() => {
		isFirstPlayRef.current = true;
	}, [jwplaylist]);

	// Show loading state when fetching video data
	if (
		videoReference &&
		typeof videoReference === 'string' &&
		!videoData &&
		!videoError
	) {
		return (
			<div className="flex h-32 items-center justify-center bg-gray-100">
				<div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-gray-900" />
			</div>
		);
	}

	// Show error state if video data fetch failed
	if (videoError) {
		return (
			<div className="flex h-32 items-center justify-center bg-red-100 text-red-800">
				<span>Failed to load video data</span>
			</div>
		);
	}

	if (!effectiveFile) {
		return null;
	}

	// Transform VideoEvents to include isFirstPlayRef
	const videoEvents = [...adEvents, ...metricEvents];
	const events = videoEvents.map(
		({ eventName, callback }): VideoEvent => ({
			eventName,
			callback: ({ event, player }) =>
				callback({
					event,
					player,
					isFirstPlayRef,
					user,
					trackVideoEvent,
					isMobile,
				}),
		}),
	);

	return (
		<>
			<DebugCollector
				componentName="VideoBlock"
				data={{
					props: {
						file,
						image,
						title,
						description,
						tracksCount: hasLength(tracksArray),
						hasPlaylist: !!playlist && playlist.length > 0,
						custom,
					},
					state: {
						videoObject: video,
						playlistItems: jwplaylist.length,
						isFirstPlay: isFirstPlayRef.current,
						events: events.map((e) => e.eventName),
					},
					performance: {
						// Could add performance metrics here if needed
					},
				}}
			/>
			<JWPlayer key={playerKey} playlist={jwplaylist} events={events} />
			{playerSettings?.showDescriptions && (
				<p
					{...descriptionAttributes}
					className={cn('my-2 text-sm', descriptionAttributes?.className)}
				>
					{video.title}
				</p>
			)}
		</>
	);
};

export default VideoBlock;
