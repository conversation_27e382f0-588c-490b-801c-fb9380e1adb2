# Video Block

The Video Block is a PayloadCMS block that allows content editors to add video content to pages and articles. It integrates with the JWPlayer component to provide a rich video playback experience.

## Features

- JWPlayer integration for video playback
- Support for single videos and playlists
- Automatic thumbnail generation
- Caption/subtitle support
- Configurable player settings
- Standardized error handling and logging
- Debug data collection for monitoring and troubleshooting

## Configuration

The Video Block is defined in the PayloadCMS schema with the following configuration options:

```typescript
interface VideoBlockConfig {
	file: string; // URL to the video file (required)
	playlist?: string | null; // Optional playlist configuration
	image?: string | null; // Optional custom thumbnail image
	title?: string | null; // Video title
	description?: string | null; // Video description
	tracks?: {
		// Caption/subtitle tracks
		file?: string | null; // URL to the track file
		label?: string | null; // Display label for the track
		kind?: ("captions" | "chapters" | "thumbnails") | null;
		default?: boolean | null; // Whether this is the default track
	};
	playerSettings?: {
		// Player display settings
		aspectRatio?: ("16:9" | "4:3" | "1:1") | null;
		width?: string | null; // Width of the player
		showDescriptions?: boolean | null; // Whether to show descriptions
	};
	id?: string | null; // Block ID
	blockName?: string | null; // Block name
	blockType: "Video"; // Block type identifier
}
```

## Usage in PayloadCMS Admin

Content editors can add a Video Block to a page or article and configure:

1. The video file URL
2. An optional custom thumbnail image
3. Title and description
4. Caption/subtitle tracks
5. Player settings like aspect ratio

## Implementation

The Video Block is implemented with the following key files:

### VideoBlock.tsx

The main component that renders the video player. It:

1. Processes the video data from PayloadCMS with memoization for performance
2. Generates thumbnails using the `getVideoThumbnail` utility with fallback handling
3. Builds a playlist using the `buildJwPlaylist` utility with reference checks
4. Registers event handlers for ads and metrics
5. Implements standardized error handling with debugLogger
6. Renders the JWPlayer component with the processed data

```tsx
export const VideoBlock: FC<VideoBlockConfig> = ({
	file,
	image,
	title,
	description,
	tracks,
	playlist,
}) => {
	const playerKey = `video-player-${useId()}`;

	// Use ref to store previous video data and prevent unnecessary updates
	const prevVideoRef = useRef<PlaylistItem>(null);

	// Memoize the video object creation
	const video = useMemo(() => {
		// Check if relevant props have changed
		const currentVideo = {
			file,
			image,
			title,
			description,
			tracks,
			playlist,
		};

		if (
			prevVideoRef.current &&
			JSON.stringify(currentVideo) === JSON.stringify(prevVideoRef.current)
		)
			return prevVideoRef.current;

		// Create tracks array from either the array or a single track
		const tracksArray: Track[] = [];
		if (!Array.isArray(tracks) && tracks?.file) {
			tracksArray.push(tracks as Track);
		} else if (Array.isArray(tracks)) {
			tracksArray.push(...tracks);
		}

		const thumbnail = image || getVideoThumbnail(file) || "/placeholder.jpg";

		const newVideo = {
			file,
			image: thumbnail,
			title,
			description,
			tracks: tracksArray,
		} as PlaylistItem;

		prevVideoRef.current = newVideo;
		return newVideo;
	}, [file, image, title, description, tracks, playlist]);

	// Memoize the playlist with ref check
	const jwplaylist = useMemo(() => {
		const newPlaylist = buildJwPlaylist(playlist || [], video) || [];
		return newPlaylist;
	}, [playlist, video]);

	if (!file) {
		return null;
	}

	// Transform VideoEvents to include isFirstPlayRef
	const videoEvents = [...adEvents, ...metricEvents];
	const events = videoEvents.map(
		({ eventName, callback }): VideoEvent => ({
			eventName,
			callback: (event: unknown, player: JWPlayerInstance) =>
				callback({
					event,
					player,
					isFirstPlayRef,
				}),
		}),
	);

	return (
		<>
			<DebugCollector
				componentName="VideoBlock"
				data={{
					props: {
						file,
						image,
						title,
						description,
						tracksCount: tracks
							? Array.isArray(tracks)
								? tracks.length
								: 1
							: 0,
						hasPlaylist: !!playlist && playlist.length > 0,
					},
					state: {
						videoObject: video,
						playlistItems: jwplaylist.length,
						isFirstPlay: isFirstPlayRef.current,
						events: events.map((e) => e.eventName),
					},
				}}
			/>
			<JWPlayer key={playerKey} playlist={jwplaylist} events={events} />
		</>
	);
};
```

### Utils

#### thumbnail.ts

Utility for generating video thumbnails from JW Player video URLs:

```typescript
export const getVideoThumbnail = (videoUrl: string): string => {
	if (!videoUrl) return PLACEHOLDER_IMAGE;

	// Extract video ID from common JW Player URL patterns
	const patterns = [
		/(?:cdn\.jwplayer\.com\/(?:videos|manifests|previews)\/)([a-zA-Z0-9]+)(?:-[a-zA-Z0-9]+)?/,
		/(?:content\.jwplatform\.com\/(?:videos|manifests)\/)([a-zA-Z0-9]+)(?:-[a-zA-Z0-9]+)?/,
		/(?:players\.jwplayer\.com\/preview\/)([a-zA-Z0-9]+)(?:-[a-zA-Z0-9]+)?/,
	];

	for (const pattern of patterns) {
		const match = videoUrl.match(pattern);
		if (match && match[1]) {
			return `https://cdn.jwplayer.com/v2/media/${match[1]}/poster.jpg`;
		}
	}

	return PLACEHOLDER_IMAGE;
};
```

#### jwplayer.ts

Utility for building JW Player playlists with improved error handling:

```typescript
export const buildJwPlaylist = (
	playlist: unknown,
	video: PlaylistItem,
): Playlist => {
	if (!video?.file) return [];

	// TODO: Update type check once we support playlists as a pointer to a collection
	// TODO: If it is a pointer, generate jwplaylist from the collection
	if (typeof playlist === "string") {
		if (!video?.file) return []; // if there is no valid video
		return [video] as Playlist;
	}

	// TODO: Remove this once we remove dsx support
	// TODO: Once we have collection pointer support,
	// TODO: this only gets used if playlist is not a pointer
	// Cast playlist to array type
	const existingPlaylist = (
		Array.isArray(playlist) ? playlist : []
	) as Playlist;

	// if there is no valid video
	if (!video?.file) return existingPlaylist;

	// Check if video is already first in playlist
	if (
		existingPlaylist.length > 0 &&
		existingPlaylist[0]?.file === video?.file
	) {
		return existingPlaylist;
	}

	// Create new playlist with video as first item
	return [
		video,
		...existingPlaylist.filter((item) => item.file !== video.file),
	];
};
```

### VideoAdminBlock.tsx

The admin component that renders the video player in the PayloadCMS admin interface:

```tsx
const VideoAdminBlock: FC = () => {
	const playerKey = `video-player-${useId()}`;

	const [fields] = useAllFormFields();
	// Use useRef to store the previous formData and prevent unnecessary updates
	const prevFormDataRef = useRef<Data>(null);

	const formData = useMemo(() => {
		const newFormData = reduceFieldsToValues(fields, true);
		// Only update if relevant fields have changed
		if (
			prevFormDataRef.current &&
			JSON.stringify(newFormData) === JSON.stringify(prevFormDataRef.current)
		)
			return prevFormDataRef.current;

		prevFormDataRef.current = newFormData;
		return newFormData;
	}, [fields]);

	const { file, image, title, description, tracks, playlist } = formData;

	// Process video data and render JWPlayer
	// ...
};
```

## Integration with JWPlayer Component

The Video Block uses the JWPlayer component (`apps/web/components/JWPlayer`) to render videos. The integration works as follows:

1. The Video Block processes the video data from PayloadCMS with memoization for performance
2. It creates a PlaylistItem object with the video file, thumbnail, title, description, and tracks
3. It builds a playlist using the `buildJwPlaylist` utility with reference checks
4. It renders the JWPlayer component with the playlist, using a unique key for proper React reconciliation

## Testing

The Video Block utilities have unit tests:

- `thumbnail.test.ts`: Tests the thumbnail generation logic
- `jwplayer.test.ts`: Tests the playlist building logic

To run the tests:

```bash
pnpm --filter web test -- -t "Video"
```

## Performance Optimizations

The Video Block includes several performance optimizations:

### 1. Memoization with Reference Checks

Both the `VideoBlock` and `VideoAdminBlock` components use memoization with reference checks to prevent unnecessary re-renders:

```typescript
// Use ref to store previous video data
const prevVideoRef = useRef<PlaylistItem>(null);

// Memoize the video object creation
const video = useMemo(() => {
	// Check if relevant props have changed
	const currentVideo = {
		/* ... */
	};

	if (
		prevVideoRef.current &&
		JSON.stringify(currentVideo) === JSON.stringify(prevVideoRef.current)
	)
		return prevVideoRef.current;

	// Create new video object only if needed
	// ...
}, [file, image, title, description, tracks, playlist]);
```

### 2. Efficient Playlist Building

The `buildJwPlaylist` utility includes optimizations to avoid unnecessary work:

- Early return if no valid video is provided
- Reuse existing playlist when possible
- Check if video is already in the correct position

### 3. Unique React Keys

The component uses generated unique IDs for React keys to ensure proper component reconciliation:

```typescript
const playerKey = `video-player-${useId()}`;

return (
  <JWPlayer
    key={playerKey}
    playlist={jwplaylist}
  />
);
```

## Error Handling and Logging

The Video Block implements standardized error handling and logging using the `@repo/logger` package:

### Event-Based Error Handling

The Video Block registers event handlers for various JWPlayer events, including error events:

```typescript
// From ad-events.ts
{
  eventName: 'adError',
  callback: async ({ event, player }) => {
    const logger = createLogger('VideoBlock');
    logger.error('Ad error occurred', event);
    player.play();
  },
},
{
  eventName: 'error',
  callback: async ({ event }) => {
    const logger = createLogger('VideoBlock');
    logger.error('Video Player error', event);
  },
},
```

### Debug Data Collection

The Video Block uses the `DebugCollector` component to collect and expose debug data:

```typescript
<DebugCollector
  componentName="VideoBlock"
  data={{
    props: {
      file,
      image,
      title,
      description,
      tracksCount: tracks
        ? Array.isArray(tracks)
          ? tracks.length
          : 1
        : 0,
      hasPlaylist: !!playlist && playlist.length > 0,
    },
    state: {
      videoObject: video,
      playlistItems: jwplaylist.length,
      isFirstPlay: isFirstPlayRef.current,
      events: events.map((e) => e.eventName),
    },
  }}
/>
```

This data is available in the FrontendAdminHeader debug panel, providing valuable insights for troubleshooting and monitoring.

### Logging Levels

The Video Block uses different logging levels based on the severity and context:

- `logger.error`: For critical errors that affect functionality
- `logger.info`: For informational messages
- `logger.lifecycle`: For component lifecycle events

Control logging output using the DEBUG environment variable:

```bash
# Enable all VideoBlock logs
DEBUG=wx-next:VideoBlock:* pnpm dev

# Enable only error logs
DEBUG=wx-next:*:error pnpm dev

# Enable specific log levels
DEBUG=wx-next:VideoBlock:lifecycle pnpm dev
```

## Future Improvements

As noted in the code comments, there are several planned improvements:

1. Update type checking once we support playlists as a pointer to a collection
2. Generate jwplaylist from the collection when it is a pointer
3. Remove DSX support once collection pointer support is implemented
4. Enhance thumbnail generation for additional video providers
5. Implement additional metric events for video analytics
6. Enhance error recovery mechanisms for failed video playback
