import { url, size } from './field';
import type { Block } from 'payload';

export const YouTubeBlockConfig: Block = {
	slug: 'YouTube',
	interfaceName: 'YouTubeBlockConfig',
	labels: {
		singular: 'YouTube Block',
		plural: 'YouTube Blocks',
	},
	fields: [url, size],
	admin: {
		components: {
			Block: {
				path: '@repo/payload/blocks/admin#YouTubeAdminBlock',
			},
		},
	},
};

export default YouTubeBlockConfig;
