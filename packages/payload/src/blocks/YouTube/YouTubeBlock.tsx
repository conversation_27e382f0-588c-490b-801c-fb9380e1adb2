import React from 'react';
import { YouTubeBlockConfig } from '@repo/payload/payload-types';

export const YoutubeEmbed: React.FC<YouTubeBlockConfig> = ({
	url,
	size,
}: YouTubeBlockConfig) => {
	const extractVideoId = (url: string) => {
		const match = url.match(
			/(?:youtube\.com\/(?:[^\\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\\/\s]{11})/,
		);
		return match ? match[1] : null;
	};

	const videoId = extractVideoId(url);
	// TODO: We need this pattern, but not sure what we are going to do with env detection just yet
	if (!videoId) {
		return process.env.NODE_ENV === 'production' ? null : (
			<p className="text-red-500">Invalid YouTube URL</p>
		);
	}

	return (
		<div
			className={`flex aspect-video w-full items-center justify-center ${size === 'hero' ? '' : 'max-w-[640px]'}`}
		>
			<iframe
				className="h-full w-full rounded-lg shadow-lg"
				src={`https://www.youtube.com/embed/${videoId}`}
				title="YouTube Video"
				allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
				allowFullScreen
			></iframe>
		</div>
	);
};

export default YoutubeEmbed;
