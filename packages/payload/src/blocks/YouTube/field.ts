import type { SelectField, TextField } from 'payload';

export const url: TextField = {
	name: 'url',
	label: 'YouTube URL',
	type: 'text',
	required: true,
	admin: {
		placeholder: 'https://www.youtube.com/watch?v=example',
		description: 'Enter the full YouTube URL',
	},
};

export const size: SelectField = {
	name: 'size',
	label: 'Video Size',
	type: 'select',
	options: [
		{ label: 'Hero (Full Width)', value: 'hero' },
		{ label: 'Article (Smaller Embed)', value: 'article' },
	],
	admin: {
		description: 'Choose how the video embed should be displayed.',
	},
	defaultValue: 'article',
};
