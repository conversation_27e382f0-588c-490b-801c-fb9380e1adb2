'use client';

import React, { FC, useEffect, useState } from 'react';
import { useAllFormFields } from '@payloadcms/ui';
import { reduceFieldsToValues } from 'payload/shared';

import AdminBlock from '@repo/payload/components/AdminBlock';

export const YouTubeAdminBlock: FC = () => {
	const [fields] = useAllFormFields();
	const formData = reduceFieldsToValues(fields, true);

	const [videoData, setVideoData] = useState({
		url: formData?.url,
		size: formData?.size,
	});

	useEffect(() => {
		setVideoData({
			url: formData?.url,
			size: formData?.size,
		});
	}, [formData?.url, formData?.size]);

	const extractVideoId = (url: string) => {
		const match = url?.match(
			/(?:youtube\.com\/(?:[^\\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\\/\s]{11})/,
		);
		return match ? match[1] : null;
	};

	const renderVideoPreview = () => {
		const videoId = extractVideoId(videoData.url);
		if (!videoId) {
			return <div className="text-red-500">Invalid YouTube URL</div>;
		}

		return (
			<div className="mb-2 flex justify-center">
				<iframe
					className="w-full rounded-lg shadow-lg"
					style={{
						height: videoData.size === 'hero' ? '300px' : '180px',
						maxWidth: videoData.size === 'hero' ? '100%' : '320px',
					}}
					src={`https://www.youtube.com/embed/${videoId}`}
					title="YouTube Video Preview"
					allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
					allowFullScreen
				></iframe>
			</div>
		);
	};

	return (
		<AdminBlock name="YouTube">
			<div className="mb-2">
				<strong className="text-black-800">Video URL:</strong>{' '}
				<span className="text-gray-600">{videoData.url || 'Not provided'}</span>
			</div>
			<div className="mb-2">
				<strong className="text-black-800">Embed Size:</strong>{' '}
				<span className="text-gray-600">
					{videoData.size === 'hero' ? 'Hero (Full Width)' : 'Article (Small)'}
				</span>
			</div>
			{renderVideoPreview()}
		</AdminBlock>
	);
};

export default YouTubeAdminBlock;
