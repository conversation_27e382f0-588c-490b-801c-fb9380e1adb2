import { Field, TextField, RelationshipField, GroupField } from 'payload';

export const title: TextField = {
	name: 'title',
	type: 'text',
	label: 'Title',
	required: true,
	admin: {
		description:
			'Main heading text for the promo (recommended maximum 25 characters, 1 line)',
	},
};

export const image: RelationshipField = {
	name: 'image',
	type: 'relationship',
	relationTo: 'images',
	label: 'Image',
	hasMany: false,
	required: true,
	admin: {
		description: 'Select an image for the promo',
	},
};

export const subtitle: TextField = {
	name: 'subtitle',
	type: 'text',
	label: 'Subtitle',
	admin: {
		description:
			'Secondary heading text for the promo (recommended maximum 30 characters, 1 line)',
	},
};

export const bodyText: TextField = {
	name: 'bodyText',
	type: 'text',
	label: 'Body Copy',
	admin: {
		description:
			'Main text content for the promo (recommended maximum 65 characters, 2 lines)',
	},
};

export const ctaButton: GroupField = {
	name: 'ctaButton',
	type: 'group',
	label: 'CTA Button',
	admin: {
		description: 'Call-to-action button configuration',
	},
	fields: [
		{
			name: 'text',
			type: 'text',
			label: 'Button Text',
			defaultValue: 'Learn More',
			validate: (value: unknown) => {
				if (!value) return true; // Allow empty value

				if (typeof value !== 'string') {
					return 'Button text must be a string';
				}

				if (value.length > 20) {
					return 'Button text must be 20 characters or less';
				}

				return true;
			},
			admin: {
				description: 'Maximum 20 characters (1 line). This limit is enforced.',
			},
		},
		{
			name: 'url',
			type: 'text',
			label: 'Button URL',
			validate: (value: unknown) => {
				if (!value) return 'URL is required';

				// Handle non-string values
				if (typeof value !== 'string') {
					return 'URL must be a string';
				}

				// Check if it's an external URL
				const isExternalUrl = /^https?:\/\//i.test(value);
				// Check if it's an internal URL starting with slash
				const isInternalUrl = value.startsWith('/');

				if (!isExternalUrl && !isInternalUrl) {
					return 'URL must either be an external URL (starting with http:// or https://) or an internal URL (starting with /)';
				}

				if (isExternalUrl) {
					try {
						new URL(value);
					} catch {
						return 'Please enter a valid URL';
					}
				}

				return true;
			},
			admin: {
				description:
					'Enter an external URL (e.g., https://example.com) or an internal URL starting with a slash (e.g., /about).',
				placeholder: 'https://example.com or /about',
			},
		},
		{
			name: 'openInNewTab',
			type: 'checkbox',
			label: 'Open in New Tab',
			defaultValue: false,
		},
	],
};

export const promoDriverFields: Field[] = [
	title,
	image,
	subtitle,
	bodyText,
	ctaButton,
];
