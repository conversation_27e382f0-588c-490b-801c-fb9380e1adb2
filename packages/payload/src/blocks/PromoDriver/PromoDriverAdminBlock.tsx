import React from 'react';
import type { PromoDriverBlock } from '@repo/payload/payload-types';

export const PromoDriverAdminBlock: React.FC<PromoDriverBlock> = (props) => {
	const { title, subtitle, bodyText, ctaButton } = props;

	return (
		<div className="rounded border border-gray-200 p-4">
			<div className="mb-2 font-bold">{title}</div>
			{subtitle && <div className="mb-1 text-sm">{subtitle}</div>}
			{bodyText && <div className="mb-2 text-xs text-gray-500">{bodyText}</div>}
			{ctaButton && (
				<div className="inline-block rounded bg-gray-800 px-2 py-1 text-xs text-white">
					{ctaButton.text}
				</div>
			)}
		</div>
	);
};

export default PromoDriverAdminBlock;
