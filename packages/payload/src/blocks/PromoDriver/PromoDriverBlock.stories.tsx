import type { Meta, StoryObj } from '@storybook/react';
import PromoGif from '@/assets/gifs/overlay_ani_v.gif';
import PromoDriverBlock from './PromoDriverBlock';
import type { PromoDriverBlock as PromoDriverBlockConfig } from '@repo/payload/payload-types';

// Omit auth-aware props from the type
type SimplifiedPromoDriverProps = Omit<
	PromoDriverBlockConfig,
	'authAware' | 'loggedInContent' | 'loggedOutContent'
>;

const meta: Meta<typeof PromoDriverBlock> = {
	title: 'Components/PromoDriver',
	component: PromoDriverBlock,
	parameters: {
		layout: 'centered',
	},
	tags: ['autodocs'],
	argTypes: {
		blockType: { control: 'text' },
		title: { control: 'text' },
		subtitle: { control: 'text' },
		bodyText: { control: 'text' },
		image: { control: 'object' },
		ctaButton: { control: 'object' },
	},
};

export default meta;
type Story = StoryObj<typeof PromoDriverBlock>;

// Base props for all stories
const baseProps: SimplifiedPromoDriverProps = {
	blockType: 'promoDriver',
	title: 'Weather Premium',
	image: {
		id: 'premium-promo',
		url: PromoGif.src,
		tenant: null,
		seo: {
			altText: 'Weather Premium',
			caption: 'Weather Premium',
			credit: null,
		},
		prefix: null,
		updatedAt: '',
		createdAt: '',
	},
	subtitle: 'Default Subtitle',
	bodyText:
		'Get ad-free radar, extended forecasts, and premium weather features.',
	ctaButton: {
		text: 'Learn More',
		url: '/premium',
		openInNewTab: false,
	},
};

// Standard promo
export const Standard: Story = {
	args: {
		...baseProps,
	},
	decorators: [
		(Story) => (
			<div className="max-w-sm">
				<Story />
			</div>
		),
	],
};

export const NoImage: Story = {
	args: {
		...baseProps,
		image: undefined,
	},
	decorators: [
		(Story) => (
			<div className="max-w-sm">
				<Story />
			</div>
		),
	],
};

export const NoTitle: Story = {
	args: {
		...baseProps,
		title: undefined,
	},
	decorators: [
		(Story) => (
			<div className="max-w-sm">
				<Story />
			</div>
		),
	],
};
