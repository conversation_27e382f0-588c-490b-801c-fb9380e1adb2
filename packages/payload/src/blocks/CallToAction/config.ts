import { ctaFields } from './field';
import type { Block } from 'payload';

export const CallToActionBlockConfig: Block = {
	slug: 'CallToAction',
	interfaceName: 'CallToActionBlockConfig',
	labels: {
		singular: 'Call To Action Block',
		plural: 'Call To Action Blocks',
	},
	fields: ctaFields,
	admin: {
		components: {
			Block: {
				path: '@repo/payload/blocks/admin#CallToActionAdminBlock',
			},
		},
	},
};

export default CallToActionBlockConfig;
