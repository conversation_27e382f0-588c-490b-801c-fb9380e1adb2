'use client';

import React, { FC } from 'react';
import ImageComponent from '@repo/ui/components/Image/Image';
import type {
	CallToActionBlockConfig,
	Image,
} from '@repo/payload/payload-types';

interface CTABlockProps extends Omit<CallToActionBlockConfig, 'blockType'> {}

export const CTABlock: FC<CTABlockProps> = ({
	title,
	subTitle,
	subText,
	ctaStyle,
	media,
	buttonText,
	linkUrl,
	openInNewTab,
	backgroundColor,
}: CTABlockProps) => {
	const isImageObject = (
		media: CallToActionBlockConfig['media'],
	): media is Image => {
		return (media as Image).url != undefined;
	};

	const imageUrl = isImageObject(media)
		? media.url
		: typeof media === 'string'
			? media
			: null;

	const backgroundColorMap = {
		white: 'bg-white',
		'light-gray': 'bg-gray-100',
		'brand-blue': 'bg-blue-500',
		accent: 'bg-accent',
	};

	return (
		<section
			className={`@container not-prose mx-auto w-full max-w-4xl rounded-md ${backgroundColorMap[backgroundColor || 'white']} p-5 shadow-2xl`}
		>
			<div className={`cta-block ${ctaStyle}`}>
				<div className="cta-content text-black">
					<h2 className="mb-6 text-2xl font-bold">{title}</h2>
					{imageUrl && (
						<a
							href={linkUrl}
							target={openInNewTab ? '_blank' : '_self'}
							rel={openInNewTab ? 'noopener noreferrer' : undefined}
							className="mb-4 block overflow-hidden rounded-lg"
						>
							<div className="relative w-full">
								<ImageComponent src={imageUrl} alt={title} />
							</div>
						</a>
					)}
					{subTitle && <h2 className="mb-2 text-lg font-bold">{subTitle}</h2>}
					{subText && <p className="mb-4">{subText}</p>}

					{ctaStyle === 'standard' && (
						<div className="mt-4 flex justify-end">
							<a
								href={linkUrl}
								target={openInNewTab ? '_blank' : '_self'}
								rel={openInNewTab ? 'noopener noreferrer' : undefined}
								className="inline-block rounded-lg bg-black px-6 py-3 text-white transition-colors hover:bg-gray-800"
							>
								{buttonText || 'Learn More'}
							</a>
						</div>
					)}
				</div>
			</div>
		</section>
	);
};

export default CTABlock;
