'use client';

import React, { FC } from 'react';
import AdminBlock from '@repo/payload/components/AdminBlock';
import { reduceFieldsToValues } from 'payload/shared';
import { useAllFormFields, usePayloadAPI } from '@payloadcms/ui';
import type { Image } from '@repo/payload/payload-types';
import NextImage from 'next/image';

export const CallToActionAdminBlock: FC = () => {
	const [fields] = useAllFormFields();
	const formData = reduceFieldsToValues(fields, true);

	let internalImgData: Image | null = null;
	[{ data: internalImgData }] = usePayloadAPI(
		formData.media ? `/api/payload/images/${formData.media}` : '',
	);

	const imgSrc: string = internalImgData?.url ? internalImgData?.url : '';

	const renderCta = () => {
		if (!formData?.title) {
			return (
				<div className="flex h-32 items-center justify-center border border-dashed border-gray-300">
					<span>CTA is empty.</span>
				</div>
			);
		}

		return (
			<div className="space-y-4 rounded-lg p-4 shadow-sm">
				{/* Content Preview */}
				<div className="mb-2">
					<div>
						<span className="text-black-800">Title:</span>
						<div className="text-gray-600">{formData.title}</div>
					</div>

					{formData.subTitle && (
						<div>
							<span className="font-semibold">Subtitle:</span>
							<div className="text-gray-700">{formData.subTitle}</div>
						</div>
					)}

					{formData.subText && (
						<div className="col-span-2">
							<span className="font-semibold">Sub Text:</span>
							<div className="text-gray-700">{formData.subText}</div>
						</div>
					)}

					<div>
						<span className="font-semibold">CTA Style:</span>
						<div className="text-gray-700">{formData.ctaStyle}</div>
					</div>

					<div>
						<span className="font-semibold">Background:</span>
						<div className="text-gray-700">
							{formData.backgroundColor || 'white'}
						</div>
					</div>

					{formData.buttonText && (
						<div>
							<span className="font-semibold">Button Text:</span>
							<div className="text-gray-700">{formData.buttonText}</div>
						</div>
					)}

					<div>
						<span className="font-semibold">Link URL:</span>
						<div className="truncate text-gray-700">{formData.linkUrl}</div>
					</div>

					<div>
						<span className="font-semibold">Opens in:</span>
						<div className="text-gray-700">
							{formData.openInNewTab ? 'New Tab' : 'Same Tab'}
						</div>
					</div>
				</div>

				{imgSrc && (
					<NextImage
						src={imgSrc}
						alt=""
						className="max-h-[240px] max-w-[240px]"
					/>
				)}
			</div>
		);
	};

	return (
		<AdminBlock name={formData?.title ?? 'Empty CTA Block'}>
			{renderCta()}
		</AdminBlock>
	);
};
export default CallToActionAdminBlock;
