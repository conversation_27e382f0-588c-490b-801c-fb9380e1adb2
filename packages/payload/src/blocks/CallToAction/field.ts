import {
	Field,
	SelectField,
	TextField,
	RelationshipField,
	CheckboxField,
	ArrayField,
} from 'payload';

export const title: TextField = {
	name: 'title',
	type: 'text',
	label: 'Title',
	required: true,
	admin: {
		description: 'Main heading text for the CTA',
	},
};

export const subTitle: TextField = {
	name: 'subTitle',
	type: 'text',
	label: 'Sub Title',
};

export const subText: TextField = {
	name: 'subText',
	type: 'text',
	label: 'Sub Text',
};

export const ctaStyle: SelectField = {
	name: 'ctaStyle',
	type: 'select',
	label: 'CTA Style',
	defaultValue: 'standard',
	options: [
		{ label: 'Standard', value: 'standard' },
		{ label: 'Featured', value: 'featured' },
	],
	required: true,
	admin: {
		description:
			"Standard contains a button to click such as 'see more' where as featured only contains the image and is clickable",
	},
};

export const media: RelationshipField = {
	name: 'media',
	type: 'relationship',
	relationTo: 'images',
	label: 'Media',
	hasMany: false,
	admin: {
		description: 'Select an image, SVG, or animated image',
	},
};

export const buttonText: TextField = {
	name: 'buttonText',
	type: 'text',
	label: 'Button Text',
	admin: {
		condition: (data, siblingData) =>
			Boolean(siblingData?.ctaStyle === 'standard'),
	},
};

export const linkUrl: TextField = {
	name: 'linkUrl',
	type: 'text',
	label: 'Link URL',
	required: true,
	validate: (value) => {
		if (!value) return 'URL is required';
		// Check if it's an external URL
		const isExternalUrl = /^https?:\/\//i.test(value);
		// Check if it's an internal URL starting with slash
		const isInternalUrl = value.startsWith('/');
		if (!isExternalUrl && !isInternalUrl) {
			return 'URL must either be an external URL (starting with http:// or https://) or an internal URL (starting with /)';
		}
		if (isExternalUrl) {
			try {
				new URL(value);
			} catch (_error) {
				return 'Please enter a valid URL';
			}
		}
		return true;
	},
	admin: {
		description:
			'Enter an external URL (e.g., https://example.com) or an internal URL starting with a slash (e.g., /about).',
		placeholder: 'https://example.com or /about',
	},
};

export const openInNewTab: CheckboxField = {
	name: 'openInNewTab',
	type: 'checkbox',
	label: 'Open in New Tab',
	defaultValue: false,
};

export const backgroundColor: SelectField = {
	name: 'backgroundColor',
	type: 'select',
	label: 'Background Color',
	defaultValue: 'white',
	options: [
		{ label: 'White', value: 'white' },
		{ label: 'Light Gray', value: 'light-gray' },
		{ label: 'Brand Blue', value: 'brand-blue' },
		{ label: 'Accent', value: 'accent' },
	],
};

export const analyticsTags: ArrayField = {
	name: 'analyticsTags',
	type: 'array',
	label: 'Analytics Tags',
	fields: [
		{
			name: 'tag',
			type: 'text',
			label: 'Tag',
		},
	],
	admin: {
		description: 'Add tracking tags for analytics',
	},
};

export const ctaFields: Field[] = [
	title,
	subTitle,
	subText,
	ctaStyle,
	media,
	buttonText,
	linkUrl,
	openInNewTab,
	backgroundColor,
	analyticsTags,
];
