import type { Field, RelationshipField, TextField } from 'payload';
import { contextParameterOverrides } from '../../fields/common/contextParameterOverridesField';

export const adId: TextField = {
	name: 'adId',
	type: 'text',
	label: 'Ad ID',
	required: true,
};

export const adSlot: RelationshipField = {
	name: 'adSlot',
	type: 'relationship',
	relationTo: 'ad-slots',
	label: 'Ad Slot',
	admin: {
		description: 'Select an ad slot to use for this ad block',
	},
};

export const adFields: Field[] = [adId, adSlot, contextParameterOverrides];
