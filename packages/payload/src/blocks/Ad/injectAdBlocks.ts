import {
	SerializedEditorState,
	SerializedLexicalNode,
} from '@payloadcms/richtext-lexical/lexical';

const MIN_PARAGRAPHS_FOR_AD = 6; // min. num of paragraphs in article body required to insert a Mobile Web Position3 (p3) ad
const NEWS_BRIEF_INSERTION_POINT = 3;

export function injectAdBlocks(
	data: SerializedEditorState | null,
): SerializedEditorState | null {
	const updatedChildren: SerializedLexicalNode[] = [];

	if (!data) {
		return data;
	}

	let paragraphCounter = 0;

	/**
	 * Determining length of article in order to place Mobile Web Position3 (p3) ad.
	 * If an article is 'long' (num of paragraphs is >=6), place p3 ad in the middle of the paragraphs
	 */
	const totalNumberOfParagraphs = data.root.children.reduce(
		(count, child) => (child.type === 'paragraph' ? count + 1 : count),
		0,
	);

	const isLongArticle = totalNumberOfParagraphs >= MIN_PARAGRAPHS_FOR_AD;
	const adInsertionPoint = isLongArticle
		? Math.round(totalNumberOfParagraphs / 2)
		: -1;

	data.root.children.forEach((child) => {
		updatedChildren.push(child);
		if (child.type === 'paragraph') {
			paragraphCounter++;

			if (isLongArticle && paragraphCounter === adInsertionPoint) {
				updatedChildren.push({
					format: '',
					type: 'block',
					version: 2,
					fields: {
						blockType: 'Ad',
						blockName: 'ad',
						adId: 'MW_Position3',
						title: 'Advertisement',
					},
				} as SerializedLexicalNode);
			}

			if (paragraphCounter === NEWS_BRIEF_INSERTION_POINT) {
				updatedChildren.push({
					format: '',
					type: 'block',
					version: 2,
					fields: {
						blockType: 'MorningBrief',
						blockName: 'morningBrief',
					},
				} as SerializedLexicalNode);
			}
		}
	});

	data.root.children = updatedChildren;
	return data;
}
