import { adFields } from './field';
import type { Block } from 'payload';

export const AdBlockConfig: Block = {
	slug: 'Ad',
	interfaceName: 'AdBlockConfig',
	labels: {
		singular: 'Ad Block',
		plural: 'Ad Blocks',
	},
	fields: adFields,
	// Component to render in the admin interface
	admin: {
		components: {
			Block: {
				path: '@repo/payload/blocks/admin#AdAdminBlock',
			},
		},
	},
};

export default AdBlockConfig;
