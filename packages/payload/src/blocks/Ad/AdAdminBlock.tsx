'use client';

import React, { FC } from 'react';
import { useAllFormFields } from '@payloadcms/ui';
import { reduceFieldsToValues } from 'payload/shared';

import AdminBlock from '@repo/payload/components/AdminBlock';

export const AdAdminBlock: FC = () => {
	const [fields] = useAllFormFields();
	const formData = reduceFieldsToValues(fields, true);

	const renderID = () => {
		return formData?.adId || 'No ID';
	};

	return (
		<AdminBlock name="Ad">
			<span>Ad ID: {renderID()}</span>
		</AdminBlock>
	);
};

export default AdAdminBlock;
