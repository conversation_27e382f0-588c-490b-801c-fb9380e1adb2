import { slideshowFields } from './field';
import type { Block } from 'payload';

export const SlideshowBlockConfig: Block = {
	slug: 'Slideshow',
	interfaceName: 'SlideshowBlockConfig',
	labels: {
		singular: 'Slideshow Block',
		plural: 'Slideshow Blocks',
	},
	fields: slideshowFields,
	// Component to render in the admin interface
	admin: {
		components: {
			Block: {
				path: '@repo/payload/blocks/admin#SlideshowAdminBlock',
			},
		},
	},
};

export default SlideshowBlockConfig;
