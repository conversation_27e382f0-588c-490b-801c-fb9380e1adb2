'use client';
import type { SlideshowBlockConfig, Image } from '@repo/payload/payload-types';
import React, { useState, useEffect, useCallback, useId } from 'react';
import { SlideshowView } from './components/SlideshowView';
import { PAGE_LIMIT, THRESHOLD } from './constants';

/**
 * Container component that handles data and state management for the Slideshow
 */
export const Slideshow: React.FC<
	SlideshowBlockConfig & {
		// UI-specific props
		assets?: Image[];
		allSlides?: Image[]; // Add this prop for all pre-loaded slides
		totalAssets?: number;
		loadNextSlideshowPage?: (params: {
			assetId: string;
			offset: number;
		}) => void;
		sendTrackString?: (trackString: string) => void;
		rounded?: boolean;
	}
> = ({
	id,
	assets = [],
	allSlides = [], // All pre-loaded slides (373)
	totalAssets = allSlides.length || 0,
	loadNextSlideshowPage,
	sendTrackString = () => {},
	rounded = false,
}) => {
	// Generate a stable, unique ID using <PERSON>act's useId hook
	const generatedId = useId();
	// Ensure id is always a string
	const safeId = id || `slideshow-${generatedId}`;

	// Determine if we're using pre-loaded slides
	const usePreloadedSlides = allSlides.length > 0;

	// State management
	const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
	const [currentPage, setCurrentPage] = useState(1);
	const [loadedPages, setLoadedPages] = useState(1);
	const [visibleAssets, setVisibleAssets] = useState<Image[]>(
		usePreloadedSlides ? allSlides.slice(0, PAGE_LIMIT) : assets,
	);

	// Viewport detection
	const inViewPort = useCallback((node: HTMLElement | null) => {
		if (!node) return false;
		const boundingRect = node.getBoundingClientRect();
		const { top } = boundingRect;

		return top >= -100 && top < 600;
	}, []);

	// Navigation functions
	const previous = useCallback(() => {
		if (currentSlideIndex - 1 >= 0) {
			// Minimum currentPage is 1
			const newPage =
				currentSlideIndex - 1
					? Math.ceil((currentSlideIndex - 1) / PAGE_LIMIT)
					: 1;

			setCurrentSlideIndex(currentSlideIndex - 1);
			setCurrentPage(newPage);

			// Track ad refresh
		}
	}, [currentSlideIndex]);

	const next = useCallback(() => {
		if (currentSlideIndex + 1 < totalAssets) {
			// Update slide index and page
			const newPage = Math.ceil((currentSlideIndex + 1) / PAGE_LIMIT);
			setCurrentSlideIndex(currentSlideIndex + 1);
			setCurrentPage(newPage);

			// If using pre-loaded slides (all slides)
			if (usePreloadedSlides) {
				if (currentPage >= loadedPages) {
					const currentTotalSlides = currentPage * PAGE_LIMIT;

					// If we're approaching the threshold, load next batch from pre-loaded assets
					if (
						currentTotalSlides - THRESHOLD < currentSlideIndex &&
						currentSlideIndex < currentTotalSlides
					) {
						const nextBatchEndIndex = Math.min(
							(loadedPages + 1) * PAGE_LIMIT,
							allSlides.length,
						);
						setVisibleAssets(allSlides.slice(0, nextBatchEndIndex));
						setLoadedPages(loadedPages + 1);
						console.log(
							`Loaded slides 0-${nextBatchEndIndex} from pre-loaded assets`,
						);
					}
				}
			}
			// Original server-side pagination logic
			else if (totalAssets > assets.length) {
				if (currentPage >= loadedPages) {
					const currentTotalSlides = currentPage * PAGE_LIMIT;

					if (
						currentTotalSlides - THRESHOLD < currentSlideIndex &&
						currentSlideIndex < currentTotalSlides &&
						loadNextSlideshowPage
					) {
						// Fetch the next set of slides
						loadNextSlideshowPage({
							assetId: safeId,
							offset: currentTotalSlides,
						});
						setLoadedPages(loadedPages + 1);
					}
				}
			}
		}
	}, [
		currentSlideIndex,
		currentPage,
		loadedPages,
		totalAssets,
		safeId,
		loadNextSlideshowPage,
		usePreloadedSlides,
		allSlides,
		assets.length,
	]);

	// Keyboard navigation
	const handleKeyDown = useCallback(
		(e: KeyboardEvent) => {
			const node = document.querySelector(`#${safeId}`);
			const isInViewPort = inViewPort(node as HTMLElement);

			if (isInViewPort) {
				switch (e.key) {
					case 'ArrowLeft':
						previous();
						break;
					case 'ArrowRight':
						next();
						break;
					default:
						break;
				}
			}
		},
		[safeId, inViewPort, previous, next],
	);

	// Set up event listeners
	useEffect(() => {
		document.body.addEventListener('keydown', handleKeyDown);
		return () => {
			document.body.removeEventListener('keydown', handleKeyDown);
		};
	}, [handleKeyDown]);

	// Initialize visibleAssets if using pre-loaded slides
	useEffect(() => {
		if (usePreloadedSlides && visibleAssets.length === 0) {
			setVisibleAssets(allSlides.slice(0, PAGE_LIMIT));
		}
	}, [usePreloadedSlides, allSlides, visibleAssets.length]);

	return (
		<SlideshowView
			id={safeId}
			assets={usePreloadedSlides ? visibleAssets : assets}
			currentSlideIndex={currentSlideIndex}
			totalAssets={totalAssets}
			previous={previous}
			next={next}
			sendTrackString={sendTrackString}
			rounded={rounded}
		/>
	);
};

export default Slideshow;
