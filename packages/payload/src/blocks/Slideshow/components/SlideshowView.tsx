'use client';

import React from 'react';
import SlideshowIcon from '../assets/SlideshowIcon.png';
import Image from '@repo/ui/components/Image/Image';
import { Text } from '@repo/ui/components/Text/Text';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import type { Image as PayloadImage } from '@repo/payload/payload-types';

// Utility function to check if a string looks like a filename
const isFilename = (str: string | null | undefined): boolean => {
	if (!str || str.trim() === '') return false;

	return (
		// Check for common image extensions
		/\.(jpg|jpeg|png|gif|webp|bmp|tiff|svg)$/i.test(str) ||
		// Check for filename patterns like DCT_SPECIAL42.jpg
		/^[A-Z0-9_]+\d*\.[a-zA-Z]+$/i.test(str) ||
		// Check for other common filename patterns
		/^[a-zA-Z0-9_-]+\d+$/i.test(str) ||
		// Check for any pattern that looks like a filename (contains dots, underscores, or numbers)
		/[._][0-9]+/i.test(str) ||
		// Check for all caps with numbers (likely a filename)
		/^[A-Z0-9_]+$/i.test(str)
	);
};

// Props for the SlideshowView component
export interface SlideshowViewProps {
	id: string;
	assets: PayloadImage[];
	currentSlideIndex: number;
	totalAssets: number;
	previous: () => void;
	next: () => void;
	sendTrackString: (trackString: string) => void;
	rounded?: boolean;
}

/**
 * Presenter component for the Slideshow
 * Handles the UI rendering based on props from the container
 */
export const SlideshowView: React.FC<SlideshowViewProps> = ({
	id,
	assets,
	currentSlideIndex,
	totalAssets,
	previous,
	next,
	sendTrackString,
	rounded = false,
}) => {
	if (
		!assets.length ||
		currentSlideIndex < 0 ||
		currentSlideIndex >= assets.length
	) {
		return null;
	}

	const currentAsset = assets[currentSlideIndex]!;
	const slideCounter = `${currentSlideIndex + 1}/${totalAssets}`;
	const isFirstSlide = currentSlideIndex === 0;
	const isLastSlide = currentSlideIndex === totalAssets - 1;

	return (
		<figure id={id} className="relative mb-4 mt-4 w-full max-w-full">
			{/* Image container with rounded corners if specified */}
			<div className={rounded ? 'overflow-hidden rounded-[16px]' : ''}>
				<div className={`relative w-full ${rounded ? 'rounded-[16px]' : ''}`}>
					<div
						className={`relative w-full ${rounded ? 'rounded-[16px]' : ''}`}
						style={{ paddingTop: '56.25%' }} // 16:9 aspect ratio
					>
						<Image
							className={`absolute left-0 top-0 h-full w-full object-cover`}
							src={currentAsset.url || ''}
							alt={currentAsset.seo?.altText || ''}
							rounded={rounded}
							width={800}
							height={450}
							sizes="(max-width: 767px) 91vw, (max-width: 1280px) 64vw, 800px"
						/>
					</div>
				</div>
			</div>

			{/* Caption and controls */}
			<figcaption>
				<div className="w-full">
					{/* Image controls */}
					<div className="flex items-center pt-2">
						<div className="relative flex w-full items-center">
							{/* Slideshow icon */}
							<div className="flex items-center">
								<Image
									src={SlideshowIcon.src}
									alt="Slideshow"
									width={20}
									height={20}
									className="h-5 w-5"
									rounded={false}
								/>
							</div>
							{/* Slide counter */}
							<div className="ml-2 mr-2">
								<Text variant="Body.S" color="primary">
									{slideCounter}
								</Text>
							</div>

							{/* Spacer to push chevrons to the right */}
							<div className="flex-grow"></div>

							{/* Previous button - now positioned just before the Next button */}
							<button
								type="button"
								aria-label="Previous"
								className="mr-[10px] flex cursor-pointer items-center justify-center border-none bg-transparent p-2 disabled:cursor-not-allowed disabled:opacity-50"
								disabled={isFirstSlide}
								onClick={() => {
									sendTrackString('wxnode_slideshow_link-previous');
									previous();
								}}
							>
								<span className="flex items-center justify-center">
									<span
										className="flex items-center justify-center"
										aria-label="chevron left"
									>
										<ChevronLeft
											className={`h-5 w-5 ${isFirstSlide ? 'text-gray-200' : 'text-blue-500'}`}
										/>
									</span>
								</span>
							</button>

							{/* Next button */}
							<button
								type="button"
								aria-label="Next"
								className="ml-auto flex cursor-pointer items-center justify-center border-none bg-transparent p-2 disabled:cursor-not-allowed disabled:opacity-50"
								disabled={isLastSlide}
								onClick={() => {
									sendTrackString('wxnode_slideshow_link-next');
									next();
								}}
							>
								<span className="flex items-center justify-center">
									<span
										className="flex items-center justify-center"
										aria-label="chevron right"
									>
										<ChevronRight
											className={`h-5 w-5 ${isLastSlide ? 'text-gray-200' : 'text-blue-500'}`}
										/>
									</span>
								</span>
							</button>
						</div>
					</div>

					{/* Title - Show if filename is provided (and not an actual filename) */}
					{currentAsset.filename && !isFilename(currentAsset.filename) && (
						<Text
							variant="Title.S"
							color="primary"
							elementType="h3"
							className="mb-1 mt-0"
						>
							{currentAsset.filename}
						</Text>
					)}

					{/* Caption - Using seo.caption */}
					{currentAsset.seo?.caption && (
						<Text
							variant="Body.S"
							color="primary"
							className={`py-1 pb-0 ${!(currentAsset.filename && !isFilename(currentAsset.filename)) ? 'mt-0 pt-0' : ''}`}
						>
							{currentAsset.seo.caption}
						</Text>
					)}

					{/* Border line */}
					<div className="border-b border-gray-200 py-2"></div>
				</div>
			</figcaption>
		</figure>
	);
};

export default SlideshowView;
