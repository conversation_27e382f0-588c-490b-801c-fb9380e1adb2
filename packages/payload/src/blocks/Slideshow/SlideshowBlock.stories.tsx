import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import SlideshowBlock from './SlideshowBlock';
import type { Image } from '@repo/payload/payload-types';

// Mock image data for the slideshow
const createMockImage = (url: string): Image => ({
	id: `img-${Math.random().toString(36).substring(2, 9)}`,
	seo: {
		altText: 'foo',
		caption: 'bar',
		credit: 'baz',
	},
	url,
	filename: url.split('/').pop() || 'image.jpg',
	mimeType: 'image/jpeg',
	filesize: 1024,
	width: 1200,
	height: 675,
	createdAt: new Date().toISOString(),
	updatedAt: new Date().toISOString(),
});

const meta: Meta<typeof SlideshowBlock> = {
	title: 'Article Blocks/Slideshow',
	component: SlideshowBlock,
	parameters: {
		layout: 'padded',
		chromatic: { diffThreshold: 0.25 },
		docs: {
			description: {
				component:
					'Slideshow block component that displays a collection of images with navigation controls. Features keyboard navigation support (arrow keys), slide counter, and optional rounded corners. Each slide can include a title, caption, description, and byline information.',
			},
		},
	},
};

export default meta;
type Story = StoryObj<typeof SlideshowBlock>;

export const Default: Story = {
	args: {
		slides: [
			{
				imageAsset: createMockImage(
					'https://s.w-x.co/util/image/w/ap24285423339442.jpg',
				),
				imageTitle: 'Hurricane damage in Florida',
				imageCaption:
					'A property owner surveys damage to his home following the passage of Hurricane Milton on October 13, 2024. (AP Photo/John Doe)',
			},
			{
				imageAsset: createMockImage('https://s.w-x.co/Winterize4.jpg'),
				imageTitle: 'Winter storm in Northeast',
				imageCaption:
					'Major winter storm blankets Northeast (AP Photo/Jane Winter)',
			},
			{
				imageAsset: createMockImage(
					'https://s.w-x.co/util/image/w/GettyImages-2177088362.jpg',
				),
				imageTitle: 'Tornado in Oklahoma',
				imageCaption:
					'A large tornado moves through a rural area outside Oklahoma City, causing significant damage to structures and vegetation. (AP Photo/Random Name)',
			},
			{
				imageAsset: createMockImage(
					'https://s.w-x.co/util/image/w/GettyImages-2177072603.jpg',
				),
				imageTitle: 'Flooding in North Carolina',
				imageCaption:
					'Severe flooding affects North Carolina communities (AP Photo/Emily Davis)',
			},
			{
				imageAsset: createMockImage(
					'https://s.w-x.co/util/image/w/AP24284529398481.jpg',
				),
				imageTitle: 'Wildfire in California',
				imageCaption:
					'Firefighters battle a fast-moving wildfire that has consumed thousands of acres of drought-stricken forest. (CNN Photo/Mark Twain)',
			},
		],
		settings: {
			rounded: false,
		},
	},
};

export const Rounded: Story = {
	args: {
		...Default.args,
		settings: {
			rounded: true,
		},
	},
};

export const WithoutCaptions: Story = {
	args: {
		slides: Default.args?.slides?.map((slide) => ({
			...slide,
			imageCaption: undefined,
		})),
		settings: {
			rounded: false,
		},
	},
};

export const WithoutTitles: Story = {
	args: {
		slides: Default.args?.slides?.map((slide) => ({
			...slide,
			imageTitle: undefined,
		})),
		settings: {
			rounded: false,
		},
	},
};
