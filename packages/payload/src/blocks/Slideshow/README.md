# Slideshow Component

A reusable slideshow component for displaying a collection of images with navigation controls, featuring a design that matches The Weather Company's visual style.

## Features

- Image slideshow with previous/next navigation
- Keyboard navigation (left/right arrow keys)
- Slide counter display in the pagination controls
- Caption and description support with clean typography
- Viewport detection for keyboard navigation
- Support for loading additional slides when needed
- Rounded corners option
- Matches The Weather Company's visual design system

## Usage

```tsx
import Slideshow from "@/components/Slideshow/Slideshow";

// Example usage
const MyComponent = () => {
	const slides = [
		{
			title: "Hurricane damage in Florida",
			variants: {
				url: "/path/to/hurricane-image.jpg",
			},
			caption: "Hurricane Milton aftermath in Englewood, Florida",
			description:
				"A property owner surveys damage to his home following the passage of Hurricane Milton.",
		},
		// More slides...
	];

	return (
		<Slideshow
			id="my-slideshow"
			assets={slides}
			totalAssets={slides.length}
			rounded={true}
			sendTrackString={(trackString) => {
				// Analytics tracking
				console.log(trackString);
			}}
		/>
	);
};
```

## Props

### Slideshow Component Props

| Prop                  | Type                                                  | Required | Default | Description                                                          |
| --------------------- | ----------------------------------------------------- | -------- | ------- | -------------------------------------------------------------------- |
| id                    | string                                                | Yes      | -       | Unique identifier for the slideshow                                  |
| assets                | Asset[]                                               | Yes      | []      | Array of assets to display in the slideshow                          |
| totalAssets           | number                                                | Yes      | 0       | Total number of assets (may be more than assets.length if paginated) |
| loadNextSlideshowPage | (params: { assetId: string; offset: number }) => void | No       | -       | Function to load additional slides                                   |
| rounded               | boolean                                               | No       | false   | Whether to use rounded corners                                       |

### Asset Interface

```typescript
interface Asset {
	title: string;
	variants: {
		url: string;
		[key: string]: any;
	};
	caption?: string;
	description?: string;
}
```

## Design Features

The slideshow has been designed to match The Weather Company's visual style:

1. **Figure-Based Layout**: Uses semantic HTML with `<figure>` and `<figcaption>` elements
2. **Intrinsic Ratio Images**: Maintains proper aspect ratio for images regardless of container size
3. **Pagination Controls**: Displays slide counter and navigation buttons in a clean control bar
4. **Semantic Typography**: Clear, readable text for captions and descriptions
5. **Accessible Navigation**: Properly labeled buttons with appropriate disabled states
6. **Visual Consistency**: Matches the design patterns used throughout The Weather Company's products

## Implementation Details

The Slideshow component follows the Container/Presenter pattern:

1. **Slideshow.tsx**: Container component that handles state management and logic
2. **SlideshowView.tsx**: Presenter component that renders the UI with the modern design

## Keyboard Navigation

The component listens for left and right arrow key presses and navigates the slideshow when it's in the viewport. This behavior is only active when the slideshow is visible on screen.
