'use client';

import React, { FC } from 'react';
import Slideshow from './Slideshow';
import { isImageObject } from '@repo/payload/utils/blockGuards';
import { hasLength } from '@repo/utils/hasLength';
import type { Image, SlideshowBlockConfig } from '@repo/payload/payload-types';

export const SlideshowBlock: FC<SlideshowBlockConfig> = ({
	slides = [],
	settings = {},
	id,
}) => {
	// Check if data exists
	if (!hasLength(slides)) {
		return null; // Don't render anything if no slides are available
	}

	// Transform the slides data into the format expected by the Slideshow component
	const slideshowAssets: Image[] = [];

	// Process each slide and add valid ones to the assets array
	for (const slide of slides) {
		if (!slide.imageAsset) {
			console.error('Slide missing imageAsset:', slide);
			continue;
		}

		const imageAsset = isImageObject(slide.imageAsset)
			? slide.imageAsset
			: null;

		if (!imageAsset || !imageAsset.url) {
			console.error('Invalid imageAsset or missing URL:', imageAsset);
			continue;
		}

		// Use the image asset directly, but enhance it with slide-specific data
		// We'll store the slide title and caption in custom properties that will be used by the view
		const enhancedImage = {
			...imageAsset,
			// Override seo properties with slide-specific data if available
			seo: {
				...imageAsset.seo,
				caption: slide.imageCaption || imageAsset.seo?.caption || '',
				// Use altText from the image if imageTitle is empty
				altText:
					slide.imageTitle && slide.imageTitle.trim() !== ''
						? slide.imageTitle
						: imageAsset.seo?.altText || '',
			},
			// Only set the filename property if it's a real title (not a filename)
			// This property will be used for displaying the title in the slideshow
			...(() => {
				// Skip if imageTitle is empty, null, or undefined
				if (!slide.imageTitle || slide.imageTitle.trim() === '') {
					return {};
				}

				// Check if it looks like a filename with more comprehensive patterns
				const isFilename =
					// Check for common image extensions
					/\.(jpg|jpeg|png|gif|webp|bmp|tiff|svg)$/i.test(slide.imageTitle) ||
					// Check for filename patterns like DCT_SPECIAL42.jpg
					/^[A-Z0-9_]+\d*\.[a-zA-Z]+$/i.test(slide.imageTitle) ||
					// Check for other common filename patterns
					/^[a-zA-Z0-9_-]+\d+$/i.test(slide.imageTitle) ||
					// Check for any pattern that looks like a filename (contains dots, underscores, or numbers)
					/[._][0-9]+/i.test(slide.imageTitle) ||
					// Check for all caps with numbers (likely a filename)
					/^[A-Z0-9_]+$/i.test(slide.imageTitle);

				if (isFilename) {
					return {};
				}

				// Set the filename property only for actual titles
				// This works for both legacy and PayloadCMS slideshows
				return { filename: slide.imageTitle };
			})(),
		};

		slideshowAssets.push(enhancedImage);
	}

	// If no valid assets after transformation, show error
	if (slideshowAssets.length === 0) {
		console.error('SlideshowBlock - No valid assets after transformation');
		return (
			<div className="text-center text-red-500">
				Error: Could not load images for the slideshow.
			</div>
		);
	}

	return (
		<Slideshow
			id={id || `slideshow-${Math.random().toString(36).substring(2, 9)}`}
			assets={slideshowAssets.slice(0, 20)} // First batch only
			allSlides={slideshowAssets} // All pre-loaded slides
			totalAssets={slideshowAssets.length}
			rounded={settings?.rounded || false}
			blockType="Slideshow"
			slides={slides}
			settings={settings}
			sendTrackString={() => {
				// Analytics tracking could be implemented here
			}}
		/>
	);
};

export default SlideshowBlock;
