'use client';

import React, { FC, useEffect, useState } from 'react';
import { useAllFormFields } from '@payloadcms/ui';
import { reduceFieldsToValues } from 'payload/shared';

import AdminBlock from '@repo/payload/components/AdminBlock';
import Image from 'next/image';

// Utility function to check if a string looks like a filename
const isFilename = (str: string | null | undefined): boolean => {
	if (!str || str.trim() === '') return false;

	return (
		// Check for common image extensions
		/\.(jpg|jpeg|png|gif|webp|bmp|tiff|svg)$/i.test(str) ||
		// Check for filename patterns like DCT_SPECIAL42.jpg
		/^[A-Z0-9_]+\d*\.[a-zA-Z]+$/i.test(str) ||
		// Check for other common filename patterns
		/^[a-zA-Z0-9_-]+\d+$/i.test(str) ||
		// Check for any pattern that looks like a filename (contains dots, underscores, or numbers)
		/[._][0-9]+/i.test(str) ||
		// Check for all caps with numbers (likely a filename)
		/^[A-Z0-9_]+$/i.test(str)
	);
};

// Function to get display title
const getDisplayTitle = (title: string | null | undefined): string => {
	if (!title || title.trim() === '') return '';
	return isFilename(title) ? '' : title;
};

export const SlideshowAdminBlock: FC = () => {
	const [fields] = useAllFormFields();
	const formData = reduceFieldsToValues(fields, true);

	const [slideshowData, setSlideshowData] = useState({
		slides: formData?.slides || [],
		settings: formData?.settings || {},
	});

	useEffect(() => {
		setSlideshowData({
			slides: formData?.slides || [],
			settings: formData?.settings || {},
		});
	}, [formData?.slides, formData?.settings]);

	// Fetch image data for the first slide (for preview)
	const firstSlide = slideshowData.slides?.[0];
	const firstImageId = firstSlide?.imageAsset;

	return (
		<AdminBlock name="Slideshow">
			<div className="mb-4">
				<strong className="text-black-800">Slideshow Images:</strong>{' '}
				<span className="text-gray-600">
					{slideshowData.slides?.length || 0} images
				</span>
			</div>

			{slideshowData.slides?.length > 0 && (
				<div className="mb-4">
					{/* Only show title if it's not a filename and not empty */}
					{getDisplayTitle(firstSlide?.imageTitle) ? (
						<div className="mb-2">
							<strong className="text-black-800">First Image:</strong>{' '}
							<span className="text-gray-600">
								{getDisplayTitle(firstSlide?.imageTitle)}
							</span>
						</div>
					) : (
						<div className="mb-2">
							<strong className="text-black-800">First Image</strong>
						</div>
					)}

					{firstImageId?.url && (
						<Image
							src={firstImageId.url}
							alt={getDisplayTitle(firstSlide?.imageTitle)}
							className="mb-2 max-h-[240px] max-w-[240px]"
						/>
					)}

					{firstSlide?.description && (
						<div className="mb-1">
							<strong className="text-black-800">Description:</strong>{' '}
							<span className="text-gray-600">{firstSlide.description}</span>
						</div>
					)}

					{firstSlide?.imageCaption && (
						<div className="mb-1">
							<strong className="text-black-800">Caption:</strong>{' '}
							<span className="text-gray-600">{firstSlide.imageCaption}</span>
						</div>
					)}

					{firstSlide?.imageByline && (
						<div className="mb-1">
							<strong className="text-black-800">Byline:</strong>{' '}
							<span className="text-gray-600">{firstSlide.imageByline}</span>
						</div>
					)}

					{firstSlide?.provider && (
						<div className="mb-1">
							<strong className="text-black-800">Provider:</strong>{' '}
							<span className="text-gray-600">{firstSlide.provider}</span>
						</div>
					)}

					{firstSlide?.property && (
						<div className="mb-1">
							<strong className="text-black-800">Property:</strong>{' '}
							<span className="text-gray-600">{firstSlide.property}</span>
						</div>
					)}
				</div>
			)}

			<div className="mb-2">
				<strong className="text-black-800">Settings:</strong>
			</div>
			<div className="mb-1">
				<strong className="text-black-800">Rounded Corners:</strong>{' '}
				<span className="text-gray-600">
					{slideshowData.settings?.rounded ? 'Yes' : 'No'}
				</span>
			</div>
		</AdminBlock>
	);
};

export default SlideshowAdminBlock;
