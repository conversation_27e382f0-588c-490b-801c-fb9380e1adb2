import { createGroup } from '@repo/payload/fields/utility';
import type { ArrayField, Field, RelationshipField, TextField } from 'payload';

// Field for each slide's image
export const imageAsset: RelationshipField = {
	name: 'imageAsset',
	label: 'Image Asset',
	type: 'relationship',
	required: true,
	relationTo: 'images',
};

// Field for image title
export const imageTitle: TextField = {
	name: 'imageTitle',
	label: 'Image Title',
	type: 'text',
	required: false,
	defaultValue: '',
	admin: {
		description:
			"Title to display for this image. Leave empty if you don't want to show a title.",
	},
};

// Field for image caption
export const imageCaption: TextField = {
	name: 'imageCaption',
	label: 'Image Caption',
	type: 'text',
};

// Fields removed: description, provider, imageByline, property

// Group all fields for a single slide
export const slideFields: Field[] = [imageAsset, imageTitle, imageCaption];

// Create a slides array field
export const slidesArray: ArrayField = {
	name: 'slides',
	label: 'Slideshow Images',
	type: 'array',
	required: true,
	minRows: 1,
	admin: {
		description:
			'Add images to your slideshow. Each image can have its own title, caption, and metadata.',
	},
	fields: slideFields,
};

// Additional slideshow settings
export const rounded: Field = {
	name: 'rounded',
	label: 'Use Rounded Corners',
	type: 'checkbox',
	defaultValue: false,
};

// Group settings fields
export const settingsFields: Field[] = [rounded];

export const settingsGroup = createGroup(
	settingsFields,
	'settings',
	'Slideshow Settings',
	{ admin: { hideGutter: true } },
);

// Export all slideshow fields
export const slideshowFields: Field[] = [slidesArray, settingsGroup];
