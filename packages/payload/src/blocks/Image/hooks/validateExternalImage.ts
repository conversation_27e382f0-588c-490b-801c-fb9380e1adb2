import { TextFieldSingleValidation } from 'payload';

export const validateExternalImage: TextFieldSingleValidation = (
	value,
): true | string => {
	if (typeof value !== 'string' || value.trim() === '') {
		return 'Please provide a URL for the external image.';
	}

	// Regex to validate HTTPS and image extension (excluding .svg)
	const urlPattern =
		/^https?:\/\/[\w.-]+(\.[\w.-]+)+(\/[\w\-.~:?#\\[\]@!$&'()*+,;=% ]*)*\.(jpg|jpeg|png|gif|webp)$/i;

	if (!urlPattern.test(value)) {
		return 'Please enter a valid HTTPS URL ending with .jpg, .jpeg, .png, .gif, or .webp.';
	}

	return true;
};
