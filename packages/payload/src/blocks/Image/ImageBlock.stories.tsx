import type { <PERSON>a, StoryObj } from '@storybook/react';
import ImageBlock from './ImageBlock';
import { type Image } from '@repo/payload/payload-types';

const meta: Meta<typeof ImageBlock> = {
	title: 'Article Blocks/Image Block',
	component: ImageBlock,
	parameters: {
		layout: 'padded',
		chromatic: { diffThreshold: 0.3 },
	},
};

export default meta;
type Story = StoryObj<typeof ImageBlock>;

// Base story with internal image
export const InternalImage: Story = {
	parameters: {
		viewport: { defaultViewport: 'mobile2' },
	},
	args: {
		imageType: 'internal',
		internalImageId: {
			id: 'foobar',
			createdAt: '2025-04-25T10:00:00Z',
			updatedAt: '2025-04-25T11:00:00Z',
			url: 'https://s.w-x.co/rsz_js111398776_4.jpg',
			seo: {
				altText: 'A bear in the wild',
				caption: 'Brown bear in natural habitat',
				credit: 'Photo by Nature Photographer',
			},
		} as Image,
		controls: {
			showCaption: true,
			useImageCaption: true,
			width: 'full',
			alignment: 'center',
		},
	},
};

// External image with custom caption
export const ExternalImage: Story = {
	parameters: {
		viewport: { defaultViewport: 'mobile2' },
	},
	args: {
		imageType: 'external',
		externalImageUrl: 'https://placebear.com/800/500',
		altText: 'A polar bear on ice',
		controls: {
			showCaption: true,
			caption: 'Polar bear on Arctic ice',
			credit: 'Wildlife Photography Inc.',
			width: 'full',
			alignment: 'center',
		},
	},
};

// Half width image
export const HalfWidth: Story = {
	parameters: {
		viewport: { defaultViewport: 'tablet' },
	},
	args: {
		imageType: 'internal',
		internalImageId: {
			id: 'foobar',
			createdAt: '2025-04-25T10:00:00Z',
			updatedAt: '2025-04-25T11:00:00Z',
			url: 'https://placebear.com/500/400',
			seo: {
				altText: 'A bear cub',
				caption: 'Bear cub exploring',
				credit: 'National Geographic',
			},
		} as Image,
		controls: {
			showCaption: true,
			useImageCaption: true,
			width: 'half',
			alignment: 'left',
		},
	},
};
