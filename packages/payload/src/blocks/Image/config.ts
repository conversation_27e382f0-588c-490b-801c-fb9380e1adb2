import { imageFields } from './field';
import type { Block } from 'payload';

export const ImageBlockConfig: Block = {
	slug: 'Image',
	interfaceName: 'ImageBlockConfig',
	labels: {
		singular: 'Image Block',
		plural: 'Image Blocks',
	},
	fields: imageFields,
	// Component to render in the admin interface
	admin: {
		components: {
			Block: {
				path: '@repo/payload/blocks/admin#ImageAdminBlock',
			},
		},
	},
};

export default ImageBlockConfig;
