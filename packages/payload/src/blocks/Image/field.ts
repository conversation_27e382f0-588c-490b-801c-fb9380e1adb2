import { createGroup } from '@repo/payload/fields/utility';
import { validateExternalImage } from './hooks/validateExternalImage';

import type {
	CheckboxField,
	Field,
	RelationshipField,
	SelectField,
	TextField,
} from 'payload';

export const imageType: SelectField = {
	name: 'imageType',
	label: 'Image Type',
	type: 'select',
	required: true,
	defaultValue: 'internal',
	options: [
		{ label: 'Internal', value: 'internal' },
		{ label: 'External', value: 'external' },
	],
};

export const internalImageId: RelationshipField = {
	name: 'internalImageId',
	label: 'Internal Image',
	type: 'relationship',
	required: true,
	relationTo: 'images',
	admin: {
		condition: (_, { imageType }) => imageType === 'internal',
	},
};

export const externalImageUrl: TextField = {
	name: 'externalImageUrl',
	label: 'External Image URL',
	type: 'text',
	required: true,
	validate: validateExternalImage,
	admin: {
		condition: (_, { imageType }) => imageType === 'external',
	},
};

/**
 * NOTE:
 * Cant put this in metadata because the conditional needs to be near imageType
 * */
export const altText: TextField = {
	name: 'altText',
	label: 'Alt Text',
	type: 'text',
	required: true,
	admin: {
		condition: (_, { imageType }) => imageType === 'external',
	},
};

export const showCaption: CheckboxField = {
	name: 'showCaption',
	label: 'Show Caption',
	type: 'checkbox',
	defaultValue: false,
};

export const useImageCaption: CheckboxField = {
	name: 'useImageCaption',
	label: 'Use Image Caption',
	type: 'checkbox',
	defaultValue: true,
	admin: {
		condition: (_, { showCaption }, { blockData }) =>
			showCaption && blockData.imageType === 'internal',
		description: 'Use the caption from the selected image',
	},
};

export const isPriorityImage: CheckboxField = {
	name: 'isPriorityImage',
	label: 'Priority Image',
	type: 'checkbox',
	defaultValue: false,
	admin: {
		description:
			'Set this image as a priority image if it is above the fold so that it loads immediately',
	},
};

export const linkUrl: TextField = {
	name: 'linkUrl',
	label: 'Link URL',
	type: 'text',
	admin: {
		description: 'Optional URL to link the image to (e.g., affiliate link)',
	},
};

export const caption: TextField = {
	name: 'caption',
	label: 'Caption',
	type: 'text',
	admin: {
		condition: (_, { showCaption, useImageCaption }, { blockData }) =>
			showCaption &&
			(blockData.imageType === 'external' ||
				(blockData.imageType === 'internal' && !useImageCaption)),
		description:
			'Custom caption (only used if "Use Image Caption" is unchecked)',
	},
};

export const credit: TextField = {
	name: 'credit',
	label: 'Credit',
	type: 'text',
	admin: {
		condition: (_, { showCaption, useImageCaption }, { blockData }) =>
			showCaption &&
			(blockData.imageType === 'external' ||
				(blockData.imageType === 'internal' && !useImageCaption)),
		description:
			'Custom credit (only used if "Use Image Caption" is unchecked)',
	},
};

export const alignment: SelectField = {
	name: 'alignment',
	label: 'Alignment',
	type: 'select',
	defaultValue: 'center',
	options: [
		{ label: 'Left', value: 'left' },
		{ label: 'Center', value: 'center' },
		{ label: 'Right', value: 'right' },
	],
};

export const width: SelectField = {
	name: 'width',
	label: 'Width',
	type: 'select',
	defaultValue: 'full',
	options: [
		{ label: 'Full', value: 'full' },
		{ label: 'Half', value: 'half' },
	],
};

/**
 * Group Fields
 * */
export const controlsFields: Field[] = [
	showCaption,
	useImageCaption,
	caption,
	credit,
	alignment,
	width,
];

export const controlsGroup = createGroup(
	controlsFields,
	'controls',
	'Controls',
	{ admin: { hideGutter: true } },
);

export const imageFields: Field[] = [
	imageType,
	internalImageId,
	externalImageUrl,
	altText,
	controlsGroup,
	isPriorityImage,
	linkUrl,
];
