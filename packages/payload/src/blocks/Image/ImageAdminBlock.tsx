'use client';

import React, { FC, useEffect, useState } from 'react';
import { useAllFormFields, usePayloadAPI } from '@payloadcms/ui';
import { reduceFieldsToValues } from 'payload/shared';

import AdminBlock from '@repo/payload/components/AdminBlock';
import { Image } from '@repo/payload/payload-types';
import UIImage from '@repo/ui/components/Image/Image';

export const ImageAdminBlock: FC = () => {
	const [fields] = useAllFormFields();
	const formData = reduceFieldsToValues(fields, true);

	const [imageData, setImageData] = useState({
		imageType: formData?.imageType,
		internalImageId: formData?.internalImageId,
		externalImageUrl: formData?.externalImageUrl,
		altText: formData?.altText,
		showCaption: formData?.controls?.showCaption,
		useImageCaption: formData?.controls?.useImageCaption,
		caption: formData?.controls?.caption,
		credit: formData?.controls?.credit,
		alignment: formData?.controls?.alignment,
		width: formData?.controls?.width,
	});

	useEffect(() => {
		setImageData({
			imageType: formData?.imageType,
			internalImageId: formData?.internalImageId,
			externalImageUrl: formData?.externalImageUrl,
			altText: formData?.altText,
			showCaption: formData?.controls?.showCaption,
			useImageCaption: formData?.controls?.useImageCaption,
			caption: formData?.controls?.caption,
			credit: formData?.controls?.credit,
			alignment: formData?.controls?.alignment,
			width: formData?.controls?.width,
		});
	}, [
		formData?.imageType,
		formData?.internalImageId,
		formData?.externalImageUrl,
		formData?.altText,
		formData?.controls?.showCaption,
		formData?.controls?.useImageCaption,
		formData?.controls?.caption,
		formData?.controls?.credit,
		formData?.controls?.alignment,
		formData?.controls?.width,
	]);

	let internalImgData: Image | null = null;
	[{ data: internalImgData }] = usePayloadAPI(
		imageData.imageType === 'internal' && imageData.internalImageId
			? `/api/payload/images/${imageData.internalImageId}`
			: '',
	);

	const imgSrc: string =
		imageData.imageType === 'external'
			? imageData.externalImageUrl
			: internalImgData?.url;

	const renderImageSource = () => {
		switch (imageData.imageType) {
			case 'internal':
				return (
					<div className="mb-2">
						<strong className="text-black-800">Internal Image ID:</strong>{' '}
						<span className="text-gray-600">
							{imageData.internalImageId || 'Not selected'}
						</span>
					</div>
				);
			case 'external':
				return (
					<div className="mb-2">
						<div>
							<strong className="text-black-800">External Image URL:</strong>{' '}
							<span className="text-gray-600">
								{imageData.externalImageUrl || 'Not provided'}
							</span>
						</div>
						<div>
							<strong className="text-black-800">Alt Text:</strong>{' '}
							<span className="text-gray-600">
								{imageData.altText || 'Not provided'}
							</span>
						</div>
					</div>
				);
			default:
				return <div className="text-red-500">Image type is undecided.</div>;
		}
	};

	const renderCaption = () => {
		if (imageData.showCaption) {
			return (
				<div className="mb-2">
					{imageData.imageType === 'internal' && (
						<div>
							<strong className="text-black-800">Use Image Caption:</strong>{' '}
							<span className="text-gray-600">
								{imageData.useImageCaption ? 'Yes' : 'No'}
							</span>
						</div>
					)}
					<div>
						<strong className="text-black-800">Caption:</strong>{' '}
						<span className="text-gray-600">
							{imageData.imageType === 'internal' && imageData.useImageCaption
								? internalImgData?.seo?.caption || ''
								: imageData.caption || 'No caption provided'}
						</span>
					</div>
					<div>
						<strong className="text-black-800">Credit:</strong>{' '}
						<span className="text-gray-600">
							{imageData.imageType === 'internal' && imageData.useImageCaption
								? internalImgData?.seo?.credit || ''
								: imageData.credit || 'No credit provided'}
						</span>
					</div>
				</div>
			);
		}
		return null;
	};

	const renderControls = () => {
		return (
			<div className="mb-2">
				<div>
					<strong className="text-black-800">Alignment:</strong>{' '}
					<span className="text-gray-600">
						{imageData.alignment || 'Center'}
					</span>
				</div>
				<div>
					<strong className="text-black-800">Width:</strong>{' '}
					<span className="text-gray-600">{imageData.width || 'Full'}</span>
				</div>
			</div>
		);
	};

	return (
		<AdminBlock name="Image">
			<div className="mb-2">
				<strong className="text-black-800">Image Type:</strong>{' '}
				<span className="text-gray-600">
					{imageData.imageType || 'Not selected'}
				</span>
			</div>
			{renderImageSource()}
			{renderCaption()}
			{renderControls()}
			{imgSrc && (
				<UIImage src={imgSrc} alt="" className="max-h-[240px] max-w-[240px]" />
			)}
		</AdminBlock>
	);
};

export default ImageAdminBlock;
