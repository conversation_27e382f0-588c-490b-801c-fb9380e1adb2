'use client';

import { useState } from 'react';
import useSearchLocation from '../hooks/useSearchLocation';
import useDebounce from '@repo/ui/hooks/useDebounce';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@repo/ui/components/Popover/Popover';
import { Button } from '@repo/ui/components/Button/Button';
import {
	Command,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from '@repo/ui/components/command';
import { cn } from '@repo/ui/lib/utils';
import { Loader2, MapPin } from 'lucide-react';
import { LocationSearchItem } from '@repo/dal/locations/types';
import { useParams } from 'next/navigation';

interface LocationInputProps {
	label?: string;
	placeholder?: string;
	className?: string;
	errors?: string[];
	location?: LocationSearchItem | null;
	onChangeLocation?: (location: LocationSearchItem | null) => void;
}

export default function LocationInput({
	label,
	placeholder,
	className,
	location: selectedLocation,
	onChangeLocation,
}: LocationInputProps) {
	const params = useParams();
	const locale = params?.locale as string;
	const [open, setOpen] = useState(false);
	const [value, setValue] = useState('');
	const debouncedValue = useDebounce(value, 300);

	const { isLoading, data } = useSearchLocation(debouncedValue, locale);

	return (
		<Popover open={open} onOpenChange={setOpen}>
			<PopoverTrigger asChild>
				<Button
					variant="outline"
					role="combobox"
					className={cn(
						'text-muted-foreground flex w-full text-nowrap bg-white px-2',
						className,
					)}
				>
					<div>
						{isLoading ? <Loader2 className="animate-spin" /> : <MapPin />}
					</div>
					<div className="flex flex-1 px-2 text-start">
						{selectedLocation?.displayName ?? label}
					</div>
				</Button>
			</PopoverTrigger>
			<PopoverContent
				className="w-[var(--radix-popover-trigger-width)]"
				showArrow={false}
			>
				<Command shouldFilter={false}>
					<CommandInput
						placeholder={placeholder}
						value={value}
						onValueChange={setValue}
					/>
					<CommandList>
						<CommandGroup>
							{data?.map((location) => (
								<CommandItem
									key={location.locId}
									value={location.locId}
									onSelect={() => {
										onChangeLocation?.(location);
										setOpen(false);
									}}
								>
									{location.address}
								</CommandItem>
							))}
						</CommandGroup>
					</CommandList>
				</Command>
			</PopoverContent>
		</Popover>
	);
}
