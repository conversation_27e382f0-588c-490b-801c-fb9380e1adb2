import type { Block } from 'payload';

export const MorningBriefBlockConfig: Block = {
	slug: 'MorningBrief',
	interfaceName: 'MorningBriefBlockConfig',
	labels: {
		singular: 'MorningBrief Block',
		plural: 'MorningBrief Blocks',
	},
	fields: [],
	admin: {
		components: {
			Block: {
				path: '@repo/payload/blocks/admin#MorningBriefAdminBlock',
			},
		},
	},
};

export default MorningBriefBlockConfig;
