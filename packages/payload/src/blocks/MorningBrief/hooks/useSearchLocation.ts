import { getLocationsByQuery } from '@repo/dal/locations/search';
import useSWR from 'swr';

export default function useSearchLocation(query: string, locale: string) {
	const shouldFetch = query.trim().length > 0;

	const { isLoading, data, error } = useSWR(
		shouldFetch ? ['search/locations', encodeURIComponent(query)] : null,
		async () => {
			return await getLocationsByQuery(query, locale);
		},
	);

	return {
		isLoading,
		data,
		error,
	};
}
