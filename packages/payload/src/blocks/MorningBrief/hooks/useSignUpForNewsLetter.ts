import { useCallback } from 'react';
import useSWRMutation from 'swr/mutation';
import { setEmailPreferences } from '@repo/dal/user/emailPreferences/setEmailPreferences';
import type {
	EmailPreferencesSetParams,
	EmailPreferencesSetResponse,
	SubscriptionGroupName,
	SubscriptionGroupWithId,
} from '@repo/dal/user/emailPreferences/types';
import type { LocationSearchItem } from '@repo/dal/locations/types';
import { getLocationPointByPlaceId } from '@repo/dal/locations/point';
import { useEmailPreferences } from '@repo/user/hooks/useEmailPreferences';

export interface SignUpForNewsLetterInput {
	email: string;
	location: LocationSearchItem;
	locale: string;
	isAnonymous?: boolean;
}

interface SentSubscriptionsRecord {
	sentSubscriptions?: EmailPreferencesSetParams['subscriptions'];
}

async function subscribe(
	subscriptionGroupName: SubscriptionGroupName,
	{ arg }: { arg: SignUpForNewsLetterInput },
): Promise<EmailPreferencesSetResponse & SentSubscriptionsRecord> {
	const { email, location, locale, isAnonymous } = arg;

	const { location: locationPoint } = await getLocationPointByPlaceId(
		location.placeId,
		locale,
	).catch(() => ({ location: null }));

	const prefs: EmailPreferencesSetParams = {
		subscriptions: [
			{
				subscriptionGroupName,
				isSubscribed: true,
				attributes: {
					location: location.placeId,
					latitude: location.latitude,
					longitude: location.longitude,
					dmaCode: locationPoint?.dmaCd || null,
					adminDistrict: location.adminDistrict,
					adminDistrictCode: location.adminDistrictCode || null,
					postalCode: location.postalCode,
				},
			},
		],
	};

	if (isAnonymous) {
		prefs.email = email;
	}

	return await setEmailPreferences(prefs).then((response) => ({
		...response,
		sentSubscriptions: prefs.subscriptions,
	}));
}

export default function useSignUpForNewsLetter() {
	const { updateSubscriptions } = useEmailPreferences();

	const { isMutating, trigger, data, error, reset } = useSWRMutation<
		EmailPreferencesSetResponse & SentSubscriptionsRecord,
		EmailPreferencesSetResponse,
		string,
		SignUpForNewsLetterInput
	>(
		'newsletter/signup/daily-newsletters-1',
		(_key: string, options: { arg: SignUpForNewsLetterInput }) =>
			subscribe('daily-newsletters-1', options),
	);

	const onSubmit = useCallback(
		async (input: SignUpForNewsLetterInput) => {
			const response = await trigger(input);

			if (response.sentSubscriptions) {
				updateSubscriptions(
					response.sentSubscriptions as SubscriptionGroupWithId[],
				);
			}

			return response;
		},
		[trigger, updateSubscriptions],
	);

	return {
		loading: isMutating,
		error,
		data,
		onSubmit,
		onReset: reset,
	};
}
