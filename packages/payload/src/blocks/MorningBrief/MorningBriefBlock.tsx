'use client';

import { <PERSON>, FormEvent<PERSON><PERSON><PERSON>, use<PERSON><PERSON>back, useMemo, useState } from 'react';
import { Check, Info, Loader2 } from 'lucide-react';
import { Button } from '@repo/ui/components/Button/Button';
import LocationInput from './components/LocationInput';
import TextInput from './components/TextInput';
import Footer from './components/Footer';
import MorningBriefLogo from './images/morning-brief-logo.svg';
import EllipseImage from './images/ellipse.png';
import useSignUpForNewsLetter from './hooks/useSignUpForNewsLetter';
import type { LocationSearchItem } from '@repo/dal/locations/types';
import { useUser } from '@repo/user/hooks/useUser';
import Image from '@repo/ui/components/Image/Image';
import { MorningBriefBlockConfig } from '@repo/payload/payload-types';
import { useEmailPreferences } from '@repo/user/hooks/useEmailPreferences';
import { findSubscriptionGroup } from '@repo/dal/user/emailPreferences/utils';
import { DAILY_NEWSLETTERS_GROUP } from '@repo/dal/user/emailPreferences/const';
import { useParams } from 'next/navigation';

export const MorningBriefBlock: FC<MorningBriefBlockConfig> = () => {
	const params = useParams();
	const locale = params?.locale as string;
	const { userProfile } = useUser();
	const [email, setEmail] = useState(userProfile?.email ?? '');
	const [location, setLocation] = useState<LocationSearchItem | null>(null);
	const [submitted, setSubmitted] = useState(false);
	const { data, loading, onSubmit, error } = useSignUpForNewsLetter();

	const { subscriptions } = useEmailPreferences();

	const newsletterSubscription = useMemo(
		() => findSubscriptionGroup(subscriptions, DAILY_NEWSLETTERS_GROUP),
		[subscriptions],
	);

	const handleSubmit: FormEventHandler<HTMLFormElement> = useCallback(
		async (e) => {
			e.preventDefault();

			if (!loading && email.trim() && location) {
				setSubmitted(true);
				await onSubmit({
					email,
					location,
					locale,
					isAnonymous: !userProfile?.email,
				});
			}
		},
		[onSubmit, email, location, locale, loading, userProfile?.email],
	);

	// Hide the block If the user is previously subscribed before submitting
	if (!submitted && newsletterSubscription?.isSubscribed) return null;

	return (
		<div
			style={{
				backgroundImage: `url("${EllipseImage.src}")`,
				backgroundPosition: 'right top',
				backgroundRepeat: 'no-repeat',
				backgroundSize: '70px',
			}}
			className="my-4 space-y-2 rounded-xl bg-white py-5 pl-5 pr-[30px] shadow-[0_3px_20px_0_rgba(181,171,162,0.5)]"
		>
			<div className="flex flex-col gap-2 sm:flex-row sm:items-end sm:gap-3">
				<Image
					src={MorningBriefLogo.src}
					alt=""
					className="w-37.5 m-0"
					rounded={false}
				/>
				<div className="mb-1.5 hidden min-h-[24px] min-w-[2px] bg-black sm:block" />
				<div className="text-lg font-semibold">Weather in your inbox</div>
			</div>
			{data?.status === 'success' ? (
				<div className="flex items-center gap-2">
					<Check className="h-6 w-6 text-green-500" />
					<div className="text-sm text-gray-700">
						<p>You are subscribed to the Morning Brief newsletter.</p>
					</div>
				</div>
			) : (
				<form
					onSubmit={handleSubmit}
					className="flex flex-1 flex-col items-end gap-4 pt-4 lg:flex-row"
				>
					<TextInput
						id="email"
						required
						type="email"
						placeholder="Email"
						className="h-12"
						value={email}
						onChange={(e) => setEmail(e.target.value)}
						error={!!data?.emailError}
						disabled={!!userProfile?.email}
					/>
					<LocationInput
						label="Forecast Location"
						placeholder="Search City or Zip Code"
						className="h-12 items-center text-sm"
						location={location}
						onChangeLocation={setLocation}
					/>
					<div>
						<Button
							type="submit"
							className="flex h-12 items-center gap-1 text-nowrap"
							disabled={loading || !(email && location)}
						>
							<span>Sign Up</span>
							{loading && <Loader2 className="animate-spin" />}
						</Button>
					</div>
				</form>
			)}
			{error && (
				<div className="flex items-center gap-2 text-red-500">
					<Info className="h-6 w-6 text-red-500" />
					<div className="text-sm">Failed to subscribe, please try again.</div>
				</div>
			)}
			{data?.status === 'error' && (
				<div className="flex items-center gap-2 text-red-500">
					<Info className="h-6 w-6 text-red-500" />
					<div className="text-sm">{data.emailError ?? data.error}</div>
				</div>
			)}
			<Footer />
		</div>
	);
};

MorningBriefBlock.displayName = 'MorningBriefBlock';

export default MorningBriefBlock;
