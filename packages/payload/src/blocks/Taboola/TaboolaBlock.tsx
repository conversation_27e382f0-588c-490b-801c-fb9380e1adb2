'use client';

import React, { FC, useEffect, useMemo } from 'react';
import { usePathname } from 'next/navigation';
import type { Block } from 'payload';
import { getClientSideURL } from '@repo/payload/utils/getURL';
import { DebugCollector } from '@repo/payload/components/FrontendAdminHeader/collectors/DebugCollector';
import useAllowTaboola from './hooks/useAllowTaboola';
import { cn } from '@repo/ui/lib/utils';
import { useTaboolaScript } from './hooks/useTaboolaScript';

declare global {
	interface Window {
		_taboola: unknown[];
		performance: {
			mark: (name: string) => void;
		};
	}
}

export interface TaboolaPlacement {
	mode: string;
	container: string;
	placement: string;
	target_type: string;
}

// Define the TaboolaBlockConfig interface since it's not in payload-types.ts yet
interface TaboolaBlockConfig extends Block {
	blockType: 'Taboola';
	placements: Array<TaboolaPlacement>;
	loaderUrl: string;
	pageType: string;
	title?: string;
	height?: string;
	configs: {
		url: string;
		scriptId: string;
		commands: Array<Record<string, unknown>>;
	};
	showTaboola: boolean;
}

interface TaboolaBlockProps
	extends Omit<
		TaboolaBlockConfig,
		'blockType' | 'loaderUrl' | 'configs' | 'fields' | 'slug'
	> {
	className?: string;
}

export const TaboolaBlock: FC<TaboolaBlockProps> = ({
	placements,
	pageType,
	className = '',
	showTaboola,
}) => {
	const allowTaboola = useAllowTaboola(); // check if Taboola is allowed to display based on current user
	const pathname = usePathname();
	const { isTaboolaLoaded } = useTaboolaScript();
	const canRenderTaboola = allowTaboola && !showTaboola;

	// Calculate the page URL at the top level of the component
	const pageUrl = useMemo(() => {
		const baseUrl = getClientSideURL();
		return `${baseUrl}${pathname || ''}`;
	}, [pathname]);

	// Handle passing details to Taboola command queue for each page
	useEffect(() => {
		// Only push the Taboola queue if Taboola script was loaded
		if (canRenderTaboola && isTaboolaLoaded) {
			// For each page, first pass the page details
			window._taboola.push({
				[pageType]: 'auto',
				url: pageUrl,
			});

			// Pass the details of each placement
			placements.forEach((placement) => {
				window._taboola.push({ ...placement });
			});

			// Flush the queue after passing details of last placement
			window._taboola.push({ flush: true });
		}

		// Clean up on unmount or when props change
		return () => {
			if (isTaboolaLoaded) {
				window._taboola.push({ notify: 'newPageLoad' });
			}
		};
	}, [canRenderTaboola, isTaboolaLoaded, pageType, pageUrl, placements]);

	if (!canRenderTaboola) return null;

	return (
		<section className={cn('@container', className)}>
			<DebugCollector
				componentName="TaboolaBlock"
				data={{
					props: {
						placements,
						pageType,
						className,
					},
				}}
			/>
		</section>
	);
};

export default TaboolaBlock;
