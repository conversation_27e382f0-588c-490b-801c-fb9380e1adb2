'use client';

import { cn } from '@repo/ui/lib/utils';
import { Card, CardHeader, CardContent } from '@repo/ui/components/card';
import useAllowTaboola from './hooks/useAllowTaboola';
import React, { useEffect, useState } from 'react';

interface TaboolaContentProps {
	taboolaId: string;
	taboolaType?: string;
	title?: string;
	className?: string;
}

// Taboola presenter component
export function TaboolaContent({
	taboolaId,
	taboolaType = 'generic',
	title = '',
	className = '',
}: TaboolaContentProps) {
	// Check if Taboola is allowed to display based on current user
	const allowTaboola = useAllowTaboola();
	// Keep track of whether Taboola container is populated with content
	const [hasContent, setHasContent] = useState(false);

	useEffect(() => {
		const taboolaDiv = document.getElementById(taboolaId);
		if (!taboolaDiv) return;

		const contentObserver = new MutationObserver(() => {
			setHasContent(taboolaDiv.hasChildNodes());
		});

		// Observe for changes to Taboola container children (i.e. content added or removed)
		contentObserver.observe(taboolaDiv, {
			childList: true,
		});

		return () => contentObserver.disconnect();
	}, [allowTaboola, taboolaId]);

	// Don't render anything if we're missing required props
	if (!allowTaboola || !taboolaId) return null;

	// For invisible type, render just the container without any styling
	if (taboolaType === 'invisible') {
		return <div id={taboolaId} />;
	}

	// Render Taboola content based on taboolaType
	return taboolaType === 'generic' ? (
		<Card className={cn(className, !hasContent && 'hidden')}>
			{title && (
				<CardHeader>
					<h2 className="text-2xl font-bold">{title}</h2>
				</CardHeader>
			)}
			<CardContent className="z-40">
				<div id={taboolaId} className="bg-none" />
			</CardContent>
		</Card>
	) : (
		<div
			className={cn(
				'wx-media-object gradient-overlay thirdparty-feed border',
				className,
				!hasContent && 'hidden',
			)}
		>
			{title && (
				<header aria-label="Taboola" className="panel-heading mb-2">
					<h2 data-testid="taboolaHeading" className="text-lg font-medium">
						{title}
					</h2>
				</header>
			)}
			<div
				id={taboolaId}
				className="flex min-h-[300px] w-full items-center justify-center"
			/>
		</div>
	);
}

export default TaboolaContent;
