# TABOOLA

## Rules

Taboola rules as of May 2020:

1. There should be a Taboola module on nearly every page in the site, for all locales. (Various privacy and legal pages, and the interactive map page, are excluded. All of the highest-traffic pages are included; e.g. home, today, fiveday, tenday, weekend, monthly, hourly, article, index, and video.)
2. On de-DE pages, Outbrain is used instead of Taboola, so, no visible Taboola instances should be present there.
3. However, on de-DE Video and Article pages, an Invisible Taboola instance is required to help the Newsroom feature work properly. Those are the only de-DE pages which should have a Taboola instance, and it must be set to "type": "invisible" in its module props, so that it does not display to users.
4. Each Taboola instance must have module props correctly set (see [The Rules For Module Props](#the-rules-for-module-props), below).

## The Rules For Module Props

Props for a typical Taboola module instance should follow this pattern:

```json
{
	"ttl": 900,
	"limit": 2,
	"wrapInPanel": true,
	"bypassServer": true,
	"uuid": "9a93243f-6141-4011-8f60-0cc3f0c1b9c1",
	"id": "taboola-below-content-thumbnails-hourly",
	"type": "generic",
	"titles": {
		"es-ES": { "title": "Contenido Patrocinado" },
		"vi-VN": { "title": "Nội dung được tài trợ" },
		"en-SG": { "title": "Sponsored Content" }
	},
	"title": "Sponsored Content",
	"configs": {
		"url": "//cdn.taboola.com/libtrc/theweatherchannel/loader.js",
		"scriptId": "tb_loader_script",
		"commands": [
			{
				"placement": "Below Content Thumbnails - hourly",
				"container": "taboola-below-content-thumbnails-hourly",
				"mode": "thumbnails-d",
				"target_type": "mix"
			},
			{
				"category": "auto"
			},
			{
				"flush": true
			}
		]
	}
}
```

### How to ensure correct values for Taboola props

The rules for correct prop vaules are as follows:

`ttl`, `limit`, `wrapInPanel`, `bypassServer`: Always use the values shown above (in the future these may be hard-coded or eliminated)

`uuid`: The uuid of the module in the CMS, or auto-generated for a dynamically-inserted instance

`id`: Must match the id of a div inserted into the DOM by the module; should always be identical to `configs.commands[0].container`

`type`: either "generic" or "invisible" according to whether this is a Taboola instance with visible content, or an "invisible" instance existing to enable Newsroom support (see [Newsroom support and "invisible" Taboola instances](#newsroom-support-and-invisible-taboola-instances) below)

`titles`: allows translations for the module heading to be set in the module props on a per-locale basis; this overrides the default behavior (which is "Sponsored Content" translated for the locale)

`title`: a single value for the module heading; this overrides both the default behavior and `titles`

`configs.url`: the loader to be used (see [Setting the loader](#setting-the-loader) below)

`configs.scriptId`: always use "tb_loader_script" as shown above

`configs.commands[0].placement`: a human-friendly description of the location of the Taboola module instance (see [Constructing the placement name](#constructing-the-placement-name), below)

`configs.commands[0].container`: a computer-friendly version ot the placement value, used to set the id of the container div in the DOM (must be identical to `id`; see [Constructing the container value](#constructing-the-container-value), below)

`configs.commands[0].mode`: either "thumbnails-d" or "thumbnails-rr", where "-d" will arrange the content thumbnails widely for the main Content area, and "-rr" will arrange them in a single column suitable for a Right Rail (see [Setting the mode](#setting-the-mode), below)

`configs.commands[0].target_type`: always "mix" as shown above

`configs.commands[1]`: an object containing a property _name_ representing the pageType; the value is always set to "auto" (for more information, see [Setting the pageType](#setting-the-pagetype), below)

`configs.commands[2].flush`: always "true" as shown above

### Setting the loader

Three loader values are used for Taboola modules, according to whether the module is on an en-US page, an "International" page (refers to any locale other than en-US, e.g. fr-FR or es-US), or is used on de-DE for Newsroom (type: "invisible").

The correct values are as follows:

- en-US: `"//cdn.taboola.com/libtrc/theweatherchannel/loader.js"`
- International: `"//cdn.taboola.com/libtrc/theweatherchannelinternational-network/loader.js"`
- de-DE Invisible/Newsroom: `"//cdn.taboola.com/libtrc/theweatherchannelinternational-germany/loader.js"`

### Constructing the placement name

Generic Taboola module instances (those with visible sponsored content) are displayed either near the bottom of the page (under the primary content) or in a right rail. The placement value will correspondingly begin with either "Below Content Thumbnails" or "Right Rail Thumbnails", followed by a space, a dash, a space, and the page key. For example, on the hourly page, two Taboola instances would use:
`"Below Content Thumbnails - hourly"`
`"Right Rail Thumbnails - hourly"`

Invisible Taboola module instances (used to enable Newsroom, but otherwise hidden) always begin with "Newsroom" followed by a space, a dash, a space, and the page key:
`"Newsroom - video"`

### Constructing the container value

The container value (also used for the module id prop) is created by taking the placement name, prepending "Taboola ", and removing all spaces, substituting dashes for word-separation, and converting to lowercase. Hence, `"Below Content Thumbnails - hourly"` becomes `"taboola-below-content-thumnails-hourly"`. Invisible Taboola instances likewise convert `"Newsroom - video"` to `"taboola-newsroom-video"`.

### Setting the mode

Three modes are available:

1. `"thumbnails-d"` for multi-column visible content (in "main" or "bottom" regions)
2. `"thumbnails-rr"` for single-column visible content (in a "rail" region)
3. `"rbox-tracking"` for invisible content (used with Newsroom)

### Setting the pageType

Taboola defines several pageType values representing broad categories of pages. The page key of a weather.com page is mapped to the Taboola pageType as follows (`default` is used for any page keys not listed):

```js
export const TABOOLA_PAGE_TYPES = {
	home: "home",
	article: "article",
	video: "video",
	today: "category",
	fiveday: "category",
	tenday: "category",
	hourly: "category",
	weekend: "category",
	monthly: "category",
	default: "other",
};
```

### Newsroom support and "invisible" Taboola instances

Newsroom is used on various pages, including de-DE Article and Video pages. To make it work properly, a Taboola instance must be present. However, de-DE (Burda) uses Outbrain for sponsored content on their pages, and does not want additional sponsored content from Taboola. For this reason, each de-DE Article or Video page should use an Invisible Taboola instance to enable Newsroom.

The following is an example of correct props for an Invisible Taboola instance used on the de-DE Article page:

```json
{
	"ttl": 900,
	"limit": 2,
	"wrapInPanel": true,
	"bypassServer": true,
	"uuid": "01234567-89ab-cdef-0123-456789abcdef",
	"id": "taboola-newsroom-article",
	"type": "invisible",
	"titles": {
		"es-ES": { "title": "Contenido Patrocinado" },
		"vi-VN": { "title": "Nội dung được tài trợ" },
		"en-SG": { "title": "Sponsored Content" }
	},
	"configs": {
		"url": "//cdn.taboola.com/libtrc/theweatherchannelinternational-germany/loader.js",
		"scriptId": "tb_loader_script",
		"commands": [
			{
				"placement": "Newsroom - article",
				"container": "taboola-newsroom-article",
				"mode": "rbox-tracking",
				"target_type": "mix"
			},
			{
				"article": "auto"
			},
			{
				"flush": true
			}
		]
	}
}
```

NOTE: The "title" and "titles" properties are skipped since this is meant to be invisible.

## Taboola module instances in Moonracer

As of May 2020, all the previously-existing Taboola module instances in Moonracer have been removed and replaced with correctly-configured instances which should cover all future needs. Examples include:

- `Taboola | de-DE | Video | Invisible`
- `Taboola | de-DE | Article | Invisible`
- `Taboola | Intl | Index | Right-Rail`
- `Taboola | Intl | Index | Below-Content`
- `Taboola | en-US | Index | Right-Rail`
- `Taboola | en-US | Index | Below-Content`
- `Taboola | Intl | Home | Below-Content`

..., etc.

The naming convention here is very specific; do not deviate from it. The names of the new instances correspond to the values used in their module props, so that it is possible to deduce the exact module props from the module's name. For example, `Taboola | Intl | Home | Below-Content` will have the props specifying the International loader; "Below Content Thumbnails - " will start the placement value, "taboola-below-content-thumbnails-" will start the container and id, and "home" will be the page key at the end of both the placement and the container.
