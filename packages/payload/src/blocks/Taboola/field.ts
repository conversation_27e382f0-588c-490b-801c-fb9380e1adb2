import type { Field, TextField, SelectField, RowField } from 'payload';

// Main fields
export const titleField: TextField = {
	name: 'title',
	type: 'text',
	label: 'Block Title',
	required: true,
	defaultValue: 'Sponsored Content',
};

export const idField: TextField = {
	name: 'taboolaId',
	type: 'text',
	label: 'Taboola Container ID',
	required: true,
	defaultValue: 'taboola-below-content-thumbnails',
};

export const placementField: TextField = {
	name: 'placement',
	type: 'text',
	label: 'Placement Name',
	required: true,
	defaultValue: 'Below Content Thumbnails',
};

export const modeField: SelectField = {
	name: 'mode',
	type: 'select',
	label: 'Display Mode',
	required: true,
	defaultValue: 'thumbnails-d',
	options: [
		{
			label: 'Multi-column (Main Content)',
			value: 'thumbnails-d',
		},
		{
			label: 'Invisible (Newsroom)',
			value: 'rbox-tracking',
		},
	],
};

export const typeField: SelectField = {
	name: 'taboolaType',
	type: 'select',
	label: 'Taboola Type',
	required: true,
	defaultValue: 'generic',
	options: [
		{
			label: 'Generic (Visible)',
			value: 'generic',
		},
		{
			label: 'Invisible (Newsroom)',
			value: 'invisible',
		},
	],
};

export const loaderUrlField: SelectField = {
	name: 'loaderUrl',
	type: 'select',
	label: 'Loader URL',
	required: true,
	defaultValue: '//cdn.taboola.com/libtrc/theweatherchannel/loader.js',
	options: [
		{
			label: 'US',
			value: '//cdn.taboola.com/libtrc/theweatherchannel/loader.js',
		},
		{
			label: 'International',
			value:
				'//cdn.taboola.com/libtrc/theweatherchannelinternational-network/loader.js',
		},
		{
			label: 'Germany (Invisible/Newsroom)',
			value:
				'//cdn.taboola.com/libtrc/theweatherchannelinternational-germany/loader.js',
		},
	],
};

export const pageTypeField: SelectField = {
	name: 'pageType',
	type: 'select',
	label: 'Page Type',
	required: true,
	defaultValue: 'category',
	options: [
		{
			label: 'Home',
			value: 'home',
		},
		{
			label: 'Article',
			value: 'article',
		},
		{
			label: 'Video',
			value: 'video',
		},
		{
			label: 'Category (Today, Forecast, etc.)',
			value: 'category',
		},
		{
			label: 'Other',
			value: 'other',
		},
	],
};

// Display options
export const variantField: SelectField = {
	name: 'variant',
	type: 'select',
	label: 'Display Variant',
	defaultValue: 'banner',
	options: [
		{
			label: 'Banner',
			value: 'banner',
		},
		{
			label: 'Sidebar',
			value: 'sidebar',
		},
	],
};

export const heightField: TextField = {
	name: 'height',
	type: 'text',
	label: 'Initial Height',
	defaultValue: '300px',
};

// Group fields into logical rows
export const displayOptionsRow: RowField = {
	type: 'row',
	fields: [variantField, heightField],
};

export const taboolaConfigRow: RowField = {
	type: 'row',
	fields: [idField, placementField],
};

export const modeConfigRow: RowField = {
	type: 'row',
	fields: [modeField, typeField],
};

// Export all fields as an array for config.ts
export const taboolaFields: Field[] = [
	titleField,
	taboolaConfigRow,
	modeConfigRow,
	loaderUrlField,
	pageTypeField,
	displayOptionsRow,
];
