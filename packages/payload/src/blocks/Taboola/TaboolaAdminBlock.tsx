'use client';

import React, { FC } from 'react';
import { useAllFormFields } from '@payloadcms/ui';
import { reduceFieldsToValues } from 'payload/shared';

import AdminBlock from '@repo/payload/components/AdminBlock';

export const TaboolaAdminBlock: FC = () => {
	const [fields] = useAllFormFields();
	const formData = reduceFieldsToValues(fields, true);

	const renderDetails = () => {
		return (
			<div className="flex flex-col gap-2">
				<div>
					<strong>ID:</strong> {formData?.taboolaId || 'No ID'}
				</div>
				<div>
					<strong>Placement:</strong> {formData?.placement || 'No Placement'}
				</div>
				<div>
					<strong>Mode:</strong> {formData?.mode || 'No Mode'}
				</div>
				<div>
					<strong>Type:</strong> {formData?.taboolaType || 'Generic'}
				</div>
				<div>
					<strong>Page Type:</strong> {formData?.pageType || 'No Page Type'}
				</div>
				{formData?.title && (
					<div>
						<strong>Title:</strong> {formData.title}
					</div>
				)}
			</div>
		);
	};

	return <AdminBlock name="Taboola">{renderDetails()}</AdminBlock>;
};

export default TaboolaAdminBlock;
