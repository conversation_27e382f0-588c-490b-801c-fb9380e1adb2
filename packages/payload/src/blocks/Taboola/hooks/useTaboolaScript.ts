'use client';

import { useEffect, useState } from 'react';

/**
 * Hook to load the Taboola script only once across all components
 * @returns Object with isTaboolaLoaded state indicating if the Taboola script is loaded
 */
export const useTaboolaScript = () => {
	const [isTaboolaLoaded, setIsTaboolaLoaded] = useState<boolean>(false);

	useEffect(() => {
		// exit if the Taboola script is already loaded
		if (isTaboolaLoaded) {
			return;
		}

		// exit if the script element is already on the page, also updating the state to reflect this
		if (document.getElementById('tb_loader_script')) {
			setIsTaboolaLoaded(true);
			return;
		}

		// otherwise, load the Taboola script
		window._taboola = window._taboola || []; // global command queue for Taboola

		if (window.performance && typeof window.performance.mark === 'function') {
			window.performance.mark('tbl_ic');
		}

		const scriptElement = document.createElement('script');
		// TODO: Taboola loaderURL is currently always constant, but will be dynamic for non-US locales
		scriptElement.src = '//cdn.taboola.com/libtrc/theweatherchannel/loader.js';
		scriptElement.id = 'tb_loader_script';
		scriptElement.async = true;
		scriptElement.onload = () => {
			setIsTaboolaLoaded(true);
		};
		scriptElement.onerror = () => {
			console.error('Failed to load Taboola script.');
		};

		document.body.appendChild(scriptElement);
	}, [isTaboolaLoaded]);

	return { isTaboolaLoaded };
};
