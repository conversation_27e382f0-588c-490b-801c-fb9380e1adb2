'use client';

import { useEffect, useState } from 'react';
import { useAtomValue } from 'jotai';
import { useUser } from '@repo/user/hooks/useUser';
import { dprSdkIsInitializedAtom } from '@repo/dpr-sdk';

// Helper function to check if the DprSdk feature flag for Taboola is set to true
const getDprAllowTaboola = (): boolean => {
	try {
		if (
			typeof window !== 'undefined' &&
			typeof window?.DprSdk !== 'undefined'
		) {
			const dprFeatureFlags = window?.DprSdk?.getFeatureFlags();

			return dprFeatureFlags?.taboola ?? true;
		}
		return true;
	} catch (_err) {
		return true;
	}
};

/**
 * Hook to check if Taboola should be allowed to display
 * @returns boolean indicating if Taboola should be displayed
 */
export function useAllowTaboola(): boolean {
	const { DISCONNECTED_user: user } = useUser();
	const [dprAllowTaboola, setDprAllowTaboola] = useState(false);
	const isDprSdkInitialized = useAtomValue(dprSdkIsInitializedAtom);

	useEffect(() => {
		if (isDprSdkInitialized) {
			setDprAllowTaboola(getDprAllowTaboola());
		}
	}, [isDprSdkInitialized]);

	return dprAllowTaboola && !user?.isUserPremium;
}

export default useAllowTaboola;
