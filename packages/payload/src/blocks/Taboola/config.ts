import { taboolaFields } from './field';
import type { Block } from 'payload';

export const TaboolaBlockConfig: Block = {
	slug: 'Taboola',
	interfaceName: 'TaboolaBlockConfig',
	labels: {
		singular: 'Taboola Block',
		plural: 'Taboola Blocks',
	},
	fields: taboolaFields,
	// Component to render in the admin interface
	admin: {
		components: {
			Block: {
				path: '@repo/payload/blocks/admin#TaboolaAdminBlock',
			},
		},
	},
};

export default TaboolaBlockConfig;
