# Blocks

## Folder Structure

```text
wx-next/apps/web/blocks/
├── BlockA/
├──── config.ts
├──── BlockABlock.tsx
├──── BLockAField.tsx
├── BlockB/
├──── config.ts
├──── BlockBBlock.tsx
├──── BLockBField.tsx
├── ...
└── README.md
```

### config.ts

Defines the Block configuration schema, fields, and admin component.

**Note:** Block slugs are permanent whereby if content/configuration is created, it will require a migrations to update old slugs to new slugs.

## Block.tsx

React component used for the rendering of the block.

## Field.tsx

React component used for rendering the field on the admin interface.

## Block Documentation

Some blocks have their own README.md files with detailed documentation:

- [Video Block](./Video/README.md): Documentation for the Video block, which integrates with the JWPlayer component to provide video playback functionality.
