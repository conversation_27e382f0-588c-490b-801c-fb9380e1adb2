import { FC } from 'react';
import MorningBriefBlock from './MorningBrief/MorningBriefBlock';
import MorningBriefBlockConfig from './MorningBrief/config';
import DailyForecastBlock from './DailyForecast/DailyForecastBlock';
import DailyForecastBlockConfig from './DailyForecast/config';
import CurrentConditionsBlock from './CurrentConditions/CurrentConditionsBlock';
import CurrentConditionsBlockConfig from './CurrentConditions/config';
import YouTubeBlock from './YouTube/YouTubeBlock';
import YouTubeBlockConfig from './YouTube/config';
import BuyButtonBlock from './BuyButton/BuyButtonBlock';
import BuyButtonBlockConfig from './BuyButton/config';
import AdBlock from './Ad/AdBlock';
import AdBlockConfig from './Ad/config';
import ImageBlock from './Image/ImageBlock';
import ImageBlockConfig from './Image/config';
import VideoBlock from './Video/VideoBlock';
import VideoBlockConfig from './Video/config';
import ContentMediaBlock from './ContentMedia/ContentMediaBlock';
import ContentMediaBlockConfig from './ContentMedia/config';
import CallToActionBlock from './CallToAction/CallToActionBlock';
import CallToActionBlockConfig from './CallToAction/config';
import TaboolaBlock from './Taboola/TaboolaBlock';
import TaboolaBlockConfig from './Taboola/config';
import TwitterBlock from './Twitter/TwitterBlock';
import TwitterBlockConfig from './Twitter/config';
import LiveblogEntriesBlock from './LiveblogEntries/LiveblogEntriesBlock';
import LiveblogEntriesBlockConfig from './LiveblogEntries/config';
import SlideshowBlock from './Slideshow/SlideshowBlock';
import SlideshowBlockConfig from './Slideshow/config';
import PromoDriverBlock from './PromoDriver/PromoDriverBlock';
import PromoDriverBlockConfig from './PromoDriver/config';
import InstagramBlock from './Instagram/InstagramBlock';
import InstagramBlockConfig from './Instagram/config';

import type {
	MorningBriefBlockConfig as IMorningBrief,
	DailyForecastBlockConfig as IDailyForecast,
	CurrentConditionsBlockConfig as ICurrentConditions,
	AdBlockConfig as IAd,
	ImageBlockConfig as IImage,
	VideoBlockConfig as IVideo,
	ContentMediaBlockConfig as IContentMedia,
	LiveblogEntriesBlockConfig as ILiveblogEntries,
	CallToActionBlockConfig as ICallToAction,
	SlideshowBlockConfig as ISlideshow,
	PromoDriverBlock as IPromoDriver,
	YouTubeBlockConfig as IYoutube,
	BuyButtonBlockConfig as IBuyButton,
	TwitterBlockConfig as ITwitter,
	InstagramBlockConfig as IInstagram,
	// generator-type-imports-insertion-point
	// missing Taboola as a block
} from '@repo/payload/payload-types';

// Union of all block types - automatically derived from payload types
// missing Taboola as a block
export type BlockConfig =
	| IMorningBrief
	| IDailyForecast
	| ICurrentConditions
	| IAd
	| IImage
	| IVideo
	| IContentMedia
	| ILiveblogEntries
	| ICallToAction
	| ISlideshow
	| IPromoDriver
	| IYoutube
	| IBuyButton
	| ITwitter
	| IInstagram;
// generator-union-type-insertion-point

// Enhanced block registration interface
export interface BlockRegistration {
	slug: string;
	component: FC<any>; // eslint-disable-line @typescript-eslint/no-explicit-any
}

// Typed block registrations - this is now the single source of truth
export const blockRegistrations: BlockRegistration[] = [
	{
		slug: MorningBriefBlockConfig.slug,
		component: MorningBriefBlock,
	},
	{
		slug: DailyForecastBlockConfig.slug,
		component: DailyForecastBlock,
	},
	{
		slug: CurrentConditionsBlockConfig.slug,
		component: CurrentConditionsBlock,
	},
	{
		slug: YouTubeBlockConfig.slug,
		component: YouTubeBlock,
	},
	{
		slug: BuyButtonBlockConfig.slug,
		component: BuyButtonBlock,
	},
	{
		slug: AdBlockConfig.slug,
		component: AdBlock,
	},
	{
		slug: ImageBlockConfig.slug,
		component: ImageBlock,
	},
	{
		slug: VideoBlockConfig.slug,
		component: VideoBlock,
	},
	{
		slug: ContentMediaBlockConfig.slug,
		component: ContentMediaBlock,
	},
	{
		slug: TwitterBlockConfig.slug,
		component: TwitterBlock,
	},
	{
		slug: LiveblogEntriesBlockConfig.slug,
		component: LiveblogEntriesBlock,
	},
	{
		slug: CallToActionBlockConfig.slug,
		component: CallToActionBlock,
	},
	{
		slug: SlideshowBlockConfig.slug,
		component: SlideshowBlock,
	},
	{
		slug: TaboolaBlockConfig.slug,
		component: TaboolaBlock,
	},
	{
		slug: PromoDriverBlockConfig.slug,
		component: PromoDriverBlock,
	},
	{
		slug: InstagramBlockConfig.slug,
		component: InstagramBlock,
	},
];

// Generate the BLOCK_MAP from registrations
export const BLOCK_MAP = blockRegistrations.reduce(
	(acc, { slug, component }) => {
		acc[slug] = component;
		return acc;
	},
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	{} as Record<string, FC<any>>,
);

export * from './MorningBrief/MorningBriefBlock';
export * from './DailyForecast/DailyForecastBlock';
export * from './CurrentConditions/CurrentConditionsBlock';
export * from './YouTube/YouTubeBlock';
export * from './BuyButton/BuyButtonBlock';
export * from './Ad/AdBlock';
export * from './Image/ImageBlock';
export * from './Video/VideoBlock';
export * from './ContentMedia/ContentMediaBlock';
export * from './CallToAction/CallToActionBlock';
export * from './Taboola/TaboolaBlock';
export * from './Twitter/TwitterBlock';
export * from './LiveblogEntries/LiveblogEntriesBlock';
export * from './Slideshow/SlideshowBlock';
export * from './PromoDriver/PromoDriverBlock';
export * from './Instagram/InstagramBlock';
// generator-exports-insertion-point
