# BuyButton Block

The BuyButton block provides a customizable button component that can be used as a call-to-action for purchases or other actions.

## Features

- Multiple color variants
- Inline or block display options
- Affiliate link support with proper SEO attributes
- Customizable button text

## Usage

### Basic Usage

```jsx
<BuyButton
	label="Buy Now"
	url="https://example.com/product"
	variant="bg-gray-900"
	inline={false}
	isAffiliate={true}
/>
```

### Affiliate Link Support

The BuyButton component includes built-in support for affiliate links with proper SEO attributes. When `isAffiliate` is set to `true` (the default), the component automatically adds the following attributes to the link:

```html
rel="nofollow noopener noreferrer"
```

This follows SEO best practices for affiliate links by:

1. `nofollow` - Tells search engines not to follow the link or pass PageRank
2. `noopener` - Prevents the new page from accessing the window.opener property
3. `noreferrer` - Prevents passing the referrer information to the linked page

For non-affiliate links, only the `noopener` attribute is added for security.

### Example: Affiliate Link

```jsx
<BuyButton
	label="Shop Now"
	url="https://affiliate.example.com/product"
	variant="bg-yellow-400"
	isAffiliate={true}
/>
```

### Example: Standard Link

```jsx
<BuyButton
	label="Learn More"
	url="https://example.com/info"
	variant="bg-brand-200"
	isAffiliate={false}
/>
```

## Props

| Prop          | Type    | Default       | Description                           |
| ------------- | ------- | ------------- | ------------------------------------- |
| `label`       | string  | Required      | The text to display on the button     |
| `url`         | string  | Required      | The URL to link to                    |
| `variant`     | string  | 'bg-gray-900' | The color variant of the button       |
| `inline`      | boolean | false         | Whether to display the button inline  |
| `isAffiliate` | boolean | true          | Whether the link is an affiliate link |

## Available Variants

- `bg-brand-200` - Light brand color
- `bg-brand-300` - Dark brand color
- `bg-yellow-400` - Yellow
- `bg-green-500` - Green
- `bg-violet-500` - Violet
- `bg-gray-900` - Dark gray (default)

## Admin Interface

The BuyButton block includes a custom admin interface in PayloadCMS that provides:

1. A live preview of the button
2. Clear indicators for display mode (inline/block)
3. Clear indicators for link type (affiliate/standard)
4. Instructions for editing settings

To edit the button settings, click the edit button in the admin interface.
