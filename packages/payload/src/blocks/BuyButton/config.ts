import { url, label, variant, inline, isAffiliate } from './field';
import type { Block } from 'payload';

export const BuyButtonBlockConfig: Block = {
	slug: 'BuyButton',
	interfaceName: 'BuyButtonBlockConfig',
	labels: {
		singular: 'BuyButton Block',
		plural: 'BuyButton Blocks',
	},
	fields: [url, label, variant, inline, isAffiliate],
	// Component to render in the admin interface
	admin: {
		components: {
			Block: {
				path: '@repo/payload/blocks/admin#BuyButtonAdminBlock',
			},
		},
	},
};

export default BuyButtonBlockConfig;
