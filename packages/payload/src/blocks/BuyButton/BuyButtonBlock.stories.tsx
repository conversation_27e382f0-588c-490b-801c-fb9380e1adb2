import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import BuyButtonBlock, { type BuyButtonProps } from './BuyButtonBlock';

const meta: Meta<typeof BuyButtonBlock> = {
	title: 'Article Blocks/Buy Button',
	component: BuyButtonBlock,
	parameters: {
		layout: 'padded',
	},
	argTypes: {
		label: { control: 'text' },
		url: { control: 'text' },
		variant: {
			control: 'select',
			options: [
				'bg-brand-200',
				'bg-brand-300',
				'bg-yellow-400',
				'bg-green-600',
				'bg-violet-500',
				'bg-gray-900',
			],
		},
		inline: { control: 'boolean' },
		isAffiliate: { control: 'boolean' },
	},
};

export default meta;
type Story = StoryObj<typeof BuyButtonBlock>;

// Interactive story with controls
export const Default: Story = {
	args: {
		label: 'Buy Now',
		url: 'https://example.com/product',
		variant: 'bg-gray-900',
		inline: false,
		isAffiliate: true,
	},
};

// Color variants
const colorVariants = [
	{ name: 'Brand Light', variant: 'bg-brand-200' },
	{ name: 'Brand Dark', variant: 'bg-brand-300' },
	{ name: 'Yellow', variant: 'bg-yellow-400' },
	{ name: 'Green', variant: 'bg-green-600' },
	{ name: 'Violet', variant: 'bg-violet-500' },
	{ name: 'Gray (Default)', variant: 'bg-gray-900' },
];

export const ColorVariants: Story = {
	render: () => (
		<div className="space-y-6 p-6">
			<h2 className="text-2xl font-bold">Color Variants</h2>
			<div className="space-y-4">
				{colorVariants.map((item) => (
					<div
						key={item.variant}
						className="grid grid-cols-10 items-center border-b py-2"
					>
						<div className="col-span-2">
							<code className="text-sm">{item.name}</code>
						</div>
						<div className="col-span-8">
							<div className="block">
								<BuyButtonBlock
									label={`${item.name} Button`}
									url="#"
									variant={item.variant as BuyButtonProps['variant']}
									inline={true}
								/>
							</div>
						</div>
					</div>
				))}
			</div>
		</div>
	),
};

// Display modes
export const DisplayModes: Story = {
	render: () => (
		<div className="space-y-6 p-6">
			<h2 className="text-2xl font-bold">Display Modes</h2>

			<div className="space-y-4 border-b py-2">
				<h3 className="text-xl font-semibold">Block Mode (Default)</h3>
				<p className="mb-4">
					Buttons appear on their own line, sized to their content:
				</p>
				<div className="space-y-2">
					<BuyButtonBlock
						label="Short Button"
						url="#"
						variant="bg-brand-200"
						inline={false}
					/>
					<BuyButtonBlock
						label="Medium Length Button Example"
						url="#"
						variant="bg-brand-300"
						inline={false}
					/>
					<BuyButtonBlock
						label="This is a much longer button text to demonstrate responsive sizing"
						url="#"
						variant="bg-gray-900"
						inline={false}
					/>
					<BuyButtonBlock
						label="Maximum Length (45 chars): The quick brown fox jumps over"
						url="#"
						variant="bg-violet-500"
						inline={false}
					/>
				</div>
			</div>

			<div className="space-y-4 py-2">
				<h3 className="text-xl font-semibold">Inline Mode</h3>
				<p className="mb-4">Buttons flow with text and appear side by side:</p>
				<div className="flex flex-wrap gap-2">
					<BuyButtonBlock
						label="First Button"
						url="#"
						variant="bg-yellow-400"
						inline={true}
					/>
					<BuyButtonBlock
						label="Second Button"
						url="#"
						variant="bg-green-600"
						inline={true}
					/>
					<BuyButtonBlock
						label="Third Button"
						url="#"
						variant="bg-violet-500"
						inline={true}
					/>
				</div>
				<p className="mt-4">
					Text before
					<BuyButtonBlock
						label="Inline Button"
						url="#"
						variant="bg-brand-200"
						inline={true}
					/>
					and text after the button to demonstrate inline flow.
				</p>
			</div>
		</div>
	),
};

// Affiliate vs Standard Links
export const LinkTypes: Story = {
	render: () => (
		<div className="space-y-6 p-6">
			<h2 className="text-2xl font-bold">Link Types</h2>

			<div className="space-y-4">
				<div className="grid grid-cols-10 items-center border-b py-2">
					<div className="col-span-3">
						<code className="text-sm">Affiliate Link (Default)</code>
						<p className="mt-1 text-xs text-gray-500">
							rel=&quot;nofollow noopener noreferrer&quot;
						</p>
					</div>
					<div className="col-span-7">
						<BuyButtonBlock
							label="Affiliate Link Example"
							url="#"
							variant="bg-brand-300"
							isAffiliate={true}
						/>
					</div>
				</div>

				<div className="grid grid-cols-10 items-center border-b py-2">
					<div className="col-span-3">
						<code className="text-sm">Standard Link</code>
						<p className="mt-1 text-xs text-gray-500">
							rel=&quot;noopener&quot;
						</p>
					</div>
					<div className="col-span-7">
						<BuyButtonBlock
							label="Standard Link Example"
							url="#"
							variant="bg-green-600"
							isAffiliate={false}
						/>
					</div>
				</div>
			</div>
		</div>
	),
};

// Combined showcase
export const AllFeatures: Story = {
	render: () => (
		<div className="space-y-8 p-6">
			<section>
				<h2 className="mb-4 text-2xl font-bold">
					BuyButton Component Showcase
				</h2>
				<p className="mb-6">
					The BuyButton component provides a customizable button for purchase
					calls-to-action, with support for different colors, display modes, and
					proper SEO attributes for affiliate links.
				</p>
			</section>

			<section className="space-y-4">
				<h3 className="text-xl font-semibold">Color Variants</h3>
				<div className="flex flex-wrap gap-2">
					{colorVariants.map((item) => (
						<BuyButtonBlock
							key={item.variant}
							label={item.name}
							url="#"
							variant={item.variant as BuyButtonProps['variant']}
							inline={true}
						/>
					))}
				</div>
			</section>

			<section className="space-y-4">
				<h3 className="text-xl font-semibold">Display Modes</h3>
				<div className="mb-4 space-y-2">
					<p className="font-medium">Block Mode:</p>
					<BuyButtonBlock
						label="Block Button Example"
						url="#"
						variant="bg-brand-300"
						inline={false}
					/>
				</div>
				<div>
					<p className="mb-2 font-medium">Inline Mode:</p>
					<p>
						Text before
						<BuyButtonBlock
							label="Inline Button"
							url="#"
							variant="bg-yellow-400"
							inline={true}
						/>
						and text after.
					</p>
				</div>
			</section>

			<section className="space-y-4">
				<h3 className="text-xl font-semibold">Link Types</h3>
				<div className="space-y-2">
					<BuyButtonBlock
						label="Affiliate Link (rel='nofollow noopener noreferrer')"
						url="#"
						variant="bg-gray-900"
						isAffiliate={true}
					/>
					<BuyButtonBlock
						label="Standard Link (rel='noopener')"
						url="#"
						variant="bg-violet-500"
						isAffiliate={false}
					/>
				</div>
			</section>
		</div>
	),
};
