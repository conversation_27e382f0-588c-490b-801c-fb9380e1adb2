import type { SelectField, TextField, CheckboxField } from 'payload';
import { buttonVariantsConfig } from '@repo/ui/components/BuyButton';

export const url: TextField = {
	name: 'url',
	label: 'Affiliant URL',
	type: 'text',
	defaultValue: '#',
	required: true,
};

export const label: TextField = {
	name: 'label',
	label: 'Label',
	type: 'text',
	maxLength: 45,
	validate: (value) => {
		if (value && value.length > 45) {
			return 'Label must be 45 characters or less';
		}
		return true;
	},
	defaultValue: 'Buy Now',
	required: true,
};

export const variant: SelectField = {
	name: 'variant',
	label: 'Color Variant',
	type: 'select',
	options: Object.keys(buttonVariantsConfig.variant).map((variant) => {
		// Format the label for better readability
		const formattedLabel = variant
			.replace('bg-', '')
			.split('-')
			.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
			.join(' ');

		return {
			label: formattedLabel,
			value: variant,
		};
	}),
	defaultValue: 'bg-gray-900',
};

export const inline: CheckboxField = {
	name: 'inline',
	label: 'Display Inline',
	type: 'checkbox',
	defaultValue: false,
};

export const isAffiliate: CheckboxField = {
	name: 'isAffiliate',
	label: 'Affiliate Link',
	type: 'checkbox',
	defaultValue: true,
	admin: {
		description:
			'Adds proper rel attributes for affiliate links (nofollow, noopener, noreferrer)',
	},
};
