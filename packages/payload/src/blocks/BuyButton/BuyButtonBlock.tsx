'use client';

import React from 'react';
import type { BuyButtonBlockConfig } from '@repo/payload/payload-types';
import { Button } from '@repo/ui/components/BuyButton';
import Link from '@repo/navigation/components/Link';

export type BuyButtonProps = Omit<BuyButtonBlockConfig, 'blockType'>;

export const BuyButtonBlock: React.FC<BuyButtonProps> = ({
	label,
	url,
	variant = 'bg-gray-900',
	inline = false,
	isAffiliate = true,
}) => {
	// Set appropriate rel attributes based on isAffiliate flag
	const relAttributes = isAffiliate
		? 'nofollow noopener noreferrer'
		: 'noopener';

	return (
		<div className={inline ? 'inline-block' : 'block'}>
			<Link href={url} target="_blank" rel={relAttributes}>
				<Button
					variant={variant}
					inline={false} // Always use inline styling for the button itself
					className="cursor-pointer whitespace-nowrap"
				>
					{label}
				</Button>
			</Link>
		</div>
	);
};

export default BuyButtonBlock;
