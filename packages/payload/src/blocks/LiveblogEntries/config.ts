import type { Block } from 'payload';

export const LiveblogEntriesBlockConfig: Block = {
	slug: 'LiveblogEntries',
	interfaceName: 'LiveblogEntriesBlockConfig', // optional
	labels: {
		singular: 'Liveblog Entry',
		plural: 'Liveblog Entries',
	},
	fields: [],
	// Component to render in the admin interface
	admin: {
		components: {
			Block: {
				path: '@repo/payload/blocks/admin#LiveblogEntriesAdminBlock',
			},
		},
	},
};

export default LiveblogEntriesBlockConfig;
