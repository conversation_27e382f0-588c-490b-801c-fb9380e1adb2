import { validateEntryMethod } from './hooks/validateEntryMethod';
import { updateMethodBeforeChange } from './hooks/updateMethodOnChange';
import { normalizeTweetURL } from './hooks/normalizeTweetURL';

import type { Field } from 'payload';

export const entryMethod: Field = {
	name: 'entryMethod',
	label: 'Entry Method',
	type: 'radio',
	defaultValue: 'url',
	hooks: {
		beforeChange: [updateMethodBeforeChange],
	},
	admin: {
		layout: 'horizontal',
	},
	options: [
		{
			label: 'Twitter URL',
			value: 'url',
		},
		{
			label: 'Tweet ID',
			value: 'id',
		},
	],
	required: true,
};

export const tweetURL: Field = {
	name: 'tweetURL',
	label: 'Tweet URL',
	type: 'text',
	required: true,
	admin: {
		condition: (_, siblingData) => siblingData?.entryMethod === 'url',
		description: 'Paste the full URL of the tweet',
	},
	hooks: {
		beforeChange: [normalizeTweetURL],
	},
	validate: validateEntryMethod,
};

export const tweetId: Field = {
	name: 'tweetId',
	label: 'Tweet ID',
	type: 'text',
	required: true,
	admin: {
		condition: (_, siblingData) => siblingData?.entryMethod === 'id',
		description: 'Enter only the numeric ID of the tweet',
	},
	validate: validateEntryMethod,
};

export const showThread: Field = {
	name: 'showThread',
	label: 'Show Thread',
	type: 'checkbox',
	defaultValue: false,
	admin: {
		description: 'Show replies in the tweet thread',
	},
};
