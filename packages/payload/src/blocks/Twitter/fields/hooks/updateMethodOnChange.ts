import type { FieldHook } from 'payload';
import type { TwitterBlockConfig } from '@repo/payload/payload-types';

/* Example of type safety */
export const updateMethodBeforeChange: FieldHook = async (
	args,
): Promise<TwitterBlockConfig['entryMethod']> => {
	const { value, siblingData, operation } = args;

	if (operation === 'update') {
		if (value !== 'url' && value !== 'id' && value !== null) {
			throw new Error('Invalid entryMethod value.');
		}

		// Reset other fields based on selection
		if (value === 'url') {
			siblingData.tweetId = null; // Reset tweetId if URL is selected
		} else if (value === 'id') {
			siblingData.tweetURL = null; // Reset tweetURL if ID is selected
		}
	}

	return value;
};
