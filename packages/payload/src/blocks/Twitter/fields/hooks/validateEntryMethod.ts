import type { Valida<PERSON>, TextField } from 'payload';
import type { TwitterBlockConfig } from '@repo/payload/payload-types';

export const validateEntryMethod: Validate<
	string,
	unknown,
	TwitterBlockConfig,
	TextField
> = (value, { siblingData }) => {
	if (!value) {
		return 'Tweet URL or ID is required';
	}

	if (siblingData?.entryMethod === 'id') {
		if (!/^[0-9]+$/.test(siblingData?.tweetId || '')) {
			return 'Tweet ID should contain only numbers.';
		}
		return true;
	}

	const twitterUrlRegex =
		/^(https:\/\/(?:twitter\.com|x\.com)\/[a-zA-Z0-9_]{1,15}\/status\/\d+)$/;

	if (siblingData?.entryMethod === 'url') {
		if (!twitterUrlRegex.test(value)) {
			return 'Invalid tweet URL. Must be in format: https://[x.com or twitter.com]/{username}/status/{tweetId}';
		}
		return true;
	}

	return 'Invalid tweet URL or ID';
};
