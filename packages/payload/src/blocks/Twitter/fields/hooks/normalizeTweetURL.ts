import type { FieldHook } from 'payload';
import type { TwitterBlockConfig } from '@repo/payload/payload-types';

export const normalizeTweetURL: FieldHook = async (
	args,
): Promise<TwitterBlockConfig['tweetURL']> => {
	const { value } = args;

	if (typeof value === 'string' && value.includes('x.com')) {
		const normalizedValue = value.replace('x.com', 'twitter.com'); // Normalize x.com to twitter.com
		return normalizedValue;
	}

	return value;
};
