import { entryMethod, tweetURL, showThread, tweetId } from './fields';
import type { Block } from 'payload';

export const TwitterBlockConfig: Block = {
	slug: 'Twitter',
	interfaceName: 'TwitterBlockConfig', // optional
	labels: {
		singular: 'Twitter Post',
		plural: 'Twitter Posts',
	},
	fields: [entryMethod, tweetURL, tweetId, showThread],
	// Component to render in the admin interface
	admin: {
		components: {
			Block: {
				path: '@repo/payload/blocks/admin#TwitterAdminBlock',
			},
		},
	},
};

export default TwitterBlockConfig;
