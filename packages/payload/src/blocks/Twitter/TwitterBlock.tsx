'use client';

import React, { useCallback, useEffect, useRef, FC } from 'react';
import type { TwitterBlockConfig } from '@repo/payload/payload-types';
import { useTwitterScript } from './useTwitterScript';

declare global {
	interface Window {
		twttr: {
			widgets: {
				load: (arg0: HTMLDivElement | null) => void;
			};
		};
	}
}

export const TwitterBlock: FC<
	Pick<
		TwitterBlockConfig,
		'entryMethod' | 'tweetURL' | 'tweetId' | 'showThread'
	>
> = ({ entryMethod, tweetURL, tweetId, showThread }) => {
	const tweetRef = useRef<HTMLDivElement | null>(null);

	const getValidTweetLink = useCallback((): string | null => {
		if (entryMethod === 'id' && tweetId) {
			return `https://twitter.com/i/status/${tweetId}`;
		}

		if (entryMethod === 'url' && tweetURL) {
			return tweetURL.replace('x.com', 'twitter.com');
		}

		return null;
	}, [entryMethod, tweetId, tweetURL]);

	// Use the custom hook to load Twitter script only once
	const { isLoaded } = useTwitterScript();

	useEffect(() => {
		const tweetLink = getValidTweetLink();

		if (!tweetLink) {
			return;
		}

		if (tweetRef.current) {
			tweetRef.current.innerHTML = `
        <blockquote class="twitter-tweet" data-conversation="none">
          <a href="${tweetLink}"></a>
        </blockquote>
      `;
		}

		// Only try to load the widget if the script is loaded
		if (isLoaded && window.twttr && window.twttr.widgets) {
			window.twttr.widgets.load(tweetRef.current);
		}
	}, [getValidTweetLink, tweetId, tweetURL, entryMethod, showThread, isLoaded]);

	return <div className="flex items-center" ref={tweetRef}></div>;
};

export default TwitterBlock;
