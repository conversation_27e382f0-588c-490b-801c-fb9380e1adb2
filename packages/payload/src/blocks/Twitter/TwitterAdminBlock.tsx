'use client';

import React, { FC, useEffect, useState } from 'react';
import { useAllFormFields, CheckboxInput } from '@payloadcms/ui';
import { reduceFieldsToValues } from 'payload/shared';

import AdminBlock from '@repo/payload/components/AdminBlock';

export const TwitterAdminBlock: FC = () => {
	const [fields, setFields] = useAllFormFields();
	const formData = reduceFieldsToValues(fields, true);

	const [tweetData, setTweetData] = useState({
		tweetURL: formData?.tweetURL,
		tweetId: formData?.tweetId,
	});

	useEffect(() => {
		setTweetData({
			tweetURL: formData?.tweetURL,
			tweetId: formData?.tweetId,
		});
	}, [formData?.tweetURL, formData?.tweetId]);

	const renderTweetIdentifier = () => {
		switch (formData?.entryMethod) {
			case 'url':
				return tweetData.tweetURL;
			case 'id':
				return `Tweet ID: ${tweetData.tweetId}`;
			default:
				return 'No tweet specified';
		}
	};

	return (
		<AdminBlock name="Twitter">
			<h4>{renderTweetIdentifier()}</h4>
			<div style={{ paddingTop: '.3rem' }}>
				<CheckboxInput
					label="Show Thread"
					name="showThread"
					checked={formData?.showThread}
					readOnly={true}
					onToggle={(value) => {
						setFields({
							type: 'UPDATE',
							path: 'showThread',
							value: !value,
						});
					}}
				/>
			</div>
		</AdminBlock>
	);
};

export default TwitterAdminBlock;
