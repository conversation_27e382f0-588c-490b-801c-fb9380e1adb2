'use client';

import { useState, useEffect } from 'react';

// Custom event name for script loaded notification
const TWITTER_SCRIPT_LOADED_EVENT = 'twitter-script-loaded';

/**
 * Custom hook to load the Twitter script only once across all components
 * @returns Object with isLoaded state indicating if the Twitter script is loaded
 */
export function useTwitterScript(): { isLoaded: boolean } {
	// Initialize state based on whether the script is already loaded in the window
	const [isLoaded, setIsLoaded] = useState<boolean>(() => {
		if (typeof window !== 'undefined') {
			return !!(window.twttr && window.twttr.widgets);
		}
		return false;
	});

	// State to hold the loading promise
	const [loadPromise, setLoadPromise] = useState<Promise<void> | null>(null);

	// Function to create the loading promise
	function createLoadPromise(): Promise<void> {
		return new Promise<void>((resolve) => {
			// Check if window.twttr already exists
			if (window.twttr && window.twttr.widgets) {
				setIsLoaded(true);
				resolve();
				return;
			}

			// Create script element if it doesn't exist
			const script = document.createElement('script');
			script.src = 'https://platform.twitter.com/widgets.js';
			script.async = true;
			script.onload = () => {
				setIsLoaded(true);
				// Dispatch event to notify other instances
				const event = new CustomEvent(TWITTER_SCRIPT_LOADED_EVENT);
				window.dispatchEvent(event);
				resolve();
			};
			document.body.appendChild(script);
		});
	}

	useEffect(() => {
		// If already loaded, no need to do anything
		if (isLoaded) {
			return;
		}

		// If we don't have a promise yet, create one
		if (!loadPromise && typeof window !== 'undefined') {
			// Check if script is already loaded
			if (window.twttr && window.twttr.widgets) {
				setIsLoaded(true);
				return;
			}

			// Create and set the promise
			const promise = createLoadPromise();
			setLoadPromise(promise);
		}

		// If we have a promise, wait for it to resolve
		if (loadPromise) {
			loadPromise.then(() => setIsLoaded(true));
		}

		// Listen for script loaded event from other instances
		const handleScriptLoaded = () => setIsLoaded(true);
		if (typeof window !== 'undefined') {
			window.addEventListener(TWITTER_SCRIPT_LOADED_EVENT, handleScriptLoaded);
		}

		return () => {
			if (typeof window !== 'undefined') {
				window.removeEventListener(
					TWITTER_SCRIPT_LOADED_EVENT,
					handleScriptLoaded,
				);
			}
		};
	}, [isLoaded, loadPromise]);

	return { isLoaded };
}
