import { locationProvider, locationEntry } from '../../fields/locationEntry';
import type { Block } from 'payload';
import { contextParameterOverrides } from '../../fields/common/contextParameterOverridesField';

export const CurrentConditionsBlockConfig: Block = {
	slug: 'CurrentConditions',
	interfaceName: 'CurrentConditionsBlockConfig',
	labels: {
		singular: 'CurrentConditions Block',
		plural: 'CurrentConditions Blocks',
	},
	fields: [locationProvider, locationEntry, contextParameterOverrides],
	// Component to render in the admin interface
	admin: {
		components: {
			Block: {
				path: '@repo/payload/blocks/admin#CurrentConditionsAdminBlock',
			},
		},
	},
};

export default CurrentConditionsBlockConfig;
