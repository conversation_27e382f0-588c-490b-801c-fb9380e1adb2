'use client';

import React from 'react';
import AlertHeadlines from './AlertHeadlines';
import Text from '@repo/ui/components/Text/Text';
import type { LocationData } from '@repo/location/types';
import type { WeatherData, ForecastData } from '../CurrentConditions';
import { WxIcon } from '@repo/icons/WxIcon';
import { useTranslations } from 'next-intl';

interface CurrentConditionsViewProps {
	location?: LocationData;
	weatherData?: WeatherData; // This will now contain iconCode
	forecastData?: ForecastData;
	isLoading: boolean;
	error: Error | null;
	formattedTime: string;
	video?: {
		title: string;
		url?: string;
	};
}

/**
 * Main presenter component for CurrentConditions
 * Renders the appropriate component based on the current state (loading, error, or content)
 */
export const CurrentConditionsView: React.FC<CurrentConditionsViewProps> = ({
	location,
	weatherData,
	forecastData,
	isLoading,
	error,
	formattedTime,
	video,
}) => {
	const t = useTranslations('CurrentConditions');

	if (!location) {
		return <CurrentConditionsLoading message={t('loadingLocation')} />;
	}

	if (isLoading) {
		return <CurrentConditionsLoading location={location} />;
	}

	if (error || !weatherData || !forecastData) {
		return <CurrentConditionsError location={location} error={error} />;
	}

	return (
		<CurrentConditionsContent
			location={location}
			weatherData={weatherData}
			forecastData={forecastData}
			formattedTime={formattedTime}
			video={video}
		/>
	);
};

interface CurrentConditionsContentProps {
	location: LocationData;
	weatherData: WeatherData; // This will now contain iconCode
	forecastData: ForecastData;
	formattedTime: string;
	video?: {
		title: string;
		url?: string;
	};
}

const CurrentConditionsContent: React.FC<CurrentConditionsContentProps> = ({
	location,
	weatherData,
	forecastData,
	formattedTime,
	video,
}) => {
	const t = useTranslations('CurrentConditions');
	return (
		<div className="overflow-hidden rounded-lg">
			{/* Top section with location and time */}
			<div className="flex items-center justify-between bg-teal-800 p-4 text-white">
				<Text variant="Title.S" color="inverse" elementType="h2">
					{location.presentationName}
				</Text>
				<Text color="inverse">
					{t('asOf', {
						formattedTime,
					})}
				</Text>
			</div>

			{/* Main section with temperature, conditions, and alerts */}
			<div className="flex min-h-[100px] flex-col bg-teal-600 p-4 text-white">
				{/* Top row with temperature and icon */}
				<div className="flex items-center">
					{/* Temperature */}
					<Text variant="Display.XL" color="inverse" elementType="h2">
						{Math.round(weatherData.temperature)}°
					</Text>

					{/* Weather icon */}
					<div className="ml-4">
						<WxIcon
							iconCode={weatherData.iconCode}
							className="lightBG h-20 w-20"
							aria-label={weatherData.wxPhraseLong}
							iconTheme="lightBG"
						/>
					</div>
				</div>

				{/* Bottom row with weather phrase/temps and alerts */}
				<div className="mt-auto flex items-end justify-between">
					{/* Weather phrase and day/night temps */}
					<div className="flex-1">
						<Text variant="Title.M" color="inverse" elementType="p">
							{weatherData.wxPhraseLong}
						</Text>
						<Text
							className="mt-2"
							variant="Title.M"
							color="inverse"
							elementType="p"
						>
							{t('dayTempLabel')}
							&nbsp;
							{Math.round(forecastData.temperatureMax)}°&nbsp;&bull;&nbsp;
							{t('nightTempLabel')}&nbsp;
							{Math.round(forecastData.temperatureMin)}°
						</Text>
					</div>

					{/* Alert Headlines aligned to bottom right */}
					<div className="max-w-[50%]">
						<AlertHeadlines location={location} />
					</div>
				</div>
			</div>

			{/* Video section */}
			{video && <VideoSection title={video.title} url={video.url} />}
		</div>
	);
};

interface CurrentConditionsErrorProps {
	location: LocationData;
	error: Error | null;
}

export const CurrentConditionsError: React.FC<CurrentConditionsErrorProps> = ({
	location,
	error,
}) => {
	return (
		<div className="overflow-hidden rounded-lg">
			<div className="flex items-center justify-between bg-slate-800 p-4 text-white">
				<h2 className="text-xl font-medium">{location.presentationName}</h2>
			</div>

			<div className="flex min-h-[100px] flex-col bg-teal-600 p-8 text-white">
				<div className="flex flex-grow items-center justify-center">
					<div className="text-xl text-red-300">
						{error?.message || 'Unable to load weather data'}
					</div>
				</div>

				<div className="mt-auto flex items-end justify-end">
					<div className="max-w-[50%]">
						<AlertHeadlines location={location} />
					</div>
				</div>
			</div>
		</div>
	);
};

interface CurrentConditionsLoadingProps {
	location?: LocationData;
	message?: string;
}

export const CurrentConditionsLoading: React.FC<
	CurrentConditionsLoadingProps
> = ({ location, message = 'Loading weather data...' }) => {
	const t = useTranslations('CurrentConditions');

	return (
		<div className="overflow-hidden rounded-lg">
			<div className="flex items-center justify-between bg-slate-800 p-4 text-white">
				<h2 className="text-xl font-medium">
					{location ? location.presentationName : t('loadingLocation')}
				</h2>
			</div>

			<div className="flex min-h-[100px] flex-col bg-teal-600 p-8 text-white">
				<div className="flex flex-grow items-center justify-center">
					<div className="animate-pulse text-xl">{message}</div>
				</div>

				{location && (
					<div className="mt-auto flex items-end justify-end">
						<div className="max-w-[50%]">
							<AlertHeadlines location={location} />
						</div>
					</div>
				)}
			</div>
		</div>
	);
};

interface VideoSectionProps {
	title: string;
	url?: string;
}

export const VideoSection: React.FC<VideoSectionProps> = ({
	title,
	// url,
}) => {
	return (
		<div className="flex cursor-pointer items-center gap-3 bg-teal-800 p-4 text-white transition-colors hover:bg-teal-700">
			<div className="flex h-8 w-8 items-center justify-center rounded-full bg-white text-teal-800">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					viewBox="0 0 24 24"
					fill="currentColor"
					className="h-5 w-5"
				>
					<path
						fillRule="evenodd"
						d="M4.5 5.653c0-1.426 1.529-2.33 2.779-1.643l11.54 6.348c1.295.712 1.295 2.573 0 3.285L7.28 19.991c-1.25.687-2.779-.217-2.779-1.643V5.653z"
						clipRule="evenodd"
					/>
				</svg>
			</div>
			<Text color="inverse">Watch: {title}</Text>
		</div>
	);
};
