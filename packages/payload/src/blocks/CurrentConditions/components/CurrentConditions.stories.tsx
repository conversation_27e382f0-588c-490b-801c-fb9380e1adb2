import type { Meta, StoryObj } from '@storybook/react';
import { CurrentConditionsView } from './CurrentConditionsView';
import {
	mockLocationData,
	mockObservations,
	mockDailyForecast,
} from '@repo/mocks';
// Use a mock component for the icon since we can't import the SVG directly in stories
// const MockIcon = (props: React.SVGProps<SVGSVGElement>) => (
//   <svg
//     xmlns="http://www.w3.org/2000/svg"
//     viewBox="0 0 24 24"
//     width="24"
//     height="24"
//     {...props}
//   >
//     <circle cx="12" cy="12" r="10" fill="currentColor" />
//   </svg>
// );

// Mock transformed data
const transformedWeatherData = {
	...mockObservations,
	iconName: 'partly-cloudy-day', // Assuming this is what the ICON_CODE_MAP returns
};

// Main component stories for the container
const meta: Meta<typeof CurrentConditionsView> = {
	title: 'Weather/CurrentConditions',
	component: CurrentConditionsView,
	parameters: {
		layout: 'padded',
	},
};

export default meta;
type ContainerStory = StoryObj<typeof CurrentConditionsView>;

// Container stories - these use the container component with location prop
export const Default: ContainerStory = {
	args: {
		location: mockLocationData,
		weatherData: transformedWeatherData,
		forecastData: mockDailyForecast[0],
		formattedTime: '2:00 PM',
	},
};

export const Video: ContainerStory = {
	args: {
		location: mockLocationData,
		weatherData: transformedWeatherData,
		forecastData: mockDailyForecast[0],
		formattedTime: '2:00 PM',
		video: {
			title: "Today's Weather Forecast",
			url: 'https://example.com/video.mp4',
		},
	},
};

export const LoadingData: ContainerStory = {
	name: 'Loading: Data',
	args: {
		location: mockLocationData,
		isLoading: true,
	},
};

export const LoadingLocation: ContainerStory = {
	name: 'Loading: Location',
	args: {
		isLoading: true,
	},
};

export const ErrorView: ContainerStory = {
	args: {
		location: mockLocationData,
		error: new Error('Failed to fetch weather data'),
	},
};
