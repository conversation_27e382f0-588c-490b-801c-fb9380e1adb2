'use client';

import React from 'react';
import { getWeatherAlertHeadlines } from '@repo/dal/weather/alerts/headlines';
import { getAlertLevel } from '@repo/dal/weather/alerts/utils/getAlertLevel';
import { isAlertSevere } from '@repo/dal/weather/alerts/utils/isAlertSevere';
import { AlertIcon } from '@repo/icons/AlertIcon';
import useSWR from 'swr';
import type { LocationData } from '@repo/location/types';
import type { WeatherAlertHeadlinesResponse } from '@repo/dal/weather/alerts/types';
import { Button } from '@repo/ui/components/Button/Button';
import { useParams } from 'next/navigation';

interface AlertHeadlinesProps {
	location?: LocationData;
}

export const AlertHeadlines: React.FC<AlertHeadlinesProps> = ({ location }) => {
	const params = useParams();
	const locale = params?.locale as string;

	// Fetch alert headlines using SWR
	const {
		data: alertsData,
		error: alertsError,
		isLoading: isAlertsLoading,
	} = useSWR<WeatherAlertHeadlinesResponse | null>(
		location?.geocode ? ['alerts', location.geocode] : null,
		([_, geocode]) => {
			return getWeatherAlertHeadlines({
				geocode: geocode as string,
				language: locale,
			});
		},
		{
			revalidateOnFocus: false,
			dedupingInterval: 60000, // 1 minute
		},
	);

	// Get the primary alert (first in sorted list)
	const primaryAlert =
		alertsData?.alerts && alertsData.alerts.length > 0
			? alertsData.alerts[0]
			: null;

	// Get alert style variables based on alert level
	const getAlertStyleVars = () => {
		if (!primaryAlert) {
			return null;
		}

		const { productIdentifier, phenomena, significance } = primaryAlert;

		if (!productIdentifier || !phenomena || !significance) {
			return null;
		}

		const alertValue = getAlertLevel({
			productIdentifier,
			phenomena,
			significance,
		});

		// Override alert level if alert is severe
		const isSevere = isAlertSevere({
			productIdentifier: productIdentifier || '',
			phenomena,
			significance,
		});

		// Define color schemes based on alert levels with direct hex values
		const alertColorSchemes = {
			1: {
				icon: '#fff', // --iconLight
				fill: '#ba0d00', // --alertLevel1
				border: '#f0f0f0', // --borderLight
				textColor: '#FFFFFF', // white text on red background
				backgroundColor: '#ba0d00', // red background
				buttonBackgroundColor: '#ba0d00', // transparent background
			},
			2: {
				icon: '#000', // --iconDark
				fill: '#e6731f', // --alertLevel2
				border: '#f0f0f0', // --borderLight
				textColor: '#ffffff', // white text on gray background
				backgroundColor: '#252422cc', // panther80
				buttonBackgroundColor: '#ffffff1a', // transparent background
			},
			3: {
				icon: '#000', // --iconDark
				fill: '#ebbb02', // --alertLevel3
				border: '#f0f0f0', // --borderLight
				textColor: '#ffffff', // white text on yellow background
				backgroundColor: '#252422cc', // panther80
				buttonBackgroundColor: '#ffffff1a', // transparent background
			},
			4: {
				icon: '#fff', // --iconLight
				fill: '#a51ccb', // --alertLevel4
				border: '#f0f0f0', // --borderLight
				textColor: '#FFFFFF', // white text on purple background
				backgroundColor: '#252422cc', // panther80
				buttonBackgroundColor: '#ffffff1a', // transparent background
			},
			severe: {
				icon: '#fff', // --iconLight
				fill: '#ba0d00', // --alertSevere
				border: '#f0f0f0', // --borderLight
				textColor: '#FFFFFF', // white text on severe red background
				backgroundColor: '#ba0d00', // red background
				buttonBackgroundColor: '#ba0d00', // transparent background
			},
		};

		// Use the severe scheme or the alert level scheme
		const schemeKey = isSevere ? 'severe' : alertValue;
		return (
			alertColorSchemes[schemeKey as keyof typeof alertColorSchemes] ||
			alertColorSchemes[4]
		);
	};

	// Get alert style variables
	const alertStyleVars = primaryAlert ? getAlertStyleVars() : null;

	const alertValue = primaryAlert
		? getAlertLevel({
				productIdentifier: primaryAlert.productIdentifier,
				phenomena: primaryAlert.phenomena,
				significance: primaryAlert.significance,
			})
		: null;

	const isSevere = primaryAlert
		? isAlertSevere({
				productIdentifier: primaryAlert.productIdentifier,
				phenomena: primaryAlert.phenomena,
				significance: primaryAlert.significance,
			})
		: null;

	// If no alerts, still loading, or errors, don't render anything
	if (isAlertsLoading || alertsError || !primaryAlert) {
		return null;
	}

	return (
		<div
			className="flex items-center gap-4 rounded-sm px-2 py-2 text-sm"
			style={
				{
					backgroundColor: alertStyleVars?.backgroundColor,
					color: alertStyleVars?.textColor,
				} as React.CSSProperties
			}
			role="alert"
			aria-live="polite"
		>
			<div className="flex flex-1 gap-2">
				{primaryAlert && (
					<AlertIcon
						productIdentifier={primaryAlert.productIdentifier}
						phenomena={primaryAlert.phenomena}
						significance={primaryAlert.significance}
						className="h-4 w-4 items-start"
						aria-hidden="true"
					/>
				)}
				<span className="uppercase">{primaryAlert.eventDescription}</span>
			</div>
			{alertsData && alertsData.alerts.length > 1 && (
				<Button
					variant="outline"
					size="sm"
					style={{
						backgroundColor: alertStyleVars?.buttonBackgroundColor,
						...(alertValue === 1 || isSevere
							? {
									outline: 'solid 1px #ffffff',
								}
							: {}),
					}}
					className="rounded-xs flex h-6 items-center justify-center border-0 px-2 py-2 text-xs text-white hover:bg-transparent hover:text-white"
					aria-label={`View ${alertsData.alerts.length - 1} more alerts`}
				>
					+{alertsData.alerts.length - 1} MORE
				</Button>
			)}
		</div>
	);
};

export default AlertHeadlines;
