'use client';

import React from 'react';
import useSWR from 'swr';
import { useLocationSource } from '@repo/location/hooks/useLocationSource';
import { getCurrentObservations } from '@repo/dal/weather/current/observations';
import { getDailyForecast } from '@repo/dal/weather/forecast/daily';
import { ICON_CODE_MAP, Units } from '@repo/dal/weather/types';
import { formatTime } from '@repo/utils/formatTime';
import type { LocationData } from '@repo/location/types';
import { CurrentConditionsView } from './components/CurrentConditionsView';
import { DebugCollector } from '@repo/payload/components/FrontendAdminHeader/collectors/DebugCollector';
import { useAtomValue } from 'jotai';
import { userUnitPreferenceAtom } from '@repo/user/atoms/preferences/units';
import { unitsSystemByName } from '@repo/units';
import { useParams } from 'next/navigation';

interface CurrentConditionsProps {
	location?: LocationData;
	video?: {
		title: string;
		url?: string;
	};
}

/**
 * Container component that handles data fetching and state management
 * for the CurrentConditions component
 */
export const CurrentConditions: React.FC<CurrentConditionsProps> = ({
	location,
	video,
}) => {
	// Use the location hook to handle location logic
	const { effectiveLocation, isLocationLoading, locationError } =
		useLocationSource({
			location,
		});

	// Use the data hook to fetch weather data
	const {
		weatherData,
		forecastData,
		// IconComponent, // Removed from hook, will be handled by CurrentConditionsView
		isLoading: isWeatherLoading,
		error: weatherError,
		formattedTime,
	} = useCurrentConditionsData(effectiveLocation);

	// Combine loading and error states
	const isLoading = isWeatherLoading || isLocationLoading;
	const error = weatherError || locationError;

	return (
		<>
			<DebugCollector
				componentName="CurrentConditions"
				data={{
					props: {
						location,
						video,
					},
					state: {
						effectiveLocation,
						isLocationLoading,
						isLoading,
						weatherData,
						forecastData,
						formattedTime,
						error,
						componentState:
							(error && 'error') || (isLoading && 'loading') || 'ready',
					},
					performance: {
						renderCount: undefined, // This is a placeholder, would need a ref to track actual renders
					},
				}}
			/>
			<CurrentConditionsView
				location={effectiveLocation}
				weatherData={weatherData}
				forecastData={forecastData}
				isLoading={isLoading}
				error={error}
				formattedTime={formattedTime}
				video={video}
			/>
		</>
	);
};

export default CurrentConditions;

export interface WeatherData {
	temperature: number;
	wxPhraseLong: string;
	iconName: string;
	iconCode: number; // Added
	validTimeLocal: string;
}

export interface ForecastData {
	temperatureMax: number;
	temperatureMin: number;
}

/**
 * Custom hook for fetching and transforming weather data
 * @param location The location to fetch weather data for
 * @returns Weather data, forecast data, loading state, error state, and formattedTime
 */
export function useCurrentConditionsData(location?: LocationData) {
	const params = useParams();
	const locale = params?.locale as string;
	const unit = useAtomValue(userUnitPreferenceAtom);
	const unitCode = unitsSystemByName(unit)?.code as Units;

	// Use SWR for data fetching
	const {
		data: observations,
		error: observationsError,
		isLoading: isObservationsLoading,
	} = useSWR(
		location ? ['weather', location.geocode, unitCode] : null,
		([_, geocode, units]) => {
			return getCurrentObservations({
				geocode,
				units,
				language: locale,
			});
		},
		{
			revalidateOnFocus: false,
			dedupingInterval: 60000, // 1 minute
		},
	);

	const {
		data: forecast,
		error: forecastError,
		isLoading: isForecastLoading,
	} = useSWR(
		location ? ['daily-forecast', location.geocode, unitCode] : null,
		([_, geocode, units]) => {
			if (!location) return null;
			return getDailyForecast({
				geocode,
				units,
				language: locale,
			});
		},
		{
			revalidateOnFocus: false,
			dedupingInterval: 60000, // 1 minute
		},
	);

	// Transform the data after fetching
	const weatherData: WeatherData | undefined = observations
		? {
				temperature: observations.temperature,
				wxPhraseLong: observations.wxPhraseLong,
				iconName: ICON_CODE_MAP[observations.iconCode] || 'na',
				iconCode: observations.iconCode, // Added
				validTimeLocal: observations.validTimeLocal,
			}
		: undefined;

	// Use actual forecast data
	const forecastData: ForecastData | undefined =
		forecast && forecast.length > 0
			? {
					temperatureMax: forecast[0]?.calendarDayTemperatureMax ?? 0,
					temperatureMin: forecast[0]?.calendarDayTemperatureMin ?? 0,
				}
			: undefined;

	// Combine loading and error states
	const isLoading = isObservationsLoading || isForecastLoading;
	const error = observationsError || forecastError;

	const formattedTime = weatherData
		? formatTime(weatherData.validTimeLocal)
		: '';

	return {
		weatherData,
		forecastData,
		isLoading,
		error,
		formattedTime,
	};
}
