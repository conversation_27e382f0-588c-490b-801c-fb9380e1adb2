'use client';

import { FC } from 'react';
import type { CurrentConditionsBlockConfig } from '@repo/payload/payload-types';
import CurrentConditions from './CurrentConditions';
import { getLocationPointByGeocode } from '@repo/dal/locations/point';
import { fromLocationPoint } from '@repo/location/utils/fromLocationPoint';
import useSWR from 'swr';
import { useParams } from 'next/navigation';

export const CurrentConditionsBlock: FC<
	Omit<CurrentConditionsBlockConfig, 'blockType'>
> = ({
	locationProvider,
	locationEntry,
}: Omit<CurrentConditionsBlockConfig, 'blockType'>) => {
	const params = useParams();
	const locale = params?.locale as string;

	// Only fetch location data when locationProvider is 'specified'
	const locationFetcher = (geocode: string) => {
		return getLocationPointByGeocode(geocode, locale);
	};

	// Fetch location data for specified location
	const {
		data: specifiedLocationData,
		error: specifiedLocationError,
		isLoading: isSpecifiedLocationLoading,
	} = useSWR(
		locationProvider === 'specified' && locationEntry
			? ['specified-location-data', locationEntry]
			: null,
		() => (locationEntry ? locationFetcher(locationEntry) : null),
		{
			revalidateOnFocus: false,
			dedupingInterval: 300000, // 5 minutes
		},
	);

	// If using specified location and it's loading, show loading state
	if (locationProvider === 'specified' && isSpecifiedLocationLoading) {
		return <div>Loading location data...</div>;
	}

	// If using specified location and there's an error, show error state
	if (locationProvider === 'specified' && specifiedLocationError) {
		return (
			<div>Error loading location data: {specifiedLocationError.message}</div>
		);
	}

	// If using specified location and we have data, create location object
	if (
		locationProvider === 'specified' &&
		specifiedLocationData &&
		locationEntry
	) {
		return (
			<CurrentConditions location={fromLocationPoint(specifiedLocationData)} />
		);
	}

	// If not using specified location, let component using useLocationSource handle page or geoip location internally
	if (locationProvider !== 'specified') {
		return <CurrentConditions />;
	}

	return null;
};

export default CurrentConditionsBlock;
