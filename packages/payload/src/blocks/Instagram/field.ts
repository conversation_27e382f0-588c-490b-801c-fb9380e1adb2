import type { SelectField, TextField } from 'payload';
import { validateInstagramURL } from './utils';

export const url: TextField = {
	name: 'url',
	label: 'Instagram URL',
	type: 'text',
	required: true,
	admin: {
		placeholder: 'https://www.instagram.com/p/igP0st1D',
		description: 'Enter the full Instagram URL',
	},
	validate: validateInstagramURL,
};

export const size: SelectField = {
	name: 'size',
	label: 'Video Size',
	type: 'select',
	options: [
		{ label: 'Hero (Full Width)', value: 'hero' },
		{ label: 'Article (Smaller Embed)', value: 'article' },
	],
	admin: {
		description: 'Choose how the video embed should be displayed.',
	},
	defaultValue: 'article',
};
