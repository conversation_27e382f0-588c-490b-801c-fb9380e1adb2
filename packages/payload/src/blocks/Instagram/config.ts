import { url, size } from './field';
import type { Block } from 'payload';

export const InstagramBlockConfig: Block = {
	slug: 'Instagram',
	interfaceName: 'InstagramBlockConfig',
	labels: {
		singular: 'Instagram Block',
		plural: 'Instagram Blocks',
	},
	fields: [url, size],
	admin: {
		components: {
			Block: {
				path: '@repo/payload/blocks/admin#InstagramAdminBlock',
			},
		},
	},
};

export default InstagramBlockConfig;
