import { locationProvider, locationEntry } from '../../fields/locationEntry';
import type { Block } from 'payload';
import { contextParameterOverrides } from '../../fields/common/contextParameterOverridesField';

export const DailyForecastBlockConfig: Block = {
	slug: 'DailyForecast',
	interfaceName: 'DailyForecastBlockConfig',
	labels: {
		singular: 'DailyForecast Block',
		plural: 'DailyForecast Blocks',
	},
	fields: [locationProvider, locationEntry, contextParameterOverrides],
	// Component to render in the admin interface
	admin: {
		components: {
			Block: {
				path: '@repo/payload/blocks/admin#DailyForecastAdminBlock',
			},
		},
	},
};

export default DailyForecastBlockConfig;
