'use client';

import React from 'react';
import { Button } from '@repo/ui/components/Button/Button';
import { getDailyForecast } from '@repo/dal/weather/forecast/daily';
import {
	ICON_CODE_MAP,
	ForecastDuration,
	Units,
} from '@repo/dal/weather/types';
import useSWR from 'swr';
import { useLocationSource } from '@repo/location/hooks/useLocationSource';
import type { LocationData } from '@repo/location/types';
import { WxIcon } from '@repo/icons/WxIcon';
import { NoData } from '@repo/icons/Data';
import { cn } from '@repo/ui/lib/utils';
import useExperiment from '@repo/experiments/useExperiment';
import { useAtomValue } from 'jotai';
import { userUnitPreferenceAtom } from '@repo/user/atoms/preferences/units';
import { useTranslations } from 'next-intl';
import { unitsSystemByName } from '@repo/units';
import { useParams } from 'next/navigation';

export interface DailyForecastProps {
	location?: LocationData;
}

interface ForecastPeriod {
	name: string;
	temperature: number;
	iconCode: number;
	precipitation: string;
}

/**
 * DailyForecast component displays weather forecast for different periods of the day
 */
export const DailyForecast: React.FC<DailyForecastProps> = ({ location }) => {
	const t = useTranslations('DailyForecast');
	const params = useParams();
	const locale = params?.locale as string;

	const { value: expVariantValue } = useExperiment(
		'reorder-current-conditions-and-forecast',
		'control',
	);

	// Use the location hook to handle location logic
	const { effectiveLocation, isLocationLoading } = useLocationSource({
		location,
	});

	const unit = useAtomValue(userUnitPreferenceAtom);
	const unitCode = unitsSystemByName(unit)?.code as Units;

	// Use SWR for data fetching
	const {
		data: dailyForecast,
		error,
		isLoading: isForecastLoading,
	} = useSWR(
		effectiveLocation
			? ['daily-forecast', effectiveLocation.geocode, unitCode]
			: null,
		([_, geocode, units]) => {
			if (!effectiveLocation) return null;
			return getDailyForecast({
				geocode,
				units, // Imperial units
				language: locale,
				duration: ForecastDuration.THREE_DAY, // Get forecast for today
			});
		},
		{
			revalidateOnFocus: false,
			dedupingInterval: 60000, // 1 minute
		},
	);

	// Process forecast data
	const forecastData: ForecastPeriod[] = [];

	if (dailyForecast && dailyForecast[0] && dailyForecast[0].daypart) {
		const daypartData = dailyForecast[0].daypart;

		if (daypartData && daypartData.daypartName) {
			for (let i = 0; i < daypartData.daypartName.length; i++) {
				const name = daypartData.daypartName[i];
				const temperature = daypartData.temperature[i];
				const iconCode = daypartData.iconCode[i];
				const precipChance = daypartData.precipChance[i];

				if (name && temperature !== undefined && iconCode !== undefined) {
					forecastData.push({
						name,
						temperature,
						iconCode: iconCode, // Store raw iconCode
						precipitation:
							precipChance !== undefined && precipChance !== null
								? `${precipChance}%`
								: '--',
					});
				}
			}
		}
	}

	// If no location is available yet, show loading state
	if (!effectiveLocation) {
		return (
			<div className="overflow-hidden rounded-lg bg-white shadow-md">
				<div className="bg-slate-800 p-4 text-white">
					<h2 className="text-2xl font-bold">{t('loadingLocation')}</h2>
				</div>
				<div className="flex h-64 items-center justify-center p-6">
					<div className="animate-pulse text-xl">Determining location...</div>
				</div>
			</div>
		);
	}

	// Combine loading states
	const isLoading = isForecastLoading || isLocationLoading;

	if (isLoading) {
		return (
			<div className="overflow-hidden rounded-lg bg-white shadow-md">
				<div className="bg-slate-800 p-4 text-white">
					<h2 className="text-2xl font-bold">
						{t('headerTitle', {
							presentationName: effectiveLocation.presentationName
								? effectiveLocation.presentationName
								: t('loadingLocation'),
						})}
					</h2>
				</div>
				<div className="flex h-64 items-center justify-center p-6">
					<div className="animate-pulse text-xl">
						{t('loadingForecastData')}
					</div>
				</div>
			</div>
		);
	}

	if (error || forecastData.length === 0) {
		return (
			<div className="overflow-hidden rounded-lg bg-white shadow-md">
				<div className="bg-slate-800 p-4 text-white">
					<h2 className="text-2xl font-bold">
						{t('headerTitle', {
							presentationName: effectiveLocation?.presentationName || '',
						})}
					</h2>
				</div>
				<div className="flex h-64 items-center justify-center p-6">
					<div className="text-xl text-red-500">
						{error?.message || t('unableToLoadData')}
					</div>
				</div>
			</div>
		);
	}

	return (
		<div
			className={cn(
				'overflow-hidden rounded-lg bg-white shadow-md',
				expVariantValue === 'treatment' ? '-order-1' : 'order-0',
			)}
			data-testid="forecast-module"
		>
			{/* Header section */}
			<div className="bg-slate-800 p-4 text-white">
				<h2 className="text-2xl font-bold" data-testid="forecast-title">
					{t('headerTitle', {
						presentationName: effectiveLocation?.presentationName || '',
					})}
				</h2>
			</div>

			{/* Forecast periods */}
			<div className="divide-y divide-gray-200" data-testid="forecast-periods">
				{forecastData.map((period, index) => {
					// Determine temperature color based on time period
					const tempColorClass =
						period.name === 'Afternoon' || period.name === 'Evening'
							? 'text-blue-600'
							: 'text-slate-700';

					return (
						<div
							key={period.name}
							className="flex items-center justify-between px-6 py-5"
							data-testid={`forecast-period-${index}`}
						>
							{/* Time period */}
							<div
								className="w-1/3 text-xl font-medium text-gray-700"
								data-testid="period-name"
							>
								{period.name}
							</div>

							{/* Temperature */}
							<div
								className={`text-5xl font-bold ${tempColorClass} w-1/3 text-center`}
								data-testid="temperature"
							>
								{period.temperature}°
							</div>

							{/* Weather icon and precipitation */}
							<div className="flex w-1/3 items-center justify-end">
								<div
									className="flex w-12 justify-center"
									data-testid="weather-icon"
								>
									<WeatherIcon
										iconCode={period.iconCode}
										wxPhraseLong={ICON_CODE_MAP[period.iconCode]}
									/>
								</div>
								<div
									className="ml-6 w-16 text-xl font-medium text-gray-700"
									data-testid="precipitation"
								>
									{period.precipitation}
								</div>
							</div>
						</div>
					);
				})}
			</div>

			{/* Button section */}
			<div className="flex justify-start p-4">
				<Button
					variant="default"
					className="rounded-md bg-slate-900 text-white hover:bg-slate-800"
					data-testid="next-48-hours"
				>
					{t('bottomCtaButton')}
				</Button>
			</div>
		</div>
	);
};

const WeatherIcon: React.FC<{ iconCode: number; wxPhraseLong?: string }> = ({
	iconCode,
	wxPhraseLong,
}) => {
	const effectiveWxPhrase = wxPhraseLong || `Weather condition ${iconCode}`;

	if ((ICON_CODE_MAP[iconCode] || 'na') !== 'na') {
		return (
			<WxIcon
				iconCode={iconCode}
				className="lightBG"
				size="xl"
				aria-label={effectiveWxPhrase}
				iconTheme="lightBG"
			/>
		);
	} else {
		return (
			<NoData
				size="xl"
				className="lightBG"
				aria-label="Weather icon not available"
			/>
		);
	}
};

export default DailyForecast;
