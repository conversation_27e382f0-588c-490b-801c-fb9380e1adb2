'use client';

import type { FC } from 'react';
import type {
	ContentMediaBlockConfig,
	ContentQuery,
	Image,
} from '@repo/payload/payload-types';
import NextImage from 'next/image';
import { Play } from 'lucide-react';
import { hasLength } from '@repo/utils/hasLength';
import Link from '@repo/navigation/components/Link';
import { Button } from '@repo/ui/components/Button/Button';
import Slideshow from '../Slideshow/Slideshow';

export const ContentMediaBlock: FC<ContentMediaBlockConfig> = ({
	contentQuery,
	limit = 4,
	title = 'Trending',
	ctaText = 'More Stories',
	ctaLink = '/',
}) => {
	// Extract the mergedContent from the contentMediaQuery
	const mediaQuery = contentQuery as ContentQuery;
	const mergedContent = mediaQuery?.mergedContent || [];

	if (!hasLength(mergedContent)) {
		return null;
	}

	// Limit the number of items to display
	const items = mergedContent.slice(0, Number(limit));

	// Check if all items are images
	const allImages = items.every((item) => item.contentType === 'image');

	// If all are images, render slideshow only
	if (allImages) {
		// Transform items to slideshow format=
		const slideshowImages = items.map(
			(item) =>
				({
					id: item.id,
					url: item.overrideThumbnail || item.thumbnail || '',
					filename: item.overrideTitle || '',
					seo: {
						altText: item.overrideTitle || item.title,
						caption: item.overrideDescription || item.description || '',
					},
				}) as Image,
		);
		return (
			<section className="not-prose mx-auto w-full max-w-4xl">
				<Slideshow
					id={`content-media-slideshow-${Math.random().toString(36).substring(2, 9)}`}
					assets={slideshowImages}
					allSlides={slideshowImages}
					slides={[]}
					totalAssets={slideshowImages.length}
					rounded={true}
					sendTrackString={() => {}}
					blockType="Slideshow"
				/>
			</section>
		);
	}

	return (
		<section className="@container not-prose mx-auto w-full max-w-4xl rounded-md bg-white p-5">
			<h2 className="not-prose mb-4 text-2xl font-bold">{title}</h2>

			<div className="@md:grid-cols-2 @lg:grid-cols-4 grid grid-cols-1 gap-4">
				{items.map((contentItem) => {
					const displayTitle = contentItem.overrideTitle || contentItem.title;
					const thumbnailUrl =
						contentItem.overrideThumbnail || contentItem.thumbnail;
					const isVideo = contentItem.url?.includes('video') || false;
					const category = contentItem.source || 'Weather News';

					return (
						<div key={contentItem.id} className="flex flex-col">
							<div className="relative mb-2 h-32 w-full overflow-hidden rounded-md">
								<NextImage
									src={thumbnailUrl || '/placeholder.svg'}
									alt={displayTitle}
									fill
									className="object-cover"
									sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
									style={{
										position: 'absolute',
										height: '100%',
										width: '100%',
										left: 0,
										top: 0,
									}}
								/>
								{isVideo && (
									<div className="absolute inset-0 flex items-center justify-center">
										<div className="flex size-10 items-center justify-center rounded-full bg-white/80">
											<Play className="ml-0.5 size-4 text-gray-800" />
										</div>
									</div>
								)}
							</div>

							<div className="mb-1 text-xs font-medium text-gray-600">
								{category}
							</div>
							<h3 className="not-prose mb-1 text-base font-normal leading-tight text-gray-900">
								<Link
									href={contentItem.url || '#'}
									className="not-prose font-normal text-gray-900 no-underline hover:no-underline"
								>
									{displayTitle}
								</Link>
							</h3>
						</div>
					);
				})}
			</div>

			<div className="mt-4">
				<Link href={ctaLink ?? '/'}>
					<Button className="rounded bg-gray-900 px-4 py-2 text-sm font-medium text-white hover:bg-gray-800">
						{ctaText}
					</Button>
				</Link>
			</div>
		</section>
	);
};

export default ContentMediaBlock;
