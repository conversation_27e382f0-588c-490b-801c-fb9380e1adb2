import type { Block } from 'payload';
import { contentMediaFields } from './fields';

export const ContentMediaBlockConfig: Block = {
	slug: 'ContentMedia',
	interfaceName: 'ContentMediaBlockConfig',
	labels: {
		singular: 'ContentMedia Block',
		plural: 'ContentMedia Blocks',
	},
	fields: contentMediaFields,
	// Component to render in the admin interface
	admin: {
		components: {
			Block: {
				path: '@repo/payload/blocks/admin#ContentMediaAdminBlock',
			},
		},
	},
};

export default ContentMediaBlockConfig;
