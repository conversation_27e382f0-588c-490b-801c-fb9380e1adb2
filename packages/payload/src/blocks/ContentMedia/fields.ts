import {
	Field,
	RelationshipField,
	<PERSON>Field,
	SelectField,
	TextField,
} from 'payload';

export const title: TextField = {
	name: 'title',
	type: 'text',
	label: 'Block Title',
	required: true,
};
export const limit: SelectField = {
	name: 'limit',
	type: 'select',
	label: 'Display Limit',
	defaultValue: '4',
	options: [
		{
			label: 'Single Item (Hero)',
			value: '1',
		},
		{
			label: 'Grid (4 Items)',
			value: '4',
		},
	],
};
export const ctaText: TextField = {
	name: 'ctaText',
	type: 'text',
	defaultValue: 'See More',
	label: 'CTA Button Text',
};
export const ctaLink: TextField = {
	name: 'ctaLink',
	type: 'text',
	defaultValue: '/',
	label: 'CTA Button Link',
};

export const contentQuery: RelationshipField = {
	name: 'contentQuery',
	label: 'Content Query',
	type: 'relationship',
	filterOptions: ({ id }) => {
		return {
			id: {
				not_in: [id],
			},
		};
	},
	hasMany: false,
	required: true,
	relationTo: 'content-queries',
};

const cta: RowField = {
	type: 'row',
	fields: [ctaText, ctaLink],
};

export const contentMediaFields: Field[] = [title, contentQuery, limit, cta];
