'use client';

import React, { FC, useEffect, useState } from 'react';
import { useAllFormFields } from '@payloadcms/ui';
import { reduceFieldsToValues } from 'payload/shared';
import AdminBlock from '@repo/payload/components/AdminBlock';

export const ContentMediaAdminBlock: FC = () => {
	const [fields, _setFields] = useAllFormFields();
	const formData = reduceFieldsToValues(fields, true);

	const [previewData, setPreviewData] = useState({
		limit: formData?.limit || 4,
		title: formData?.title || '',
		ctaText: formData?.ctaText || '',
		ctaLink: formData?.ctaLink || '',
	});

	useEffect(() => {
		setPreviewData({
			limit: formData?.limit || 4,
			title: formData?.title || '',
			ctaText: formData?.ctaText || '',
			ctaLink: formData?.ctaLink || '',
		});
	}, [formData?.limit, formData?.title, formData?.ctaText, formData?.ctaLink]);

	return (
		<AdminBlock name="Content Media Block">
			<div style={{ fontWeight: 'bold', marginBottom: '.5em' }}>
				<div
					style={{ marginTop: '10px', fontSize: '14px', fontWeight: 'normal' }}
				>
					<p>
						<strong>Layout:</strong>{' '}
						{previewData.limit === 1 ? 'Single item (hero)' : 'Grid (4 items)'}
					</p>
					{previewData.title && (
						<p>
							<strong>Title:</strong> {previewData.title}
						</p>
					)}
					{previewData.ctaText && (
						<p>
							<strong>CTA Button:</strong> {previewData.ctaText}
						</p>
					)}
					{previewData.ctaLink && (
						<p>
							<strong>CTA Link:</strong> {previewData.ctaLink}
						</p>
					)}
				</div>
			</div>
		</AdminBlock>
	);
};

export default ContentMediaAdminBlock;
