'use client';

import React, { useEffect } from 'react';
import { RefreshRouteOnSave as PayloadLivePreview } from '@payloadcms/live-preview-react';
import { useRouter } from 'next/navigation';
import { getClientSideURL } from '@repo/payload/utils/getURL';

const RefreshRouteOnSave: React.FC = () => {
	const router = useRouter();
	const serverURL = getClientSideURL();

	useEffect(() => {
		// Log when the component mounts to help with debugging
		console.debug('RefreshRouteOnSave mounted with server URL:', serverURL);

		// Set up error handling for the live preview
		const handleLivePreviewError = (event: ErrorEvent) => {
			console.error('Live preview error:', event.error);
		};

		window.addEventListener('error', handleLivePreviewError);

		return () => {
			window.removeEventListener('error', handleLivePreviewError);
		};
	}, [serverURL]);

	// Handle refresh callback with error handling
	const handleRefresh = () => {
		try {
			console.debug('Refreshing route via Next.js router');
			router.refresh();
		} catch (error) {
			console.error('Error refreshing route:', error);
		}
	};

	return <PayloadLivePreview refresh={handleRefresh} serverURL={serverURL} />;
};

export default RefreshRouteOnSave;
