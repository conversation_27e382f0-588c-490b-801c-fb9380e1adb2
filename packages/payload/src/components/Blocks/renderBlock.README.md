# renderBlock Utility

The `renderBlock` utility provides a type-safe way to render blocks from the PayloadCMS content system in React components.

## Overview

When rendering blocks from PayloadCMS, we need to ensure that each block component receives the correct props based on its type. The `renderBlock` utility handles this by:

1. Identifying the block type
2. Finding the corresponding component from the block configs
3. Casting the block data to the correct TypeScript interface
4. Rendering the component with the properly typed props

## Usage

```tsx
import { renderBlock } from "@/utils/renderBlock";

// In a component that receives blocks from the API
function BlockRenderer({ blocks }) {
	return (
		<div className="blocks-container">
			{blocks?.map((block, index) => {
				return renderBlock(block, `block-${index}`);
			})}
		</div>
	);
}
```

## Implementation Details

The utility uses TypeScript's type casting to ensure that each block component receives the correct props:

```tsx
export function renderBlock(block: any, key?: string): React.ReactNode {
	if (!block || !block.blockType) {
		return null;
	}

	const blockConfig = blockConfigs.find(
		(config) => config.slug === block.blockType,
	);
	if (!blockConfig) {
		console.warn(`No component found for block type: ${block.blockType}`);
		return null;
	}

	const Component = blockConfig.component;

	// Type-specific rendering with appropriate props
	switch (block.blockType) {
		case "CurrentConditions":
			return (
				<Component key={key} {...(block as CurrentConditionsBlockConfig)} />
			);
		case "DailyForecast":
			return <Component key={key} {...(block as DailyForecastBlockConfig)} />;
		// ... other block types
		default:
			// Fallback for unknown block types
			return <Component key={key} {...block} />;
	}
}
```

## Type Safety

The utility includes a type guard function to check if a block is of a specific type:

```tsx
export function isBlockType<T extends { blockType: string }>(
	block: any,
	type: string,
): block is T {
	return block?.blockType === type;
}
```

This can be used in components that need to perform type-specific operations:

```tsx
import { isBlockType } from "@/utils/renderBlock";
import type { CurrentConditionsBlockConfig } from "@/payload-types";

function processBlock(block: any) {
	if (isBlockType<CurrentConditionsBlockConfig>(block, "CurrentConditions")) {
		// TypeScript now knows that block is a CurrentConditionsBlockConfig
		const { locationProvider } = block;
		// ...
	}
}
```

## Adding New Block Types

To add support for a new block type:

1. Create the block component and configuration in `apps/web/blocks/`
2. Add the block configuration to `apps/web/blocks/index.ts`
3. Update the `renderBlock` function to handle the new block type:

```tsx
// In renderBlock.tsx
import type { NewBlockConfig } from "@/payload-types";

// ...

export function renderBlock(block: any, key?: string): React.ReactNode {
	// ...

	switch (block.blockType) {
		// ... existing cases
		case "NewBlock":
			return <Component key={key} {...(block as NewBlockConfig)} />;
		default:
			return <Component key={key} {...block} />;
	}
}
```

## Benefits

- **Type Safety**: Ensures that block components receive correctly typed props
- **Centralized Rendering**: Provides a single place to handle block rendering logic
- **Maintainability**: Makes it easy to add new block types without modifying page components
- **Error Handling**: Includes graceful handling of missing or invalid block types

## Integration with Context Parameters

The `renderBlock` utility works seamlessly with the context parameters system:

1. The `getContextualizedPage` function returns a page with filtered layouts and blocks
2. Page components map through these layouts and blocks
3. The `renderBlock` utility renders each block with the correct props

This allows for dynamic page composition based on context parameters while maintaining type safety.
