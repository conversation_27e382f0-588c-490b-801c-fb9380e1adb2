import React from 'react';
import { BL<PERSON><PERSON>_MAP, BlockConfig } from '@repo/payload/blocks';

/**
 * Renders a block with the correct component and props based on its type
 *
 * @param block The block configuration from the API
 * @param key Optional key for the React component
 * @returns The rendered block component
 */
export function renderBlock(block: BlockConfig, key?: string): React.ReactNode {
	if (!block || !block.blockType) {
		return null;
	}

	const Component = BLOCK_MAP[block.blockType];

	if (!Component) {
		console.warn(`No component found for block type: ${block.blockType}`);
		return null;
	}

	return <Component key={key} {...block} />;
}
