import React, { Fragment } from 'react';
import { renderBlock } from './renderBlock';
import type { BlockConfig } from '@repo/payload/blocks';

const Blocks: React.FC<{
	blocks: BlockConfig[] | null | undefined;
}> = (props) => {
	const { blocks } = props;

	const hasBlocks = blocks && Array.isArray(blocks) && blocks.length > 0;

	if (hasBlocks) {
		return (
			<Fragment>
				{blocks.map((block, index) => {
					return renderBlock(block, `${block.blockName}-${index}`);
				})}
			</Fragment>
		);
	}

	return null;
};

export default Blocks;
