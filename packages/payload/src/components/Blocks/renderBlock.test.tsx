import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render } from '@testing-library/react';
// Import after mocks are defined
import { renderBlock } from './renderBlock';
import { BLOCK_MAP } from '@repo/payload/blocks';
import {
	AdBlockConfig,
	CurrentConditionsBlockConfig,
	DailyForecastBlockConfig,
} from '@repo/payload/payload-types';

// Revisit below statement, vi.mock doesnt require things befined beforehand
// Mock the BLOCK_MAP - must be defined before importing modules that use it
vi.mock('@repo/payload/blocks', () => {
	return {
		BLOCK_MAP: {
			CurrentConditions: vi
				.fn()
				.mockImplementation(({ locationProvider }) => (
					<div data-testid="current-conditions">
						Location Provider: {locationProvider}
					</div>
				)),
			DailyForecast: vi
				.fn()
				.mockImplementation(({ locationProvider }) => (
					<div data-testid="daily-forecast">
						Location Provider: {locationProvider}
					</div>
				)),
			Ad: vi
				.fn()
				.mockImplementation(({ adId }) => (
					<div data-testid="ad">Ad ID: {adId}</div>
				)),
		},
	};
});

describe('renderBlock', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	it('should render a CurrentConditions block with correct props', () => {
		const block: CurrentConditionsBlockConfig = {
			blockType: 'CurrentConditions',
			locationProvider: 'geolocation',
			id: '123',
		};

		const { getByTestId } = render(renderBlock(block) as React.ReactElement);

		const element = getByTestId('current-conditions');
		expect(element.textContent).toContain('Location Provider: geolocation');
		expect(BLOCK_MAP.CurrentConditions).toHaveBeenCalledWith(
			expect.objectContaining(block),
			undefined,
		);
	});

	it('should render a DailyForecast block with correct props', () => {
		const block: DailyForecastBlockConfig = {
			blockType: 'DailyForecast',
			locationProvider: 'specified',
			id: '456',
		};

		const { getByTestId } = render(renderBlock(block) as React.ReactElement);

		const dailyElement = getByTestId('daily-forecast');
		expect(dailyElement.textContent).toContain('Location Provider: specified');
		expect(BLOCK_MAP.DailyForecast).toHaveBeenCalledWith(
			expect.objectContaining(block),
			undefined,
		);
	});

	it('should render an Ad block with correct props', () => {
		const block: AdBlockConfig = {
			blockType: 'Ad',
			adId: 'WX_Top300Variable',
			id: '789',
		};

		const { getByTestId } = render(renderBlock(block) as React.ReactElement);

		const adElement = getByTestId('ad');
		expect(adElement.textContent).toContain('Ad ID: WX_Top300Variable');
		expect(BLOCK_MAP.Ad).toHaveBeenCalledWith(
			expect.objectContaining(block),
			undefined,
		);
	});

	it('should return null for a null block', () => {
		const result = renderBlock(null as any);
		expect(result).toBeNull();
	});

	it('should return null for an undefined block', () => {
		const result = renderBlock(undefined as any);
		expect(result).toBeNull();
	});

	it('should return null for a block with an unknown blockType', () => {
		const block = {
			blockType: 'UnknownBlock',
			id: 'unknown',
		} as any;
		const result = renderBlock(block);
		expect(result).toBeNull();
	});

	it('should pass the key prop to the rendered component', () => {
		const block = {
			blockType: 'CurrentConditions',
			locationProvider: 'geolocation',
		} as CurrentConditionsBlockConfig;

		const result = renderBlock(block, 'test-key') as React.ReactElement;

		expect(result.key).toBe('test-key');
	});
});
