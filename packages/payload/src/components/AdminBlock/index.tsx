'use client';

import React, { FC } from 'react';
import {
	BlockEditButton,
	BlockRemoveButton,
} from '@payloadcms/richtext-lexical/client';

interface AdminBlockProps {
	name: string;
	children?: React.ReactNode;
}

const AdminBlock: FC<AdminBlockProps> = ({ name, children }) => {
	return (
		<div
			className="min-w-xl mb-1 flex w-full flex-col p-3"
			style={{
				border: '2px solid var(--theme-elevation-200)',
				borderRadius: '5px',
				background: 'var(--theme-input-bg)',
				color: 'var(--theme-elevation-800)',
				boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
			}}
		>
			<h4
				className="flex items-center gap-2 pb-3"
				style={{
					borderBottom: '1px solid var(--theme-elevation-200)',
					marginBottom: '8px',
				}}
			>
				<span className="flex-grow truncate text-left text-sm font-medium">
					{name}
				</span>
				<div className="flex items-center gap-1">
					<BlockEditButton />
					<BlockRemoveButton />
				</div>
			</h4>
			<div className="text-sm">
				<div className="pt-1">{children}</div>
			</div>
		</div>
	);
};

export default AdminBlock;
