import { JSX, HTMLAttributes } from 'react';
import {
	DefaultNodeTypes,
	SerializedBlockNode,
	SerializedLinkNode,
} from '@payloadcms/richtext-lexical';
import { SerializedEditorState } from '@payloadcms/richtext-lexical/lexical';
import { WeatherLocationServerJSXConverter } from '../../lexical/features/WeatherLocationTicker/converters';
import {
	JSXConvertersFunction,
	LinkJSXConverter,
	RichText as RichTextWithoutBlocks,
} from '@payloadcms/richtext-lexical/react';
import { blockRegistrations, BlockConfig } from '@repo/payload/blocks';

type NodeTypes = DefaultNodeTypes | SerializedBlockNode<BlockConfig>;

// TODO: This still needs to be handled properly similar to generatePreviewPath
// Related docs: https://payloadcms.com/docs/rich-text/converting-jsx#internal-links
const internalDocToHref = ({ linkNode }: { linkNode: SerializedLinkNode }) => {
	const { value, relationTo } = linkNode.fields.doc!;
	if (typeof value !== 'object') {
		throw new Error('Expected value to be an object');
	}
	const slug = value.slug;
	return relationTo === 'posts' ? `/posts/${slug}` : `/${slug}`;
};

function buildSerializedBlocks() {
	return blockRegistrations.reduce(
		(acc, { slug, component: BlockComponent }) => {
			acc[slug] = ({ node }) => {
				return <BlockComponent {...node.fields} />;
			};
			return acc;
		},
		{} as Record<
			string,
			(props: { node: SerializedBlockNode<BlockConfig> }) => JSX.Element
		>,
	);
}

const jsxConverters: JSXConvertersFunction<NodeTypes> = ({
	defaultConverters,
}) => ({
	...defaultConverters,
	...LinkJSXConverter({ internalDocToHref }),
	...WeatherLocationServerJSXConverter,
	blocks: buildSerializedBlocks(),
});

type Props = {
	data: SerializedEditorState | null;
	enableGutter?: boolean;
	enableProse?: boolean;
	disableContainer?: boolean;
} & HTMLAttributes<HTMLDivElement>;

export function RichText(props: Props) {
	const { data, ...rest } = props;
	// Provide a default minimal lexical state if `data` is missing
	const safeData = data || {
		root: {
			children: [],
			direction: 'ltr',
			format: '',
			indent: 0,
			type: 'root',
			version: 1,
		},
	};

	return (
		<RichTextWithoutBlocks
			converters={jsxConverters}
			data={safeData}
			{...rest}
		/>
	);
}
