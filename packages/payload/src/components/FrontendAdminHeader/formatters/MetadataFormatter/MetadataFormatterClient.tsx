'use client';

import { useState, useTransition } from 'react';
import { useDebugSystem } from '../../state/hooks';
import { translateMetadataDisplayInfo } from '../../translators/metadataDisplay';
import type { FormattedMetadataInfo } from '../../translators/metadataDisplay';
import { MetadataFormatterClientProps } from '../types';
import { PageDebugData } from '../../state/types';

/**
 * MetadataFormatterClient - Client Component (Formatter)
 *
 * This component is responsible for formatting and displaying metadata.
 * It follows the Collector-Translator-Formatter pattern as a formatter:
 *
 * 1. It receives translated metadata from the server component
 * 2. It uses a translator function to get UI-ready display information
 * 3. It renders that information without applying business logic
 */
export function MetadataFormatterClient({
	userRoles,
	metadata,
	path,
}: MetadataFormatterClientProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [isPending, startTransition] = useTransition();

	// Access debug data from the hook
	const { debugData } = useDebugSystem();

	// Use the translator to process the metadata and get display info
	// All business logic is now contained in the translator
	const displayInfo: FormattedMetadataInfo = translateMetadataDisplayInfo(
		metadata,
		debugData,
		path,
	);

	// Check if user has required roles (content team or developers)
	const hasRequiredRole = userRoles.some((role) =>
		['admin', 'web-developer', 'editor'].includes(role),
	);

	// If user doesn't have required roles, don't render anything
	if (!hasRequiredRole) {
		return null;
	}

	// Toggle function to ensure consistent open/close behavior
	const toggleMetadataPanel = () => {
		startTransition(() => {
			setIsOpen((prevState) => !prevState);
		});
	};

	// Helper function to safely get tenant name
	const getTenantName = (tenant: PageDebugData['tenant']): string => {
		if (typeof tenant === 'string') {
			return tenant;
		}
		if (
			tenant &&
			typeof tenant === 'object' &&
			'name' in tenant &&
			tenant.name
		) {
			return tenant.name;
		}
		return 'Unknown';
	};

	// Helper function to safely get author name
	const getAuthorName = (author: PageDebugData['author']): string => {
		if (typeof author === 'string') {
			return author;
		}
		if (
			author &&
			typeof author === 'object' &&
			'name' in author &&
			author.name
		) {
			return author.name;
		}
		return 'Unknown';
	};

	// Helper function to safely get name from tag or category
	const getTagName = (tag: string | { name?: string }): string => {
		if (typeof tag === 'string') {
			return tag;
		}
		if (tag && typeof tag === 'object' && 'name' in tag && tag.name) {
			return tag.name;
		}
		return 'Unknown';
	};

	// Helper function to safely get version
	const getVersion = (metadata: PageDebugData | null): string => {
		if (!metadata) return 'Unknown';

		if (metadata.version !== undefined) {
			return String(metadata.version);
		}

		if ('_version' in metadata && metadata._version !== undefined) {
			return String(metadata._version);
		}

		return 'Unknown';
	};

	// The formatter now just uses the pre-processed display info to render UI
	return (
		<>
			{/* Metadata toggle button */}
			<button
				onClick={toggleMetadataPanel}
				className={`ml-2 rounded px-3 py-1 text-xs font-bold transition-colors ${
					displayInfo.buttonStyle.backgroundColor
				} ${displayInfo.buttonStyle.hoverColor} ${
					displayInfo.buttonStyle.textColor
				}`}
				title={displayInfo.tooltipText}
				disabled={isPending}
				data-testid="admin-metadata-button"
			>
				{displayInfo.hasMetadata
					? isOpen
						? displayInfo.buttonText.open
						: displayInfo.buttonText.closed
					: 'No Metadata'}
			</button>

			{/* Metadata panel */}
			{isOpen && (
				<div
					className="fixed inset-0 z-50 flex items-center justify-center overflow-hidden bg-black/50 p-4"
					data-testid="admin-metadata-panel"
					data-debug-panel
				>
					<div className="flex max-h-[80vh] w-full max-w-2xl flex-col rounded-lg bg-blue-900 text-white shadow-xl">
						{/* Header with title and close button */}
						<div className="flex items-center justify-between border-b border-blue-700 p-4">
							<div>
								<h3 className="text-lg font-bold">Page Metadata</h3>
								{displayInfo.dataState === 'stale' && (
									<span className="text-xs text-orange-400">
										* This data may be from a previous page
									</span>
								)}
							</div>
							<button
								onClick={toggleMetadataPanel}
								className="rounded-full p-1 text-gray-300 transition-colors hover:bg-blue-800 hover:text-white"
								aria-label="Close"
								data-testid="admin-metadata-close-button"
							>
								×
							</button>
						</div>

						{/* Content area with scrolling */}
						<div className="flex-1 overflow-auto p-4">
							{displayInfo.hasMetadata ? (
								<div className="space-y-4">
									{/* Title */}
									<div className="space-y-1">
										<h4 className="text-sm font-medium text-blue-300">Title</h4>
										<div className="rounded bg-blue-800 p-3">
											<p className="text-sm">
												{displayInfo.metadata?.title ||
													displayInfo.metadata?.seo?.title ||
													'No title defined'}
											</p>
										</div>
									</div>

									{/* Description */}
									<div className="space-y-1">
										<h4 className="text-sm font-medium text-blue-300">
											Description
										</h4>
										<div className="rounded bg-blue-800 p-3">
											<p className="text-sm">
												{displayInfo.metadata?.description ||
													displayInfo.metadata?.seo?.description ||
													'No description defined'}
											</p>
										</div>
									</div>

									{/* URL */}
									<div className="space-y-1">
										<h4 className="text-sm font-medium text-blue-300">URL</h4>
										<div className="rounded bg-blue-800 p-3">
											<p className="break-all text-sm">
												{displayInfo.path || 'Unknown'}
											</p>
										</div>
									</div>

									{/* Asset Name */}
									<div className="space-y-1">
										<h4 className="text-sm font-medium text-blue-300">
											Asset Name
										</h4>
										<div className="rounded bg-blue-800 p-3">
											<p className="break-all text-sm">
												{displayInfo.metadata?.assetName || 'Unknown'}
											</p>
										</div>
									</div>

									{/* Content Status */}
									<div className="grid grid-cols-2 gap-4">
										<div className="space-y-1">
											<h4 className="text-sm font-medium text-blue-300">
												Status
											</h4>
											<div className="rounded bg-blue-800 p-3">
												<p className="text-sm">
													{displayInfo.metadata?.status ? (
														<span
															className={`rounded px-2 py-1 text-xs ${
																displayInfo.metadata.status === 'published'
																	? 'bg-green-900 text-green-300'
																	: 'bg-yellow-900 text-yellow-300'
															}`}
														>
															{displayInfo.metadata.status}
														</span>
													) : (
														'Unknown'
													)}
												</p>
											</div>
										</div>

										<div className="space-y-1">
											<h4 className="text-sm font-medium text-blue-300">
												Locale
											</h4>
											<div className="rounded bg-blue-800 p-3">
												<p className="text-sm">
													{displayInfo.metadata?.locale || 'Unknown'}
												</p>
											</div>
										</div>
									</div>

									{/* Dates */}
									<div className="grid grid-cols-2 gap-4">
										<div className="space-y-1">
											<h4 className="text-sm font-medium text-blue-300">
												Created
											</h4>
											<div className="rounded bg-blue-800 p-3">
												<p className="text-sm">
													{displayInfo.metadata?.createdAt
														? new Date(
																displayInfo.metadata.createdAt,
															).toLocaleString()
														: 'Unknown'}
												</p>
											</div>
										</div>

										<div className="space-y-1">
											<h4 className="text-sm font-medium text-blue-300">
												Last Updated
											</h4>
											<div className="rounded bg-blue-800 p-3">
												<p className="text-sm">
													{displayInfo.metadata?.updatedAt
														? new Date(
																displayInfo.metadata.updatedAt,
															).toLocaleString()
														: 'Unknown'}
												</p>
											</div>
										</div>
									</div>

									{/* Published Date */}
									<div className="space-y-1">
										<h4 className="text-sm font-medium text-blue-300">
											Published Date
										</h4>
										<div className="rounded bg-blue-800 p-3">
											<p className="text-sm">
												{displayInfo.metadata?.publishedAt
													? new Date(
															displayInfo.metadata.publishedAt,
														).toLocaleString()
													: 'Not published'}
											</p>
										</div>
									</div>

									{/* Tenant */}
									<div className="space-y-1">
										<h4 className="text-sm font-medium text-blue-300">
											Tenant
										</h4>
										<div className="rounded bg-blue-800 p-3">
											<p className="text-sm">
												{displayInfo.metadata?.tenant
													? getTenantName(displayInfo.metadata.tenant)
													: 'Unknown'}
											</p>
										</div>
									</div>

									{/* Author (if available) */}
									{(displayInfo.metadata?.author ||
										debugData?.page?.author) && (
										<div className="space-y-1">
											<h4 className="text-sm font-medium text-blue-300">
												Author
											</h4>
											<div className="rounded bg-blue-800 p-3">
												<p className="text-sm">
													{displayInfo.metadata?.author
														? getAuthorName(displayInfo.metadata.author)
														: debugData?.page?.author
															? getAuthorName(debugData.page.author)
															: 'Unknown'}
												</p>
											</div>
										</div>
									)}

									{/* Categories (if available) */}
									{(displayInfo.metadata?.categories ||
										debugData?.page?.categories) && (
										<div className="space-y-1">
											<h4 className="text-sm font-medium text-blue-300">
												Categories
											</h4>
											<div className="rounded bg-blue-800 p-3">
												<div className="flex flex-wrap gap-1">
													{(() => {
														const categories = Array.isArray(
															displayInfo.metadata?.categories,
														)
															? displayInfo.metadata.categories
															: Array.isArray(debugData?.page?.categories)
																? debugData.page.categories
																: [];

														return categories.map((category, index) => (
															<span
																key={index}
																className="rounded bg-blue-700 px-2 py-1 text-xs"
															>
																{getTagName(category)}
															</span>
														));
													})()}
												</div>
											</div>
										</div>
									)}

									{/* Tags (if available) */}
									{(displayInfo.metadata?.tags || debugData?.page?.tags) && (
										<div className="space-y-1">
											<h4 className="text-sm font-medium text-blue-300">
												Tags
											</h4>
											<div className="rounded bg-blue-800 p-3">
												<div className="flex flex-wrap gap-1">
													{(() => {
														const tags = Array.isArray(
															displayInfo.metadata?.tags,
														)
															? displayInfo.metadata.tags
															: Array.isArray(debugData?.page?.tags)
																? debugData.page.tags
																: [];

														return tags.map((tag, index) => (
															<span
																key={index}
																className="rounded bg-blue-700 px-2 py-1 text-xs"
															>
																{getTagName(tag)}
															</span>
														));
													})()}
												</div>
											</div>
										</div>
									)}

									{/* Technical details (for developers) */}
									{userRoles.some((role) =>
										['admin', 'web-developer'].includes(role),
									) && (
										<>
											<div className="space-y-1">
												<h4 className="text-sm font-medium text-blue-300">
													ID
												</h4>
												<div className="rounded bg-blue-800 p-3">
													<p className="font-mono text-sm">
														{displayInfo.metadata?.id || 'Unknown'}
													</p>
												</div>
											</div>

											<div className="grid grid-cols-2 gap-4">
												<div className="space-y-1">
													<h4 className="text-sm font-medium text-blue-300">
														Collection
													</h4>
													<div className="rounded bg-blue-800 p-3">
														<p className="text-sm">
															{displayInfo.metadata?.collection || 'Unknown'}
														</p>
													</div>
												</div>

												<div className="space-y-1">
													<h4 className="text-sm font-medium text-blue-300">
														Version
													</h4>
													<div className="rounded bg-blue-800 p-3">
														<p className="text-sm">
															{getVersion(displayInfo.metadata)}
														</p>
													</div>
												</div>
											</div>

											{/* SEO Details */}
											<div className="space-y-1">
												<h4 className="text-sm font-medium text-blue-300">
													SEO Details
												</h4>
												<div className="space-y-2 rounded bg-blue-800 p-3">
													<div>
														<span className="text-xs text-gray-300">
															OG Title:
														</span>
														<p className="text-sm">
															{displayInfo.metadata?.ogTitle ||
																displayInfo.metadata?.seo?.ogTitle ||
																'Not set'}
														</p>
													</div>
													<div>
														<span className="text-xs text-gray-300">
															OG Description:
														</span>
														<p className="text-sm">
															{displayInfo.metadata?.ogDescription ||
																displayInfo.metadata?.seo?.ogDescription ||
																'Not set'}
														</p>
													</div>
													<div>
														<span className="text-xs text-gray-300">
															OG Image:
														</span>
														<p className="break-all text-sm">
															{displayInfo.metadata?.ogImage ||
																(displayInfo.metadata?.seo?.ogImage?.url
																	? displayInfo.metadata.seo.ogImage.url
																	: 'Not set')}
														</p>
													</div>
												</div>
											</div>
										</>
									)}
								</div>
							) : (
								// New "no metadata" state
								<div className="flex h-[300px] flex-col items-center justify-center">
									<p className="mb-4 text-gray-300">
										No metadata available for this page.
									</p>
									<p className="max-w-md text-center text-sm text-gray-500">
										This page may not have a DebugCollector component or the
										page was loaded directly without navigation from a page with
										metadata.
									</p>
								</div>
							)}
						</div>

						{/* Footer with actions */}
						<div className="flex justify-between border-t border-blue-700 p-4">
							{displayInfo.metadata?.id &&
								userRoles.some((role) =>
									['admin', 'editor'].includes(role),
								) && (
									<a
										href={`/admin/collections/pages/${displayInfo.metadata.id}`}
										target="_blank"
										rel="noopener noreferrer"
										className="rounded bg-indigo-600 px-4 py-2 text-sm text-white transition-colors hover:bg-indigo-700"
									>
										Edit in Admin
									</a>
								)}
							<button
								onClick={toggleMetadataPanel}
								className="rounded bg-blue-700 px-4 py-2 text-sm text-white transition-colors hover:bg-blue-600"
							>
								Close
							</button>
						</div>
					</div>
				</div>
			)}
		</>
	);
}
