import dynamic from 'next/dynamic';

// Dynamically import the client component with proper loading state
const MetadataFormatterClient = dynamic(
	() =>
		import('./MetadataFormatterClient').then(
			(mod) => mod.MetadataFormatterClient,
		),
	{
		ssr: false,
		loading: () => (
			<button className="ml-2 rounded bg-indigo-600 px-3 py-1 text-xs font-bold text-white opacity-50">
				Loading...
			</button>
		),
	},
);

interface MetadataFormatterProps {
	userRoles: string[];
	metadata: Record<string, unknown> | null; // Replace any with specific type
	path?: string;
}

/**
 * MetadataFormatter - Component (Formatter)
 *
 * This component formats metadata for display.
 * It follows the Collector-Translator-Formatter pattern as a formatter:
 * - It receives pre-translated metadata
 * - It formats this data into a user-friendly UI
 * - It does NOT fetch or transform data
 *
 * Performance optimization: Uses dynamic import for client component
 */
export function MetadataFormatter({
	userRoles,
	metadata,
	path,
}: MetadataFormatterProps) {
	// Check if user has required roles (content team or developers)
	const hasRequiredRole = userRoles.some((role) =>
		['admin', 'web-developer', 'editor'].includes(role),
	);

	// If user doesn't have required roles, don't render anything
	if (!hasRequiredRole) {
		return null;
	}

	// Simply pass the data to the client component for formatting
	return (
		<MetadataFormatterClient
			userRoles={userRoles}
			metadata={metadata}
			path={path || ''}
		/>
	);
}

// Export the legacy name for backward compatibility
export const MetadataProvider = MetadataFormatter;
