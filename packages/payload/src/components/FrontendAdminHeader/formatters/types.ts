/**
 * Common types for formatter components
 *
 * These types standardize the API of formatter components following the
 * Collector-Translator-Formatter pattern.
 */

import type { ComponentDebugData } from '../collectors/types';
import type { DebugData, PageDebugData, ErrorData } from '../state/types';
import type { LocationData } from '@repo/location/types';
import type { SanitizedObject } from '../translators/sanitizers';
import type { UseAuth } from '../hooks/usePayloadAuth';

/**
 * Base props for formatters that require user role checks
 */
export interface AuthorizedFormatterProps {
	/**
	 * Array of user roles used to determine access permissions
	 */
	userRoles: string[];
}

/**
 * Props for MetadataFormatter component
 */
export interface MetadataFormatterProps extends AuthorizedFormatterProps {
	/**
	 * Pre-translated metadata from a collector/translator
	 * Now uses the strongly-typed PageDebugData interface
	 */
	metadata: PageDebugData | null;

	/**
	 * Current path or route
	 */
	path?: string;
}

/**
 * Props for MetadataFormatterClient component
 */
export interface MetadataFormatterClientProps extends MetadataFormatterProps {
	/**
	 * Current path (required in client component)
	 */
	path: string;
}

/**
 * Props for AdminDebugPanel component
 */
export interface AdminDebugPanelProps extends AuthorizedFormatterProps {
	/**
	 * Optional custom initial active tab
	 */
	initialTab?: TabType;
}

/**
 * Tab types for organizing debug data in AdminDebugPanel
 */
export type TabType =
	| 'context'
	| 'page'
	| 'location'
	| 'headers'
	| 'environment'
	| 'components'
	| 'errors'
	| 'all';

/**
 * Base props for all AdminDebugPanel tab components
 */
export interface TabProps {
	/**
	 * Search term for filtering content
	 */
	searchTerm: string;
}

/**
 * Props for HeadersTab component
 */
export interface HeadersTabProps extends TabProps {
	/**
	 * HTTP headers data
	 */
	headers?: SanitizedObject;
}

/**
 * Props for EnvironmentTab component
 */
export interface EnvironmentTabProps extends TabProps {
	/**
	 * Environment variables and system info
	 */
	environment?: SanitizedObject;
}

/**
 * Props for LocationTab component
 */
export interface LocationTabProps extends TabProps {
	/**
	 * Location data
	 * Now uses the LocationData interface with additional debug metadata
	 */
	location?: LocationData & {
		_observedFrom?: string;
		_collectedBy?: string;
		_updatedAt?: string;
		[key: string]: unknown;
	};
}

/**
 * Props for ComponentsTab component
 */
export interface ComponentsTabProps extends TabProps {
	/**
	 * Component-specific debug data
	 * Now uses the ComponentDebugData interface
	 */
	components?: Record<string, ComponentDebugData>;
}

/**
 * Props for ErrorsTab component
 */
export interface ErrorsTabProps extends TabProps {
	/**
	 * Error information
	 * Now uses the ErrorData interface
	 */
	errors?: Record<string, ErrorData>;
}

/**
 * Props for PageTab component
 */
export interface PageTabProps extends TabProps {
	/**
	 * Page metadata
	 * Now uses the PageDebugData interface
	 */
	page?: PageDebugData;
}

/**
 * Props for ContextTab component
 */
export interface ContextTabProps extends TabProps {
	/**
	 * Context match data
	 */
	match?: DebugData['match'];
}

/**
 * Props for JsonTab component
 */
export interface JsonTabProps {
	/**
	 * Any JSON-serializable data to display
	 */
	data: Record<string, unknown>;
}

/**
 * Props for NoDataMessage component
 */
export interface NoDataMessageProps {
	/**
	 * Message to display when no data is available
	 */
	message: string;
}

/**
 * Props for DebugPanelContent component
 */
export interface DebugPanelContentProps {
	/**
	 * Currently active tab
	 */
	activeTab: TabType;

	/**
	 * Debug data to display - properly typed from state/types.ts
	 */
	debugData: DebugData | null;

	/**
	 * Search term for filtering content in applicable tabs
	 */
	searchTerm: string;
}

/**
 * Props for DebugPanelHeader component
 */
export interface DebugPanelHeaderProps {
	/**
	 * Function to call when close button is clicked
	 */
	onClose: () => void;
}

/**
 * Props for DebugPanelFooter component
 */
export interface DebugPanelFooterProps {
	/**
	 * Function to call when close button is clicked
	 */
	onClose: () => void;
}

/**
 * Props for DebugPanelTabs component
 */
export interface DebugPanelTabsProps {
	/**
	 * Currently active tab
	 */
	activeTab: TabType;

	/**
	 * Function to call when a tab is selected
	 */
	onTabChange: (tab: TabType) => void;
}

/**
 * Props for ClientHeader component
 */
export interface FrontendAdminHeaderProps {
	/**
	 * The state of the user authentication
	 * This is a custom hook that manages user authentication state
	 * and provides methods for login/logout operations
	 */
	useAuth: Pick<UseAuth, 'user' | 'logout' | 'loading'>;
}

/**
 * Props for DraftMode components
 */
export interface DraftModeButtonProps {
	/**
	 * Optional CSS class name
	 */
	className?: string;
}
