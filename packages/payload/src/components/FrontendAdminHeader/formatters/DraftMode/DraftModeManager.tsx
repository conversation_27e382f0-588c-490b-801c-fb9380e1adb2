'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useDraft } from '../../hooks/useDraft';

interface DraftModeManagerProps {
	isAdmin: boolean;
}

/**
 * DraftModeManager - Client Component (Formatter)
 *
 * This component consolidates all draft mode functionality:
 * 1. Uses the useDraft hook to manage draft mode state
 * 2. Provides UI for enabling/disabling draft mode
 * 3. Handles all state management and server actions
 *
 * It follows the Collector-Translator-Formatter pattern as a formatter:
 * - Uses useDraft hook to collect state and provide actions
 * - Formats the UI based on current state
 */
export const DraftModeManager: React.FC<DraftModeManagerProps> = ({
	// eslint-disable-next-line react/prop-types
	isAdmin,
}) => {
	const { refresh } = useRouter();
	const [localError, setLocalError] = useState<string | null>(null);

	// Use the existing useDraft hook for all draft mode functionality
	const {
		isEnabled: isDraftMode,
		loading: isLoading,
		error,
		enable,
		disable,
	} = useDraft();

	// Handle enabling draft mode
	const handleEnableDraftMode = async () => {
		try {
			setLocalError(null);

			// Use the hook's enable method
			await enable();

			// Refresh the page to ensure server state is reflected
			refresh();
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'An unexpected error occurred';
			setLocalError(errorMessage);
			console.error('Enable draft mode error:', err);
		}
	};

	// Handle exiting draft mode
	const handleExitDraftMode = async () => {
		try {
			setLocalError(null);

			// Use the hook's disable method
			await disable();

			// Refresh the page to ensure server state is reflected
			refresh();
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'An unexpected error occurred';
			setLocalError(errorMessage);
			console.error('Exit draft mode error:', err);
		}
	};

	// If not an admin user, only render draft mode indicator and exit button when in draft mode
	if (!isAdmin) {
		return isDraftMode ? (
			<div
				className="flex items-center gap-2"
				data-testid="admin-draft-mode-container"
			>
				<div
					className="rounded bg-red-600 px-3 py-1 text-xs font-bold text-white"
					data-testid="admin-draft-indicator"
				>
					DRAFT MODE
				</div>
				<button
					onClick={handleExitDraftMode}
					className="rounded bg-gray-600 px-3 py-1 text-xs font-bold text-white transition-colors hover:bg-blue-700 disabled:opacity-50"
					aria-label="Exit draft mode"
					data-testid="admin-exit-draft-button"
					disabled={isLoading}
				>
					{isLoading ? 'Exiting...' : 'Exit Draft'}
				</button>
			</div>
		) : null;
	}

	// For admin users, show appropriate controls based on current state
	return (
		<>
			{isDraftMode ? (
				<div
					className="flex items-center gap-2"
					data-testid="admin-draft-mode-container"
				>
					<div
						className="rounded bg-red-600 px-3 py-1 text-xs font-bold text-white"
						data-testid="admin-draft-indicator"
					>
						DRAFT MODE
					</div>
					<button
						onClick={handleExitDraftMode}
						className="rounded bg-gray-600 px-3 py-1 text-xs font-bold text-white transition-colors hover:bg-blue-700 disabled:opacity-50"
						aria-label="Exit draft mode"
						data-testid="admin-exit-draft-button"
						disabled={isLoading}
					>
						{isLoading ? 'Exiting...' : 'Exit Draft'}
					</button>
				</div>
			) : (
				<button
					onClick={handleEnableDraftMode}
					className="rounded bg-blue-600 px-3 py-1 text-xs font-bold text-white transition-colors hover:bg-blue-700 disabled:opacity-50"
					aria-label="Enable draft mode"
					data-testid="admin-draft-mode-button"
					disabled={isLoading}
				>
					{isLoading ? 'Enabling...' : 'View Draft'}
				</button>
			)}

			{(localError || error) && (
				<div className="mt-1 text-xs text-red-500" role="alert">
					{localError || error?.message || 'An error occurred'}
				</div>
			)}
		</>
	);
};
