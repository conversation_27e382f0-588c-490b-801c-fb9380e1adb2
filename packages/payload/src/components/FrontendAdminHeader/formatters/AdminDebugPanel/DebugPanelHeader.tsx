'use client';

import type { DebugPanelHeaderProps } from '../types';

export const DebugPanelHeader = ({ onClose }: DebugPanelHeaderProps) => {
	return (
		<div className="flex items-center justify-between border-b border-blue-700 p-4">
			<h3 className="text-lg font-bold">Debug Information</h3>
			<button
				onClick={onClose}
				className="flex h-8 w-8 items-center justify-center rounded-full text-xl text-gray-300 transition-colors hover:bg-blue-800 hover:text-white"
				aria-label="Close"
				data-testid="admin-debug-close-button"
			>
				×
			</button>
		</div>
	);
};
