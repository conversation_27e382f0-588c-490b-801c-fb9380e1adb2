'use client';

import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useDebugSystem } from '../../state/hooks';
import { DebugPanelHeader } from './DebugPanelHeader';
import { DebugPanelTabs } from './DebugPanelTabs';
import { DebugPanelFooter } from './DebugPanelFooter';
import dynamic from 'next/dynamic';
import type { TabType, AdminDebugPanelProps } from '../types';

// Use next/dynamic instead of React.lazy
const DebugPanelContent = dynamic(() => import('./DebugPanelContent'), {
	loading: () => (
		<div className="flex flex-1 items-center justify-center p-4">
			Loading debug data...
		</div>
	),
	ssr: false, // Disable SSR for this component since it's only used in a modal
});

/**
 * AdminDebugPanel - Client Component (Formatter)
 *
 * This component formats and displays debug data in a tabbed interface.
 * It follows the Collector-Translator-Formatter pattern as a formatter:
 *
 * 1. It receives pre-processed debug data from debugDataAtom
 * 2. It formats this data into an organized UI with tabs
 * 3. It enables filtering and visualization of the data
 *
 * The component doesn't collect or translate data directly - it only
 * consumes data from the central atom populated by collectors.
 */
export function AdminDebugPanel({ userRoles }: AdminDebugPanelProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [activeTab, setActiveTab] = useState<TabType>('all');
	const [searchTerm, setSearchTerm] = useState('');
	const [isMounted, setIsMounted] = useState(false);
	const { debugData } = useDebugSystem();

	// Check if user has required roles
	const hasRequiredRole = userRoles.some(
		(role) => role === 'admin' || role === 'web-developer',
	);

	// Set isMounted to true after component mounts
	// This ensures we only try to use createPortal on the client
	useEffect(() => {
		setIsMounted(true);
	}, []);

	// Keyboard shortcut for toggling panel (Ctrl+Shift+D)
	useEffect(() => {
		const handleKeyDown = (e: KeyboardEvent) => {
			if (e.ctrlKey && e.shiftKey && e.key === 'd') {
				e.preventDefault();
				setIsOpen((prev) => !prev);
			}
		};

		window.addEventListener('keydown', handleKeyDown);
		return () => window.removeEventListener('keydown', handleKeyDown);
	}, []);

	// If user doesn't have required roles, don't render anything
	// This check must come AFTER all hook calls to follow React's rules of hooks
	if (!hasRequiredRole) {
		return null;
	}

	// Toggle function to ensure consistent open/close behavior
	const toggleDebugPanel = () => {
		setIsOpen((prevState) => !prevState);
	};

	return (
		<>
			{/* Debug toggle button */}
			<button
				onClick={toggleDebugPanel}
				className="ml-2 rounded bg-blue-600 px-3 py-1 text-xs font-bold text-white transition-colors hover:bg-blue-700"
				title="Toggle Debug Panel (Ctrl+Shift+D)"
				data-testid="admin-debug-button"
			>
				{isOpen ? 'Hide Debug' : 'Show Debug'}
			</button>

			{/* Debug panel rendered via portal - only load when needed */}
			{isOpen &&
				isMounted &&
				createPortal(
					<div
						className="fixed inset-0 z-[100] flex items-center justify-center overflow-hidden bg-black/50 p-4"
						data-testid="admin-debug-panel"
						data-debug-panel
					>
						<div className="flex max-h-[80vh] w-full max-w-4xl flex-col overflow-hidden rounded-lg bg-blue-900 text-white shadow-xl">
							{/* Header with title and close button */}
							<DebugPanelHeader onClose={toggleDebugPanel} />

							{/* Tab navigation */}
							<DebugPanelTabs
								activeTab={activeTab}
								onTabChange={setActiveTab}
							/>

							{/* Search input for headers and environment tabs */}
							{(activeTab === 'headers' ||
								activeTab === 'environment' ||
								activeTab === 'components') && (
								<div className="border-b border-blue-700 p-4">
									<input
										type="text"
										placeholder={`Search ${activeTab}...`}
										value={searchTerm}
										onChange={(e) => setSearchTerm(e.target.value)}
										className="w-full rounded border border-blue-700 bg-blue-800 px-3 py-2 text-sm text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
										data-testid="admin-headers-search"
									/>
								</div>
							)}

							{/* Content area with proper overflow handling */}
							<div className="flex-1 overflow-auto">
								<DebugPanelContent
									activeTab={activeTab}
									debugData={debugData}
									searchTerm={searchTerm}
								/>
							</div>

							{/* Footer with actions - now in a fixed position at the bottom */}
							<div className="flex-shrink-0">
								<DebugPanelFooter onClose={toggleDebugPanel} />
							</div>
						</div>
					</div>,
					document.body,
				)}
		</>
	);
}
