'use client';

import type { TabType, DebugPanelTabsProps } from '../types';

export const DebugPanelTabs = ({
	activeTab,
	onTabChange,
}: DebugPanelTabsProps) => {
	const tabs: { id: TabType; label: string }[] = [
		{ id: 'all', label: 'All Data' },
		{ id: 'context', label: 'Context' },
		{ id: 'page', label: 'Page Data' },
		{ id: 'location', label: 'Location' },
		{ id: 'components', label: 'Components' },
		{ id: 'headers', label: 'Headers' },
		{ id: 'environment', label: 'Environment' },
		{ id: 'errors', label: 'Errors' },
	];

	return (
		<div className="flex flex-wrap border-b border-blue-700">
			{tabs.map((tab) => (
				<button
					key={tab.id}
					onClick={() => onTabChange(tab.id)}
					className={`px-4 py-2 text-sm ${
						activeTab === tab.id
							? 'border-b-2 border-blue-500 bg-blue-800 text-white'
							: 'text-gray-300 hover:bg-blue-800 hover:text-white'
					}`}
					data-testid={`admin-debug-tab-${tab.id}`}
				>
					{tab.label}
				</button>
			))}
		</div>
	);
};
