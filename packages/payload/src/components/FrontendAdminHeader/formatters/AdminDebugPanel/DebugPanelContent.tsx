'use client';

import type { DebugPanelContentProps } from '../../formatters/types';
import { HeadersTab } from './tabs/HeadersTab';
import { EnvironmentTab } from './tabs/EnvironmentTab';
import { LocationTab } from './tabs/LocationTab';
import { JsonTab } from './tabs/JsonTab';
import { ComponentsTab } from './tabs/ComponentsTab';
import { ContextTab } from './tabs/ContextTab';
import { PageDataTab } from './tabs/PageDataTab';
import { ErrorTab } from './tabs/ErrorTab';
import { translateTabData } from '../../translators/debugTabData';
import { DebugData } from '../../state/types';

// Interface for error detail in error tab
interface ErrorDetail {
	message?: string;
	timestamp?: string;
	stack?: string;
	details?: Record<string, unknown>;
	[key: string]: unknown;
}

/**
 * DebugPanelContent - Component (Formatter)
 *
 * This component formats and displays debug data based on the selected tab.
 * It follows the Collector-Translator-Formatter pattern as a formatter.
 */
const DebugPanelContent = ({
	activeTab,
	debugData,
	searchTerm,
}: DebugPanelContentProps) => {
	// Use translator function to get filtered data - no transformation in formatter
	const tabData = translateTabData(debugData, activeTab);

	// Default empty state for debugData to avoid null checks everywhere
	const safeDebugData: DebugData = debugData || {
		timestamp: new Date().toISOString(),
	};

	// Safe access to debugData properties with type assertions
	const headers = safeDebugData.headers;
	const environment = safeDebugData.environment;
	const components = safeDebugData.components;
	const location = safeDebugData.location;
	const errors = safeDebugData.errors as
		| Record<string, ErrorDetail>
		| undefined;
	const page = safeDebugData.page || {};
	const match = safeDebugData.match;
	const meta = safeDebugData.meta || {
		dataState: 'fresh' as const,
		lastConfirmedPath: null,
		currentPath: '',
		isDirectLoad: false,
		timestamp: '',
	};

	return (
		<div className="p-4">
			{activeTab === 'headers' ? (
				<HeadersTab headers={headers} searchTerm={searchTerm} />
			) : activeTab === 'environment' ? (
				<EnvironmentTab environment={environment} searchTerm={searchTerm} />
			) : activeTab === 'components' ? (
				<ComponentsTab components={components} searchTerm={searchTerm} />
			) : activeTab === 'location' ? (
				<LocationTab location={location} searchTerm={searchTerm} />
			) : activeTab === 'errors' ? (
				<ErrorTab errors={errors} searchTerm={searchTerm} />
			) : activeTab === 'context' ? (
				<ContextTab
					context={{
						parameters: page?.contextParameters,
						match,
						...Object.values(components || {}).reduce(
							(acc, comp: Record<string, unknown>) => {
								if (comp?.context) {
									return { ...acc, ...comp.context };
								}
								return acc;
							},
							{} as Record<string, unknown>,
						),
					}}
					searchTerm={searchTerm}
				/>
			) : activeTab === 'page' ? (
				// Use the new PageDataTab here, ensuring userRoles is always an array
				<PageDataTab
					page={page}
					userRoles={
						Array.isArray(safeDebugData.userRoles)
							? safeDebugData.userRoles
							: []
					}
				/>
			) : activeTab === 'all' && safeDebugData.meta ? (
				// Special handling for "all" tab when meta data is available
				<div className="space-y-6">
					{/* Meta Data Section */}
					<div className="mb-4 rounded border border-blue-800 bg-blue-800 p-4">
						<h4 className="mb-2 text-sm font-medium text-blue-300">
							Debug Data Status
						</h4>
						<div className="grid grid-cols-1 gap-3 md:grid-cols-2">
							<div>
								<span className="text-xs text-gray-300">Status:</span>
								<span
									className={`ml-2 rounded px-2 py-1 text-xs ${
										meta.dataState === 'fresh'
											? 'bg-green-900 text-green-300'
											: 'bg-orange-900 text-orange-300'
									}`}
								>
									{meta.dataState === 'fresh' ? 'Fresh Data' : 'Stale Data'}
								</span>
							</div>
							<div>
								<span className="text-xs text-gray-300">Current Path:</span>
								<span className="ml-2 break-all text-sm text-white">
									{meta.currentPath}
								</span>
							</div>
							<div>
								<span className="text-xs text-gray-300">
									Last Confirmed Path:
								</span>
								<span className="ml-2 break-all text-sm text-white">
									{meta.lastConfirmedPath || 'None'}
								</span>
							</div>
							<div>
								<span className="text-xs text-gray-300">Load Type:</span>
								<span className="ml-2 text-sm text-white">
									{meta.isDirectLoad
										? 'Direct Load/Refresh'
										: 'Client Navigation'}
								</span>
							</div>
							<div>
								<span className="text-xs text-gray-300">Timestamp:</span>
								<span className="ml-2 text-sm text-white">
									{new Date(meta.timestamp).toLocaleString()}
								</span>
							</div>
						</div>
					</div>

					{/* All Other Data */}
					<JsonTab data={tabData} />
				</div>
			) : (
				<JsonTab data={tabData} />
			)}
		</div>
	);
};

export default DebugPanelContent;
