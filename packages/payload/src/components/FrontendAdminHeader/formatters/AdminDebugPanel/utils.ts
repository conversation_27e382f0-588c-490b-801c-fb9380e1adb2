'use client';

export function filterObjectBySearchTerm(
	data: Record<string, unknown>,
	searchTerm: string,
): Record<string, unknown> {
	if (!searchTerm) return data;

	return Object.fromEntries(
		Object.entries(data).filter(
			([key, value]) =>
				key.toLowerCase().includes(searchTerm.toLowerCase()) ||
				(typeof value === 'string' &&
					value.toLowerCase().includes(searchTerm.toLowerCase())),
		),
	);
}

/**
 * Checks if text needs truncation and returns information for rendering
 *
 * @param text The text to check for truncation
 * @param maxLength The maximum length before truncation (default: 40)
 * @returns An object with truncation information
 */
export function shouldTruncate(
	text: string,
	maxLength: number = 40,
): {
	needsTruncation: boolean;
	displayText: string;
	fullText: string;
} {
	if (!text || text.length <= maxLength) {
		return {
			needsTruncation: false,
			displayText: text || '',
			fullText: text || '',
		};
	}

	return {
		needsTruncation: true,
		displayText: `${text.substring(0, maxLength)}...`,
		fullText: text,
	};
}

/**
 * CSS styles for custom tooltips
 * This is injected once into the document head
 */
export function injectTooltipStyles(): void {
	// Only run in the browser
	if (typeof window === 'undefined') return;

	// Check if styles are already injected
	if (document.getElementById('debug-tooltip-styles')) return;

	// Create style element
	const style = document.createElement('style');
	style.id = 'debug-tooltip-styles';
	style.innerHTML = `
		.debug-tooltip-container {
			position: relative;
			display: inline-block;
			cursor: pointer;
			border-bottom: 1px dotted #ccc;
		}
		
		.debug-tooltip-container .debug-tooltip {
			visibility: hidden;
			position: absolute;
			z-index: 100;
			bottom: 125%;
			left: 50%;
			transform: translateX(-50%);
			background-color: #333;
			color: #fff;
			text-align: center;
			padding: 5px 10px;
			border-radius: 4px;
			width: max-content;
			max-width: 300px;
			opacity: 0;
			transition: opacity 0.3s;
			word-break: break-all;
			font-size: 12px;
			line-height: 1.4;
		}
		
		.debug-tooltip-container:hover .debug-tooltip {
			visibility: visible;
			opacity: 1;
		}
		
		.debug-tooltip::after {
			content: "";
			position: absolute;
			top: 100%;
			left: 50%;
			margin-left: -5px;
			border-width: 5px;
			border-style: solid;
			border-color: #333 transparent transparent transparent;
		}
	`;

	// Append to document head
	document.head.appendChild(style);
}
