'use client';

import { DebugPanelFooterProps } from '../types';

export const DebugPanelFooter = ({ onClose }: DebugPanelFooterProps) => {
	return (
		<div className="flex justify-between border-t border-blue-700 p-4">
			<div className="text-xs text-gray-300">
				Press <kbd className="rounded bg-blue-800 px-1 py-0.5">Ctrl</kbd>+
				<kbd className="rounded bg-blue-800 px-1 py-0.5">Shift</kbd>+
				<kbd className="rounded bg-blue-800 px-1 py-0.5">D</kbd> to toggle debug
				panel
			</div>
			<button
				onClick={onClose}
				className="rounded bg-blue-700 px-4 py-2 text-sm text-white transition-colors hover:bg-gray-600"
			>
				Close
			</button>
		</div>
	);
};
