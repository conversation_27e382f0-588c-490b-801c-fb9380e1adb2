'use client';

import { useEffect } from 'react';
import { NoDataMessage } from '../NoDataMessage';
import {
	filterObjectBySearchTerm,
	shouldTruncate,
	injectTooltipStyles,
} from '../utils';
import { HeadersTabProps } from '../../types';

export const HeadersTab = ({ headers, searchTerm }: HeadersTabProps) => {
	const filteredHeaders = filterObjectBySearchTerm(headers || {}, searchTerm);
	const hasHeaders = headers && Object.keys(filteredHeaders).length > 0;

	// Inject tooltip styles on component mount
	useEffect(() => {
		injectTooltipStyles();
	}, []);

	return (
		<div className="space-y-2">
			<h4 className="mb-2 text-sm font-medium">HTTP Headers</h4>
			{hasHeaders ? (
				<div className="grid grid-cols-1 gap-1">
					{Object.entries(filteredHeaders).map(([name, value]) => {
						// Check if the header name or value needs truncation
						const nameInfo = shouldTruncate(name, 30);
						const valueInfo = shouldTruncate(String(value), 50);

						return (
							<div
								key={name}
								className="flex flex-col rounded bg-blue-800 p-2 sm:flex-row sm:items-start"
							>
								<span className="text-sm font-medium text-blue-300 sm:w-1/3">
									{nameInfo.needsTruncation ? (
										<span className="debug-tooltip-container">
											{nameInfo.displayText}
											<span className="debug-tooltip">{nameInfo.fullText}</span>
										</span>
									) : (
										nameInfo.displayText
									)}
									:
								</span>
								<span className="break-all text-sm text-gray-300 sm:w-2/3">
									{valueInfo.needsTruncation ? (
										<span className="debug-tooltip-container">
											{valueInfo.displayText}
											<span className="debug-tooltip">
												{valueInfo.fullText}
											</span>
										</span>
									) : (
										valueInfo.displayText
									)}
								</span>
							</div>
						);
					})}
				</div>
			) : (
				<NoDataMessage message="No matching headers found." />
			)}
		</div>
	);
};
