'use client';

import { NoDataMessage } from '../NoDataMessage';
import { translateLocationDisplay } from '../../../translators/locationDisplay';
import type { LocationTabProps } from '../../types';

export const LocationTab = ({ location, searchTerm }: LocationTabProps) => {
	if (!location) {
		return <NoDataMessage message="No location data available." />;
	}

	// Use the translator to format location data for display
	const formattedLocation = translateLocationDisplay(location);

	if (!formattedLocation) {
		return <NoDataMessage message="Could not format location data." />;
	}

	// Ensure source is properly typed
	const source =
		typeof formattedLocation.source === 'object' &&
		formattedLocation.source !== null
			? formattedLocation.source
			: { type: 'unknown', displayName: 'Unknown Source' };

	// Ensure rawLocation is properly typed
	const rawLocation = formattedLocation.rawLocation || {};
	const pathUpdatedFor =
		typeof rawLocation._pathUpdatedFor === 'string'
			? rawLocation._pathUpdatedFor
			: '';

	return (
		<div className="space-y-4">
			<div className="rounded bg-blue-800 p-4">
				{/* Location Header with Type */}
				<div className="mb-4">
					<h4 className="flex items-center gap-2 text-lg font-medium">
						{formattedLocation.displayName}
						<span
							className={`ml-2 rounded px-2 py-1 text-xs ${
								formattedLocation.source.type === 'geoIP'
									? 'bg-blue-700 text-blue-200'
									: 'bg-indigo-700 text-purple-200'
							}`}
						>
							{formattedLocation.source.displayName}
						</span>
					</h4>
					<p className="text-sm text-gray-300">
						{formattedLocation.fullDisplayName}
					</p>
				</div>

				{/* Coordinates Map (if coordinates are available) */}
				{formattedLocation.hasCoordinates && formattedLocation.coordinates && (
					<div className="mb-4 rounded border border-blue-700 bg-blue-900 p-4">
						<div className="mb-3 flex flex-col items-start justify-between sm:flex-row sm:items-center">
							<span className="mb-2 text-sm font-medium text-blue-300 sm:mb-0">
								Coordinates:
							</span>
							<span className="font-mono text-sm text-white">
								{formattedLocation.coordinates.formattedString}
							</span>
						</div>

						{formattedLocation.geocode && (
							<div className="mb-3 flex flex-col items-start justify-between sm:flex-row sm:items-center">
								<span className="mb-2 text-sm font-medium text-blue-300 sm:mb-0">
									Geocode:
								</span>
								<span className="break-all font-mono text-sm text-white">
									{formattedLocation.geocode}
								</span>
							</div>
						)}

						<div className="mt-3 flex justify-end gap-2">
							<a
								href={formattedLocation.coordinates.mapsLink}
								target="_blank"
								rel="noopener noreferrer"
								className="inline-flex items-center rounded bg-blue-600 px-3 py-1 text-xs text-white transition-colors hover:bg-blue-700"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									className="mr-1 h-3 w-3"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
									/>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
									/>
								</svg>
								View on Google Maps
							</a>

							{formattedLocation.properties.placeId && (
								<a
									href={`/weather/today/l/${formattedLocation.properties.placeId}`}
									target="_blank"
									rel="noopener noreferrer"
									className="inline-flex items-center rounded bg-green-600 px-3 py-1 text-xs text-white transition-colors hover:bg-green-700"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="mr-1 h-3 w-3"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"
										/>
									</svg>
									View Weather
								</a>
							)}
						</div>
					</div>
				)}

				{/* Location Properties */}
				<div className="grid grid-cols-1 gap-2 sm:grid-cols-2">
					{/* Display important location properties first */}
					{formattedLocation.properties.placeId && (
						<div className="rounded bg-blue-900 p-3">
							<span className="mb-1 block text-xs font-medium text-blue-300">
								Place ID:
							</span>
							<span className="break-all text-sm text-white">
								{formattedLocation.properties.placeId}
							</span>
						</div>
					)}

					{/* Show other properties */}
					{Object.entries(formattedLocation.properties)
						.filter(([key]) => key !== 'placeId')
						.filter(([key, value]) => {
							// Filter by search term if provided
							if (!searchTerm) return true;
							return (
								key.toLowerCase().includes(searchTerm.toLowerCase()) ||
								(typeof value === 'string' &&
									value.toLowerCase().includes(searchTerm.toLowerCase()))
							);
						})
						.map(([key, value]) => (
							<div key={key} className="rounded bg-blue-900 p-3">
								<span className="mb-1 block text-xs font-medium text-blue-300">
									{key.charAt(0).toUpperCase() +
										key.slice(1).replace(/([A-Z])/g, ' $1')}
									:
								</span>
								<span className="break-all text-sm text-white">
									{typeof value === 'object'
										? JSON.stringify(value)
										: String(value)}
								</span>
							</div>
						))}
				</div>

				{/* Update Timestamp */}
				{formattedLocation.updatedAt && (
					<div className="mt-4 text-xs text-gray-500">
						Last updated:{' '}
						{new Date(formattedLocation.updatedAt).toLocaleString()}
					</div>
				)}
			</div>

			{/* Source Information */}
			{/* Ensure div is only rendered if content is valid */}
			{typeof formattedLocation.source === 'object' &&
				formattedLocation.source !== null && (
					<div className="mt-2 text-xs text-gray-300">
						<p>
							{formattedLocation.source.type === 'geoIP'
								? 'Location determined via GeoIP from your network connection.'
								: `Location data source: ${String(formattedLocation.source.displayName)}`}
						</p>
						<p className="mt-1">
							This location is used for weather data and other location-based
							features.
						</p>
					</div>
				)}

			{/* Collected Information */}
			{source.collectedBy && pathUpdatedFor && (
				<div className="mt-2 text-xs text-gray-500">
					<p>
						Collected by: {String(source.collectedBy)} for path:{' '}
						{pathUpdatedFor}
					</p>
				</div>
			)}

			{/* Raw Data Disclosure */}
			<div className="mt-4">
				<details className="text-sm">
					<summary className="cursor-pointer text-gray-300 hover:text-white">
						View raw location data
					</summary>
					<pre className="mt-2 max-h-[200px] overflow-auto rounded bg-blue-800 p-3 text-xs">
						{JSON.stringify(formattedLocation.rawLocation, null, 2)}
					</pre>
				</details>
			</div>
		</div>
	);
};
