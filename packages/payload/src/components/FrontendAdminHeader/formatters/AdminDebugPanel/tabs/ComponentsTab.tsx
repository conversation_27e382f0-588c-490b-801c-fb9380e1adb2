'use client';

import { NoDataMessage } from '../NoDataMessage';
import { filterObjectBySearchTerm } from '../utils';
import { ComponentsTabProps } from '../../types';

export const ComponentsTab = ({
	components,
	searchTerm,
}: ComponentsTabProps) => {
	const filteredComponents = filterObjectBySearchTerm(
		components || {},
		searchTerm,
	);
	const hasComponents =
		components && Object.keys(filteredComponents).length > 0;

	return (
		<div className="space-y-2">
			<h4 className="mb-2 text-sm font-medium">Component Debug Data</h4>
			{hasComponents ? (
				<div className="grid grid-cols-1 gap-4">
					{Object.entries(filteredComponents).map(([componentName, data]) => (
						<div key={componentName} className="rounded bg-blue-800 p-4">
							<h5 className="mb-2 text-sm font-medium text-blue-300">
								{componentName}
							</h5>
							<pre className="max-h-[300px] overflow-auto whitespace-pre-wrap text-xs text-gray-300">
								{JSON.stringify(data, null, 2)}
							</pre>
						</div>
					))}
				</div>
			) : (
				<NoDataMessage message="No component debug data available. Use the DebugCollector component to add debug data from your components." />
			)}

			<div className="mt-4 text-xs text-gray-300">
				<p>To add debug data from a component, use the DebugCollector:</p>
				<pre className="mt-1 overflow-auto rounded bg-blue-800 p-2">
					{`<DebugCollector
  componentName="YourComponentName"
  data={yourDebugData} 
/>`}
				</pre>
			</div>
		</div>
	);
};
