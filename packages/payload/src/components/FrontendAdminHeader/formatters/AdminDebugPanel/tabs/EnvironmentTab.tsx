'use client';

import { useEffect } from 'react';
import { NoDataMessage } from '../NoDataMessage';
import {
	filterObjectBySearchTerm,
	shouldTruncate,
	injectTooltipStyles,
} from '../utils';
import { EnvironmentTabProps } from '../../types';

export const EnvironmentTab = ({
	environment,
	searchTerm,
}: EnvironmentTabProps) => {
	// Inject tooltip styles on component mount
	useEffect(() => {
		injectTooltipStyles();
	}, []);

	if (!environment) {
		return <NoDataMessage message="No environment data available." />;
	}

	// Create a flattened version of the environment object for display
	const flattenedEnv: Record<string, string> = {};

	// Process each property in the environment object
	Object.entries(environment).forEach(([key, value]) => {
		// Handle nested objects (like publicEnvVars)
		if (typeof value === 'object' && value !== null) {
			// For nested objects, flatten with dot notation
			Object.entries(value).forEach(([nestedKey, nestedValue]) => {
				flattenedEnv[`${key}.${nestedKey}`] = String(nestedValue);
			});
		} else {
			// For simple values, just convert to string
			flattenedEnv[key] = String(value);
		}
	});

	// Now filter the flattened object
	const filteredEnv = filterObjectBySearchTerm(flattenedEnv, searchTerm);
	const hasEnvVars = Object.keys(filteredEnv).length > 0;

	// Group environment variables by category for better organization
	const groupedEnv: Record<string, Record<string, string>> = {
		'Runtime Info': {},
		'System Info': {},
		'Vercel Info': {},
		'Public Variables': {},
		Other: {},
	};

	Object.entries(filteredEnv).forEach(([key, value]) => {
		// Ensure value is a string
		const stringValue = String(value);

		if (key.startsWith('publicEnvVars.')) {
			// Ensure the category exists before accessing it
			if (!groupedEnv['Public Variables']) {
				groupedEnv['Public Variables'] = {};
			}
			groupedEnv['Public Variables'][key.replace('publicEnvVars.', '')] =
				stringValue;
		} else if (
			key.includes('nodeVersion') ||
			key.includes('runtime') ||
			key.includes('memoryUsage')
		) {
			// Ensure the category exists
			if (!groupedEnv['System Info']) {
				groupedEnv['System Info'] = {};
			}
			groupedEnv['System Info'][key] = stringValue;
		} else if (
			key.includes('vercel') ||
			key.includes('region') ||
			key.includes('buildTime')
		) {
			// Ensure the category exists
			if (!groupedEnv['Vercel Info']) {
				groupedEnv['Vercel Info'] = {};
			}
			groupedEnv['Vercel Info'][key] = stringValue;
		} else if (key.includes('isDraftMode') || key.includes('nodeEnv')) {
			// Ensure the category exists
			if (!groupedEnv['Runtime Info']) {
				groupedEnv['Runtime Info'] = {};
			}
			groupedEnv['Runtime Info'][key] = stringValue;
		} else {
			// Ensure the category exists
			if (!groupedEnv['Other']) {
				groupedEnv['Other'] = {};
			}
			groupedEnv['Other'][key] = stringValue;
		}
	});

	return (
		<div className="space-y-4">
			<h4 className="mb-2 text-sm font-medium">Environment Variables</h4>
			{hasEnvVars ? (
				<>
					{Object.entries(groupedEnv).map(([groupName, variables]) => {
						const groupVars = Object.entries(variables);
						if (groupVars.length === 0) return null;

						return (
							<div key={groupName} className="mb-4">
								<h5 className="mb-2 border-b border-blue-700 pb-1 text-sm font-semibold text-blue-300">
									{groupName}
								</h5>
								<div className="grid grid-cols-1 gap-1">
									{groupVars.map(([name, value]) => {
										// Check if the env var name or value needs truncation
										const nameInfo = shouldTruncate(name, 30);
										const valueInfo = shouldTruncate(value, 50);
										const isMasked = value === '********';

										return (
											<div
												key={name}
												className="flex flex-col rounded bg-blue-800 p-2 sm:flex-row sm:items-start"
											>
												<span className="text-sm font-medium text-green-300 sm:w-1/3">
													{nameInfo.needsTruncation ? (
														<span className="debug-tooltip-container">
															{nameInfo.displayText}
															<span className="debug-tooltip">
																{nameInfo.fullText}
															</span>
														</span>
													) : (
														nameInfo.displayText
													)}
													:
												</span>
												<span className="break-all text-sm text-gray-300 sm:w-2/3">
													{isMasked ? (
														<span className="text-yellow-500">{value}</span>
													) : valueInfo.needsTruncation ? (
														<span className="debug-tooltip-container">
															{valueInfo.displayText}
															<span className="debug-tooltip">
																{valueInfo.fullText}
															</span>
														</span>
													) : (
														valueInfo.displayText
													)}
												</span>
											</div>
										);
									})}
								</div>
							</div>
						);
					})}
				</>
			) : (
				<NoDataMessage message="No matching environment variables found." />
			)}
			<div className="mt-4 text-xs text-gray-300">
				<p>
					Note: Only non-sensitive environment variables are shown for security
					reasons.
				</p>
				<p>
					Variables with sensitive patterns in their names are masked with
					********.
				</p>
				<p>In development mode, more variables are shown than in production.</p>
				<p>Hover over truncated values to see the full text.</p>
			</div>
		</div>
	);
};
