'use client';

import { NoDataMessage } from '../NoDataMessage';
import {
	translateContextData,
	organizeContextData,
} from '../../../translators/contextDataTranslator';
import { ContextParameters } from '@repo/payload/contextParameters/extractParameters';
import { ContextMatch } from '@repo/payload/contextParameters/findBestMatch';

interface ContextTabProps {
	context: {
		match?: ContextMatch | Record<string, unknown>;
		parameters?: ContextParameters | Record<string, unknown>;
		hash?: string;
		weight?: number;
		[key: string]: unknown;
	};
	searchTerm: string;
}

export const ContextTab = ({ context, searchTerm }: ContextTabProps) => {
	// Use translator to filter and process data
	const { filteredContext, hasContextData } = translateContextData(
		context,
		searchTerm,
	);

	if (!hasContextData) {
		return <NoDataMessage message="No matching context data found." />;
	}

	// Use translator to organize data for display
	const { hasParameters, hasMatch, hasHashOrWeight, otherContextData } =
		organizeContextData(filteredContext);

	return (
		<div className="space-y-4">
			{/* Parameters Section */}
			{hasParameters && (
				<div className="mb-6">
					<h4 className="mb-2 border-b border-blue-700 pb-1 text-sm font-semibold text-blue-300">
						Context Parameters
					</h4>
					<div className="grid grid-cols-1 gap-2">
						{Object.entries(
							filteredContext.parameters as Record<string, unknown>,
						).map(([key, value]) => (
							<div key={key} className="rounded bg-blue-800 p-3">
								<div className="flex items-start justify-between">
									<span className="text-sm font-medium text-blue-300">
										{key}:
									</span>
									<span className="text-right text-sm text-gray-300">
										{typeof value === 'object'
											? JSON.stringify(value)
											: String(value)}
									</span>
								</div>
							</div>
						))}
					</div>
				</div>
			)}

			{/* Match Section */}
			{hasMatch && (
				<div className="mb-6">
					<h4 className="mb-2 border-b border-blue-700 pb-1 text-sm font-semibold text-green-300">
						Match Results
					</h4>
					<div className="rounded bg-blue-800 p-3">
						<pre className="max-h-[300px] overflow-auto text-xs">
							{JSON.stringify(filteredContext.match, null, 2)}
						</pre>
					</div>
				</div>
			)}

			{/* Hash & Weight Section */}
			{hasHashOrWeight && (
				<div className="grid grid-cols-2 gap-4">
					{filteredContext.hash && (
						<div className="rounded bg-blue-800 p-3">
							<div className="text-xs text-gray-300">Hash</div>
							<div className="break-all font-mono text-sm text-white">
								{filteredContext.hash}
							</div>
						</div>
					)}
					{filteredContext.weight !== undefined && (
						<div className="rounded bg-blue-800 p-3">
							<div className="text-xs text-gray-300">Weight</div>
							<div className="text-sm text-white">{filteredContext.weight}</div>
						</div>
					)}
				</div>
			)}

			{/* Other Context Data */}
			{otherContextData.map(([key, value]) => (
				<div key={key} className="mb-4">
					<h4 className="mb-2 border-b border-blue-700 pb-1 text-sm font-semibold text-blue-300">
						{key.charAt(0).toUpperCase() + key.slice(1)}
					</h4>
					<div className="rounded bg-blue-800 p-3">
						<pre className="max-h-[300px] overflow-auto text-xs">
							{JSON.stringify(value, null, 2)}
						</pre>
					</div>
				</div>
			))}
		</div>
	);
};
