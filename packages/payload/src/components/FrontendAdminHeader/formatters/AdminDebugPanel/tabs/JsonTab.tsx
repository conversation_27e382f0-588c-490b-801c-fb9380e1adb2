'use client';

import { NoDataMessage } from '../NoDataMessage';

/**
 * Props for JsonTab component
 */
export interface JsonTabProps {
	/**
	 * JSON-serializable data to display
	 * This should be an object that can be safely stringified
	 */
	data: Record<string, unknown>;
}

/**
 * JsonTab - Client Component (Formatter)
 *
 * Displays any JSON-serializable data in a formatted, readable way.
 * Used by various debug panels to show structured data.
 *
 * @param {JsonTabProps} props - Component properties
 */
export const JsonTab = ({ data }: JsonTabProps) => {
	const hasData = Object.keys(data).length > 0;

	return (
		<div className="min-h-[300px]">
			{hasData ? (
				<pre className="whitespace-pre-wrap text-xs">
					{JSON.stringify(data, null, 2)}
				</pre>
			) : (
				<NoDataMessage message="No data available for this section." />
			)}
		</div>
	);
};
