'use client';

import { NoDataMessage } from '../NoDataMessage';
import { JsonTab } from './JsonTab';

interface PageDataTabProps {
	page: Record<string, unknown>;
	userRoles: string[];
}

export const PageDataTab = ({
	page,
	userRoles = [],
}: PageDataTabProps): React.ReactNode => {
	if (!page || Object.keys(page).length === 0) {
		return <NoDataMessage message="No page data available." />;
	}

	// Ensure userRoles is always an array before calling .some()
	const userRolesArray = Array.isArray(userRoles) ? userRoles : [];
	const canEditInAdmin = userRolesArray.some((role) =>
		['admin', 'web-developer', 'editor'].includes(role),
	);

	// Ensure pageId and collection are properly typed
	const pageId = typeof page.id === 'string' ? page.id : undefined;
	const collection =
		typeof page.collection === 'string' ? page.collection : 'pages';

	return (
		<div className="space-y-4">
			{canEditInAdmin && pageId !== undefined && (
				<div className="mb-4 flex justify-end">
					<a
						href={`/admin/collections/${collection}/${pageId}`}
						target="_blank"
						rel="noopener noreferrer"
						className="flex items-center rounded bg-blue-600 px-3 py-2 text-xs text-white transition-colors hover:bg-blue-700"
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							className="mr-1 h-4 w-4"
							viewBox="0 0 20 20"
							fill="currentColor"
						>
							<path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
						</svg>
						Edit in Admin
					</a>
				</div>
			)}

			{/* Summary Cards */}
			<div className="grid grid-cols-1 gap-3 md:grid-cols-2">
				{typeof page.title === 'string' && (
					<div className="rounded bg-blue-800 p-3">
						<div className="mb-1 text-xs text-gray-300">Title</div>
						<div className="text-sm text-white">{page.title}</div>
					</div>
				)}

				{typeof page.collection === 'string' && (
					<div className="rounded bg-blue-800 p-3">
						<div className="mb-1 text-xs text-gray-300">Collection</div>
						<div className="text-sm capitalize text-white">
							{page.collection}
						</div>
					</div>
				)}

				{/* Asset Name */}
				{typeof page.assetName === 'string' && (
					<div className="rounded bg-blue-800 p-3">
						<div className="mb-1 text-xs text-gray-300">Asset Name</div>
						<div className="break-all text-sm text-white">{page.assetName}</div>
					</div>
				)}

				{/* Tenant */}
				{typeof page.tenant === 'string' && (
					<div className="rounded bg-blue-800 p-3">
						<div className="mb-1 text-xs text-gray-300">Tenant</div>
						<div className="text-sm text-white">{page.tenant}</div>
					</div>
				)}

				{/* Status */}
				{typeof page.status === 'string' && (
					<div className="rounded bg-blue-800 p-3">
						<div className="mb-1 text-xs text-gray-300">Status</div>
						<div className="text-sm">
							<span
								className={`rounded px-2 py-1 text-xs ${
									page.status === 'published'
										? 'bg-green-900 text-green-300'
										: 'bg-yellow-900 text-yellow-300'
								}`}
							>
								{page.status}
							</span>
						</div>
					</div>
				)}

				{/* Last Updated */}
				{typeof page.updatedAt === 'string' && (
					<div className="rounded bg-blue-800 p-3">
						<div className="mb-1 text-xs text-gray-300">Last Updated</div>
						<div className="text-sm text-white">
							{new Date(page.updatedAt).toLocaleString()}
						</div>
					</div>
				)}
			</div>

			{/* All Page Data as JSON */}
			<div className="mt-6">
				<h4 className="mb-2 text-sm font-medium text-blue-300">
					Complete Page Data
				</h4>
				<div className="rounded bg-blue-800 p-3">
					<JsonTab data={page as Record<string, unknown>} />
				</div>
			</div>
		</div>
	);
};
