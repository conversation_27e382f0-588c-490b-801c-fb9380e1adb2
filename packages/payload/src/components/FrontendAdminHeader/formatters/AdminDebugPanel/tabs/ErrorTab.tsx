'use client';

import { NoDataMessage } from '../NoDataMessage';

interface ErrorDetail {
	message?: string;
	timestamp?: string;
	stack?: string;
	details?: Record<string, unknown>;
	[key: string]: unknown;
}

interface ErrorTabProps {
	errors?: Record<string, ErrorDetail>;
	searchTerm?: string;
}

/**
 * ErrorTab - Client Component (Formatter)
 *
 * Displays application errors collected during debug data gathering.
 * This component follows the Collector-Translator-Formatter pattern as a formatter.
 *
 * @param {ErrorTabProps} props - Component properties
 */
export const ErrorTab = ({ errors, searchTerm }: ErrorTabProps) => {
	if (!errors || Object.keys(errors).length === 0) {
		return <NoDataMessage message="No errors have been recorded." />;
	}

	// Filter errors by search term if provided
	const filteredErrors = searchTerm
		? Object.entries(errors).filter(
				([key, value]) =>
					key.toLowerCase().includes(searchTerm.toLowerCase()) ||
					(value.message &&
						value.message.toLowerCase().includes(searchTerm.toLowerCase())),
			)
		: Object.entries(errors);

	if (filteredErrors.length === 0) {
		return <NoDataMessage message="No errors match your search." />;
	}

	return (
		<div className="space-y-4">
			<h4 className="mb-2 text-sm font-medium">Application Errors</h4>

			{filteredErrors.map(([errorType, errorData]: [string, ErrorDetail]) => (
				<div key={errorType} className="rounded bg-red-900/30 p-4">
					<div className="flex items-start justify-between">
						<h5 className="text-sm font-medium text-red-300">
							{formatErrorType(errorType)}
						</h5>
						{errorData.timestamp && (
							<span className="text-xs text-red-200/70">
								{new Date(errorData.timestamp).toLocaleString()}
							</span>
						)}
					</div>

					<div className="mt-2 rounded bg-blue-900 p-3">
						<p className="break-words font-mono text-sm text-white">
							{errorData.message || JSON.stringify(errorData)}
						</p>
					</div>

					{errorData.stack && (
						<div className="mt-2">
							<details>
								<summary className="cursor-pointer text-xs text-red-200 hover:text-white">
									View Stack Trace
								</summary>
								<pre className="mt-2 max-h-[200px] overflow-auto rounded bg-blue-900 p-2 text-xs text-gray-300">
									{errorData.stack}
								</pre>
							</details>
						</div>
					)}

					{/* Show additional error details if available */}
					{errorData.details && (
						<div className="mt-2">
							<details>
								<summary className="cursor-pointer text-xs text-red-200 hover:text-white">
									Additional Details
								</summary>
								<pre className="mt-2 max-h-[200px] overflow-auto rounded bg-blue-900 p-2 text-xs text-gray-300">
									{JSON.stringify(errorData.details, null, 2)}
								</pre>
							</details>
						</div>
					)}
				</div>
			))}

			<div className="mt-4 text-xs text-gray-300">
				<p>
					Errors shown here are collected during debug data gathering and
					processing. They may not represent all application errors.
				</p>
			</div>
		</div>
	);
};

/**
 * Format the error type string for display
 */
function formatErrorType(errorType: string): string {
	return (
		errorType
			.replace(/([A-Z])/g, ' $1') // Add space before capital letters
			.replace(/^./, (str) => str.toUpperCase()) // Capitalize first letter
			.replace(/Error$/, '') // Remove trailing "Error" if present
			.replace(/^Error /, '') + // Remove leading "Error " if present
		' Error'
	); // Add Error suffix
}
