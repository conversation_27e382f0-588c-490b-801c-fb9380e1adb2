import 'server-only';
import { headers as nextHeaders } from 'next/headers';
import { draftMode } from 'next/headers';
import { isAuthorizedForEnvironmentVars } from '../utils/environmentAccess';

/**
 * collectEnvironmentData - Collects environment variables (Collector function)
 */
export async function collectEnvironmentData(): Promise<
	Record<string, unknown>
> {
	// Basic environment info that's always available
	const envVars: Record<string, unknown> = {
		// Standard environment info
		vercelTargetEnv: process.env.VERCEL_TARGET_ENV || 'unknown',
		vercelEnv: process.env.VERCEL_ENV || 'local',
		runtime: process.env.NEXT_RUNTIME || 'unknown',

		// System information
		platform: process.platform || 'unknown',
		arch: process.arch || 'unknown',
	};

	// Add draft mode information
	try {
		const draft = await draftMode();
		envVars.isDraftMode = draft.isEnabled.toString();
	} catch (error) {
		console.error('Error getting draft mode:', error);
		envVars.isDraftMode = 'unknown (error)';
	}

	// Check if we should include detailed environment variables
	const isAuthorized = await isAuthorizedForEnvironmentVars();

	// Only include detailed environment data if authorized
	if (isAuthorized) {
		// Add memory usage info (more detailed)
		envVars.memoryUsage = process.memoryUsage ? process.memoryUsage() : {};
		envVars.pid = process.pid ? process.pid.toString() : 'unknown';

		// Add all environment variables (they'll be sanitized by the translator)
		envVars.env = {};

		if (process.env) {
			// Create a plain object from process.env
			const envObject = process.env as Record<string, string>;
			for (const key in envObject) {
				if (Object.prototype.hasOwnProperty.call(envObject, key)) {
					const value = envObject[key];
					if (value !== undefined) {
						(envVars.env as Record<string, string>)[key] = value;
					} else {
						(envVars.env as Record<string, string>)[key] = '';
					}
				}
			}
		}
	} else {
		// If not authorized, just indicate that detailed env vars are restricted
		envVars.env = {
			note: "Detailed environment variables are restricted. Use the 'x-show-environment-vars: true' and 'x-payload-secret' headers to access them.",
		};
	}

	return envVars;
}

/**
 * collectHeadersData - Collects HTTP headers (Collector function)
 */
export async function collectHeadersData(): Promise<Record<string, string>> {
	try {
		const headersList = await nextHeaders();
		const headers: Record<string, string> = {};

		// Get all headers
		headersList.forEach((value, key) => {
			headers[key] = value;
		});

		return headers;
	} catch (error) {
		console.error(
			'Error collecting headers:',
			error instanceof Error ? error.message : String(error),
		);
		return { error: 'Failed to collect headers' };
	}
}

/**
 * collectContextParametersData - Collects context parameters (Collector function)
 */
export async function collectContextParametersData(
	params: Record<string, string>,
): Promise<Record<string, unknown> | null> {
	try {
		// Use dynamic import for server-only module
		const { getContextualizedPage } = await import(
			'@repo/payload/contextParameters/getContextualizedPage'
		);
		return await getContextualizedPage(params);
	} catch (error) {
		console.error(
			'Error collecting context parameters:',
			error instanceof Error ? error.message : String(error),
		);
		return null;
	}
}
