'use server';
import 'server-only';

/**
 * collectContextParameters - Server Collector
 *
 * This function collects context parameters using the getContextualizedPage utility.
 * It follows the Collector-Translator-Formatter pattern as a collector:
 * - It gathers raw data without modifying it
 * - It makes this data available for translators to process
 *
 * @param params The parameters to process
 * @returns The context parameters data
 */
export async function collectContextParameters(params: Record<string, string>) {
	try {
		// Import the server-only module here, safely behind the "server-only" directive
		const { getContextualizedPage } = await import(
			'@repo/payload/contextParameters/getContextualizedPage'
		);
		return await getContextualizedPage(params);
	} catch (error) {
		console.error('Error in collectContextParameters:', error);
		return null;
	}
}
