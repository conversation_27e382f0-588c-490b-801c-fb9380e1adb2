'use client';

import { useEffect, useCallback, useRef } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { useDebugSystem } from '../state/hooks';
import { translateDebugDataAction } from '../translators/debugData.actions';
import { createLogger } from '@repo/logger';
import { DataCollectorProps } from './types';
import { useLocationCollector } from './locationCollector';

const logger = createLogger('DataCollector');

/**
 * DataCollector component (Collector)
 *
 * This is a headless component that collects debug information from various
 * sources and stores it in the central debugDataAtom for use by formatters.
 *
 * In the Collector-Translator-Formatter pattern:
 * - It acts as a Collector that gathers raw information
 * - It calls the translator action to transform data into the DebugData structure
 * - It stores the result in the atom for formatters to consume
 *
 * Important: This is the SINGLE collection entry point that developers should use.
 * All collection logic (including location collection) is handled internally.
 */
export function DataCollector({ pageProps, userRoles }: DataCollectorProps) {
	const pathname = usePathname();
	const searchParams = useSearchParams();
	const { updateSection } = useDebugSystem();

	// Track initialization
	const initialized = useRef(false);

	// Use the location collector hook to handle all location data collection
	useLocationCollector();

	// Add user roles to debug data
	useEffect(() => {
		if (userRoles && userRoles.length > 0) {
			// Ensure userRoles is an array before setting it
			const rolesArray = Array.isArray(userRoles) ? userRoles : [];
			updateSection('userRoles', rolesArray);
			logger.info('DataCollector', 'User roles updated', rolesArray);
		}
	}, [userRoles, updateSection]);

	// Extract and translate page data on mount or route change
	const collectData = useCallback(async () => {
		// Always run in all environments - the admin header should always be available
		try {
			logger.info('DataCollector', 'Collecting data for route:', {
				path: pathname,
				paramsCount: searchParams ? Array.from(searchParams.keys()).length : 0,
			});

			// Construct the URL params for debugging
			const params: Record<string, string> = {};
			searchParams?.forEach((value, key) => {
				params[key] = value;
			});

			// Call server action to translate the raw data into our debug structure
			logger.lifecycle('DataCollector', 'Calling translateDebugDataAction');
			const debugData = await translateDebugDataAction({
				route: pathname ?? '',
				params,
				pageProps,
			});

			if (debugData) {
				logger.info('DataCollector', 'Debug data received', {
					route: debugData.route,
					hasPage: !!debugData.page,
					hasMatch: !!debugData.match,
					hasHeaders: !!debugData.headers,
					hasEnvironment: !!debugData.environment,
				});

				// Update various sections with the translated data
				if (debugData.route) {
					updateSection('route', debugData.route);
					logger.info('DataCollector', 'Route updated', debugData.route);
				}

				if (debugData.page) {
					updateSection('page', debugData.page);
					logger.info('DataCollector', 'Page data updated', {
						title: debugData.page.title,
						id: debugData.page.id,
					});

					// When we get proper page data, mark it as fresh
					updateSection('meta', {
						dataState: 'fresh',
						lastConfirmedPath: pathname,
						currentPath: pathname,
						isDirectLoad: false,
						timestamp: new Date().toISOString(),
					});
				}

				if (debugData.match) {
					updateSection('match', debugData.match);
					logger.info('DataCollector', 'Match data updated', {
						hasMatch: true,
						matchKeys: Object.keys(debugData.match || {}),
					});
				}

				if (debugData.headers) {
					updateSection('headers', debugData.headers);
					logger.info('DataCollector', 'Headers updated', {
						headerCount: Object.keys(debugData.headers || {}).length,
					});
				}

				if (debugData.environment) {
					updateSection('environment', debugData.environment);
					logger.info('DataCollector', 'Environment data updated', {
						envVarCount: Object.keys(debugData.environment || {}).length,
					});
				}

				// Always capture the timestamp
				updateSection('timestamp', new Date().toISOString());
			}
		} catch (error) {
			logger.error('DataCollector', 'Error collecting debug data:', error);

			// Store the error in debug data for diagnosis
			updateSection('errors', {
				dataCollection: {
					message: (error as Error).message,
					timestamp: new Date().toISOString(),
				},
			});
		}
	}, [pathname, searchParams, pageProps, updateSection]);

	// Run collectData on mount and when route changes
	useEffect(() => {
		logger.lifecycle(
			'DataCollector',
			'Setting up data collection for path',
			pathname,
		);

		// Initialize component state
		if (!initialized.current) {
			initialized.current = true;
		}

		// Add a small delay to ensure core application logic runs first
		const timerId = setTimeout(() => {
			collectData();
		}, 200);

		return () => {
			clearTimeout(timerId);
			logger.lifecycle('DataCollector', 'Cleanup for path', pathname);
		};
	}, [collectData, pathname]); // Added pathname to dependencies

	// This is a headless component
	return null;
}

// Function removed to fix ESLint warning: 'isDevelopmentOrPreview' is assigned a value but never used
