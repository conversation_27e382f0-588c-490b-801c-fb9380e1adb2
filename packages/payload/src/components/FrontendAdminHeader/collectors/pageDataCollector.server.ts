import 'server-only';
import { cache } from 'react';
import { getPayload } from 'payload';
import configPromise from '@repo/payload/payload-config';
import { draftMode } from 'next/headers';
import { PageIdentifier } from './types';

/**
 * collectPageData - Fetches raw page data from PayloadCMS (Collector function)
 *
 * This function is cached to prevent duplicate fetches for the same page.
 * It handles different ways of identifying a page (id, assetName, route).
 *
 * In the Collector-Translator-Formatter pattern, this is a collector
 * that retrieves raw data without transforming it.
 */
export const collectPageData = cache(async (identifier: PageIdentifier) => {
	const { isEnabled: draft } = await draftMode();
	const payload = await getPayload({ config: configPromise });

	// Determine which collection to query
	const collection = identifier.collection || 'pages';

	// Determine query based on available identifiers
	const where: Record<string, { equals: string }> = {};

	if (identifier.id) {
		where.id = { equals: identifier.id };
	} else if (identifier.assetName) {
		where.assetName = { equals: identifier.assetName };
	} else if (identifier.route) {
		// Convert route to assetName format if needed
		const assetName = identifier.route === '/' ? '/' : identifier.route;
		where.assetName = { equals: assetName };
	}

	try {
		console.log(
			`Fetching page data for ${collection} with where:`,
			JSON.stringify(where),
		);
		const result = await payload.find({
			collection,
			depth: 2, // Include related data like tenant
			limit: 1,
			draft,
			where,
		});

		console.log(`Found ${result.docs.length} results for ${collection}`);
		return result.docs?.[0] || null;
	} catch (error) {
		console.error(`Error fetching page data for ${collection}:`, error);
		return null;
	}
});
