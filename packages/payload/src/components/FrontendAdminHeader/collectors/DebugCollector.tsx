'use client';

import { useEffect, useRef } from 'react';
import { useDebugSystem } from '../state/hooks';
import { createLogger } from '@repo/logger';

const logger = createLogger('DebugCollector');
import { DebugCollectorProps } from './types';
import { PageDebugData } from '../state/types';

/**
 * DebugCollector - Client Component (Collector)
 *
 * Unified component for collecting both component and page debug data.
 * It follows the Collector-Translator-Formatter pattern as a collector.
 *
 * @example Component debug data:
 * ```tsx
 * <DebugCollector
 *   componentName="WeatherCard"
 *   data={{
 *     props: { location: "New York" },
 *     state: { isLoading: false },
 *     performance: { renderCount: 3 }
 *   }}
 * />
 * ```
 *
 * @example Page debug data:
 * ```tsx
 * <DebugCollector
 *   componentName="HomePage"
 *   data={{ pageType: 'home' }}
 *   page={{
 *     title: 'Home Page',
 *     description: 'Welcome to the home page',
 *     id: 'page-123',
 *     assetName: '/home'
 *   }}
 * />
 * ```
 */
export function DebugCollector({
	componentName,
	data,
	page,
	onUpdateData,
	onSetPageData,
}: DebugCollectorProps) {
	const { updateComponentData, setPageData } = useDebugSystem();
	const initializedRef = useRef<boolean>(false);

	// Use provided update functions or fallback to useDebugSystem
	const updateComponentDataFn = onUpdateData || updateComponentData;
	const setPageDataFn = onSetPageData || setPageData;

	// Handle component debug data collection
	useEffect(() => {
		if (data) {
			logger.lifecycle(`Collecting component data for ${componentName}`, {
				dataKeys: Object.keys(data),
				hasPerformance: !!data.performance,
				hasContext: !!data.context,
			});

			// Add timestamp to track when this data was collected
			const enhancedData = {
				...data,
				_collectedAt: new Date().toISOString(),
			};

			updateComponentDataFn(componentName, enhancedData);

			// Clean up when the component unmounts
			return () => {
				logger.lifecycle(`Cleaning up data for ${componentName}`);
				updateComponentDataFn(componentName, null);
			};
		}
	}, [componentName, data, updateComponentDataFn]);

	// Handle page metadata collection (if provided)
	useEffect(() => {
		// Only run once for page data to prevent loops
		if (page && !initializedRef.current) {
			logger.lifecycle('Setting page metadata', {
				title: page.title,
				assetName: page.assetName,
				collection: page.collection,
			});

			setPageDataFn({
				...page,
				_updatedAt: new Date().toISOString(),
			} as PageDebugData);

			initializedRef.current = true;
		}
	}, [page, setPageDataFn]);

	// This is a headless component
	return null;
}
