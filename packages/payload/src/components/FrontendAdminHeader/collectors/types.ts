/**
 * Common types for collector components
 *
 * These types standardize the API of collector components following the
 * Collector-Translator-Formatter pattern.
 */

// Removed unused import: ContextParameters
import { ContextMatch } from '@repo/payload/contextParameters/findBestMatch';
import { PageDebugData } from '../state/types';

/**
 * Base props interface for all collector components
 */
export interface CollectorProps {
	/**
	 * Unique identifier for the component providing debug data
	 * This should be descriptive and unique across the application.
	 *
	 * @example "HomePage", "WeatherCard", "NavigationMenu"
	 */
	componentName: string;
}

/**
 * Performance metrics interface for component debugging
 */
export interface PerformanceMetrics {
	/**
	 * Number of times the component has rendered
	 */
	renderCount?: number;

	/**
	 * Time taken to render the component (in ms)
	 */
	renderTime?: number;

	/**
	 * Time taken to fetch data (in ms)
	 */
	dataFetchTime?: number;

	/**
	 * Additional performance metrics
	 */
	[key: string]: unknown;
}

/**
 * Context data interface for component debugging
 */
export interface ContextData {
	/**
	 * Context parameters from URL or server
	 */
	parameters?: Record<string, unknown>;

	/**
	 * Context match results
	 */
	match?: ContextMatch | null;

	/**
	 * Context hash
	 */
	hash?: string;

	/**
	 * Context weight
	 */
	weight?: number;

	/**
	 * Other context data
	 */
	[key: string]: unknown;
}

/**
 * Debug data interface for component-level debugging
 */
export interface ComponentDebugData {
	/**
	 * Component props (optional)
	 * Include relevant props that help with debugging
	 */
	props?: Record<string, unknown>;

	/**
	 * Component state (optional)
	 * Include relevant state variables that help with debugging
	 */
	state?: Record<string, unknown>;

	/**
	 * Performance metrics (optional)
	 */
	performance?: PerformanceMetrics;

	/**
	 * Component context (optional)
	 */
	context?: ContextData;

	/**
	 * When this data was collected
	 */
	_collectedAt?: string;

	/**
	 * Additional debug data (optional)
	 * Any other data that might be useful for debugging
	 */
	[key: string]: unknown;
}

/**
 * Props for DataCollector
 */
export interface DataCollectorProps {
	/**
	 * Page props to include in debug data
	 * These will be added to the debug data automatically.
	 */
	pageProps?: Record<string, unknown>;

	/**
	 * User roles for permission checks
	 * These determine what debug information is visible to which users.
	 */
	userRoles?: string[];
}

/**
 * Props for unified DebugCollector
 */
export interface DebugCollectorProps extends CollectorProps {
	/**
	 * Component-specific debug data to collect (optional)
	 */
	data?: ComponentDebugData;

	/**
	 * Optional page metadata for page components
	 * When provided, this data will appear in the Page tab of the debug panel
	 * Uses the PageDebugData interface from state/types.ts
	 */
	page?: PageDebugData;

	/**
	 * Optional function to update component data
	 * If not provided, the component will use useDebugSystem internally
	 */
	onUpdateData?: (
		componentName: string,
		data: ComponentDebugData | null,
	) => void;

	/**
	 * Optional function to set page data
	 * If not provided, the component will use useDebugSystem internally
	 */
	onSetPageData?: (pageData: PageDebugData) => void;
}

/**
 * PageIdentifier - Ways to identify a page
 */
export interface PageIdentifier {
	/**
	 * Page ID in the CMS
	 */
	id?: string;

	/**
	 * Page asset name (URL path)
	 */
	assetName?: string;

	/**
	 * Current route path
	 */
	route?: string;

	/**
	 * Page collection type
	 */
	collection?: 'pages' | 'articles';
}

/**
 * Props for LocationCollectorComponent
 */
export interface LocationCollectorProps {
	/**
	 * Optional source identifier
	 */
	source?: string;
}
