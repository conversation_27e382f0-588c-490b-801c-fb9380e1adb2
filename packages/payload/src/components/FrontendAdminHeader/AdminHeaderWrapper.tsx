'use client';

import React, { ReactNode, useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import { setCookie, getCookie } from 'cookies-next';
import { DebugProvider } from './state/DebugProvider';

// Dynamically import the admin header content to avoid including it in the main bundle
// This ensures regular users don't pay the performance cost of admin functionality
const FrontendAdminHeader = dynamic(
	() =>
		import('./FrontendAdminHeader').then((mod) => ({
			default: mod.FrontendAdminHeader,
		})),
	{
		ssr: false,
		loading: () => null,
	},
);

interface AdminHeaderWrapperProps {
	children: ReactNode;
}

/**
 * AdminHeaderWrapper
 *
 * This component wraps the application content and conditionally renders
 * the admin header based on debug mode and user authentication status.
 *
 * Performance optimization:
 * - Only performs authentication when debug mode is active
 * - Only loads admin header code when needed via dynamic import
 * - Avoids unnecessary API calls for regular users
 * - Prevents hydration errors by deferring debug mode check to client-side
 */
export function AdminHeaderWrapper({ children }: AdminHeaderWrapperProps) {
	// Start with null (not hydrated yet)
	// Will be set to true/false after hydration
	const [isDebugMode, setIsDebugMode] = useState<boolean | null>(null);

	// Set cookie and determine debug mode after hydration
	useEffect(() => {
		// Use URLSearchParams with window.location.search
		const urlParams = new URLSearchParams(window.location.search);
		const hasDebugParam = urlParams.has('debug');
		const hasDebugCookie = getCookie('twc-debug-mode') === 'true';

		if (hasDebugParam) {
			// Set cookie with 30-day expiration
			setCookie('twc-debug-mode', 'true', {
				maxAge: 30 * 24 * 60 * 60, // 30 days in seconds
			});
		}

		// Set the actual debug mode state after hydration
		setIsDebugMode(hasDebugParam || hasDebugCookie);
	}, []);

	// Conditionally render both DebugProvider and FrontendAdminHeader when in debug mode
	if (isDebugMode === true) {
		return (
			<DebugProvider>
				<FrontendAdminHeader />
				{children}
			</DebugProvider>
		);
	}

	// When not in debug mode, just render the children without any debug components
	return <>{children}</>;
}
