import { atom } from 'jotai';
import { DebugData } from './types';

/**
 * debugDataAtom
 *
 * Jotai atom for storing and accessing debug data throughout the application.
 * This atom is hydrated by collector components and consumed by formatter components.
 *
 * In the Collector-Translator-Formatter pattern:
 * - It serves as the state container that bridges server-side collection/translation
 *   with client-side formatting
 * - It enables formatters to access data without direct dependency on collectors
 *
 * Note: All location data is now stored in debugDataAtom.location rather than
 * in a separate dedicated atom, following the single source of truth principle.
 */
export const debugDataAtom = atom<DebugData | null>({
	timestamp: new Date().toISOString(),
});
