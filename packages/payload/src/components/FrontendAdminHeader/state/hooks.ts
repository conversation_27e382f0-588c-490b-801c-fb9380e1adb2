'use client';

import { use<PERSON>tom } from 'jotai';
import { useCallback } from 'react';
import { debugData<PERSON>tom } from './atoms';
import type { DebugData } from './types';

/**
 * useDebugSystem - Custom hook for centralized debug data management
 *
 * This is the central hook for all debug data operations in the application.
 * It provides a unified interface for reading and writing debug information.
 * Use this hook throughout the application when working with debug data.
 *
 * @returns Object with debugData and methods to update it
 */
export function useDebugSystem() {
	const [debugData, setDebugData] = useAtom(debugDataAtom);

	/**
	 * Updates a specific section of debug data
	 *
	 * @param section - The section key to update (e.g., 'page', 'location')
	 * @param data - The data to merge into this section
	 */
	const updateSection = useCallback(
		(section: keyof DebugData, data: unknown) => {
			setDebugData((prevData) => {
				const prevSection = prevData?.[section] || {};

				// Handle different types of data properly
				const newSectionData =
					typeof data === 'object' &&
					data !== null &&
					typeof prevSection === 'object'
						? { ...prevSection, ...data }
						: data;

				return {
					...prevData,
					[section]: newSectionData,
				};
			});
		},
		[setDebugData],
	);

	/**
	 * Updates component-specific debug data
	 *
	 * @param componentName - The name of the component providing debug data
	 * @param data - The data to store for this component
	 */
	const updateComponentData = useCallback(
		(componentName: string, data: Record<string, unknown> | null) => {
			setDebugData((prevData) => {
				// Create a new object if debugData is null
				const currentData = prevData || { timestamp: new Date().toISOString() };

				// Create components section if it doesn't exist
				const components = currentData.components || {};

				// If data is null, remove the component data
				if (data === null) {
					const newComponents = { ...components };
					delete newComponents[componentName];
					return {
						...currentData,
						components: newComponents,
					};
				}

				// Otherwise update/add component data
				return {
					...currentData,
					components: {
						...components,
						[componentName]: data,
					},
				};
			});
		},
		[setDebugData],
	);

	/**
	 * Sets page data directly
	 *
	 * @param pageData - The page data to set
	 */
	const setPageData = useCallback(
		(pageData: Record<string, unknown>) => {
			updateSection('page', pageData);
		},
		[updateSection],
	);

	/**
	 * Update route information
	 *
	 * @param route - The current route path
	 */
	const updateRouteInfo = useCallback(
		(route: string) => {
			updateSection('route', route);
		},
		[updateSection],
	);

	/**
	 * Update page debug info with multiple fields
	 *
	 * @param data - Object containing sections to update
	 */
	const updatePageDebug = useCallback(
		(data: Partial<DebugData>) => {
			Object.entries(data).forEach(([key, value]) => {
				if (value) {
					updateSection(key as keyof DebugData, value);
				}
			});
		},
		[updateSection],
	);

	/**
	 * Clear all debug data
	 */
	const clearAllDebugData = useCallback(() => {
		setDebugData({ timestamp: new Date().toISOString() });
	}, [setDebugData]);

	return {
		debugData,
		updateSection,
		updateComponentData,
		setPageData,
		clearAllDebugData,
		updateRouteInfo,
		updatePageDebug,
	};
}
