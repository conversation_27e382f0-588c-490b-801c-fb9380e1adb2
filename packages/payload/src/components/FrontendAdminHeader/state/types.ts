import { ContextMatch } from '@repo/payload/contextParameters/findBestMatch';
import { ContextParameters } from '@repo/payload/contextParameters/extractParameters';
import { LocationData } from '@repo/location/types';
import { ComponentDebugData } from '../collectors/types';
import { SanitizedObject } from '../translators/sanitizers';
import type { Page } from '@repo/payload/payload-types';
// Removed unused imports: Article, Tag, User, Seo

/**
 * Page metadata interface for debug data
 * Based on PayloadCMS Page and Article types
 */
export interface PageDebugData {
	/**
	 * Page ID in the CMS
	 */
	id?: string;

	/**
	 * Page title
	 */
	title?: string;

	/**
	 * Page description
	 */
	description?: string;

	/**
	 * Page asset name (URL path)
	 */
	assetName?: string;

	/**
	 * Page publication status ('draft' | 'published')
	 */
	status?: string;

	/**
	 * Page creation timestamp
	 */
	createdAt?: string;

	/**
	 * Page last update timestamp
	 */
	updatedAt?: string;

	/**
	 * Page publication timestamp
	 */
	publishedAt?: string | null;

	/**
	 * Page locale
	 */
	locale?: string;

	/**
	 * Page tenant/site identifier
	 */
	tenant?: string | { name?: string } | null;

	/**
	 * Page author
	 */
	author?: string | { name?: string } | null;

	/**
	 * Page categories
	 */
	categories?: (string | { name?: string })[] | null;

	/**
	 * Page tags
	 */
	tags?: (string | { name?: string })[] | null;

	/**
	 * Open Graph image URL
	 */
	ogImage?: string;

	/**
	 * Open Graph title
	 */
	ogTitle?: string;

	/**
	 * Open Graph description
	 */
	ogDescription?: string;

	/**
	 * Page collection type ('pages' | 'articles')
	 */
	collection?: string;

	/**
	 * Page version number
	 */
	version?: number;

	/**
	 * SEO data
	 */
	seo?: {
		title?: string;
		description?: string;
		ogTitle?: string;
		ogDescription?: string;
		ogImage?: {
			url?: string;
		};
	};

	/**
	 * Context parameters for this page
	 */
	contextParameters?: ContextParameters;

	/**
	 * Ad configuration
	 */
	adConfig?: Page['adConfig'];

	/**
	 * Additional page metadata
	 * Use specific properties above when possible instead of this catch-all
	 */
	[key: string]: unknown;
}

/**
 * Debug metadata interface
 */
export interface DebugMetadata {
	/**
	 * Indicates whether the data is fresh (just collected) or stale (from previous page)
	 */
	dataState: 'fresh' | 'stale';

	/**
	 * The path for which this data was last confirmed accurate
	 */
	lastConfirmedPath: string | null;

	/**
	 * Current path
	 */
	currentPath: string;

	/**
	 * Whether this was loaded directly or via client navigation
	 */
	isDirectLoad: boolean;

	/**
	 * When this metadata was updated
	 */
	timestamp: string;
}

/**
 * Error information interface
 */
export interface ErrorData {
	/**
	 * Error message
	 */
	message: string;

	/**
	 * Error code if available
	 */
	code?: string | number;

	/**
	 * Error timestamp
	 */
	timestamp: string;

	/**
	 * Additional error details
	 */
	details?: unknown;
}

/**
 * DebugData interface
 *
 * Defines the structure of debug data collected and shared across the application.
 * This serves as the contract between collectors, translators, and formatters.
 *
 * In the Collector-Translator-Formatter pattern:
 * - Collectors gather raw data
 * - Translators transform it into this structure
 * - Formatters consume it to render UI
 */
export interface DebugData {
	/**
	 * Current route path
	 */
	route?: string;

	/**
	 * Page metadata
	 */
	page?: PageDebugData;

	/**
	 * Context matching information
	 */
	match?: ContextMatch;

	/**
	 * Location information
	 * Uses the shared LocationData interface from location/types.ts
	 * with additional metadata fields for debugging
	 */
	location?: LocationData & {
		/**
		 * Source of the location data
		 */
		_observedFrom?: string;

		/**
		 * Component that collected the location data
		 */
		_collectedBy?: string;

		/**
		 * When the location data was updated
		 */
		_updatedAt?: string;

		/**
		 * Additional location metadata
		 */
		[key: string]: unknown;
	};

	/**
	 * Request headers
	 */
	headers?: SanitizedObject;

	/**
	 * Environment variables (filtered for security)
	 */
	environment?: SanitizedObject;

	/**
	 * Component-specific debug data
	 * Uses the ComponentDebugData interface from collectors/types.ts
	 */
	components?: Record<string, ComponentDebugData>;

	/**
	 * Collection timestamp
	 */
	timestamp?: string;

	/**
	 * Metadata about the debug data itself
	 */
	meta?: DebugMetadata;

	/**
	 * User roles for permission checks
	 */
	userRoles?: string[];

	/**
	 * Error information
	 */
	errors?: Record<string, ErrorData>;
}

/**
 * Minimal type for favorited locations
 * Only storing essential data (id and geocode) to minimize storage size
 */
export interface FavoritedLocationMinimal {
	/**
	 * Location ID
	 */
	id: string;

	/**
	 * Location geocode (latitude,longitude)
	 */
	geocode: string;
}
