'use client';

import React, { ReactNode } from 'react';
import { Provider as <PERSON><PERSON>P<PERSON>ider } from 'jotai';
import { AtomDebugHydrationBoundaries } from './AtomDebugHydrationBoundary';
import { DataCollector } from '../collectors/DataCollector';

interface DebugProviderProps {
	children: ReactNode;
	pageProps?: Record<string, unknown>;
}

/**
 * StateProvider - Client Component
 *
 * This component provides state management for the FrontendAdminHeader system.
 * It sets up Jotai context, hydrates atoms with initial data, and includes
 * the DataCollector to automatically gather debug information.
 *
 * In the Collector-Translator-Formatter pattern, this serves as an integration
 * layer between data collection and formatting by:
 * - Making debug data available to formatters via Jotai atoms
 * - Including the DataCollector to automatically collect all necessary data
 */
export function DebugProvider({ children, pageProps }: DebugProviderProps) {
	return (
		<JotaiProvider>
			<AtomDebugHydrationBoundaries debugData={null} />
			{/* Integrated DataCollector that runs automatically */}
			<DataCollector pageProps={pageProps} />
			{children}
		</JotaiProvider>
	);
}
