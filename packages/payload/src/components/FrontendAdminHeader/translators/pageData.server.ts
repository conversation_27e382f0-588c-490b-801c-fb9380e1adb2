import 'server-only';
import { collectPageData } from '../collectors/pageDataCollector.server';
import type { Page, Article } from '@repo/payload/payload-types';

/**
 * PageMetadata - Standardized page metadata structure
 *
 * This is the structure that translators produce and formatters consume.
 */
export interface PageMetadata {
	id: string;
	title: string;
	description?: string;
	assetName?: string;
	status?: string;
	createdAt?: string;
	updatedAt?: string;
	publishedAt?: string | null;
	locale?: string;
	tenant?: string | null;
	author?: string;
	categories?: string[];
	tags?: string[];
	ogImage?: string;
	ogTitle?: string;
	ogDescription?: string;
	collection: string;
	version?: number;
	[key: string]: unknown;
}

/**
 * PageIdentifier - Ways to identify a page
 * Re-exported for backward compatibility
 */
export interface PageIdentifier {
	id?: string;
	assetName?: string;
	route?: string;
	collection?: 'pages' | 'articles';
}

// Type for the raw page data that can be either a Page, Article, or a generic Record
type RawPageData = Page | Article | Record<string, unknown> | null;

/**
 * translatePageData - Transforms raw page data into a consistent format (Translator function)
 *
 * This function normalizes data from different collections (pages, articles)
 * into a consistent structure for use throughout the application.
 *
 * In the Collector-Translator-Formatter pattern, this is the core translator
 * that bridges raw data from the CMS with the structured format needed by formatters.
 */
export const translatePageData = async (
	rawPageData: RawPageData,
	collection = 'pages',
): Promise<PageMetadata | null> => {
	if (!rawPageData) return null;

	// Cast to Record<string, unknown> for consistent access
	const data = rawPageData as Record<string, unknown>;

	// Common fields across all content types
	const commonData = {
		id: data.id as string,
		title:
			((data.seo as Record<string, unknown>)?.title as string) ||
			(data.title as string),
		description: (data.seo as Record<string, unknown>)?.description as string,
		assetName: data.assetName as string,
		status: data._status as string,
		createdAt: data.createdAt as string,
		updatedAt: data.updatedAt as string,
		publishedAt:
			data._status === 'published' ? (data.updatedAt as string) : null,
		locale: data.locale as string,
		tenant:
			typeof data.tenant === 'string'
				? data.tenant
				: ((data.tenant as Record<string, unknown>)?.name as string) || null,
		collection,
		version: data._version as number,

		// SEO fields
		ogTitle: (data.seo as Record<string, unknown>)?.ogTitle as string,
		ogDescription: (data.seo as Record<string, unknown>)
			?.ogDescription as string,
		ogImage:
			typeof (data.seo as Record<string, unknown>)?.ogImage === 'object'
				? ((
						(data.seo as Record<string, unknown>)?.ogImage as Record<
							string,
							unknown
						>
					)?.url as string)
				: ((data.seo as Record<string, unknown>)?.ogImage as string),
	};

	// Collection-specific fields
	if (collection === 'articles') {
		return {
			...commonData,
			author:
				typeof data.author === 'string'
					? data.author
					: ((data.author as Record<string, unknown>)?.name as string),
			categories: data.category
				? [
						typeof (data.category as Record<string, unknown>).value === 'string'
							? ((data.category as Record<string, unknown>).value as string)
							: ((
									(data.category as Record<string, unknown>).value as Record<
										string,
										unknown
									>
								)?.name as string),
					]
				: [],
			topic: (
				(data.topic as Record<string, unknown>)?.value as Record<
					string,
					unknown
				>
			)?.name as string,
			publishDate: data.publishDate as string,
			content: data.content as Record<string, unknown>,
		};
	}

	// Default to page structure
	return {
		...commonData,
		layout: (data.content as Record<string, unknown>)?.layout as string,
		blocks: (data.content as Record<string, unknown>)?.blocks as unknown[],
	};
};

/**
 * getPageMetadataByRoute - Convenience function to get page metadata by route
 *
 * This combines collection and translation into a single helper function.
 */
export const getPageMetadataByRoute = async (
	route: string,
	collection: 'pages' | 'articles' = 'pages',
): Promise<PageMetadata | null> => {
	console.log(
		`Fetching metadata for route: ${route}, collection: ${collection}`,
	);

	// If route is empty, try to get the home page
	if (!route) {
		console.log('Empty route, trying to get home page');
		const homePageData = await collectPageData({
			assetName: '/home',
			collection: 'pages',
		});

		if (homePageData) {
			console.log('Found home page:', homePageData.title);
			return translatePageData(homePageData, 'pages');
		}

		console.log('Home page not found');
		return null;
	}

	// Check if this is a content route
	const contentRouteMatch = route.match(
		/^\/(en-US|es-US)\/\d{4}\/\d{2}\/\d{2}\//,
	);
	if (contentRouteMatch) {
		console.log('Content route detected, using articles collection');

		// For content routes, we need to check if the route is handled by our rewrites
		// First, try to get the article by assetName
		const rawArticleData = await collectPageData({
			assetName: route,
			collection: 'articles',
		});

		if (rawArticleData) {
			console.log('Found article by assetName:', rawArticleData.title);
			return translatePageData(rawArticleData, 'articles');
		}

		// If not found by assetName, the route might be rewritten
		// We'll return null and let the page component handle the 404
		console.log('Article not found for route:', route);
		return null;
	}

	// For non-content routes, try to get the page by route
	const rawPageData = await collectPageData({
		route,
		collection,
	});
	const result = await translatePageData(rawPageData, collection);

	console.log(
		'Page metadata result:',
		result
			? `Found: ${result.title} (${result.assetName})`
			: 'Not found for route: ' + route,
	);

	return result;
};

/**
 * getPageMetadataById - Convenience function to get page metadata by ID
 *
 * This combines collection and translation into a single helper function.
 */
export const getPageMetadataById = async (
	id: string,
): Promise<PageMetadata | null> => {
	const rawPageData = await collectPageData({ id });
	return translatePageData(rawPageData);
};

/**
 * getPageMetadataByAssetName - Convenience function to get page metadata by asset name
 *
 * This combines collection and translation into a single helper function.
 */
export const getPageMetadataByAssetName = async (
	assetName: string,
): Promise<PageMetadata | null> => {
	const rawPageData = await collectPageData({ assetName });
	return translatePageData(rawPageData);
};
