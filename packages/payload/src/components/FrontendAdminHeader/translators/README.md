# Translators

This directory contains translator functions and server actions following the Collector-Translator-Formatter pattern.

## Purpose

Translators are responsible for transforming raw data into structured formats:

- They receive raw data from collectors
- They process and structure this data into consistent formats
- They prepare data for formatters to consume and display

Translators do NOT collect data directly from sources, and they do NOT format data for display - they only transform data between these steps.

## Available Translators

- `pageData.ts`: Transforms raw page data from PayloadCMS into structured metadata
- `debugData.ts`: Processes raw debug data into a structured format
- `sanitizers.ts`: Provides security-focused transformations to mask sensitive data

## Usage Pattern

```typescript
// Example usage of a translator
import { translatePageData } from "@/components/FrontendAdminHeader/translators/pageData";

// In a server component or server action:
const rawPageData = await getPageData({ id: pageId });
const structuredMetadata = await translatePageData(rawPageData);

// Now the structured metadata can be passed to formatters for display
```

This approach ensures clear separation of concerns and makes our debugging system more maintainable.

### 3. :wrench:&nbsp; Start development

#### Install Dependencies and Run Dev Scripts

```sh
# Installs dependencies
# Tip: Run this when changes impact dependencies or local workspace packages
pnpm install

# Link `web` app to Vercel `wx-next-web` project locally
# Required for next command.  This folder should not be committed to git.
pnpm --filter web vercel:link

# See answers to questions in the outputs below
> web@0.1.0 vercel:link /Users/<USER>/projects/wx-next/apps/web
> pnpx vercel link

Vercel CLI 41.3.2
> No existing credentials found. Please log in:
? Log in to Vercel Continue with SAML Single Sign-On
? Enter your Team slug: the-weather-company
> Success! SAML Single Sign-On authentication <NAME_EMAIL>
? Set up "~/projects/wx-next/apps/web"? yes
? Which scope should contain your project? TWC
? Link to existing project? yes
? What's the name of your existing project? wx-next-web
✅  Linked to the-weather-company/wx-next-web (created .vercel)

# Pull env from Vercel and save to .env.local
pnpm --filter web vercel:env:pull

# Runs all dev scripts and compiles while watching for changes
pnpm turbo dev
```

> [!NOTE]
> See [docs/development.md](docs/development.md) for getting started with development.

### 4. :rocket:&nbsp; Deploy to Vercel

#### Create a branch and push changes

```sh
# Create new branch from main
git checkout -b feature/new-shiny-thing

# Code stuff and commit changes
git push origin feature/new-shiny-thing
```

#### Create a pull request

Access [wx-next on Github](https://github.com/TheWeatherCompany/wx-next/compare) and create a new Pull Request.

> [!NOTE]
> Creating a Pull Request automatically tells [Vercel](https://vercel.com/the-weather-company/wx-next-web/deployments?environment=preview) to build and deploy the branch in a Preview environment. The Pull Request will show if the deployment failed as well as the preview link if available. Previewing requires a Vercel account.

Pull Requests will run the following actions

- CI / Build and Test - Runs type checks, lint, tests, and builds
- Checkmarx - Security scan
- Vercel Preview Comments - Checks for feedback submitted in the Vercel Preview environment and prevents merging if comments are not resolved
- Vercel Deployments - Checks for completed deployments

## Apps and Packages

- `web`: Main [Next.js](https://nextjs.org/) app with support for payload
- `@repo/ui`: a stub React component library shared by both `web` and `docs` applications
- `@repo/dal`: DAL that uses Next.js `fetch` and reuses previous concepts of URL Configs but typed Request / Responses.
- `@repo/eslint-config`: `eslint` configurations (includes `eslint-config-next` and `eslint-config-prettier`)
- `@repo/typescript-config`: `tsconfig.json`s used throughout the monorepo

## Important Note

This README.md file has been moved to the root directory of the FrontendAdminHeader component.
Please refer to the README.md file at `@/components/FrontendAdminHeader/README.md` for comprehensive documentation.
This file can be removed in a future cleanup
