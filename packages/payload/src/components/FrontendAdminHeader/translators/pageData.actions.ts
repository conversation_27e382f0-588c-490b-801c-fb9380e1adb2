'use server';
import 'server-only';

import {
	getPageMetadataByRoute,
	getPageMetadataById,
	getPageMetadataByAssetName,
	type PageMetadata,
} from './pageData.server';

/**
 * getPageMetadataByRouteAction - Server Action
 *
 * This is a server action wrapper around getPageMetadataByRoute.
 * It creates a proper server/client boundary.
 *
 * @param route The route to get metadata for
 * @param collection The collection to check (pages or articles)
 */
export async function getPageMetadataByRouteAction(
	route: string,
	collection: 'pages' | 'articles' = 'pages',
): Promise<PageMetadata | null> {
	try {
		return await getPageMetadataByRoute(route, collection);
	} catch (error) {
		console.error('Error in getPageMetadataByRouteAction:', error);
		return null;
	}
}

/**
 * getPageMetadataByIdAction - Server Action
 *
 * This is a server action wrapper around getPageMetadataById.
 * It creates a proper server/client boundary.
 *
 * @param id The page ID
 */
export async function getPageMetadataByIdAction(
	id: string,
): Promise<PageMetadata | null> {
	try {
		return await getPageMetadataById(id);
	} catch (error) {
		console.error('Error in getPageMetadataByIdAction:', error);
		return null;
	}
}

/**
 * getPageMetadataByAssetNameAction - Server Action
 *
 * This is a server action wrapper around getPageMetadataByAssetName.
 * It creates a proper server/client boundary.
 *
 * @param assetName The asset name
 */
export async function getPageMetadataByAssetNameAction(
	assetName: string,
): Promise<PageMetadata | null> {
	try {
		return await getPageMetadataByAssetName(assetName);
	} catch (error) {
		console.error('Error in getPageMetadataByAssetNameAction:', error);
		return null;
	}
}
