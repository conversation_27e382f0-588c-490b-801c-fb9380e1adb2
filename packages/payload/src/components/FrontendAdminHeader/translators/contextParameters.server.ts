'use server';
import 'server-only';
import { collectContextParameters } from '../collectors/contextParametersCollector.server';

/**
 * Server action wrapper for getContextualizedPage
 * This creates a proper server/client boundary that prevents the server-only
 * module from being imported in client components.
 */
export async function getDebugContextParameters(
	params: Record<string, string>,
) {
	return collectContextParameters(params);
}
