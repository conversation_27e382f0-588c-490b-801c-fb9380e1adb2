'use client';

import { filterObjectBySearchTerm } from '../formatters/AdminDebugPanel/utils';
import { ContextParameters } from '@repo/payload/contextParameters/extractParameters';
import { ContextMatch } from '@repo/payload/contextParameters/findBestMatch';

interface TranslatedContextData {
	filteredContext: {
		parameters?: ContextParameters;
		match?: ContextMatch | Record<string, unknown>;
		hash?: string;
		weight?: number;
		[key: string]: unknown;
	};
	hasContextData: boolean;
}

/**
 * translateContextData - Translator function
 *
 * Transforms and filters context data for display based on search terms.
 * This follows the Collector-Translator-Formatter pattern as a translator function
 * by transforming raw data into a display-ready format without collecting data.
 *
 * @param context The raw context data object
 * @param searchTerm Optional search term to filter data by
 * @returns Filtered context data organized by category
 */
export function translateContextData(
	context: Record<string, unknown> | undefined,
	searchTerm: string,
): TranslatedContextData {
	if (!context || Object.keys(context).length === 0) {
		return {
			filteredContext: {},
			hasContextData: false,
		};
	}

	// Filter context data based on search term
	const filteredContext = Object.entries(context).reduce(
		(acc, [key, value]) => {
			if (typeof value === 'object' && value !== null) {
				const filteredValue = filterObjectBySearchTerm(
					value as Record<string, unknown>,
					searchTerm,
				);
				if (Object.keys(filteredValue).length > 0) {
					acc[key] = filteredValue;
				}
			} else if (
				!searchTerm ||
				key.toLowerCase().includes(searchTerm.toLowerCase()) ||
				(typeof value === 'string' &&
					value.toLowerCase().includes(searchTerm.toLowerCase()))
			) {
				acc[key] = value;
			}
			return acc;
		},
		{} as Record<string, unknown>,
	);

	// Process and type-cast specific properties to ensure they have the correct types
	const processedContext: TranslatedContextData['filteredContext'] = {};

	// Process parameters if it exists
	if (
		typeof filteredContext.parameters === 'object' &&
		filteredContext.parameters !== null &&
		!Array.isArray(filteredContext.parameters)
	) {
		processedContext.parameters = filteredContext.parameters as Record<
			string,
			unknown
		>;
	}

	// Process match if it exists and has the required properties for ContextMatch
	if (
		typeof filteredContext.match === 'object' &&
		filteredContext.match !== null &&
		!Array.isArray(filteredContext.match) &&
		'pageId' in filteredContext.match &&
		'hash' in filteredContext.match &&
		'parameters' in filteredContext.match &&
		'exactMatch' in filteredContext.match
	) {
		processedContext.match = filteredContext.match as ContextMatch;
	} else if (
		typeof filteredContext.match === 'object' &&
		filteredContext.match !== null &&
		!Array.isArray(filteredContext.match)
	) {
		// If it doesn't have all required properties, just include it as a generic object
		processedContext.match = filteredContext.match as unknown as Record<
			string,
			unknown
		>;
	}

	// Process hash if it exists
	if (typeof filteredContext.hash === 'string') {
		processedContext.hash = filteredContext.hash;
	}

	// Process weight if it exists
	if (typeof filteredContext.weight === 'number') {
		processedContext.weight = filteredContext.weight;
	}

	// Add other properties
	Object.entries(filteredContext).forEach(([key, value]) => {
		if (!['parameters', 'match', 'hash', 'weight'].includes(key)) {
			processedContext[key] = value;
		}
	});

	const hasContextData = Object.keys(processedContext).length > 0;

	return {
		filteredContext: processedContext,
		hasContextData,
	};
}

interface OrganizedContextData {
	hasParameters: boolean;
	hasMatch: boolean;
	hasHashOrWeight: boolean;
	otherContextData: Array<[string, unknown]>;
}

/**
 * organizeContextData - Translator function
 *
 * Further organizes filtered context data for display by separating
 * parameters, match data, and other context information.
 *
 * @param filteredContext The already filtered context data
 * @returns Organized context data ready for display
 */
export function organizeContextData(
	filteredContext: Record<string, unknown>,
): OrganizedContextData {
	// Safely check if parameters exists and is an object
	const hasParameters =
		typeof filteredContext.parameters === 'object' &&
		filteredContext.parameters !== null &&
		!Array.isArray(filteredContext.parameters) &&
		Object.keys(filteredContext.parameters as Record<string, unknown>).length >
			0;

	// Safely check if match exists and is an object
	const hasMatch =
		typeof filteredContext.match === 'object' &&
		filteredContext.match !== null &&
		!Array.isArray(filteredContext.match) &&
		Object.keys(filteredContext.match as Record<string, unknown>).length > 0;

	// Safely check if hash or weight exists
	const hasHashOrWeight =
		typeof filteredContext.hash === 'string' ||
		typeof filteredContext.weight === 'number';

	const otherContextData = Object.entries(filteredContext).filter(
		([key]) => !['parameters', 'match', 'hash', 'weight'].includes(key),
	);

	return {
		hasParameters,
		hasMatch,
		hasHashOrWeight,
		otherContextData,
	};
}
