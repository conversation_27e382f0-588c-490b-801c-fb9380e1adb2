/**
 * Sanitizes a value, masking it if it appears sensitive and converting to string
 *
 * @param value The value to sanitize
 * @param key The key/name of the value (used to identify likely sensitive data)
 * @returns The sanitized string value
 */
export function sanitizeValue(value: unknown, key: string): string {
	// Convert key to lowercase for easier matching
	const keyLower = key.toLowerCase();

	// List of patterns that suggest sensitive data
	const sensitivePatterns = [
		'secret',
		'password',
		'token',
		'key',
		'auth',
		'credential',
		'private',
		'cert',
		'signature',
		'access_token',
		'session',
		'api_key',
		'apikey',
		'pwd',
		'login',
		'passcode',
		'pass',
	];

	// Check if key contains any sensitive patterns
	const isSensitiveKey = sensitivePatterns.some((pattern) =>
		keyLower.includes(pattern),
	);

	// Mask value if key suggests sensitive data
	if (isSensitiveKey) {
		return '********';
	}

	// Convert value to string
	return typeof value === 'string' ? value : String(value);
}

/**
 * Type for sanitized object that can contain strings or nested sanitized objects
 */
export type SanitizedObject = {
	[key: string]: string | SanitizedObject;
};

/**
 * Recursively sanitizes an object, masking sensitive values and converting all values to strings
 *
 * @param obj The object to sanitize
 * @returns A new object with sanitized string values
 */
export function sanitizeObject(obj: Record<string, unknown>): SanitizedObject {
	const sanitized: SanitizedObject = {};

	// Process each key-value pair
	for (const [key, value] of Object.entries(obj)) {
		// Handle nested objects recursively
		if (typeof value === 'object' && value !== null) {
			sanitized[key] = sanitizeObject(value as Record<string, unknown>);
		} else {
			// Sanitize single value
			sanitized[key] = sanitizeValue(value, key);
		}
	}

	return sanitized;
}
