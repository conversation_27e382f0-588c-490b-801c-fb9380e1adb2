'use client';

import { DebugData } from '../state/types';
import { TabType } from '../formatters/types';
import { ComponentDebugData } from '../collectors/types';

interface ComponentData extends ComponentDebugData {
	_collectedAt?: string;
	context?: Record<string, unknown>;
	[key: string]: unknown;
}

/**
 * translateTabData - Translator function
 *
 * Transforms debug data based on the selected tab type.
 * This follows the Collector-Translator-Formatter pattern as a translator by:
 * - Taking raw debug data and transforming it for specific tab displays
 * - Filtering and extracting relevant data based on tab type
 * - Creating properly structured objects that formatters can render directly
 *
 * @param debugData The complete debug data object
 * @param activeTab The currently selected tab
 * @returns Filtered and transformed data appropriate for the active tab
 */
export function translateTabData(
	debugData: DebugData | null,
	activeTab: TabType,
): Record<string, unknown> {
	if (!debugData) return {};

	if (activeTab === 'all') return { ...debugData };

	// Filter data based on tab type
	switch (activeTab) {
		case 'context': {
			// Check for context data in multiple places
			const contextData: Record<string, unknown> = {
				match: debugData.match || {},
			};

			// Look for context in component data
			if (debugData.components) {
				Object.values(debugData.components).forEach(
					(componentData: ComponentData) => {
						if (componentData.context) {
							Object.assign(contextData, componentData.context);
						}
					},
				);
			}

			// Include any contextParameters from page data
			if (debugData.page?.contextParameters) {
				contextData.parameters = debugData.page.contextParameters;
			}

			return contextData;
		}
		case 'page':
			return debugData.page || {};
		case 'location':
			return debugData.location || {};
		case 'headers':
			return debugData.headers || {};
		case 'environment':
			return debugData.environment || {};
		case 'components':
			return debugData.components || {};
		case 'errors':
			return debugData.errors || {};
		default:
			// Convert DebugData to Record<string, unknown> to satisfy the return type
			return { ...debugData } as Record<string, unknown>;
	}
}
