'use client';

import { PageDebugData, DebugData } from '../state/types';

export interface FormattedMetadataInfo {
	metadata: PageDebugData | null;
	path: string;
	dataSource: 'direct' | 'debugAtom' | 'none';
	dataState: 'fresh' | 'stale' | 'missing';
	// Add additional formatted data for UI rendering
	buttonStyle: {
		backgroundColor: string;
		hoverColor: string;
		textColor: string;
	};
	tooltipText: string;
	buttonText: {
		closed: string;
		open: string;
	};
	// Additional computed values to assist with UI rendering
	hasMetadata: boolean;
}

/**
 * translateMetadataDisplayInfo - Translator function
 *
 * Processes and combines metadata from multiple sources into a consistent format
 * for display in the MetadataFormatterClient. This follows the
 * Collector-Translator-Formatter pattern as a translator.
 *
 * This function handles all business logic for metadata presentation, leaving
 * the formatter component to focus purely on UI rendering.
 *
 * @param directMetadata Metadata directly passed to the formatter
 * @param debugData Debug data from atom that may contain page metadata
 * @param path Current path
 * @returns A standardized metadata object for display with UI-specific properties
 */
export function translateMetadataDisplayInfo(
	directMetadata: PageDebugData | null,
	debugData: DebugData | null,
	path: string,
): FormattedMetadataInfo {
	// Determine which metadata source to use and its state
	let resultMetadata: PageDebugData | null = null;
	let dataSource: 'direct' | 'debugAtom' | 'none' = 'none';
	let dataState: 'fresh' | 'stale' | 'missing' = 'missing';

	// First check direct metadata (highest priority)
	if (directMetadata) {
		resultMetadata = directMetadata;
		dataSource = 'direct';
		dataState = 'fresh';
	}
	// Then fall back to debug atom metadata if available
	else if (debugData?.page) {
		resultMetadata = debugData.page;
		dataSource = 'debugAtom';

		// Check if the data is stale (from a previous page)
		dataState = debugData.meta?.dataState === 'stale' ? 'stale' : 'fresh';
	}
	// No metadata available
	else {
		dataSource = 'none';
		dataState = 'missing';
	}

	// Determine UI styling based on metadata availability and freshness
	let buttonStyle = {
		backgroundColor: 'bg-gray-600',
		hoverColor: 'hover:bg-blue-700',
		textColor: 'text-white',
	};

	let tooltipText = 'No page metadata available';
	let buttonText = {
		closed: 'No Metadata',
		open: 'Hide Metadata',
	};

	// Update styling based on data state
	if (resultMetadata) {
		if (dataState === 'stale') {
			// Stale data styling (orange)
			buttonStyle = {
				backgroundColor: 'bg-orange-600',
				hoverColor: 'hover:bg-orange-700',
				textColor: 'text-white',
			};
			tooltipText = 'View Page Metadata (may be from previous page)';
			buttonText = {
				closed: 'Metadata*',
				open: 'Hide Metadata',
			};
		} else {
			// Fresh data styling (purple)
			buttonStyle = {
				backgroundColor: 'bg-indigo-600',
				hoverColor: 'hover:bg-indigo-700',
				textColor: 'text-white',
			};
			tooltipText = 'View Page Metadata';
			buttonText = {
				closed: 'Metadata',
				open: 'Hide Metadata',
			};
		}
	}

	// Return complete formatted object
	return {
		metadata: resultMetadata,
		path: path || debugData?.route || '',
		dataSource,
		dataState,
		buttonStyle,
		tooltipText,
		buttonText,
		hasMetadata: !!resultMetadata,
	};
}
