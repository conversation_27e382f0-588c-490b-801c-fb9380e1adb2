import 'server-only';
// Use dynamic imports instead of direct imports for server-only modules
import { DebugData } from '../state/types';
import type { ContextMatch } from '@repo/payload/contextParameters/findBestMatch';
import type { Page, Article } from '@repo/payload/payload-types';
import { sanitizeObject } from './sanitizers';
import {
	collectEnvironmentData,
	collectHeadersData,
	collectContextParametersData,
} from '../collectors/debugDataCollector.server';

/**
 * translateDebugData - Server Action (Translator)
 *
 * Translates raw data collected from various sources into a structured DebugData object.
 * This follows the Collector-Translator-Formatter pattern, where this function is the Translator.
 *
 * @param {Object} data - Raw data collected from various sources
 * @returns {Promise<DebugData>} - Translated debug data in the standardized format
 */
export async function translateDebugData(data: {
	route?: string;
	params?: Record<string, string>;
	pageProps?: Record<string, unknown>;
}): Promise<DebugData> {
	// Create initial debug data structure
	const debugData: DebugData = {
		timestamp: new Date().toISOString(),
	};

	// Add route information
	if (data.route) {
		debugData.route = data.route;
	}

	// Collect ALL environment variables using collector and sanitize them
	const envVars = await collectEnvironmentData();
	debugData.environment = sanitizeObject(envVars);

	// Collect ALL headers using collector and sanitize them
	const allHeaders = await collectHeadersData();
	debugData.headers = sanitizeObject(allHeaders);

	// Analyze context parameters when available
	if (data.params) {
		try {
			// Use the collector to get context parameters
			const contextDebug = await collectContextParametersData(data.params);

			if (contextDebug && contextDebug.match) {
				// Ensure match is properly typed as ContextMatch
				debugData.match = contextDebug.match as ContextMatch;

				// Extract page information if available
				if (contextDebug.page) {
					// Check if it's a Page or Article from payload-types
					const payloadDoc = contextDebug.page as unknown;

					if (isPayloadPage(payloadDoc)) {
						// Handle Page type
						debugData.page = {
							id: payloadDoc.id,
							title: payloadDoc.title,
							assetName: payloadDoc.assetName || undefined,
							status: payloadDoc._status || undefined,
							createdAt: payloadDoc.createdAt,
							updatedAt: payloadDoc.updatedAt,
							publishedAt:
								payloadDoc._status === 'published'
									? payloadDoc.updatedAt
									: null,
							tenant:
								payloadDoc.tenant && typeof payloadDoc.tenant !== 'string'
									? payloadDoc.tenant.name
									: null,
							collection: 'pages',
						};
					} else if (isPayloadArticle(payloadDoc)) {
						// Handle Article type
						debugData.page = {
							id: payloadDoc.id,
							title: payloadDoc.title,
							assetName: payloadDoc.assetName || undefined,
							status: payloadDoc._status || undefined,
							createdAt: payloadDoc.createdAt,
							updatedAt: payloadDoc.updatedAt,
							publishedAt:
								payloadDoc._status === 'published'
									? payloadDoc.updatedAt
									: null,
							tenant:
								payloadDoc.tenant && typeof payloadDoc.tenant !== 'string'
									? payloadDoc.tenant.name
									: null,
							collection: 'articles',
						};
					} else {
						// Fallback to generic object handling
						const page = contextDebug.page as Record<string, unknown>;
						debugData.page = {
							id: typeof page.id === 'string' ? page.id : undefined,
							title: typeof page.title === 'string' ? page.title : undefined,
							assetName:
								typeof page.assetName === 'string' ? page.assetName : undefined,
							status:
								typeof page._status === 'string' ? page._status : undefined,
							createdAt:
								typeof page.createdAt === 'string' ? page.createdAt : undefined,
							updatedAt:
								typeof page.updatedAt === 'string' ? page.updatedAt : undefined,
							publishedAt:
								typeof page._publishedAt === 'string'
									? page._publishedAt
									: null,
							tenant:
								typeof page.tenant === 'object' && page.tenant
									? typeof (page.tenant as Record<string, unknown>).name ===
										'string'
										? ((page.tenant as Record<string, unknown>).name as string)
										: null
									: null,
							collection: 'unknown',
						};
					}
				}
			}
		} catch (error) {
			console.error('Error processing context parameters:', error);
			debugData.errors = {
				...debugData.errors,
				contextParameters: {
					message: (error as Error).message,
					timestamp: new Date().toISOString(),
				},
			};
		}
	}

	// Add additional page props if provided
	if (data.pageProps) {
		if (!debugData.page) {
			debugData.page = {};
		}

		// Safely extract any useful page information from pageProps
		const pageProps = data.pageProps as Record<string, unknown>;

		// Type-check each property before assignment
		if (typeof pageProps.title === 'string')
			debugData.page.title = pageProps.title;
		if (typeof pageProps.description === 'string')
			debugData.page.description = pageProps.description;
		if (typeof pageProps.ogImage === 'string')
			debugData.page.ogImage = pageProps.ogImage;
		if (typeof pageProps.ogTitle === 'string')
			debugData.page.ogTitle = pageProps.ogTitle;
		if (typeof pageProps.ogDescription === 'string')
			debugData.page.ogDescription = pageProps.ogDescription;
	}

	return debugData;
}

// Type guards for Page and Article
function isPayloadPage(obj: unknown): obj is Page {
	return (
		typeof obj === 'object' &&
		obj !== null &&
		'id' in obj &&
		'title' in obj &&
		'createdAt' in obj &&
		'updatedAt' in obj
	);
}

function isPayloadArticle(obj: unknown): obj is Article {
	return (
		typeof obj === 'object' &&
		obj !== null &&
		'id' in obj &&
		'title' in obj &&
		'featuredImage' in obj &&
		'createdAt' in obj &&
		'updatedAt' in obj
	);
}
