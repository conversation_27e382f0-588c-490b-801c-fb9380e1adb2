'use server';
import 'server-only';

// Import from the server file instead of debugData.ts
import { translateDebugData as serverTranslateDebugData } from './debugData.server';
import { DebugData } from '../state/types';

/**
 * translateDebugDataAction - Server Action
 *
 * This is a server action wrapper around the server-only translateDebugData function.
 * It creates a proper server/client boundary that prevents the server-only
 * module from being imported in client components.
 *
 * @param data The debug data to translate
 * @returns The translated debug data
 */
export async function translateDebugDataAction(data: {
	route?: string;
	params?: Record<string, string>;
	pageProps?: Record<string, unknown>;
}): Promise<DebugData> {
	try {
		return await serverTranslateDebugData(data);
	} catch (error) {
		console.error('Error in translateDebugDataAction:', error);

		// Return a minimal valid object with error information
		return {
			timestamp: new Date().toISOString(),
			errors: {
				translation: {
					message: error instanceof Error ? error.message : String(error),
					timestamp: new Date().toISOString(),
				},
			},
		};
	}
}
