'use client';

/**
 * Types for location display data after translation
 */
export interface FormattedLocationInfo {
	// Basic location info
	displayName: string;
	fullDisplayName: string;
	rawLocation: Record<string, unknown>;

	// Coordinates info
	coordinates: {
		latitude: number;
		longitude: number;
		formattedString: string;
		mapsLink: string;
	} | null;

	// Geocode
	geocode: string | null;

	// Source info
	source: {
		type: string;
		displayName: string;
		collectedBy?: string;
		observedFrom?: string;
	};

	// Timestamps
	updatedAt: string | null;

	// Location properties
	properties: {
		placeId?: string;
		city?: string;
		adminDistrict?: string;
		countryCode?: string;
		adminDistrictCode?: string;
		[key: string]: unknown;
	};

	// Metadata for rendering decisions
	hasCoordinates: boolean;
	hasProperties: boolean;
	hasMetadata: boolean;
}

/**
 * Interface for location data passed to translator
 */
export interface LocationData {
	displayName?: string;
	latitude?: number | string;
	longitude?: number | string;
	geocode?: string;
	source?: string;
	adminDistrict?: string;
	countryCode?: string;
	city?: string;
	placeId?: string;
	adminDistrictCode?: string;
	_collectedBy?: string;
	_observedFrom?: string;
	_updatedAt?: string;
	_pathUpdatedFor?: string;
	[key: string]: unknown;
}

/**
 * translateLocationDisplay - Translator function
 *
 * Transforms raw location data into display-ready format.
 * This follows the Collector-Translator-Formatter pattern as a translator:
 * - It takes raw data from the collector
 * - It transforms it into a standardized display-ready format
 * - It handles all business logic for how the data should be presented
 *
 * @param location The raw location data
 * @returns Formatted location data ready for display
 */
export function translateLocationDisplay(
	location: LocationData | undefined,
): FormattedLocationInfo | null {
	if (!location) return null;

	// Extract coordinates from either direct properties or geocode
	let coordinates = null;
	if (location.latitude !== undefined && location.longitude !== undefined) {
		// Convert values to numbers and ensure they're not undefined before using them
		const lat =
			typeof location.latitude === 'string'
				? parseFloat(location.latitude)
				: (location.latitude ?? 0);
		const lon =
			typeof location.longitude === 'string'
				? parseFloat(location.longitude)
				: (location.longitude ?? 0);

		coordinates = {
			latitude: lat,
			longitude: lon,
			formattedString: formatCoordinates(lat, lon),
			mapsLink: getMapsLink(lat, lon),
		};
	} else if (location.geocode && typeof location.geocode === 'string') {
		const parts = location.geocode.split(',');
		if (parts.length === 2) {
			const latStr = parts[0] || '';
			const lonStr = parts[1] || '';
			const lat = parseFloat(latStr);
			const lon = parseFloat(lonStr);

			if (!isNaN(lat) && !isNaN(lon)) {
				// Ensure lat and lon are numbers before passing to formatCoordinates and getMapsLink
				const validLat = Number(lat);
				const validLon = Number(lon);
				coordinates = {
					latitude: validLat,
					longitude: validLon,
					formattedString: formatCoordinates(validLat, validLon),
					mapsLink: getMapsLink(validLat, validLon),
				};
			}
		}
	}

	// Determine the source of location data
	const sourceType = determineSourceType(location);
	const sourceDisplayName = getSourceDisplayName(sourceType, location);

	// Build full display name
	const fullDisplayName = buildFullDisplayName(location);

	// Extract the geocode
	const geocode =
		location.geocode ||
		(coordinates ? `${coordinates.latitude},${coordinates.longitude}` : null);

	// Extract important properties
	const properties = extractLocationProperties(location);

	return {
		displayName: location.displayName || 'Unknown Location',
		fullDisplayName,
		rawLocation: location as Record<string, unknown>,
		coordinates,
		geocode,
		source: {
			type: sourceType,
			displayName: sourceDisplayName,
			collectedBy: location._collectedBy,
			observedFrom: location._observedFrom,
		},
		updatedAt: location._updatedAt || null,
		properties,
		hasCoordinates: !!coordinates,
		hasProperties: Object.keys(properties).length > 0,
		hasMetadata: !!(
			location._collectedBy ||
			location._observedFrom ||
			location._updatedAt
		),
	};
}

/**
 * Helper function to format coordinates in a readable way
 */
function formatCoordinates(lat: number, lon: number): string {
	const latDir = lat >= 0 ? 'N' : 'S';
	const lonDir = lon >= 0 ? 'E' : 'W';

	return `${Math.abs(lat).toFixed(4)}° ${latDir}, ${Math.abs(lon).toFixed(4)}° ${lonDir}`;
}

/**
 * Helper function to create Google Maps link from coordinates
 */
function getMapsLink(lat: number, lon: number): string {
	return `https://www.google.com/maps/search/?api=1&query=${lat},${lon}`;
}

/**
 * Helper function to determine the source type of location data
 */
function determineSourceType(location: LocationData): string {
	if (location.source === 'geoIP' || location.source === 'server-geoip') {
		return 'geoIP';
	}
	if (location.source === 'favorite' || location._observedFrom === 'favorite') {
		return 'favorite';
	}
	if (location.source === 'manual' || location._observedFrom === 'manual') {
		return 'manual';
	}
	if (location.source === 'server-hydration') {
		return 'server-hydration';
	}
	if (location._observedFrom === 'useLocationSource') {
		return 'locationHook';
	}
	if (location._observedFrom === 'custom' || location.source === 'custom') {
		return 'custom';
	}
	return location.source || location._observedFrom || 'unknown';
}

/**
 * Helper function to get a user-friendly display name for the source type
 */
function getSourceDisplayName(
	sourceType: string,
	location: LocationData,
): string {
	switch (sourceType) {
		case 'geoIP':
			return 'GeoIP (detected from network)';
		case 'favorite':
			return 'User Favorite';
		case 'manual':
			return 'Manual Selection';
		case 'server-hydration':
			return 'Server Data';
		case 'locationHook':
			return 'Location Hook';
		case 'custom':
			return 'Custom Location';
		default:
			return location.source || location._observedFrom || 'Unknown Source';
	}
}

/**
 * Helper function to build a full display name from location components
 */
function buildFullDisplayName(location: LocationData): string {
	const parts = [];

	if (location.displayName) {
		parts.push(location.displayName);
	}

	if (location.adminDistrict && !parts.includes(location.adminDistrict)) {
		parts.push(location.adminDistrict);
	}

	if (location.countryCode && location.countryCode !== 'US') {
		parts.push(location.countryCode);
	}

	return parts.join(', ') || 'Unknown Location';
}

/**
 * Helper function to extract important location properties
 */
function extractLocationProperties(
	location: LocationData,
): Record<string, unknown> {
	const properties: Record<string, unknown> = {};

	// Extract important properties
	const keyProperties = [
		'placeId',
		'city',
		'adminDistrict',
		'countryCode',
		'adminDistrictCode',
	];

	keyProperties.forEach((key) => {
		if (location[key] !== undefined) {
			properties[key] = location[key];
		}
	});

	// Add other properties that aren't internal metadata or already handled
	Object.entries(location)
		.filter(
			([key]) =>
				!key.startsWith('_') &&
				!['displayName', 'latitude', 'longitude', 'geocode', 'source'].includes(
					key,
				) &&
				!keyProperties.includes(key),
		)
		.forEach(([key, value]) => {
			properties[key] = value;
		});

	return properties;
}
