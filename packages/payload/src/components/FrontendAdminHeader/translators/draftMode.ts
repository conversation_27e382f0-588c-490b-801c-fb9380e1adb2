'use server';
import 'server-only';

import { draftMode } from 'next/headers';
import { getCurrentUser } from '@repo/payload/utils/auth/getPayloadUser';

/**
 * Helper function to check if user has required roles
 */
async function validateUserRoles(): Promise<boolean> {
	try {
		const { user, userRoles } = await getCurrentUser();

		if (!user) {
			return false;
		}

		// Check if user has any of the required roles
		return userRoles.some((role) =>
			['admin', 'editor', 'web-developer'].includes(role),
		);
	} catch (error) {
		console.error('Error validating user roles:', error);
		return false;
	}
}

/**
 * exitDraftMode - Server Action (Translator)
 *
 * Translates a user action (clicking "Exit Draft" button) into a server-side state change.
 * This follows the Collector-Translator-Formatter pattern, where this action acts as a simple Translator.
 *
 * It disables the Next.js draft mode, which affects how content is rendered throughout the application.
 * Now includes authentication and authorization checks.
 *
 * @returns {Promise<{success: boolean, message?: string}>} Result of the operation
 */
export async function exitDraftMode(): Promise<{
	success: boolean;
	message?: string;
}> {
	try {
		// Validate user has required roles
		const isAuthorized = await validateUserRoles();

		if (!isAuthorized) {
			console.warn('Unauthorized attempt to exit draft mode');
			return {
				success: false,
				message:
					'Unauthorized: You must be logged in with appropriate permissions',
			};
		}

		// This is recommended https://nextjs.org/docs/messages/sync-dynamic-apis
		const draft = await draftMode();
		draft.disable();

		return { success: true };
	} catch (error) {
		console.error('Error exiting draft mode:', error);
		return {
			success: false,
			message:
				error instanceof Error ? error.message : 'Failed to exit draft mode',
		};
	}
}

/**
 * enableDraftMode - Server Action (Translator)
 *
 * Translates a user action (clicking "Enable Draft" button) into a server-side state change.
 * This follows the Collector-Translator-Formatter pattern, where this action acts as a simple Translator.
 *
 * It enables the Next.js draft mode, allowing editors to preview draft content.
 * Now includes authentication and authorization checks.
 *
 * @returns {Promise<{success: boolean, message?: string}>} Result of the operation
 */
export async function enableDraftMode(): Promise<{
	success: boolean;
	message?: string;
}> {
	try {
		// Validate user has required roles
		const isAuthorized = await validateUserRoles();

		if (!isAuthorized) {
			console.warn('Unauthorized attempt to enable draft mode');
			return {
				success: false,
				message:
					'Unauthorized: You must be logged in with appropriate permissions',
			};
		}

		// This is recommended https://nextjs.org/docs/messages/sync-dynamic-apis
		const draft = await draftMode();
		draft.enable();

		return { success: true };
	} catch (error) {
		console.error('Error enabling draft mode:', error);
		return {
			success: false,
			message:
				error instanceof Error ? error.message : 'Failed to enable draft mode',
		};
	}
}
