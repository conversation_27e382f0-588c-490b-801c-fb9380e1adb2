import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useDraft } from './useDraft';
import * as SWR from 'swr';
import * as jotai from 'jotai';

// Mock SWR
vi.mock('swr', () => {
	return {
		__esModule: true,
		default: vi.fn(),
	};
});

// Mock jotai
vi.mock('jotai', () => {
	return {
		atom: vi.fn(),
		useAtom: vi.fn(),
	};
});

// Type for the useAtom return value:
type UseAtomReturn<T> = [T, (value: T) => void];

// Define a type for the fetchDraftStatus function
type FetchDraftStatus = (
	url: string,
	forceRefresh?: boolean,
) => Promise<unknown>;

// Mock fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('useDraft', () => {
	// Setup mock responses
	const mockDraftData = { isEnabled: true };
	const mockError = new Error('Failed to fetch draft status');
	const mockMutate = vi.fn();

	// Mock atom setters
	const setIsEnabled = vi.fn();
	const setLoading = vi.fn();
	const setError = vi.fn();

	// Reset mocks between tests
	beforeEach(() => {
		vi.clearAllMocks();

		// Setup default atom mock values
		let callCount = 0;
		(jotai.useAtom as any).mockImplementation(() => {
			callCount++;
			// Return different values based on call order
			if (callCount === 1) {
				return [false, setIsEnabled] as UseAtomReturn<boolean>;
			} else if (callCount === 2) {
				return [false, setLoading] as UseAtomReturn<boolean>;
			} else {
				return [null, setError] as UseAtomReturn<null>;
			}
		});
	});

	afterEach(() => {
		vi.resetAllMocks();
	});

	describe('initial state and data fetching', () => {
		it('should return loading state while fetching draft status', () => {
			// Mock SWR to return loading state
			(SWR.default as any).mockReturnValue({
				data: null,
				error: null,
				isLoading: true,
				isValidating: true,
				mutate: mockMutate,
			});

			const { result } = renderHook(() => useDraft());

			expect(result.current.loading).toBe(true);
			expect(result.current.isEnabled).toBe(false);
			expect(result.current.error).toBeNull();
			expect(SWR.default).toHaveBeenCalledWith(
				'/api/payload/v1/draft',
				expect.any(Function),
				expect.objectContaining({
					revalidateOnFocus: true,
					dedupingInterval: 5000,
				}),
			);
		});

		it('should return draft status when fetch is successful', () => {
			// Mock SWR to return draft data
			(SWR.default as any).mockReturnValue({
				data: mockDraftData,
				error: null,
				isLoading: false,
				isValidating: false,
				mutate: mockMutate,
			});

			const { result } = renderHook(() => useDraft());

			expect(result.current.loading).toBe(false);
			expect(result.current.isEnabled).toBe(true);
			expect(result.current.error).toBeNull();
		});

		it('should return error when fetch fails', () => {
			// Mock SWR to return error
			(SWR.default as any).mockReturnValue({
				data: null,
				error: mockError,
				isLoading: false,
				isValidating: false,
				mutate: mockMutate,
			});

			const { result } = renderHook(() => useDraft());

			expect(result.current.loading).toBe(false);
			expect(result.current.isEnabled).toBe(false);
			expect(result.current.error).toEqual(mockError);
		});

		it('should handle non-Error objects in error state', () => {
			// Mock SWR to return a non-Error object as error
			(SWR.default as any).mockReturnValue({
				data: null,
				error: 'String error',
				isLoading: false,
				isValidating: false,
				mutate: mockMutate,
			});

			const { result } = renderHook(() => useDraft());

			expect(result.current.loading).toBe(false);
			expect(result.current.isEnabled).toBe(false);
			expect(result.current.error).toBeInstanceOf(Error);
			expect(result.current.error?.message).toBe('String error');
		});

		it('should update atoms on successful data fetch', () => {
			// Mock SWR with onSuccess callback
			(SWR.default as any).mockImplementation(
				(key: any, fetcher: any, options: any) => {
					// Call onSuccess handler with mock data
					if (options?.onSuccess) {
						options.onSuccess(mockDraftData);
					}
					return {
						data: mockDraftData,
						error: null,
						isLoading: false,
						isValidating: false,
						mutate: mockMutate,
					};
				},
			);

			renderHook(() => useDraft());

			// Verify atoms were updated
			expect(setIsEnabled).toHaveBeenCalledWith(true);
			expect(setLoading).toHaveBeenCalledWith(false);
		});

		it('should update atoms on fetch error', () => {
			// Mock SWR with onError callback
			(SWR.default as any).mockImplementation(
				(key: any, fetcher: any, options: any) => {
					// Call onError handler with mock error
					if (options?.onError) {
						options.onError(mockError);
					}
					return {
						data: null,
						error: mockError,
						isLoading: false,
						isValidating: false,
						mutate: mockMutate,
					};
				},
			);

			renderHook(() => useDraft());

			// Verify atoms were updated
			expect(setError).toHaveBeenCalledWith(mockError);
			expect(setIsEnabled).toHaveBeenCalledWith(false);
			expect(setLoading).toHaveBeenCalledWith(false);
		});
	});

	describe('fetchDraftStatus function', () => {
		it('should fetch draft status correctly', async () => {
			// Create a variable to store the fetcher function
			// Initialize with a dummy function to satisfy TypeScript
			let fetchDraftStatus: FetchDraftStatus = async () => null;

			// Mock SWR to capture the fetchDraftStatus function
			(SWR.default as any).mockImplementationOnce((key: any, fetcher: any) => {
				// Store the second argument (fetchDraftStatus function)
				fetchDraftStatus = fetcher;
				return {
					data: null,
					error: null,
					isLoading: true,
					isValidating: true,
					mutate: mockMutate,
				};
			});

			// Render the hook to trigger the SWR call
			renderHook(() => useDraft());

			// Mock successful fetch response
			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: async () => ({ data: mockDraftData, error: null }),
			});

			// Call the captured fetchDraftStatus function directly
			const result = await fetchDraftStatus('/api/payload/v1/draft');

			// Verify fetch was called correctly
			expect(mockFetch).toHaveBeenCalledWith('/api/payload/v1/draft', {
				credentials: 'include',
				headers: {
					'Content-Type': 'application/json',
				},
				cache: 'default',
			});

			// Verify the result
			expect(result).toEqual(mockDraftData);
		});

		it('should handle force refresh parameter', async () => {
			// Create a variable to store the fetcher function
			// Initialize with a dummy function to satisfy TypeScript
			let fetchDraftStatus: FetchDraftStatus = async () => null;

			// Mock SWR to capture the fetchDraftStatus function
			(SWR.default as any).mockImplementationOnce((key: any, fetcher: any) => {
				// Store the second argument (fetchDraftStatus function)
				fetchDraftStatus = fetcher;
				return {
					data: null,
					error: null,
					isLoading: true,
					isValidating: true,
					mutate: mockMutate,
				};
			});

			// Render the hook to trigger the SWR call
			renderHook(() => useDraft());

			// Mock successful fetch response
			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: async () => ({ data: mockDraftData, error: null }),
			});

			// Call the captured fetchDraftStatus function with forceRefresh=true
			await fetchDraftStatus('/api/payload/v1/draft', true);

			// Verify fetch was called with no-store cache
			expect(mockFetch).toHaveBeenCalledWith('/api/payload/v1/draft', {
				credentials: 'include',
				headers: {
					'Content-Type': 'application/json',
				},
				cache: 'no-store',
			});
		});

		it('should throw error when fetch fails', async () => {
			// Create a variable to store the fetcher function
			// Initialize with a dummy function to satisfy TypeScript
			let fetchDraftStatus: FetchDraftStatus = async () => null;

			// Mock SWR to capture the fetchDraftStatus function
			(SWR.default as any).mockImplementationOnce((key: any, fetcher: any) => {
				// Store the second argument (fetchDraftStatus function)
				fetchDraftStatus = fetcher;
				return {
					data: null,
					error: null,
					isLoading: true,
					isValidating: true,
					mutate: mockMutate,
				};
			});

			// Render the hook to trigger the SWR call
			renderHook(() => useDraft());

			// Mock failed fetch response
			mockFetch.mockResolvedValueOnce({
				ok: false,
				json: async () => ({ error: 'API error' }),
			});

			// Verify the fetchDraftStatus function throws
			await expect(fetchDraftStatus('/api/payload/v1/draft')).rejects.toThrow(
				'API error',
			);
		});

		it('should throw default error when fetch fails without error message', async () => {
			// Create a variable to store the fetcher function
			// Initialize with a dummy function to satisfy TypeScript
			let fetchDraftStatus: FetchDraftStatus = async () => null;

			// Mock SWR to capture the fetchDraftStatus function
			(SWR.default as any).mockImplementationOnce((key: any, fetcher: any) => {
				// Store the second argument (fetchDraftStatus function)
				fetchDraftStatus = fetcher;
				return {
					data: null,
					error: null,
					isLoading: true,
					isValidating: true,
					mutate: mockMutate,
				};
			});

			// Render the hook to trigger the SWR call
			renderHook(() => useDraft());

			// Mock failed fetch response without error message
			mockFetch.mockResolvedValueOnce({
				ok: false,
				json: async () => ({}),
			});

			// Verify the fetchDraftStatus function throws with default message
			await expect(fetchDraftStatus('/api/payload/v1/draft')).rejects.toThrow(
				'Failed to fetch draft status',
			);
		});
	});

	describe('enable function', () => {
		it('should enable draft mode successfully', async () => {
			// Mock SWR to return the mutate function
			(SWR.default as any).mockReturnValue({
				data: null,
				error: null,
				isLoading: false,
				isValidating: false,
				mutate: mockMutate,
			});

			// Mock successful enable response
			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: async () => ({ data: { isEnabled: true }, error: null }),
			});

			const { result } = renderHook(() => useDraft());

			// Call enable function
			await act(async () => {
				await result.current.enable();
			});

			// Verify fetch was called correctly
			expect(mockFetch).toHaveBeenCalledWith('/api/payload/v1/draft', {
				method: 'POST',
				credentials: 'include',
				headers: {
					'Content-Type': 'application/json',
				},
			});

			// Verify atoms were updated
			expect(setIsEnabled).toHaveBeenCalledWith(true);
			expect(setLoading).toHaveBeenCalledWith(true);
			expect(setLoading).toHaveBeenCalledWith(false);

			// Verify mutate was called
			expect(mockMutate).toHaveBeenCalled();
		});

		it('should handle enable failure with error message', async () => {
			// Mock SWR to return the mutate function
			(SWR.default as any).mockReturnValue({
				data: null,
				error: null,
				isLoading: false,
				isValidating: false,
				mutate: mockMutate,
			});

			// Mock failed enable response
			mockFetch.mockResolvedValueOnce({
				ok: false,
				json: async () => ({ error: 'Failed to enable draft mode' }),
			});

			const { result } = renderHook(() => useDraft());

			// Call enable function
			await act(async () => {
				await result.current.enable();
			});

			// Verify error was set
			expect(setError).toHaveBeenCalledWith(expect.any(Error));

			// Get the first call arguments
			const errorArg = setError.mock?.calls?.[0]?.[0];
			expect(errorArg.message).toBe('Failed to enable draft mode');

			// Verify loading state was reset
			expect(setLoading).toHaveBeenCalledWith(false);

			// Verify mutate was not called
			expect(mockMutate).not.toHaveBeenCalled();
		});

		it('should handle network errors during enable', async () => {
			// Mock SWR to return the mutate function
			(SWR.default as any).mockReturnValue({
				data: null,
				error: null,
				isLoading: false,
				isValidating: false,
				mutate: mockMutate,
			});

			// Mock network error
			mockFetch.mockRejectedValueOnce(new Error('Network error'));

			const { result } = renderHook(() => useDraft());

			// Call enable function
			await act(async () => {
				await result.current.enable();
			});

			// Verify error was set
			expect(setError).toHaveBeenCalledWith(expect.any(Error));

			// Get the first call arguments
			const errorArg = setError.mock.calls?.[0]?.[0];
			expect(errorArg.message).toBe('Network error');

			// Verify loading state was reset
			expect(setLoading).toHaveBeenCalledWith(false);
		});
	});

	describe('disable function', () => {
		it('should disable draft mode successfully', async () => {
			// Mock SWR to return the mutate function
			(SWR.default as any).mockReturnValue({
				data: null,
				error: null,
				isLoading: false,
				isValidating: false,
				mutate: mockMutate,
			});

			// Mock successful disable response
			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: async () => ({ data: { isEnabled: false }, error: null }),
			});

			const { result } = renderHook(() => useDraft());

			// Call disable function
			await act(async () => {
				await result.current.disable();
			});

			// Verify fetch was called correctly
			expect(mockFetch).toHaveBeenCalledWith('/api/payload/v1/draft', {
				method: 'DELETE',
				credentials: 'include',
				headers: {
					'Content-Type': 'application/json',
				},
			});

			// Verify atoms were updated
			expect(setIsEnabled).toHaveBeenCalledWith(false);
			expect(setLoading).toHaveBeenCalledWith(true);
			expect(setLoading).toHaveBeenCalledWith(false);

			// Verify mutate was called
			expect(mockMutate).toHaveBeenCalled();
		});

		it('should handle disable failure with error message', async () => {
			// Mock SWR to return the mutate function
			(SWR.default as any).mockReturnValue({
				data: null,
				error: null,
				isLoading: false,
				isValidating: false,
				mutate: mockMutate,
			});

			// Mock failed disable response
			mockFetch.mockResolvedValueOnce({
				ok: false,
				json: async () => ({ error: 'Failed to disable draft mode' }),
			});

			const { result } = renderHook(() => useDraft());

			// Call disable function
			await act(async () => {
				await result.current.disable();
			});

			// Verify error was set
			expect(setError).toHaveBeenCalledWith(expect.any(Error));

			// Get the first call arguments
			const errorArg = setError.mock.calls[0]?.[0];
			expect(errorArg.message).toBe('Failed to disable draft mode');

			// Verify loading state was reset
			expect(setLoading).toHaveBeenCalledWith(false);

			// Verify mutate was not called
			expect(mockMutate).not.toHaveBeenCalled();
		});

		it('should handle network errors during disable', async () => {
			// Mock SWR to return the mutate function
			(SWR.default as any).mockReturnValue({
				data: null,
				error: null,
				isLoading: false,
				isValidating: false,
				mutate: mockMutate,
			});

			// Mock network error
			mockFetch.mockRejectedValueOnce(new Error('Network error'));

			const { result } = renderHook(() => useDraft());

			// Call disable function
			await act(async () => {
				await result.current.disable();
			});

			// Verify error was set
			expect(setError).toHaveBeenCalledWith(expect.any(Error));

			// Get the first call arguments
			const errorArg = setError.mock.calls[0]?.[0];
			expect(errorArg.message).toBe('Network error');

			// Verify loading state was reset
			expect(setLoading).toHaveBeenCalledWith(false);
		});
	});

	describe('refresh function', () => {
		it('should call mutate to refresh draft status', async () => {
			// Mock SWR to return the mutate function
			(SWR.default as any).mockReturnValue({
				data: mockDraftData,
				error: null,
				isLoading: false,
				isValidating: false,
				mutate: mockMutate,
			});

			const { result } = renderHook(() => useDraft());

			// Call refresh function
			await act(async () => {
				await result.current.refresh();
			});

			// Verify mutate was called
			expect(mockMutate).toHaveBeenCalled();
		});
	});
});
