'use client';

import { useCallback } from 'react';
import { TypedUser } from 'payload';
import useSWRImmutable from 'swr/immutable';

export type UseAuth = {
	user: TypedUser | null;
	loading: boolean;
	error: Error | null;
	logout: () => Promise<void>;
	login: (email: string, password: string) => Promise<TypedUser | null>;
};

/**
 * Fetcher function for user data
 */
const fetchUser = async (url: string) => {
	const res = await fetch(url, {
		credentials: 'include',
		headers: {
			'Content-Type': 'application/json',
		},
	});

	if (!res.ok) {
		// If 401 or other auth error, return null instead of throwing
		if (res.status === 401) {
			return null;
		}
		throw new Error('Failed to fetch user');
	}

	const json = await res.json();
	return json.user;
};

/**
 * Custom hook for authentication operations with PayloadCMS
 * Based on the PayloadCMS useAuth hook documentation:
 * https://payloadcms.com/docs/admin/react-hooks#useauth
 *
 * Uses PayloadCMS built-in REST API endpoints:
 * https://payloadcms.com/docs/rest-api/overview#auth-operations
 *
 * Uses SWR for data fetching to prevent duplicate calls
 */
export const usePayloadAuth = (): UseAuth => {
	// Use SWR for fetching user data
	const {
		data: user,
		error,
		isLoading,
		mutate,
	} = useSWRImmutable('/api/payload/users/me', fetchUser);

	/**
	 * Log out the current user using PayloadCMS built-in logout endpoint
	 */
	const logout = useCallback(async () => {
		try {
			// Use PayloadCMS built-in endpoint for logout
			const res = await fetch('/api/payload/users/logout', {
				method: 'POST',
				credentials: 'include',
				headers: {
					'Content-Type': 'application/json',
				},
			});

			if (res.ok) {
				// Update SWR cache to reflect logged out state
				await mutate(null, false);
				// Redirect to home page after logout
				window.location.href = '/';
			} else {
				throw new Error('Failed to logout');
			}
		} catch (err) {
			throw err instanceof Error ? err : new Error('Failed to logout');
		}
	}, [mutate]);

	/**
	 * Log in a user with email and password using PayloadCMS built-in login endpoint
	 */
	const login = useCallback(
		async (email: string, password: string) => {
			try {
				// Use PayloadCMS built-in endpoint for login
				const res = await fetch('/api/payload/users/login', {
					method: 'POST',
					credentials: 'include',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({ email, password }),
				});

				if (res.ok) {
					const json = await res.json();
					// Update SWR cache with the new user data
					await mutate(json.user, false);
					return json.user;
				} else {
					const json = await res.json();
					throw new Error(json.message || 'Failed to login');
				}
			} catch (err) {
				throw err instanceof Error ? err : new Error('Failed to login');
			}
		},
		[mutate],
	);

	// Convert SWR error to the expected format
	const authError = error
		? error instanceof Error
			? error
			: new Error(String(error))
		: null;

	return {
		user: user || null,
		loading: isLoading,
		error: authError,
		logout,
		login,
	};
};
