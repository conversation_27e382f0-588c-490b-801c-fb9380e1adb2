import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { usePayloadAuth } from './usePayloadAuth';
import * as SWRImmutable from 'swr/immutable';

// Mock SWR Immutable
vi.mock('swr/immutable', () => {
	return {
		__esModule: true,
		default: vi.fn(),
	};
});

// Mock fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('usePayloadAuth', () => {
	// Setup mock responses
	const mockUser = { id: '123', email: '<EMAIL>' };
	const mockError = new Error('Failed to fetch user');
	const mockMutate = vi.fn();

	// Reset mocks between tests
	beforeEach(() => {
		vi.clearAllMocks();
	});

	afterEach(() => {
		vi.resetAllMocks();
	});

	describe('initial state and data fetching', () => {
		it('should return loading state while fetching user data', () => {
			// Mock SWR to return loading state
			(SWRImmutable.default as any).mockReturnValue({
				data: undefined,
				error: undefined,
				isLoading: true,
				mutate: mockMutate,
			});

			const { result } = renderHook(() => usePayloadAuth());

			expect(result.current.loading).toBe(true);
			expect(result.current.user).toBeNull();
			expect(result.current.error).toBeNull();
			expect(SWRImmutable.default).toHaveBeenCalledWith(
				'/api/payload/users/me',
				expect.any(Function),
			);
		});

		it('should return user data when fetch is successful', () => {
			// Mock SWR to return user data
			(SWRImmutable.default as any).mockReturnValue({
				data: mockUser,
				error: undefined,
				isLoading: false,
				mutate: mockMutate,
			});

			const { result } = renderHook(() => usePayloadAuth());

			expect(result.current.loading).toBe(false);
			expect(result.current.user).toEqual(mockUser);
			expect(result.current.error).toBeNull();
		});

		it('should return error when fetch fails', () => {
			// Mock SWR to return error
			(SWRImmutable.default as any).mockReturnValue({
				data: undefined,
				error: mockError,
				isLoading: false,
				mutate: mockMutate,
			});

			const { result } = renderHook(() => usePayloadAuth());

			expect(result.current.loading).toBe(false);
			expect(result.current.user).toBeNull();
			expect(result.current.error).toEqual(mockError);
		});

		it('should handle non-Error objects in error state', () => {
			// Mock SWR to return a non-Error object as error
			(SWRImmutable.default as any).mockReturnValue({
				data: undefined,
				error: 'String error',
				isLoading: false,
				mutate: mockMutate,
			});

			const { result } = renderHook(() => usePayloadAuth());

			expect(result.current.loading).toBe(false);
			expect(result.current.user).toBeNull();
			expect(result.current.error).toBeInstanceOf(Error);
			expect(result.current.error?.message).toBe('String error');
		});
	});

	describe('fetchUser function', () => {
		it('should fetch user data correctly', async () => {
			// Extract the fetchUser function directly from the hook implementation
			let capturedFetchUser: any;

			// Mock SWR to capture the fetchUser function
			(SWRImmutable.default as any).mockImplementationOnce(
				(url: string, fetcher: any) => {
					// Store the second argument (fetchUser function) for later use
					capturedFetchUser = fetcher;
					return {
						data: null,
						error: null,
						isLoading: true,
						mutate: mockMutate,
					};
				},
			);

			// Render the hook to trigger the SWR call
			renderHook(() => usePayloadAuth());

			// Mock successful fetch response
			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: async () => ({ user: mockUser }),
			});

			// Call the captured fetchUser function directly
			if (capturedFetchUser) {
				const result = await capturedFetchUser('/api/payload/users/me');

				// Verify fetch was called correctly
				expect(mockFetch).toHaveBeenCalledWith('/api/payload/users/me', {
					credentials: 'include',
					headers: {
						'Content-Type': 'application/json',
					},
				});

				// Verify the result
				expect(result).toEqual(mockUser);
			} else {
				throw new Error('fetchUser function was not captured');
			}
		});

		it('should return null for 401 responses', async () => {
			// Extract the fetchUser function directly
			let capturedFetchUser: any;

			// Mock SWR to capture the fetchUser function
			(SWRImmutable.default as any).mockImplementationOnce(
				(url: string, fetcher: any) => {
					// Store the second argument (fetchUser function) for later use
					capturedFetchUser = fetcher;
					return {
						data: null,
						error: null,
						isLoading: true,
						mutate: mockMutate,
					};
				},
			);

			// Render the hook to trigger the SWR call
			renderHook(() => usePayloadAuth());

			// Mock 401 response
			mockFetch.mockResolvedValueOnce({
				ok: false,
				status: 401,
			});

			// Call the captured fetchUser function
			if (capturedFetchUser) {
				const result = await capturedFetchUser('/api/payload/users/me');

				// Verify result is null for 401
				expect(result).toBeNull();
			} else {
				throw new Error('fetchUser function was not captured');
			}
		});

		it('should throw error for non-401 error responses', async () => {
			// Extract the fetchUser function directly
			let capturedFetchUser: any;

			// Mock SWR to capture the fetchUser function
			(SWRImmutable.default as any).mockImplementationOnce(
				(url: string, fetcher: any) => {
					// Store the second argument (fetchUser function) for later use
					capturedFetchUser = fetcher;
					return {
						data: null,
						error: null,
						isLoading: true,
						mutate: mockMutate,
					};
				},
			);

			// Render the hook to trigger the SWR call
			renderHook(() => usePayloadAuth());

			// Mock 500 response
			mockFetch.mockResolvedValueOnce({
				ok: false,
				status: 500,
			});

			// Verify the fetchUser function throws for non-401 errors
			if (capturedFetchUser) {
				await expect(
					capturedFetchUser('/api/payload/users/me'),
				).rejects.toThrow('Failed to fetch user');
			} else {
				throw new Error('fetchUser function was not captured');
			}
		});
	});

	describe('login function', () => {
		it('should login user successfully', async () => {
			// Mock SWR to return the mutate function
			(SWRImmutable.default as any).mockReturnValue({
				data: null,
				error: null,
				isLoading: false,
				mutate: mockMutate,
			});

			// Mock successful login response
			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: async () => ({ user: mockUser }),
			});

			const { result } = renderHook(() => usePayloadAuth());

			// Call login function
			let returnedUser;
			await act(async () => {
				returnedUser = await result.current.login(
					'<EMAIL>',
					'password',
				);
			});

			// Verify fetch was called correctly
			expect(mockFetch).toHaveBeenCalledWith('/api/payload/users/login', {
				method: 'POST',
				credentials: 'include',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					email: '<EMAIL>',
					password: 'password',
				}),
			});

			// Verify mutate was called with the user data
			expect(mockMutate).toHaveBeenCalledWith(mockUser, false);

			// Verify the returned user
			expect(returnedUser).toEqual(mockUser);
		});

		it('should handle login failure with error message', async () => {
			// Mock SWR to return the mutate function
			(SWRImmutable.default as any).mockReturnValue({
				data: null,
				error: null,
				isLoading: false,
				mutate: mockMutate,
			});

			// Mock failed login response
			mockFetch.mockResolvedValueOnce({
				ok: false,
				json: async () => ({ message: 'Invalid credentials' }),
			});

			const { result } = renderHook(() => usePayloadAuth());

			// Call login function and expect it to throw
			await expect(
				act(async () => {
					await result.current.login('<EMAIL>', 'wrong-password');
				}),
			).rejects.toThrow('Invalid credentials');

			// Verify mutate was not called
			expect(mockMutate).not.toHaveBeenCalled();
		});

		it('should handle login failure without error message', async () => {
			// Mock SWR to return the mutate function
			(SWRImmutable.default as any).mockReturnValue({
				data: null,
				error: null,
				isLoading: false,
				mutate: mockMutate,
			});

			// Mock failed login response without message
			mockFetch.mockResolvedValueOnce({
				ok: false,
				json: async () => ({}),
			});

			const { result } = renderHook(() => usePayloadAuth());

			// Call login function and expect it to throw with default message
			await expect(
				act(async () => {
					await result.current.login('<EMAIL>', 'wrong-password');
				}),
			).rejects.toThrow('Failed to login');

			// Verify mutate was not called
			expect(mockMutate).not.toHaveBeenCalled();
		});

		it('should handle network errors during login', async () => {
			// Mock SWR to return the mutate function
			(SWRImmutable.default as any).mockReturnValue({
				data: null,
				error: null,
				isLoading: false,
				mutate: mockMutate,
			});

			// Mock network error
			mockFetch.mockRejectedValueOnce(new Error('Network error'));

			const { result } = renderHook(() => usePayloadAuth());

			// Call login function and expect it to throw
			await expect(
				act(async () => {
					await result.current.login('<EMAIL>', 'password');
				}),
			).rejects.toThrow('Network error');

			// Verify mutate was not called
			expect(mockMutate).not.toHaveBeenCalled();
		});
	});

	describe('logout function', () => {
		it('should logout user successfully', async () => {
			// Mock SWR to return the mutate function
			(SWRImmutable.default as any).mockReturnValue({
				data: mockUser,
				error: null,
				isLoading: false,
				mutate: mockMutate,
			});

			// Mock successful logout response
			mockFetch.mockResolvedValueOnce({
				ok: true,
			});

			// Save original window.location
			const originalHref = window.location.href;

			// Create a mock implementation for window.location.href
			// @ts-expect-error - we're intentionally mocking this
			delete window.location;
			// @ts-expect-error - we're intentionally mocking this
			window.location = { href: '' };

			const { result } = renderHook(() => usePayloadAuth());

			// Call logout function
			await act(async () => {
				await result.current.logout();
			});

			// Verify fetch was called correctly
			expect(mockFetch).toHaveBeenCalledWith('/api/payload/users/logout', {
				method: 'POST',
				credentials: 'include',
				headers: {
					'Content-Type': 'application/json',
				},
			});

			// Verify mutate was called with null
			expect(mockMutate).toHaveBeenCalledWith(null, false);

			// Verify redirect
			expect(window.location.href).toBe('/');

			// Restore original window.location
			// @ts-expect-error - we're intentionally restoring this
			window.location = { href: originalHref };
		});

		it('should handle logout failure', async () => {
			// Mock SWR to return the mutate function
			(SWRImmutable.default as any).mockReturnValue({
				data: mockUser,
				error: null,
				isLoading: false,
				mutate: mockMutate,
			});

			// Mock failed logout response
			mockFetch.mockResolvedValueOnce({
				ok: false,
			});

			const { result } = renderHook(() => usePayloadAuth());

			// Call logout function and expect it to throw
			await expect(
				act(async () => {
					await result.current.logout();
				}),
			).rejects.toThrow('Failed to logout');

			// Verify mutate was not called
			expect(mockMutate).not.toHaveBeenCalled();
		});

		it('should handle network errors during logout', async () => {
			// Mock SWR to return the mutate function
			(SWRImmutable.default as any).mockReturnValue({
				data: mockUser,
				error: null,
				isLoading: false,
				mutate: mockMutate,
			});

			// Mock network error
			mockFetch.mockRejectedValueOnce(new Error('Network error'));

			const { result } = renderHook(() => usePayloadAuth());

			// Call logout function and expect it to throw
			await expect(
				act(async () => {
					await result.current.logout();
				}),
			).rejects.toThrow('Network error');

			// Verify mutate was not called
			expect(mockMutate).not.toHaveBeenCalled();
		});
	});
});
