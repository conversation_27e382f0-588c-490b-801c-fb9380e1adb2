import { atom } from 'jotai';

/**
 * Atom for storing the draft mode state
 * This provides a single source of truth for draft mode across the application
 */
export const draftModeAtom = atom<boolean>(false);

/**
 * Atom for storing the loading state of draft mode operations
 */
export const draftModeLoadingAtom = atom<boolean>(true);

/**
 * Atom for storing any errors that occur during draft mode operations
 */
export const draftModeErrorAtom = atom<Error | null>(null);
