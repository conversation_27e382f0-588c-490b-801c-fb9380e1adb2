# FrontendAdminHeader

The FrontendAdminHeader is a developer and content team utility component that provides debugging, metadata visualization, and draft mode controls for the wx-next platform.

## TLDR: Quick Implementation Guide

### Step 1: Add StateProvider to your layout

```tsx
// app/layout.tsx
import { StateProvider } from "@/components/FrontendAdminHeader/state/StateProvider";
import FrontendAdminHeader from "@/components/FrontendAdminHeader";

export default async function RootLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	// Get user roles from authentication system
	const userRoles = ["admin"]; // Example roles

	return (
		<html lang="en">
			<body>
				<StateProvider userRoles={userRoles}>
					<FrontendAdminHeader />
					{children}
				</StateProvider>
			</body>
		</html>
	);
}
```

### Step 2: Add debug data to components

```tsx
// MyComponent.tsx
import { DebugCollector } from "@/components/FrontendAdminHeader/collectors/DebugCollector";

function MyComponent() {
	const [count, setCount] = useState(0);

	return (
		<>
			<DebugCollector
				componentName="MyComponent"
				data={{
					props: {
						/* props */
					},
					state: { count },
					performance: { renderTime: 42 },
				}}
			/>
			{/* Component content */}
		</>
	);
}
```

### Step 3: Add page metadata

```tsx
// page.tsx
import { DebugCollector } from "@/components/FrontendAdminHeader/collectors/DebugCollector";

export default function MyPage() {
	return (
		<>
			<DebugCollector
				componentName="MyPage"
				data={{ pageType: "content" }}
				page={{
					title: "My Page Title",
					description: "Page description",
					id: "page-123",
					status: "published",
				}}
			/>
			{/* Page content */}
		</>
	);
}
```

## Architecture: Collector-Translator-Formatter Pattern

The FrontendAdminHeader follows the Collector-Translator-Formatter pattern, which separates concerns into three distinct roles:

```mermaid
flowchart LR
    subgraph Collectors
        DC[DataCollector]
        DBC[DebugCollector]
    end

    subgraph Translators
        TD[translateDebugData]
        TPD[translatePageData]
        TLD[translateLocationDisplay]
    end

    subgraph State
        DA[debugDataAtom]
    end

    subgraph Formatters
        ADP[AdminDebugPanel]
        MF[MetadataFormatter]
        DMB[DraftModeButtons]
    end

    Collectors --> Translators
    Translators --> State
    State --> Formatters
```

### 1. Collectors

Components that gather raw data from various sources:

- **DataCollector**: Automatically collects debug data from headers, environment, etc.
- **DebugCollector**: Unified component for collecting both component and page debug data

### 2. Translators

Functions that transform raw data into structured formats:

- **translateDebugData**: Processes raw debug data into structured DebugData
- **translatePageData**: Transforms raw page data into structured metadata
- **translateLocationDisplay**: Formats location data for display

### 3. State Management

Jotai atoms that store and provide access to debug data:

- **debugDataAtom**: Central atom for storing all debug information
- **AtomDebugHydrationBoundary**: Hydrates atoms with server-side data

### 4. Formatters

Components that render data for the user:

- **AdminDebugPanel**: Renders debug information in a tabbed interface
- **MetadataFormatterClient**: Renders page metadata
- **DraftModeButtons**: Controls for toggling draft mode

## Data Flow

```mermaid
sequenceDiagram
    participant Server
    participant StateProvider
    participant DataCollector
    participant DebugCollector
    participant Atoms as Jotai Atoms
    participant Formatters
    participant User

    Server->>StateProvider: Initial data
    StateProvider->>Atoms: Hydrate atoms
    StateProvider->>DataCollector: Initialize

    DataCollector->>Server: Fetch debug data
    Server->>DataCollector: Return data
    DataCollector->>Atoms: Update atoms

    DebugCollector->>Atoms: Add component data

    Atoms->>Formatters: Subscribe to state
    Formatters->>User: Render UI
    User->>Formatters: Interact
    Formatters->>Atoms: Update state
```

## Component API

### StateProvider

The entry point for the debug system. Add this to your layout to enable debugging.

```tsx
<StateProvider
	userRoles={["admin", "editor"]}
	initialData={
		{
			/* optional initial debug data */
		}
	}
	pageProps={
		{
			/* optional page props */
		}
	}
>
	{children}
</StateProvider>
```

**Props:**

- `children`: React children
- `userRoles`: Array of user roles for permission checks
- `initialData`: Optional initial debug data
- `pageProps`: Optional page props to include in debug data

### DebugCollector

Unified component for collecting both component and page debug data.

```tsx
<DebugCollector
	componentName="MyComponent"
	data={{
		props: {
			/* props */
		},
		state: {
			/* state */
		},
		performance: {
			/* performance metrics */
		},
		context: {
			/* custom context data */
		},
	}}
	page={{
		title: "Page Title",
		id: "page-123",
		status: "published",
	}}
/>
```

**Props:**

- `componentName`: Name of the component providing data
- `data`: Component-specific debug data (props, state, performance metrics)
- `page`: Optional page metadata
- `onUpdateData`: Optional custom function for updating component data
- `onSetPageData`: Optional custom function for updating page data

### useDebugSystem Hook

Hook for accessing and updating debug data programmatically.

```tsx
const {
	debugData,
	updateSection,
	updateComponentData,
	setPageData,
	clearAllDebugData,
} = useDebugSystem();
```

**Returns:**

- `debugData`: Current debug data
- `updateSection`: Function to update a specific section of debug data
- `updateComponentData`: Function to update component-specific debug data
- `setPageData`: Function to set page metadata
- `clearAllDebugData`: Function to reset debug data

## Debug Panel Tabs

The AdminDebugPanel provides several tabs for different types of debug information:

### 1. All Data Tab

Comprehensive view of all debug data with status information.

### 2. Context Tab

Shows context parameters from the URL and matched context values.

### 3. Page Data Tab

Displays page metadata with "Edit in Admin" link for content editors.

### 4. Location Tab

Shows current location information with coordinates and source.

### 5. Components Tab

Lists all components with debug data including state and props.

### 6. Headers Tab

Shows all HTTP headers with search functionality.

### 7. Environment Tab

Displays environment variables with sensitive values masked.

### 8. Errors Tab

Shows any errors that occurred during data collection.

## Role-Based Access

The FrontendAdminHeader provides different features based on user roles:

- **Admin/Web Developer**: Full access to all features
- **Editor**: Access to metadata viewer and draft mode controls
- **Other authenticated users**: Access to draft mode controls only
- **Unauthenticated users**: No access

## Best Practices

### 1. Use StateProvider at the Root Layout

Always add the StateProvider at the root layout to ensure debug data is available throughout the application.

```tsx
// app/layout.tsx
import { StateProvider } from "@/components/FrontendAdminHeader/state/StateProvider";
import FrontendAdminHeader from "@/components/FrontendAdminHeader";

export default function RootLayout({ children }) {
	return (
		<html>
			<body>
				<StateProvider>
					<FrontendAdminHeader />
					{children}
				</StateProvider>
			</body>
		</html>
	);
}
```

### 2. Add DebugCollector to Complex Components

Include the DebugCollector in components with complex state or props.

```tsx
import { DebugCollector } from "@/components/FrontendAdminHeader/collectors/DebugCollector";

function ComplexComponent(props) {
	const [state, setState] = useState({});

	return (
		<>
			<DebugCollector
				componentName="ComplexComponent"
				data={{
					props,
					state,
					performance: {
						/* metrics */
					},
				}}
			/>
			{/* Component content */}
		</>
	);
}
```

### 3. Add Page Metadata to Page Components

Use the DebugCollector to provide page metadata in page components.

```tsx
import { DebugCollector } from "@/components/FrontendAdminHeader/collectors/DebugCollector";

export default function ProductPage() {
	return (
		<>
			<DebugCollector
				componentName="ProductPage"
				data={{ pageType: "product" }}
				page={{
					title: "Product Name",
					description: "Product description",
					id: "product-123",
					collection: "products",
				}}
			/>
			{/* Page content */}
		</>
	);
}
```

### 4. Use Direct Imports

Always import components directly from their source files, not from barrel files.

```tsx
// ✅ Correct: Import directly from source file
import { DebugCollector } from "@/components/FrontendAdminHeader/collectors/DebugCollector";
import { StateProvider } from "@/components/FrontendAdminHeader/state/StateProvider";

// ❌ Incorrect: Will cause server/client boundary issues
import { DebugCollector } from "@/components/FrontendAdminHeader";
```

### 5. Keep Debug Data Lightweight

Don't include large objects or sensitive information in debug data.

```tsx
// ✅ Good: Lightweight debug data
<DebugCollector
  componentName="MyComponent"
  data={{
    props: { id: props.id, type: props.type },
    state: { isLoading, error: error?.message }
  }}
/>

// ❌ Bad: Including large or sensitive data
<DebugCollector
  componentName="MyComponent"
  data={{
    props: props, // Don't include entire props object
    state: {
      user: { /* large user object */ },
      token: "sensitive-token" // Don't include sensitive data
    }
  }}
/>
```

## Keyboard Shortcuts

- `Ctrl+Shift+D`: Toggle debug panel

## Security Considerations

- Sensitive environment variables and headers are automatically masked
- Debug panel only appears for authenticated users with appropriate roles
- Technical details are only shown to users with appropriate roles

## Environment Control

The FrontendAdminHeader is now always available in production environments for authenticated users with appropriate roles. However, for security reasons, detailed environment variables are protected and only available under specific conditions.

### Environment Variable Protection

In production environments, detailed environment variables are only available when the following conditions are met:

1. The request includes the `x-show-environment-vars: true` header
2. The request includes the `x-payload-secret` header with the correct PAYLOAD_SECRET value

This ensures that sensitive environment information is only accessible to authorized users who have knowledge of the payload secret.

### Accessing Protected Environment Variables

To access detailed environment variables in production:

1. Use a browser extension like ModHeader to add the required headers:

   - `x-show-environment-vars: true`
   - `x-payload-secret: your-payload-secret-value`

2. Refresh the page to see the complete environment information in the debug panel

### Environment Variable Availability

The component uses the following logic to determine whether to show detailed environment variables:

1. **Development and Preview Environments**:

   - All environment variables are always available

2. **Production Environment**:
   - Basic environment info (NODE_ENV, VERCEL_ENV, platform, architecture) is always available
   - Detailed environment variables (including all process.env values) are only available with the proper headers

## Troubleshooting

### Common Issues

1. **Debug Panel Not Appearing**

   - Check user authentication and roles
   - Verify StateProvider is included in the layout
   - Check for console errors related to the debug system
   - Verify environment variables (NODE_ENV, VERCEL_ENV, SHOW_ADMIN_HEADER)

2. **Missing Debug Data**

   - Ensure DebugCollector is properly implemented
   - Check that data is being passed correctly to collectors
   - Verify translators are processing data correctly

3. **Server/Client Component Errors**

   - Make sure you're using direct imports, not barrel files
   - Check that server components aren't importing client components
   - Verify that client components are properly marked with 'use client'

4. **Not Working in Production**
   - Check if `SHOW_ADMIN_HEADER` environment variable is set to 'true'
   - Verify that the user has the appropriate roles
   - Check browser console for any errors
