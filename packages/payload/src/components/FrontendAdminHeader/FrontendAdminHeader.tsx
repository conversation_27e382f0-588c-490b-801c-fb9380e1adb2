'use client';

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { usePayloadAuth } from './hooks/usePayloadAuth';
import { useDebugSystem } from './state/hooks';
import Link from 'next/link';
import { UserIcon, Menu, X } from 'lucide-react';
import { DraftModeManager } from './formatters/DraftMode/DraftModeManager';
import dynamic from 'next/dynamic';

// Dynamically import admin components with proper loading indicators
const AdminDebugPanel = dynamic(
	() =>
		import('./formatters/AdminDebugPanel/AdminDebugPanel').then((mod) => ({
			default: mod.AdminDebugPanel,
		})),
	{
		ssr: false,
		loading: () => (
			<button className="ml-2 rounded bg-blue-600 px-3 py-1 text-xs font-bold text-white opacity-50">
				Loading...
			</button>
		),
	},
);

const MetadataFormatterClient = dynamic(
	() =>
		import('./formatters/MetadataFormatter/MetadataFormatterClient').then(
			(mod) => ({
				default: mod.MetadataFormatterClient,
			}),
		),
	{
		ssr: false,
		loading: () => (
			<button className="ml-2 rounded bg-indigo-600 px-3 py-1 text-xs font-bold text-white opacity-50">
				Loading...
			</button>
		),
	},
);

/**
 * FrontendAdminHeader
 *
 * This component handles the authentication and rendering of the admin header UI.
 * It's dynamically imported only when debug mode is active.
 *
 * Performance optimization:
 * - Uses dynamic imports for admin components
 * - Only renders for authenticated users with appropriate roles
 * - Consolidates draft mode functionality into a single component
 */
export function FrontendAdminHeader() {
	const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
	const { debugData } = useDebugSystem();
	const metadata = debugData?.page || null;
	const path = debugData?.route || '';
	const menuRef = useRef<HTMLDivElement>(null);
	const buttonRef = useRef<HTMLButtonElement>(null);

	// Handle authentication
	const { user, logout, loading } = usePayloadAuth();
	const userRoles = useMemo(() => user?.role || [], [user]);
	const userRoleSlugs = useMemo(
		() =>
			userRoles
				.map((role) => (typeof role === 'string' ? role : role?.slug))
				.filter(Boolean) as string[],
		[userRoles],
	);

	// Check if user has admin/developer roles
	const isAllowed = useMemo(() => {
		if (!user?.role) return false;
		return user.role.some((role) => {
			const roleSlug = typeof role === 'string' ? role : role?.slug;
			return (
				roleSlug && ['admin', 'editor', 'web-developer'].includes(roleSlug)
			);
		});
	}, [user?.role]);

	// Close mobile menu when clicking outside
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				!isMobileMenuOpen ||
				(menuRef.current && menuRef.current.contains(event.target as Node)) ||
				(buttonRef.current && buttonRef.current.contains(event.target as Node))
			) {
				return;
			}

			const target = event.target as HTMLElement;
			const isInsideDebugPanel = target.closest('[data-debug-panel]') !== null;

			if (!isInsideDebugPanel) {
				setIsMobileMenuOpen(false);
			}
		};

		document.addEventListener('mousedown', handleClickOutside);
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, [isMobileMenuOpen]);

	// Handle logout click
	const handleLogout = async (e: React.MouseEvent) => {
		e.preventDefault();
		await logout();
	};

	// Toggle mobile menu
	const toggleMobileMenu = () => {
		setIsMobileMenuOpen(!isMobileMenuOpen);
	};

	// If still loading auth state, render nothing
	if (loading) {
		return null;
	}

	// If user is not authenticated or not allowed, render nothing or minimal controls
	if (!user || !isAllowed) {
		return <DraftModeManager isAdmin={false} />;
	}

	return (
		<div className="h-12 w-full bg-black shadow-md" data-testid="admin-header">
			<div className="container mx-auto px-4">
				{/* Desktop and Mobile Header */}
				<div className="relative z-50 flex h-10 items-center justify-between">
					{/* Logo - always visible */}
					<div className="mr-4 text-sm font-bold text-white">
						<span className="text-blue-400">TWC</span> Inspector
					</div>

					{/* Mobile Menu Button - only visible on small screens */}
					<button
						ref={buttonRef}
						onClick={toggleMobileMenu}
						className="rounded p-1 text-white focus:outline-none focus:ring-1 focus:ring-blue-500 md:hidden"
						aria-label="Toggle menu"
						aria-expanded={isMobileMenuOpen}
					>
						{isMobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
					</button>

					{/* Desktop Controls - hidden on mobile */}
					<div className="hidden items-center gap-2 overflow-hidden md:flex">
						<DraftModeManager isAdmin={true} />
						<MetadataFormatterClient
							userRoles={userRoleSlugs}
							metadata={metadata}
							path={path}
						/>
						<AdminDebugPanel userRoles={userRoleSlugs} />
					</div>

					{/* Desktop User Info and Actions - hidden on mobile */}
					<div className="ml-auto hidden items-center gap-3 md:flex">
						<div className="flex items-center gap-1 text-white">
							<UserIcon size={14} className="text-gray-300" />
							<span className="text-xs font-medium">
								{user?.username || 'User'}
							</span>
						</div>
						<Link
							href="/admin"
							className="inline-block rounded bg-blue-600 px-2 py-1 text-xs font-bold text-white transition-colors hover:bg-blue-700"
							data-testid="admin-link"
						>
							Admin
						</Link>
						<button
							onClick={handleLogout}
							disabled={loading}
							className="inline-block rounded bg-gray-600 px-2 py-1 text-xs font-bold text-white transition-colors hover:bg-blue-700 disabled:opacity-50"
							data-testid="logout-button"
						>
							{loading ? 'Logging out...' : 'Logout'}
						</button>
					</div>
				</div>

				{/* Mobile Menu - only visible when open */}
				{isMobileMenuOpen &&
					createPortal(
						<>
							{/* Backdrop */}
							<div
								className="fixed inset-0 z-40 bg-black bg-opacity-50"
								onClick={() => setIsMobileMenuOpen(false)}
							/>

							{/* Mobile menu */}
							<div
								ref={menuRef}
								className="fixed left-0 right-0 top-10 z-50 border-t border-gray-800 bg-blue-900 shadow-lg md:hidden"
								aria-label="Mobile menu"
							>
								<div className="space-y-4 p-4">
									{/* User Info */}
									<div className="flex items-center gap-2 border-b border-gray-800 pb-3">
										<UserIcon size={16} className="text-gray-300" />
										<span className="text-sm text-white">
											{user?.username || 'User'}
										</span>
									</div>

									{/* Draft Mode Controls */}
									<div className="border-b border-gray-800 pb-3">
										<DraftModeManager isAdmin={true} />
									</div>

									{/* Debug Tools */}
									<div className="border-b border-gray-800 pb-3">
										<div className="mb-2 text-xs text-gray-300">
											Debug Tools
										</div>
										<div className="space-y-2">
											<MetadataFormatterClient
												userRoles={userRoleSlugs}
												metadata={metadata}
												path={path}
											/>
											<AdminDebugPanel userRoles={userRoleSlugs} />
										</div>
									</div>

									{/* Actions */}
									<div className="space-y-2 pt-1">
										<Link
											href="/admin"
											className="block w-full rounded bg-blue-600 px-3 py-2 text-center text-sm font-bold text-white transition-colors hover:bg-blue-700"
											onClick={() => setIsMobileMenuOpen(false)}
										>
											Admin
										</Link>
										<button
											onClick={(e) => {
												handleLogout(e);
												setIsMobileMenuOpen(false);
											}}
											disabled={loading}
											className="block w-full rounded bg-gray-600 px-3 py-2 text-center text-sm font-bold text-white transition-colors hover:bg-blue-700 disabled:opacity-50"
										>
											{loading ? 'Logging out...' : 'Logout'}
										</button>
									</div>
								</div>
							</div>
						</>,
						document.body,
					)}
			</div>
		</div>
	);
}
