import 'server-only';
import { headers as nextHeaders } from 'next/headers';

/**
 * Checks if the request is authorized to access detailed environment variables
 *
 * This function determines whether the current request should have access to
 * detailed environment variables based on:
 * 1. The current environment (development, preview, production)
 * 2. The presence of specific headers with correct values
 *
 * @returns {Promise<boolean>} True if the request should have access to detailed environment variables
 */
export async function isAuthorizedForEnvironmentVars(): Promise<boolean> {
	try {
		// In development or preview, always allow access
		const isDevelopmentOrPreview =
			process.env.VERCEL_TARGET_ENV === 'preview' ||
			process.env.VERCEL_TARGET_ENV === 'development';

		if (isDevelopmentOrPreview) {
			return true;
		}

		// In production, check for required headers
		const headersList = await nextHeaders();
		const showEnvHeader = headersList.get('x-show-environment-vars');
		const payloadSecretHeader = headersList.get('x-payload-secret');
		const payloadSecret = process.env.PAYLOAD_SECRET;

		// Only allow access if both headers are present and payload secret matches
		return (
			showEnvHeader === 'true' &&
			payloadSecretHeader === payloadSecret &&
			!!payloadSecret
		);
	} catch (error) {
		// If any error occurs, deny access for safety
		console.error('Error checking environment access:', error);
		return false;
	}
}

/**
 * Gets the level of environment variable access for the current request
 *
 * @returns {Promise<string>} 'full' for complete access, 'basic' for limited access
 */
export async function getEnvironmentAccessLevel(): Promise<'full' | 'basic'> {
	return (await isAuthorizedForEnvironmentVars()) ? 'full' : 'basic';
}
