import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
	isAuthorizedForEnvironmentVars,
	getEnvironmentAccessLevel,
} from './environmentAccess';

// Mock the next/headers module
vi.mock('next/headers', () => ({
	headers: vi.fn(),
}));

// Mock the server-only module
vi.mock('server-only', () => ({}));

// Import the mocked module to control its behavior
import { headers as nextHeaders } from 'next/headers';

describe('Environment Access Utilities', () => {
	// Mock headers implementation
	const mockHeaders = {
		get: vi.fn().mockReturnValue(null),
	};

	beforeEach(() => {
		// Reset mocks before each test
		vi.resetAllMocks();

		// Setup default mock for headers
		(nextHeaders as unknown as ReturnType<typeof vi.fn>).mockResolvedValue(
			mockHeaders,
		);
	});

	afterEach(() => {
		// Restore original environment after each test
		vi.unstubAllEnvs();
	});

	describe('isAuthorizedForEnvironmentVars', () => {
		it('should allow access in development environment', async () => {
			// Set VERCEL_TARGET_ENV to development
			vi.stubEnv('VERCEL_TARGET_ENV', 'development');
			// Clear other potentially conflicting env vars for this specific test
			vi.stubEnv('NODE_ENV', ''); // or 'development'
			vi.stubEnv('VERCEL_ENV', '');

			const result = await isAuthorizedForEnvironmentVars();

			expect(result).toBe(true);
			// Headers should not be checked in development
			expect(nextHeaders).not.toHaveBeenCalled();
		});

		it('should allow access in preview environment', async () => {
			// Set VERCEL_TARGET_ENV to preview
			vi.stubEnv('VERCEL_TARGET_ENV', 'preview');
			// NODE_ENV can be production, VERCEL_TARGET_ENV should take precedence
			vi.stubEnv('NODE_ENV', 'production');
			vi.stubEnv('VERCEL_ENV', ''); // Clear this as VERCEL_TARGET_ENV is used

			const result = await isAuthorizedForEnvironmentVars();

			expect(result).toBe(true);
			// Headers should not be checked in preview
			expect(nextHeaders).not.toHaveBeenCalled();
		});

		it('should deny access in production without headers', async () => {
			// Set NODE_ENV to production and VERCEL_TARGET_ENV to production
			vi.stubEnv('NODE_ENV', 'production');
			vi.stubEnv('VERCEL_ENV', 'production'); // Kept for consistency if other parts of system use it
			vi.stubEnv('VERCEL_TARGET_ENV', 'production');

			// Mock headers to return null
			mockHeaders.get.mockReturnValue(null);

			const result = await isAuthorizedForEnvironmentVars();

			expect(result).toBe(false);
			expect(nextHeaders).toHaveBeenCalled();
			expect(mockHeaders.get).toHaveBeenCalledWith('x-show-environment-vars');
			expect(mockHeaders.get).toHaveBeenCalledWith('x-payload-secret');
		});

		it('should allow access in production with correct headers', async () => {
			// Set NODE_ENV to production and VERCEL_TARGET_ENV to production
			vi.stubEnv('NODE_ENV', 'production');
			vi.stubEnv('VERCEL_ENV', 'production');
			vi.stubEnv('VERCEL_TARGET_ENV', 'production');
			vi.stubEnv('PAYLOAD_SECRET', 'test-secret');

			// Mock headers to return expected values
			mockHeaders.get.mockImplementation((name: string) => {
				if (name === 'x-show-environment-vars') return 'true';
				if (name === 'x-payload-secret') return 'test-secret';
				return null;
			});

			const result = await isAuthorizedForEnvironmentVars();

			expect(result).toBe(true);
			expect(nextHeaders).toHaveBeenCalled();
			expect(mockHeaders.get).toHaveBeenCalledWith('x-show-environment-vars');
			expect(mockHeaders.get).toHaveBeenCalledWith('x-payload-secret');
		});

		it('should deny access in production with incorrect payload secret', async () => {
			// Set NODE_ENV to production and VERCEL_TARGET_ENV to production
			vi.stubEnv('NODE_ENV', 'production');
			vi.stubEnv('VERCEL_ENV', 'production');
			vi.stubEnv('VERCEL_TARGET_ENV', 'production');
			vi.stubEnv('PAYLOAD_SECRET', 'test-secret');

			// Mock headers to return incorrect secret
			mockHeaders.get.mockImplementation((name: string) => {
				if (name === 'x-show-environment-vars') return 'true';
				if (name === 'x-payload-secret') return 'wrong-secret';
				return null;
			});

			const result = await isAuthorizedForEnvironmentVars();

			expect(result).toBe(false);
		});

		it('should deny access in production with missing show-env header', async () => {
			// Set NODE_ENV to production and VERCEL_TARGET_ENV to production
			vi.stubEnv('NODE_ENV', 'production');
			vi.stubEnv('VERCEL_ENV', 'production');
			vi.stubEnv('VERCEL_TARGET_ENV', 'production');
			vi.stubEnv('PAYLOAD_SECRET', 'test-secret');

			// Mock headers to return only payload secret
			mockHeaders.get.mockImplementation((name: string) => {
				if (name === 'x-payload-secret') return 'test-secret';
				return null;
			});

			const result = await isAuthorizedForEnvironmentVars();

			expect(result).toBe(false);
		});

		it('should handle errors gracefully', async () => {
			// Set NODE_ENV to production and VERCEL_TARGET_ENV to production
			vi.stubEnv('NODE_ENV', 'production');
			vi.stubEnv('VERCEL_ENV', 'production');
			vi.stubEnv('VERCEL_TARGET_ENV', 'production');

			// Mock headers to throw an error
			(nextHeaders as unknown as ReturnType<typeof vi.fn>).mockRejectedValue(
				new Error('Test error'),
			);

			const result = await isAuthorizedForEnvironmentVars();

			expect(result).toBe(false);
		});
	});

	describe('getEnvironmentAccessLevel', () => {
		it('should return "full" when authorized', async () => {
			// Set VERCEL_TARGET_ENV to development to ensure authorization
			vi.stubEnv('VERCEL_TARGET_ENV', 'development');
			vi.stubEnv('NODE_ENV', ''); // or 'development'
			vi.stubEnv('VERCEL_ENV', '');

			const result = await getEnvironmentAccessLevel();

			expect(result).toBe('full');
		});

		it('should return "basic" when not authorized', async () => {
			// Set NODE_ENV to production and VERCEL_TARGET_ENV to production
			vi.stubEnv('NODE_ENV', 'production');
			vi.stubEnv('VERCEL_ENV', 'production');
			vi.stubEnv('VERCEL_TARGET_ENV', 'production');

			// Mock headers to return null
			mockHeaders.get.mockReturnValue(null);

			const result = await getEnvironmentAccessLevel();

			expect(result).toBe('basic');
		});
	});
});
