import { type Page } from '@playwright/test';

/**
 * User roles for testing
 */
export type UserRole = 'admin' | 'editor' | 'web-developer' | 'authenticated';

/**
 * Test users with different roles
 */
export const TEST_USERS = {
	admin: {
		username: '<EMAIL>',
		password: 'Password123!',
		role: ['admin'],
	},
	editor: {
		username: '<EMAIL>',
		password: 'Password123!',
		role: ['editor'],
	},
	developer: {
		username: '<EMAIL>',
		password: 'Password123!',
		role: ['web-developer'],
	},
	regular: {
		username: '<EMAIL>',
		password: 'Password123!',
		role: ['authenticated'],
	},
};

/**
 * Helper function to set up PayloadCMS auto-login
 * This uses PayloadCMS's built-in auto-login feature for testing
 * https://payloadcms.com/docs/authentication/overview#auto-login
 *
 * Note: This approach is being used alongside the API Key authentication
 * configured in playwright.config.ts. The API Key provides base authentication,
 * while auto-login helps simulate different user roles.
 */
export async function setupAutoLogin(
	page: Page,
	userType: keyof typeof TEST_USERS,
): Promise<void> {
	const user = TEST_USERS[userType];

	// Set auto-login environment variables via localStorage
	// These will be picked up by PayloadCMS to automatically log in the user
	await page.evaluate(({ username, password }) => {
		localStorage.setItem('PAYLOAD_PUBLIC_AUTOLOGIN', 'true');
		localStorage.setItem('PAYLOAD_PUBLIC_AUTOLOGIN_USERNAME', username);
		localStorage.setItem('PAYLOAD_PUBLIC_AUTOLOGIN_PASSWORD', password);
	}, user);
}

/**
 * Helper function to clear auto-login settings
 */
export async function clearAutoLogin(page: Page): Promise<void> {
	await page.evaluate(() => {
		localStorage.removeItem('PAYLOAD_PUBLIC_AUTOLOGIN');
		localStorage.removeItem('PAYLOAD_PUBLIC_AUTOLOGIN_USERNAME');
		localStorage.removeItem('PAYLOAD_PUBLIC_AUTOLOGIN_PASSWORD');
	});
}
