import { test, expect } from '@playwright/test';
import { setupAutoLogin, clearAutoLogin } from '../utils/testAuth';

/**
 * FrontendAdminHeader Playwright Tests
 *
 * These tests verify the functionality of the FrontendAdminHeader component,
 * which provides debugging, metadata visualization, and draft mode controls.
 *
 * The tests cover:
 * 1. Visibility based on user authentication and roles
 * 2. Draft mode toggle functionality
 * 3. Metadata display functionality
 * 4. Debug panel functionality
 *
 * TODO: Fix authentication for Playwright tests. Currently, only the unauthenticated
 * user test is enabled. The API Key authentication in playwright.config.ts needs to be
 * properly integrated with the auto-login feature to test different user roles.
 */

// Base URL for tests
const baseUrl = process.env.VERCEL_PREVIEW_URL || 'https://local.weather.com';

// Test page with the FrontendAdminHeader component
const TEST_PAGE = '/';

test.describe('FrontendAdminHeader', () => {
	test.afterEach(async ({ page }) => {
		// Clear auto-login settings after each test
		await clearAutoLogin(page);
	});

	test('should not be visible for unauthenticated users', async ({ page }) => {
		// Navigate to the test page without authentication
		await page.goto(`${baseUrl}${TEST_PAGE}`);

		// Check that the admin header is not visible
		const adminHeader = page.getByTestId('admin-header');
		await expect(adminHeader).not.toBeVisible();
	});

	test.skip('should be visible for admin users', async ({ page }) => {
		// Set up auto-login for admin user
		await page.goto(baseUrl);
		await setupAutoLogin(page, 'admin');

		// Navigate to the test page
		await page.goto(`${baseUrl}${TEST_PAGE}`);

		// Wait for authentication to take effect
		await page.waitForTimeout(1000);

		// Check that the admin header is visible
		const adminHeader = page.getByTestId('admin-header');
		await expect(adminHeader).toBeVisible({ timeout: 10000 });

		// Check for admin-specific elements
		const showDebugButton = page.getByTestId('admin-debug-button');
		await expect(showDebugButton).toBeVisible({ timeout: 10000 });
	});

	test.skip('should show draft mode controls for editors', async ({ page }) => {
		// Set up auto-login for editor user
		await page.goto(baseUrl);
		await setupAutoLogin(page, 'editor');

		// Navigate to the test page
		await page.goto(`${baseUrl}${TEST_PAGE}`);

		// Wait for authentication to take effect
		await page.waitForTimeout(1000);

		// Check for draft mode button
		const viewDraftButton = page.getByTestId('admin-draft-mode-button');
		await expect(viewDraftButton).toBeVisible({ timeout: 10000 });
	});

	test.skip('should toggle draft mode when clicking draft mode buttons', async ({
		page,
	}) => {
		// Set up auto-login for admin user
		await page.goto(baseUrl);
		await setupAutoLogin(page, 'admin');

		// Navigate to the test page
		await page.goto(`${baseUrl}${TEST_PAGE}`);

		// Wait for authentication to take effect
		await page.waitForTimeout(1000);

		// Find and wait for the "View Draft" button
		const viewDraftButton = page.getByTestId('admin-draft-mode-button');
		await expect(viewDraftButton).toBeVisible({ timeout: 10000 });

		// Click the "View Draft" button
		await viewDraftButton.click();

		// Wait for the page to refresh and draft mode to be enabled
		await page.waitForLoadState('networkidle');

		// Check that draft mode indicator is visible
		const draftModeIndicator = page.getByTestId('admin-draft-indicator');
		await expect(draftModeIndicator).toBeVisible({ timeout: 10000 });

		// Check that "Exit Draft" button is visible
		const exitDraftButton = page.getByTestId('admin-exit-draft-button');
		await expect(exitDraftButton).toBeVisible({ timeout: 10000 });

		// Click the "Exit Draft" button
		await exitDraftButton.click();

		// Wait for the page to refresh and draft mode to be disabled
		await page.waitForLoadState('networkidle');

		// Check that draft mode indicator is no longer visible
		await expect(draftModeIndicator).not.toBeVisible();

		// Check that "View Draft" button is visible again
		await expect(viewDraftButton).toBeVisible({ timeout: 10000 });
	});

	test.skip('should open metadata panel when clicking metadata button', async ({
		page,
	}) => {
		// Set up auto-login for admin user
		await page.goto(baseUrl);
		await setupAutoLogin(page, 'admin');

		// Navigate to a page with metadata
		// For this test, we'll assume the home page has metadata
		await page.goto(`${baseUrl}${TEST_PAGE}`);

		// Wait for authentication to take effect
		await page.waitForTimeout(1000);

		// Find and click the metadata button
		const metadataButton = page.getByTestId('admin-metadata-button');

		// Check if the button exists before proceeding
		const buttonVisible = await metadataButton.isVisible().catch(() => false);
		if (!buttonVisible) {
			test.skip();
			return;
		}

		await metadataButton.click();

		// Check that the metadata panel is visible
		const metadataPanel = page.getByTestId('admin-metadata-panel');
		await expect(metadataPanel).toBeVisible({ timeout: 10000 });

		// Check for common metadata fields
		const titleSection = page.locator('h4').filter({ hasText: 'Title' });
		await expect(titleSection).toBeVisible({ timeout: 10000 });

		// Close the panel
		const closeButton = page.getByTestId('admin-metadata-close-button');
		await closeButton.click();

		// Check that the panel is closed
		await expect(metadataPanel).not.toBeVisible({ timeout: 10000 });
	});

	test.skip('should open debug panel when clicking Show Debug button', async ({
		page,
	}) => {
		// Set up auto-login for developer user
		await page.goto(baseUrl);
		await setupAutoLogin(page, 'developer');

		// Navigate to the test page
		await page.goto(`${baseUrl}${TEST_PAGE}`);

		// Wait for authentication to take effect
		await page.waitForTimeout(1000);

		// Find and wait for the Show Debug button
		const showDebugButton = page.getByTestId('admin-debug-button');
		await expect(showDebugButton).toBeVisible({ timeout: 10000 });

		// Click the Show Debug button
		await showDebugButton.click();

		// Check that the debug panel is visible
		const debugPanel = page.getByTestId('admin-debug-panel');
		await expect(debugPanel).toBeVisible({ timeout: 10000 });

		// Check for debug panel tabs
		const allTab = page.getByTestId('admin-debug-tab-all');
		await expect(allTab).toBeVisible({ timeout: 10000 });

		const contextTab = page.getByTestId('admin-debug-tab-context');
		await expect(contextTab).toBeVisible({ timeout: 10000 });

		const pageTab = page.getByTestId('admin-debug-tab-page');
		await expect(pageTab).toBeVisible({ timeout: 10000 });

		// Test tab switching
		await contextTab.click();

		// Close the panel
		const closeButton = page.getByTestId('admin-debug-close-button');
		await closeButton.click();

		// Check that the panel is closed
		await expect(debugPanel).not.toBeVisible({ timeout: 10000 });
	});

	test.skip('should toggle debug panel with keyboard shortcut', async ({
		page,
	}) => {
		// Set up auto-login for developer user
		await page.goto(baseUrl);
		await setupAutoLogin(page, 'developer');

		// Navigate to the test page
		await page.goto(`${baseUrl}${TEST_PAGE}`);

		// Wait for authentication to take effect
		await page.waitForTimeout(1000);

		// Use keyboard shortcut to open debug panel (Ctrl+Shift+D)
		await page.keyboard.press('Control+Shift+D');

		// Check that the debug panel is visible
		const debugPanel = page.getByTestId('admin-debug-panel');
		await expect(debugPanel).toBeVisible({ timeout: 10000 });

		// Use keyboard shortcut again to close the panel
		await page.keyboard.press('Control+Shift+D');

		// Check that the panel is closed
		await expect(debugPanel).not.toBeVisible({ timeout: 10000 });
	});

	test.skip('should filter headers in debug panel', async ({ page }) => {
		// Set up auto-login for developer user
		await page.goto(baseUrl);
		await setupAutoLogin(page, 'developer');

		// Navigate to the test page
		await page.goto(`${baseUrl}${TEST_PAGE}`);

		// Wait for authentication to take effect
		await page.waitForTimeout(1000);

		// Find and wait for the Show Debug button
		const showDebugButton = page.getByTestId('admin-debug-button');
		await expect(showDebugButton).toBeVisible({ timeout: 10000 });

		// Open debug panel
		await showDebugButton.click();

		// Go to Headers tab
		const headersTab = page.getByTestId('admin-debug-tab-headers');
		await expect(headersTab).toBeVisible({ timeout: 10000 });
		await headersTab.click();

		// Check that the search input is visible
		const searchInput = page.getByTestId('admin-headers-search');
		await expect(searchInput).toBeVisible({ timeout: 10000 });

		// Type a search term
		await searchInput.fill('user-agent');

		// Check that the filtered results show only matching headers
		const filteredHeaders = page
			.locator('div')
			.filter({ hasText: /user-agent/i });
		await expect(filteredHeaders).toBeVisible({ timeout: 10000 });

		// Clear the search
		await searchInput.clear();

		// Close the panel
		const closeButton = page.getByTestId('admin-debug-close-button');
		await closeButton.click();
	});

	test.skip('should show limited functionality for regular users', async ({
		page,
	}) => {
		// Set up auto-login for regular user
		await page.goto(baseUrl);
		await setupAutoLogin(page, 'regular');

		// Navigate to the test page
		await page.goto(`${baseUrl}${TEST_PAGE}`);

		// Wait for authentication to take effect
		await page.waitForTimeout(1000);

		// Check that the admin header is not visible for regular users
		const adminHeader = page.getByTestId('admin-header');
		await expect(adminHeader).not.toBeVisible({ timeout: 10000 });

		// If the app is in draft mode, regular users might see the draft mode indicator
		// This is an optional check based on your application's behavior
		const draftModeIndicator = page.getByTestId('admin-draft-indicator');
		const hasDraftMode = (await draftModeIndicator.count()) > 0;

		if (hasDraftMode) {
			// If in draft mode, check that exit draft button is visible
			const exitDraftButton = page.getByTestId('admin-exit-draft-button');
			await expect(exitDraftButton).toBeVisible({ timeout: 10000 });
		}
	});
});
