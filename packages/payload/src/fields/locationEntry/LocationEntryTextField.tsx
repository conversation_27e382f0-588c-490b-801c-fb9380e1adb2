'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useField } from '@payloadcms/ui';
import type { TextFieldClientComponent, TextFieldClientProps } from 'payload';
import { getLocationsByQuery } from '@repo/dal/locations/search';
import type { LocationSearchItem } from '@repo/dal/locations/types';
import useSWR from 'swr';

export const LocationEntryTextField: TextFieldClientComponent = (
	props: TextFieldClientProps,
) => {
	const { value, setValue } = useField({ path: props.path });
	const [searchQuery, setSearchQuery] = useState('');
	const [showDropdown, setShowDropdown] = useState(false);
	const dropdownRef = useRef<HTMLDivElement>(null);
	const debouncedQueryRef = useRef<string>('');

	// Use SWR for data fetching
	const locationFetcher = () => {
		if (debouncedQueryRef.current.length < 2) return [];
		return getLocationsByQuery(debouncedQueryRef.current, 'en-US');
	};

	const {
		data: searchResults = [],
		error,
		isLoading,
	} = useSWR(
		debouncedQueryRef.current.length >= 2
			? ['location-search', debouncedQueryRef.current]
			: null,
		locationFetcher,
		{
			revalidateOnFocus: false,
			dedupingInterval: 60000, // 1 minute
		},
	);

	// Debounce the search query
	useEffect(() => {
		if (searchQuery.length < 2) {
			setShowDropdown(false);
			return;
		}

		const handler = setTimeout(() => {
			debouncedQueryRef.current = searchQuery;
			setShowDropdown(true);
		}, 300);

		return () => clearTimeout(handler);
	}, [searchQuery]);

	// Handle input change
	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const query = e.target.value;
		setSearchQuery(query);
	};

	// Handle result selection
	const handleSelectResult = (result: LocationSearchItem) => {
		setValue(`${result.latitude},${result.longitude}`);
		setShowDropdown(false);
	};

	// Close dropdown when clicking outside
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				dropdownRef.current &&
				!dropdownRef.current.contains(event.target as Node)
			) {
				setShowDropdown(false);
			}
		};

		document.addEventListener('mousedown', handleClickOutside);
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, []);

	// Dropdown component
	const renderDropdown = () => {
		if (!showDropdown) return null;

		return (
			<div
				className="absolute z-10 max-h-[300px] w-full overflow-y-auto rounded-md border border-gray-200 bg-white shadow-md"
				ref={dropdownRef}
			>
				{isLoading ? (
					<div className="p-3 text-center">Loading...</div>
				) : error ? (
					<div className="p-3 text-center text-red-500">
						Error loading locations
					</div>
				) : searchResults.length > 0 ? (
					searchResults.map((result) => (
						<div
							key={result.placeId}
							className={`cursor-pointer border-b border-gray-100 bg-black p-3 hover:bg-gray-50`}
							onClick={() => handleSelectResult(result)}
						>
							<div className="text-sm text-gray-600">
								{result.displayName}, {result.adminDistrict}
							</div>
						</div>
					))
				) : (
					<div className="p-3 text-center text-gray-500">
						No locations found
					</div>
				)}
			</div>
		);
	};

	return (
		<div className="relative">
			<label className="field-label">Location Picked: {value as string}</label>
			<input
				type="text"
				className="w-full rounded-md border border-gray-300 p-2.5 text-base"
				value={searchQuery}
				onChange={handleInputChange}
				placeholder="Query a location"
			/>
			{renderDropdown()}
		</div>
	);
};
