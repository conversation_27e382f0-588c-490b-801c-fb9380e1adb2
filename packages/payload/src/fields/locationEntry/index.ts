import type { Field } from 'payload';

export const locationProvider: Field = {
	name: 'locationProvider',
	label: 'Location Provider',
	type: 'radio',
	defaultValue: 'geolocation',
	admin: {
		layout: 'horizontal',
	},
	options: [
		{
			label: "Use User's Location",
			value: 'geolocation',
		},
		{
			label: 'Use Specified Location',
			value: 'specified',
		},
		{
			label: 'Use Page Location',
			value: 'page',
		},
	],
	required: true,
};

export const locationEntry: Field = {
	name: 'locationEntry',
	label: 'Specify Location',
	type: 'text',
	required: true,
	admin: {
		condition: (_, siblingData) =>
			siblingData?.locationProvider === 'specified',
		description: 'Enter a location to display weather information for',
		components: {
			Field: '@repo/payload/fields/components#LocationEntryTextField',
		},
	},
	// hooks: {
	// 	beforeChange: [normalizeTweetURL],
	// },
	// validate: validateEntryMethod,
};
