import { Field } from 'payload';
import {
	validConfigs,
	validNames,
} from '../../collections/Pages/utils/validConfigs';

/**
 * Field definition for context parameter overrides
 * This field allows blocks to have context parameter overrides
 */
export const contextParameterOverrides: Field = {
	name: 'contextParameterOverrides',
	type: 'group',
	admin: {
		description: 'Configure context parameter overrides for this block',
	},
	fields: [
		{
			name: 'enabled',
			type: 'checkbox',
			defaultValue: false,
			admin: {
				description: 'Enable context parameter overrides for this block',
			},
		},
		{
			name: 'parameters',
			type: 'group',
			admin: {
				description:
					'Configure context parameters for this block. Multiple values for the same parameter create an OR relationship.',
				condition: (data, siblingData) => siblingData?.enabled === true,
			},
			fields: (() => {
				// ignore pageKey, as that's handled by the asset name
				const names = validNames.filter((name) => name !== 'pageKey');
				return names.map((name: string) => {
					const validValues = validConfigs[name] || [];

					// For parameters that can have any value (like pageKey), use a text field
					if (validValues.length === 0) {
						return {
							name,
							type: 'text',
							admin: {
								description: `Value for ${name} parameter`,
							},
						} as const;
					}

					// For parameters with predefined values, use a select field with multiple option
					return {
						name,
						type: 'select',
						hasMany: true, // This enables OR relationship by allowing multiple selections
						admin: {
							description: `Value(s) for ${name} parameter. Multiple selections create an OR relationship.`,
						},
						options: validValues.map((value: string) => ({
							label: value,
							value,
						})),
					} as const;
				});
			})(),
		},
		{
			name: 'visibilityRule',
			type: 'select',
			defaultValue: 'show_always',
			options: [
				{
					label: 'Always Show',
					value: 'show_always',
				},
				{
					label: 'Show When Matching',
					value: 'show_matching',
				},
				{
					label: 'Hide When Matching',
					value: 'hide_matching',
				},
			],
			admin: {
				description: 'Determine when this block should be visible',
				condition: (data, siblingData) => siblingData?.enabled === true,
			},
		},
	],
};
