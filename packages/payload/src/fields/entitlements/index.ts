import type { SelectField } from 'payload';

export const entitlementsField: SelectField = {
	name: 'entitlements',
	type: 'select',
	label: 'Entitlements',
	localized: true,
	hasMany: true,
	options: [
		{ label: 'Platform All Free', value: 'platform-all-free' },
		{ label: 'Platform All Premium', value: 'platform-all-premium' },
		{ label: 'Platform All Standard', value: 'platform-all-standard' },
		{ label: 'Platform All Freemium', value: 'platform-all-freemium' },
	],
	defaultValue: [
		'platform-all-free',
		'platform-all-standard',
		'platform-all-premium',
	],
	admin: {
		description: 'Select entitlements for the content.',
	},
};
