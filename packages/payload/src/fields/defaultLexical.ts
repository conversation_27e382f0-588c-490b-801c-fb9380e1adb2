import {
	Config,
	type TextFieldSingleValidation,
	CollectionSlug,
} from 'payload';
import {
	BoldFeature,
	BlocksFeature,
	ItalicFeature,
	LinkFeature,
	ParagraphFeature,
	lexicalEditor,
	UnderlineFeature,
	ChecklistFeature,
	HeadingFeature,
	type LinkFields,
	OrderedListFeature,
	UnorderedListFeature,
} from '@payloadcms/richtext-lexical';
import WeatherLocationTickerFeature from '../lexical/features/WeatherLocationTicker';
import { allowedBlocks } from '../collections/Articles/fields/blocks';

export const enabledCollections: CollectionSlug[] = ['pages', 'articles'];

export const defaultLexical: Config['editor'] = lexicalEditor({
	features: () => {
		return [
			HeadingFeature({
				enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'],
			}),
			BlocksFeature({
				blocks: allowedBlocks ?? [],
			}),
			ParagraphFeature(),
			UnderlineFeature(),
			OrderedListFeature(),
			UnorderedListFeature(),
			ChecklistFeature(),
			BoldFeature(),
			ItalicFeature(),
			WeatherLocationTickerFeature(),
			LinkFeature({
				enabledCollections,
				fields: ({ defaultFields }) => {
					const defaultFieldsWithoutUrl = defaultFields.filter((field) => {
						if ('name' in field && field.name === 'url') return false;
						return true;
					});
					return [
						...defaultFieldsWithoutUrl,
						{
							name: 'url',
							type: 'text',
							admin: {
								condition: (_data, siblingData) =>
									siblingData?.linkType !== 'internal',
							},
							label: ({ t }) => t('fields:enterURL'),
							required: true,
							validate: ((value, options) => {
								if (
									(options?.siblingData as LinkFields)?.linkType === 'internal'
								) {
									return true; // no validation needed, as no url should exist for internal links
								}
								return value ? true : 'URL is required';
							}) as TextFieldSingleValidation,
						},
					];
				},
			}),
		];
	},
});
