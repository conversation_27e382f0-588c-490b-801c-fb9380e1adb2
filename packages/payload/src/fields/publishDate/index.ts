import type { CheckboxField, TextField } from 'payload';

import { setPublishDateHook } from '../../hooks/setPublishDate';

type Overrides = {
	publishDateOverrides?: Partial<TextField>;
	checkboxOverrides?: Partial<CheckboxField>;
};

type PublishDate = (
	fieldToUse?: string,
	overrides?: Overrides,
) => [TextField, CheckboxField];

export const publishDateField: PublishDate = (
	fieldToUse = 'publishDate',
	overrides = {},
) => {
	const { publishDateOverrides, checkboxOverrides } = overrides;

	const checkBoxField: CheckboxField = {
		name: 'publishDateLock',
		type: 'checkbox',
		defaultValue: true,
		admin: {
			hidden: true,
			position: 'sidebar',
		},
		...checkboxOverrides,
	};

	// @ts-expect-error - ts mismatch Partial<TextField> with TextField
	const publishDateField: TextField = {
		name: 'publishDate',
		type: 'text',
		localized: false,
		index: true,
		label: 'Publish Date',
		// Some hooks require a value on document creation
		defaultValue: new Date().toISOString(),
		...(publishDateOverrides || {}),
		hooks: {
			// Kept this in for hook or API based updates
			beforeValidate: [setPublishDateHook(fieldToUse)],
		},
		admin: {
			...(publishDateOverrides?.admin || {}),
			position: 'sidebar',
			components: {
				Field: {
					path: '@repo/payload/fields/components#PublishDateComponent',
					clientProps: {
						fieldToUse,
						checkboxFieldPath: checkBoxField.name,
					},
				},
			},
		},
	};

	return [publishDateField, checkBoxField];
};
