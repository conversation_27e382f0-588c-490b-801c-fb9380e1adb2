import { Tag } from '@repo/payload/payload-types';
import { hasLength } from '@repo/utils/hasLength';
import { RelationshipField } from 'payload';

import type { FilterOptions, PayloadRequest, Where } from 'payload';

export const categoryFilterOptions: FilterOptions<Tag> = () => {
	// Categories are top-level categoryTags (no parent)
	const category: Where = {
		and: [
			{
				type: {
					equals: 'categoryTags',
				},
			},
			{
				parent: {
					exists: false,
				},
			},
		],
	};
	return category;
};

export const topicFilterOptions: FilterOptions<Tag> = () => {
	// Topics are child categoryTags (have a parent)
	const topic: Where = {
		and: [
			{
				type: {
					equals: 'categoryTags',
				},
			},
			{
				parent: {
					exists: true,
				},
			},
		],
	};
	return topic;
};

export const defaultContentTags: RelationshipField = {
	name: 'tags',
	label: 'Tags',
	type: 'relationship',
	relationTo: 'tags',
	hasMany: true,
	required: false,
	localized: true,
	filterOptions: () => {
		return {
			type: {
				in: ['analyticsTag', 'contentTags', 'entitlement', 'region', 'state'],
			},
		};
	},
	admin: {
		position: 'sidebar',
		allowCreate: true,
		sortOptions: 'name',
	},
};

const setDefaultCategory = async ({ req }: { req: PayloadRequest }) => {
	if (!req?.payload) {
		return;
	}

	const response = await req.payload.find({
		collection: 'tags',
		req,
		limit: 1,
		where: {
			and: [
				{
					slug: {
						equals: 'news',
					},
				},
				{
					type: {
						equals: 'categoryTags',
					},
				},
				{
					parent: {
						exists: false,
					},
				},
			],
		},
	});

	if (!hasLength(response?.docs)) {
		return;
	}

	const newsTag = response.docs?.[0];

	if (!newsTag || !newsTag.id) {
		return;
	}

	return {
		relationTo: 'tags',
		value: newsTag.id,
	};
};

export const categoryTag: RelationshipField = {
	name: 'category',
	label: 'Category',
	type: 'relationship',
	relationTo: ['tags'],
	hasMany: false,
	required: true,
	localized: true,
	defaultValue: setDefaultCategory,
	admin: {
		position: 'sidebar',
		allowCreate: false,
	},
	filterOptions: categoryFilterOptions,
};

export const topicTag: RelationshipField = {
	name: 'topic',
	label: 'Topic',
	type: 'relationship',
	relationTo: ['tags'],
	hasMany: false,
	required: false,
	localized: true,
	admin: {
		allowCreate: false,
		position: 'sidebar',
	},
	filterOptions: topicFilterOptions,
};
