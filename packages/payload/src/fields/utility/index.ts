import type {
	CollapsibleField,
	Field,
	GroupField,
	Tab,
	TabsField,
} from 'payload';

export interface CollapsibleFactory {
	(
		fields: Field[],
		label: string,
		options?: Omit<CollapsibleField, 'type' | 'fields' | 'label'>,
	): CollapsibleField;
}

export const createCollapsible: CollapsibleFactory = (
	fields,
	label,
	options = {},
) => ({
	type: 'collapsible',
	label,
	fields: [...fields],
	...options,
});

export interface GroupFactory {
	(
		fields: Field[],
		name: string,
		label: string,
		options?: Omit<
			GroupField,
			'type' | 'fields' | 'label' | 'name' | 'interfaceName'
		>,
	): GroupField;
}

export const createGroup: GroupFactory = (
	fields,
	name,
	label,
	options = {},
) => ({
	type: 'group',
	name,
	label,
	interfaceName: name,
	fields: [...fields],
	...options,
});

export interface TabsFactory {
	(tabs: Tab[], options?: Omit<TabsField, 'type' | 'tabs'>): TabsField;
}

export const createTabs: TabsFactory = (tabs, options = {}) => ({
	type: 'tabs',
	tabs: [...tabs],
	...options,
});
