import { Article } from '@repo/payload/payload-types';
import { CollectionBeforeChangeHook } from 'payload';

export const setUpdatedBy: CollectionBeforeChangeHook<Article> = async ({
	data,
	req,
	collection,
	operation,
}) => {
	if (operation !== 'update') return;

	// dont need to set updatedBy if the user is the one updating the document
	if (data.coreMetadata?.updatedBy === req.user?.username) return;

	if (collection.slug !== 'articles') return;

	if (!data.coreMetadata) return;

	data.coreMetadata = {
		...data.coreMetadata,
		updatedBy: req.user?.username,
	};

	return data;
};
