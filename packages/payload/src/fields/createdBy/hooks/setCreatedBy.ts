import { Article } from '@repo/payload/payload-types';
import { CollectionBeforeChangeHook } from 'payload';

export const setCreatedBy: CollectionBeforeChangeHook<Article> = async ({
	data,
	req,
	collection,
	operation,
}) => {
	if (operation !== 'create') return;

	if (collection.slug !== 'articles') return;

	if (!data.coreMetadata) return;

	data.coreMetadata = {
		createdBy: req.user?.username,
	};

	return data;
};
