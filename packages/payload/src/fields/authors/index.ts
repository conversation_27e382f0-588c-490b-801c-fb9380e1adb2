import {
	isAdmin,
	isAuthor as checkIsAuthor,
} from '@repo/payload/configs/access';
import { isValidUrl } from '@repo/payload/utils/isValidUrl';
import type {
	ArrayField,
	CheckboxField,
	JoinField,
	RelationshipField,
	TextareaField,
	TextField,
	GroupField,
	FilterOptions,
	FilterOptionsProps,
	Where,
} from 'payload';

export const authorsFilterOptions: FilterOptions = (
	_options: FilterOptionsProps,
) => {
	const authors: Where = {
		isAuthor: {
			equals: true,
		},
	};
	return authors;
};

export const authors: RelationshipField = {
	name: 'authors',
	type: 'relationship',
	hasMany: true,
	index: false,
	relationTo: 'users',
	filterOptions: authorsFilterOptions,
};

export const isAuthor: CheckboxField = {
	name: 'isAuthor',
	label: 'Is Author',
	type: 'checkbox',
	index: false,
	defaultValue: false,
	access: {
		update: ({ req }) => {
			return isAdmin(req.user);
		},
	},
};

export const firstName: TextField = {
	name: 'firstName',
	label: 'First Name',
	type: 'text',
	index: false,
	required: true,
	admin: {
		condition: (_data) => checkIsAuthor(_data),
	},
};

export const lastName: TextField = {
	name: 'lastName',
	label: 'Last Name',
	type: 'text',
	index: false,
	required: true,
	admin: {
		condition: (_data) => checkIsAuthor(_data),
	},
};

// TODO: This will need to be rich text
export const bio: TextareaField = {
	name: 'bio',
	label: 'Bio',
	index: false,
	type: 'textarea',
	admin: {
		condition: (_data) => checkIsAuthor(_data),
	},
};

export const profilePicture: RelationshipField = {
	name: 'profilePicture',
	label: 'Profile Picture',
	type: 'relationship',
	index: false,
	required: false, // Changed from true to false to allow creation without a profile picture
	relationTo: 'images',
	admin: {
		condition: (_data) => checkIsAuthor(_data),
	},
};

export const bioUrl: TextField = {
	name: 'bioUrl',
	label: 'Bio URL',
	type: 'text',
	index: false,
	required: true,
	validate: (data) => {
		if (!data || !isValidUrl(data)) {
			return 'The Bio Url provided is invalid';
		}
		return true;
	},
	admin: {
		description:
			'Please enter in a valid bio url. https://weather.com/bios/my-bio-url',
		condition: (_data) => checkIsAuthor(_data),
	},
};

export const socialMediaProfiles: ArrayField = {
	name: 'socialMediaProfiles',
	label: 'Social Media Profiles',
	type: 'array',
	index: false,
	fields: [
		{
			name: 'platform',
			type: 'text',
			label: 'Platform',
			required: true,
		},
		{
			name: 'url',
			type: 'text',
			label: 'URL',
			required: true,
		},
	],
	admin: {
		condition: (_data) => checkIsAuthor(_data),
	},
};

export const authoredContent: JoinField = {
	name: 'authoredContent',
	label: 'Authored Content',
	type: 'join',
	collection: 'articles',
	on: 'authors',
	hasMany: true,
	admin: {
		allowCreate: false,
		condition: (_data) => checkIsAuthor(_data),
	},
};

export const authorData: GroupField = {
	name: 'authorData',
	label: 'Author Data',
	type: 'group',
	index: false,
	fields: [
		firstName,
		lastName,
		bio,
		bioUrl,
		profilePicture,
		socialMediaProfiles,
		authoredContent,
	],
	admin: {
		condition: (_data) => checkIsAuthor(_data),
	},
};
