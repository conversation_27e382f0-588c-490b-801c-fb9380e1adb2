import React from 'react';
import type { JSXConverters } from '@payloadcms/richtext-lexical/react';
import type { SerializedWeatherLocationTickerNode } from '../nodes/WeatherLocationTickerNode';
import { WeatherLocationTickerClientComponent } from '../components';

export const WeatherLocationServerJSXConverter: JSXConverters<SerializedWeatherLocationTickerNode> =
	{
		weatherLocationTicker: ({
			node,
		}: {
			node: SerializedWeatherLocationTickerNode;
		}) => {
			// Use the client component for rich rendering
			return <WeatherLocationTickerClientComponent node={node} />;
		},
	};
