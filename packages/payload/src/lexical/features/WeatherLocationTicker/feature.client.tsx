'use client';

import { createClientFeature } from '@payloadcms/richtext-lexical/client';
import { WeatherLocationTickerNode } from './nodes/WeatherLocationTickerNode';
import { WeatherLocationTickerPlugin } from './plugins';
import { WeatherLocationTickerToggleButton } from './toolbar/WeatherLocationTickerToggleButton';
import { toolbarFormatGroupWithItems } from '@payloadcms/richtext-lexical/client';
import { CloudSunRain } from 'lucide-react';

export const WeatherLocationTickerClientFeature = createClientFeature({
	nodes: [WeatherLocationTickerNode],
	plugins: [
		{
			Component: WeatherLocationTickerPlugin,
			position: 'normal',
		},
	],
	toolbarFixed: {
		groups: [
			toolbarFormatGroupWithItems([
				{
					key: 'weatherLocationTickerToggle',
					label: 'Weather Location Ticker',
					ChildComponent: () => <CloudSunRain className="h-5 w-5" />,
					Component: WeatherLocationTickerToggleButton,
				},
			]),
		],
	},
});

export default WeatherLocationTickerClientFeature;
