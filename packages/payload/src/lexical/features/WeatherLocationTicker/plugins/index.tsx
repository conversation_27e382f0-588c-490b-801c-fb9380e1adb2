'use client';

import { useEffect } from 'react';
import { useLexicalComposerContext } from '@payloadcms/richtext-lexical/lexical/react/LexicalComposerContext';
import { mergeRegister } from '@payloadcms/richtext-lexical/lexical/utils';
import type { PluginComponent } from '@payloadcms/richtext-lexical';
import {
	$getSelection,
	$isRangeSelection,
	COMMAND_PRIORITY_EDITOR,
	LexicalCommand,
	createCommand,
} from '@payloadcms/richtext-lexical/lexical';
import { $createWeatherLocationTickerNode } from '../nodes/WeatherLocationTickerNode';

export const WEATHER_LOCATION_COMMAND: LexicalCommand<string> = createCommand();

export const WeatherLocationTickerPlugin: PluginComponent = () => {
	const [editor] = useLexicalComposerContext();

	useEffect(() => {
		if (!editor) {
			return;
		}
		// Handle the custom command to create a weather location node
		const handleWeatherLocationTransform = (locationName: string): void => {
			editor.update(() => {
				const selection = $getSelection();
				if (!$isRangeSelection(selection)) {
					return;
				}

				const weatherLocationNode =
					$createWeatherLocationTickerNode(locationName);
				selection.insertNodes([weatherLocationNode]);
			});
		};

		// Register command listener
		const commandListener = editor.registerCommand<string>(
			WEATHER_LOCATION_COMMAND,
			(locationName) => {
				handleWeatherLocationTransform(locationName);
				return true;
			},
			COMMAND_PRIORITY_EDITOR,
		);

		return () => {
			mergeRegister(commandListener);
		};
	}, [editor]);

	return null;
};
