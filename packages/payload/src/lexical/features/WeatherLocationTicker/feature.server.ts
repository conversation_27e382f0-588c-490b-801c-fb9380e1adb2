import { createServerFeature, createNode } from '@payloadcms/richtext-lexical';
import type { SerializedWeatherLocationTickerNode } from './nodes/WeatherLocationTickerNode';

/**
 * Server-side registration for the WeatherLocationNode
 * This is used by PayloadCMS to properly handle the node during serialization/deserialization
 */
export const weatherLocationTickerFeature = createServerFeature({
	feature: {
		nodes: [
			createNode({
				converters: {
					html: {
						converter: (args) => {
							const node =
								args.node as unknown as SerializedWeatherLocationTickerNode;
							const { locationName, placeId, geocode } = node;

							// Create a link to the location's weather page if we have a placeId
							if (placeId) {
								return `<a href="/weather/today/l/${placeId}" class="weather-location-ticker" data-location-name="${locationName}" data-place-id="${placeId}" ${geocode ? `data-geocode="${geocode}"` : ''}>${locationName}</a>`;
							}

							// Otherwise just render the location name
							return `<span class="weather-location-ticker" data-location-name="${locationName}" ${geocode ? `data-geocode="${geocode}"` : ''}>${locationName}</span>`;
						},
						nodeTypes: ['weatherLocationTicker'],
					},
				},
				node: {
					type: 'weatherLocationTicker',
					importJSON: (serializedNode: SerializedWeatherLocationTickerNode) => {
						return serializedNode;
					},
					getType: () => 'weatherLocationTicker',
				},
			}),
		],
		ClientFeature:
			'@repo/payload/lexical/features/WeatherLocationTicker/feature.client#WeatherLocationTickerClientFeature',
	},
	key: 'weatherLocationTicker',
});

export default weatherLocationTickerFeature;
