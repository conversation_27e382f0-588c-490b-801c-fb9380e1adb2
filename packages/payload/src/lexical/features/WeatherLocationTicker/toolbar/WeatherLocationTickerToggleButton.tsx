'use client';

import React, { useCallback, useEffect, useState } from 'react';
import { useLexicalComposerContext } from '@payloadcms/richtext-lexical/lexical/react/LexicalComposerContext';
import {
	$getSelection,
	$isRangeSelection,
	$isNodeSelection,
	$createTextNode,
	COMMAND_PRIORITY_EDITOR,
	createCommand,
	LexicalCommand,
} from '@payloadcms/richtext-lexical/lexical';
import { $isWeatherLocationTickerNode } from '../nodes/WeatherLocationTickerNode';
import { WEATHER_LOCATION_COMMAND } from '../plugins';
import { CloudSunRain, MapPinX } from 'lucide-react';

// Command for removing weather location formatting
export const REMOVE_WEATHER_LOCATION_COMMAND: LexicalCommand<void> =
	createCommand();

export function WeatherLocationTickerToggleButton(): React.ReactElement | null {
	const [editor] = useLexicalComposerContext();
	const [isWeatherLocationSelected, setIsWeatherLocationSelected] =
		useState(false);
	const [hasValidSelection, setHasValidSelection] = useState(false);

	// Check selection state on editor updates
	useEffect(() => {
		return editor.registerUpdateListener(({ editorState }) => {
			editorState.read(() => {
				const selection = $getSelection();

				// Check if a weather location node is selected
				if ($isNodeSelection(selection)) {
					const nodes = selection.getNodes();
					if (nodes.length === 1 && $isWeatherLocationTickerNode(nodes[0])) {
						setIsWeatherLocationSelected(true);
						setHasValidSelection(true);
						return;
					}
				}

				// Check if there's valid text selected for conversion
				if ($isRangeSelection(selection) && !selection.isCollapsed()) {
					const selectedText = selection.getTextContent();
					if (selectedText && selectedText.trim() !== '') {
						setHasValidSelection(true);
						setIsWeatherLocationSelected(false);
						return;
					}
				}

				// No valid selection
				setHasValidSelection(false);
				setIsWeatherLocationSelected(false);
			});
		});
	}, [editor]);

	// Register command to remove weather location formatting
	useEffect(() => {
		return editor.registerCommand(
			REMOVE_WEATHER_LOCATION_COMMAND,
			() => {
				editor.update(() => {
					const selection = $getSelection();
					if ($isNodeSelection(selection)) {
						const nodes = selection.getNodes();
						if (nodes.length === 1 && $isWeatherLocationTickerNode(nodes[0])) {
							const weatherLocationNode = nodes[0];
							const textContent = weatherLocationNode.getTextContent();

							// Replace the weather location node with plain text
							const textNode = $createTextNode(textContent);
							weatherLocationNode.replace(textNode);
							return true;
						}
					}
					return false;
				});
				return true;
			},
			COMMAND_PRIORITY_EDITOR,
		);
	}, [editor]);

	const handleClick = useCallback(() => {
		if (isWeatherLocationSelected) {
			// Remove weather location formatting
			editor.dispatchCommand(REMOVE_WEATHER_LOCATION_COMMAND, undefined);
		} else {
			// Convert to weather location
			editor.update(() => {
				const selection = $getSelection();
				if (!$isRangeSelection(selection) || selection.isCollapsed()) {
					return;
				}

				const selectedText = selection.getTextContent();
				if (!selectedText || selectedText.trim() === '') {
					return;
				}

				// Dispatch the command to transform the selected text into a weather location
				editor.dispatchCommand(WEATHER_LOCATION_COMMAND, selectedText.trim());
			});
		}
	}, [editor, isWeatherLocationSelected]);

	// Only show the button when there's a valid selection
	if (!hasValidSelection) {
		return null;
	}

	return (
		<button
			type="button"
			className={`toolbar-popup__button ${isWeatherLocationSelected ? 'remove-weather-location-button' : 'weather-location-button'}`}
			onClick={handleClick}
			title={
				isWeatherLocationSelected
					? 'Remove Weather Location Ticker'
					: 'Convert to Weather Location Ticker'
			}
			aria-label={
				isWeatherLocationSelected
					? 'Remove weather location ticker formatting'
					: 'Convert selected text to weather location with temperature'
			}
		>
			<span
				className={
					isWeatherLocationSelected
						? 'remove-weather-location-ticker-icon'
						: 'weather-location-ticker-icon'
				}
			>
				{!isWeatherLocationSelected ? (
					<CloudSunRain className="h-5 w-5" />
				) : (
					<MapPinX className="h-5 w-5" />
				)}
			</span>
		</button>
	);
}

export default WeatherLocationTickerToggleButton;
