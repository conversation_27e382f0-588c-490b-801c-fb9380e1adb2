import type { JsonObject } from 'payload';
import { TwitterBlockConfig } from '@repo/payload/blocks/Twitter';
import { VideoBlockConfig } from '@repo/payload/blocks/Video';
import { ImageBlockConfig } from '@repo/payload/blocks/Image';
import { BuyButtonBlockConfig } from '@repo/payload/blocks/BuyButton';
import { YouTubeBlockConfig } from '@repo/payload/blocks/YouTube';
import { SlideshowBlockConfig } from '@repo/payload/blocks/Slideshow';

export type BaseBlockFields<TBlockFields extends JsonObject = JsonObject> = {
	/** Block form data */
	blockName: string;
	blockType: string;
} & TBlockFields;

export type BlockFieldsOptionalID<
	TBlockFields extends JsonObject = JsonObject,
> = {
	id?: string;
} & BaseBlockFields<TBlockFields>;

// TODO: Expand types when new blocks are created
export const wxNodesToBlockTypes: Record<string, string> = {
	wxnode_youtube: YouTubeBlockConfig.slug,
	wxnode_product: BuyButtonBlockConfig.slug,
	wxnode_video: VideoBlockConfig.slug,
	wxnode_map: ImageBlockConfig.slug,
	wxnode_internal_image: ImageBlockConfig.slug,
	wxnode_twitter: TwitterBlockConfig.slug,
	wxnode_slideshow: SlideshowBlockConfig.slug,
};

type BlockConfigTypes = BaseBlockFields | object;

// TODO: Expand converter functions when new blocks are created
export function transformWxNodesToBlockConfig(
	wxNodeConfig: JsonObject,
): BlockConfigTypes {
	switch (wxNodeConfig.type) {
		case 'wxnode_video': {
			return {
				blockType: wxNodesToBlockTypes[wxNodeConfig.type],
				blockName: `${wxNodeConfig.id}__${wxNodeConfig.type}`,
				file: wxNodeConfig.__wxnext?.file,
				image: wxNodeConfig.__wxnext?.image,
				title: wxNodeConfig.__wxnext?.title,
				description: wxNodeConfig.__wxnext?.description,
				playlist: wxNodeConfig.__wxnext?.playlist,
				tracks: wxNodeConfig.__wxnext?.tracks,
				custom: wxNodeConfig.__wxnext?.custom,
				playerSettings: {
					showDescriptions: wxNodeConfig.__wxnext?.showDescriptions || false,
				},
			};
		}
		case 'wxnode_youtube': {
			return {
				blockType: wxNodesToBlockTypes[wxNodeConfig.type],
				blockName: `${wxNodeConfig.id}__${wxNodeConfig.type}`,
				url: wxNodeConfig.src,
				size: 'hero',
			};
		}
		case 'wxnode_map': {
			return {
				blockType: wxNodesToBlockTypes[wxNodeConfig.type],
				blockName: `${wxNodeConfig.id}__${wxNodeConfig.type}`,
				imageType: 'external',
				isPriorityImage: wxNodeConfig?.__wxnext?._index === 0,
				externalImageUrl: (
					wxNodeConfig.map?.imageurl ||
					wxNodeConfig.__wxnext?.imageUrl ||
					''
				).trim(),
				controls: {
					showCaption: true,
					caption: wxNodeConfig.description,
					width: 'full',
					alignment: 'center',
				},
				altText: wxNodeConfig.help_text,
			};
		}
		case 'wxnode_product': {
			return {
				blockType: wxNodesToBlockTypes[wxNodeConfig.type],
				blockName: `${wxNodeConfig.id}__${wxNodeConfig.type}`,
				label: wxNodeConfig.title,
				url: wxNodeConfig.linkUrl,
				variant: 'bg-gray-900',
				inline: false,
				isAffiliate: true,
			};
		}
		case 'wxnode_internal_image': {
			return {
				blockType: wxNodesToBlockTypes[wxNodeConfig.type],
				blockName: `${wxNodeConfig.id}__${wxNodeConfig.type}`,
				imageType: 'external',
				isPriorityImage: wxNodeConfig?.__wxnext?.priority ?? false,
				//NOTE: The wxnext property is custom and not part of the original wxNode
				externalImageUrl: wxNodeConfig?.__wxnext?.imageUrl || '',
				linkUrl: wxNodeConfig?.__wxnext?.linkUrl || '', // Include the linkUrl property
				controls: {
					showCaption: true,
					caption: wxNodeConfig?.__wxnext?.caption || '',
					credit: wxNodeConfig?.__wxnext?.credit || '',
					// TODO: Phase 1: wxNodeConfig?.sizecode determines inset, but inset is being deprecated
					// TODO: Phase 2: design will determine break points for container queries instead of using inset
					width: 'full',
					alignment: wxNodeConfig?.align || 'center',
				},
				altText: wxNodeConfig?.__wxnext?.altText || '',
			};
		}
		case 'wxnode_twitter': {
			return {
				blockType: wxNodesToBlockTypes[wxNodeConfig.type],
				blockName: `${wxNodeConfig.id}__${wxNodeConfig.type}`,
				tweetId: wxNodeConfig?.twitter_widget?.embed_options?.tweet_id || null,
				tweetURL: wxNodeConfig?.twitter_widget?.embed_text || null,
				showThread: false,
				entryMethod: 'id',
			};
		}
		case 'wxnode_slideshow': {
			// Check if __wxnext exists before trying to access it
			if (!wxNodeConfig.__wxnext) {
				// Return a minimal valid block config that the SlideshowBlock can handle
				return {
					blockType: wxNodesToBlockTypes[wxNodeConfig.type],
					blockName: `${wxNodeConfig.id}__${wxNodeConfig.type}`,
					slides: [], // Empty array that the SlideshowBlock's fallback will handle
					title: wxNodeConfig.title || '',
					settings: {
						rounded: false,
					},
				};
			}

			interface TransformedImage {
				imageUrl: string;
				caption?: string;
				credit?: string;
				altText?: string;
				id: string;
				title?: string;
			}

			// Transform the array of images into the format expected by the Slideshow component
			const slides =
				(wxNodeConfig.__wxnext?.images as TransformedImage[] | undefined)?.map(
					(image) => {
						// The SlideshowBlock expects imageAsset to be an object with a url property
						// that matches the Image type from payload-types
						return {
							// This format matches what isImageObject in SlideshowBlock expects
							imageAsset: {
								url: image.imageUrl || '',
							},
							// Only set imageTitle if it's a real title (not a filename)
							imageTitle: (() => {
								// Check if it looks like a filename with more comprehensive patterns
								const isFilename = image.title
									? // Check for common image extensions
										/\.(jpg|jpeg|png|gif|webp|bmp|tiff|svg)$/i.test(
											image.title,
										) ||
										// Check for filename patterns like DCT_SPECIAL42.jpg
										/^[A-Z0-9_]+\d*\.[a-zA-Z]+$/i.test(image.title) ||
										// Check for other common filename patterns
										/^[a-zA-Z0-9_-]+\d+$/i.test(image.title)
									: false;

								return image.title && !isFilename ? image.title : '';
							})(),
							imageCaption: image.caption || '',
							id: image.id || '',
						};
					},
				) || [];

			const result = {
				blockType: wxNodesToBlockTypes[wxNodeConfig.type],
				blockName: `${wxNodeConfig.id}__${wxNodeConfig.type}`,
				slides,
				title: wxNodeConfig.__wxnext?.title || '',
				settings: {
					rounded: wxNodeConfig.rounded || false,
				},
			};

			return result;
		}
		default:
			return {};
	}
}
