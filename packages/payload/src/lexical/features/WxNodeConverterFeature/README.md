# WxNode Converter Feature

This Lexical editor feature enables automatic conversion of wxnode divs from DSX CMS and Drupal into Lexical blocks, facilitating content migration and integration.

## Table of Contents

- [Overview](#overview)
  - [Purpose](#purpose)
  - [Key Features](#key-features)
- [Component Structure](#component-structure)
  - [Node Implementation](#node-implementation)
  - [Feature Registration](#feature-registration)
  - [Transformation Logic](#transformation-logic)
- [Supported WxNode Types](#supported-wxnode-types)
- [Data Flow](#data-flow)
  - [Conversion Process](#conversion-process)
  - [Block Mapping](#block-mapping)
- [Implementation Details](#implementation-details)
  - [DOM Conversion](#dom-conversion)
  - [Block Creation](#block-creation)
  - [Type Handling](#type-handling)
- [Examples](#examples)
- [Technical Notes](#technical-notes)
- [Integration](#integration)

## Overview

### Purpose

The WxNode Converter Feature bridges the gap between legacy content systems (DSX CMS and Drupal) and the modern Lexical editor. It automatically detects wxnode div elements in imported HTML content and converts them to appropriate Lexical blocks, preserving their functionality and appearance while enabling them to work within the new content editing system.

### Key Features

- Automatic detection and conversion of wxnode divs in imported content
- Seamless mapping between wxnode types and corresponding Lexical blocks
- Support for various media and interactive content types (YouTube, Video, Images, Twitter, etc.)
- Preservation of original wxnode configuration data for reference and debugging
- Server-side implementation that works during content import and transformation

## Component Structure

The WxNode Converter Feature consists of three main components:

### Node Implementation

`WxNodeBlockNode.tsx` defines a custom Lexical node that extends `ServerBlockNode` to handle wxnode conversion:

```typescript
export class WxNodeBlockNode extends ServerBlockNode {
	// Overrides importDOM to detect wxnode divs
	static override importDOM(): DOMConversionMap<HTMLDivElement> | null {
		return {
			div: (domNode) => {
				if (domNode.id.includes("wxn")) {
					return {
						conversion: $wxNodeDivToNodeConverter,
						priority: 0,
					};
				}
				return null;
			},
		};
	}

	// Other methods and overrides...
}
```

This node is responsible for:

- Detecting wxnode divs in imported HTML
- Converting them to block nodes with appropriate fields
- Handling serialization and deserialization

### Feature Registration

`index.tsx` registers the feature with PayloadCMS's Lexical integration:

```typescript
export const WxNodeConverterFeature = createServerFeature({
	feature: {
		nodes: [
			createNode({
				node: WxNodeBlockNode,
			}),
		],
	},
	key: "wxNodeConverterFeature",
});
```

This registration makes the feature available to the Lexical editor system, allowing it to process wxnode divs during content import.

### Transformation Logic

`transformWxNodesToBlockConfigs.ts` contains the logic for transforming wxnode configurations into block configurations:

```typescript
export function transformWxNodesToBlockConfig(
	wxNodeConfig: JsonObject,
): BlockConfigTypes {
	switch (wxNodeConfig.type) {
		case "wxnode_video": {
			return {
				blockType: wxNodesToBlockTypes[wxNodeConfig.type],
				blockName: `${wxNodeConfig.id}__${wxNodeConfig.type}`,
				videoUrl: wxNodeConfig.__wxnext?.videoUrl,
			};
		}
		// Other transformations...
	}
}
```

This component:

- Maps wxnode types to corresponding block types
- Extracts relevant data from wxnode configurations
- Formats data to match the expected block configuration structure

## Supported WxNode Types

The feature currently supports the following wxnode types:

| WxNode Type           | Block Type | Description              |
| --------------------- | ---------- | ------------------------ |
| wxnode_youtube        | YouTube    | YouTube video embeds     |
| wxnode_product        | BuyButton  | Product purchase buttons |
| wxnode_video          | Video      | Video player embeds      |
| wxnode_map            | Image      | Map images               |
| wxnode_internal_image | Image      | Internal images          |
| wxnode_twitter        | Twitter    | Twitter embeds           |

Additional wxnode types can be added by:

1. Updating the `wxNodesToBlockTypes` mapping
2. Adding a new case to the `transformWxNodesToBlockConfig` function

## Data Flow

### Conversion Process

1. **Detection**: When HTML content is imported, the Lexical editor processes DOM nodes
2. **Identification**: The `importDOM` method in `WxNodeBlockNode` identifies divs with IDs containing "wxn"
3. **Extraction**: The wxnode configuration is extracted from the `data-config` attribute (base64 encoded)
4. **Transformation**: The configuration is decoded and passed to `transformWxNodesToBlockConfig`
5. **Mapping**: The function maps the wxnode type to a corresponding block type
6. **Creation**: A new block node is created with the transformed configuration
7. **Insertion**: The block node is inserted into the Lexical editor tree

### Block Mapping

The transformation process maps wxnode properties to block properties based on the wxnode type:

```typescript
// Example mapping for wxnode_youtube
{
  blockType: 'YouTube',
  blockName: `${wxNodeConfig.id}__${wxNodeConfig.type}`,
  url: wxNodeConfig.src,
  size: 'hero',
}
```

Each wxnode type has a specific mapping that extracts relevant data and formats it according to the corresponding block's expected structure.

## Implementation Details

### DOM Conversion

The DOM conversion process is handled by the `$wxNodeDivToNodeConverter` function:

```typescript
function $wxNodeDivToNodeConverter(
	domNode: HTMLDivElement,
): DOMConversionOutput {
	const id = domNode.id;
	const configString = domNode.getAttribute("data-config");
	const wxNodeConfig = JSON.parse(
		Buffer.from(configString as string, "base64").toString(),
	);

	const fields: BlockFieldsOptionalID = {
		id,
		__wxnode: wxNodeConfig,
		...(transformWxNodesToBlockConfig(wxNodeConfig) as BaseBlockFields),
	};

	return {
		node: $createBlockNode(fields),
	};
}
```

This function:

1. Extracts the wxnode ID and configuration
2. Decodes the base64-encoded configuration
3. Transforms the configuration into block fields
4. Creates a new block node with these fields

### Block Creation

Blocks are created using the `$createBlockNode` function:

```typescript
export function $createBlockNode(
	fields: BlockFieldsOptionalID,
): WxNodeBlockNode {
	return new WxNodeBlockNode({
		fields: {
			...fields,
			id: fields?.id ?? "",
		},
	});
}
```

This function ensures that:

- All required fields are present
- The ID is properly set (using the wxnode ID or generating a new one)
- The block node is properly initialized

### Type Handling

The feature uses TypeScript to ensure type safety throughout the conversion process:

```typescript
export type BaseBlockFields<TBlockFields extends JsonObject = JsonObject> = {
	/** Block form data */
	blockName: string;
	blockType: string;
} & TBlockFields;

export type BlockFieldsOptionalID<
	TBlockFields extends JsonObject = JsonObject,
> = {
	id?: string;
} & BaseBlockFields<TBlockFields>;
```

These types ensure that:

- All blocks have the required fields (blockName, blockType)
- Additional fields can be added based on the specific block type
- The ID field is optional during creation (but will be set during the process)

## Examples

### Example 1: YouTube WxNode Conversion

**Original wxnode div:**

```html
<div
	id="wxn_youtube_123"
	data-config="eyJ0eXBlIjoid3hub2RlX3lvdXR1YmUiLCJpZCI6IjEyMyIsInNyYyI6Imh0dHBzOi8vd3d3LnlvdXR1YmUuY29tL3dhdGNoP3Y9ZFF3NHc5V2dYY1EifQ=="
></div>
```

**Decoded configuration:**

```json
{
	"type": "wxnode_youtube",
	"id": "123",
	"src": "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
}
```

**Resulting block configuration:**

```json
{
	"blockType": "YouTube",
	"blockName": "123__wxnode_youtube",
	"url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
	"size": "hero"
}
```

### Example 2: Image WxNode Conversion

**Original wxnode div:**

```html
<div
	id="wxn_internal_image_456"
	data-config="eyJ0eXBlIjoid3hub2RlX2ludGVybmFsX2ltYWdlIiwiaWQiOiI0NTYiLCJkZXNjcmlwdGlvbiI6IkEgYmVhdXRpZnVsIHN1bnNldCIsIl9fd3huZXh0Ijp7ImltYWdlVXJsIjoiaHR0cHM6Ly9leGFtcGxlLmNvbS9pbWFnZXMvc3Vuc2V0LmpwZyJ9fQ=="
></div>
```

**Decoded configuration:**

```json
{
	"type": "wxnode_internal_image",
	"id": "456",
	"description": "A beautiful sunset",
	"__wxnext": {
		"imageUrl": "https://example.com/images/sunset.jpg"
	}
}
```

**Resulting block configuration:**

```json
{
	"blockType": "Image",
	"blockName": "456__wxnode_internal_image",
	"imageType": "external",
	"externalImageUrl": "https://example.com/images/sunset.jpg",
	"controls": {
		"showCaption": true,
		"caption": "A beautiful sunset",
		"width": "full",
		"alignment": "center"
	}
}
```

## Technical Notes

- **Base64 Decoding**: The feature decodes base64-encoded wxnode configurations to extract the original data
- **Original Data Preservation**: The original wxnode configuration is preserved in the `__wxnode` field for reference and debugging
- **Version Handling**: The feature includes logic to handle different versions of serialized nodes
- **Extensibility**: New wxnode types can be added by updating the mapping and transformation functions
- **Server-Side Only**: This feature operates on the server side during content import and doesn't require client-side components

### Special Considerations

- Some wxnode types may require custom handling or additional data transformation
- The `__wxnext` property is a custom extension for data that isn't part of the original wxnode but is needed for proper rendering
- The feature assumes that corresponding blocks exist for each wxnode type it handles

## Integration

To use this feature in a Lexical editor configuration:

```typescript
import WxNodeConverterFeature from "@/lexical/features/WxNodeConverterFeature";

// In your Lexical editor configuration
const features = [
	// Other features...
	WxNodeConverterFeature,
];

// Create editor with features
const Editor = createEditor({
	features,
	// Other configuration...
});
```

This feature is particularly useful when:

- Migrating content from DSX CMS or Drupal to PayloadCMS
- Importing legacy content that contains wxnode elements
- Ensuring backward compatibility with existing content

For extending the feature to support additional wxnode types, update the `wxNodesToBlockTypes` mapping and add a new case to the `transformWxNodesToBlockConfig` function.
