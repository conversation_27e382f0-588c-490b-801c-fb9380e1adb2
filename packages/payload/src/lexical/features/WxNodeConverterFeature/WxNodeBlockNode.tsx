import React from 'react';
import type {
	DOMConversionMap,
	DOMConversionOutput,
	LexicalNode,
} from '@payloadcms/richtext-lexical/lexical';
import {
	ServerBlockNode,
	type SerializedBlockNode,
} from '@payloadcms/richtext-lexical';

import {
	transformWxNodesToBlockConfig,
	type BlockFieldsOptionalID,
	type BaseBlockFields,
} from '@repo/payload/lexical/features/WxNodeConverterFeature/transformWxNodesToBlockConfigs';

/**
 * This class reuses ServerBlockNode overrides importDOM to provide way to detect
 * wxnoe divs and convert to a block like structure.  This assumes that we will
 * have a corresponding block to a wxnode
 */
export class WxNodeBlockNode extends ServerBlockNode {
	static override clone(node: ServerBlockNode): ServerBlockNode {
		return super.clone(node);
	}

	static override getType(): string {
		return super.getType();
	}

	static override importDOM(): DOMConversionMap<HTMLDivElement> | null {
		return {
			div: (domNode) => {
				if (domNode.id.includes('wxn')) {
					return {
						conversion: $wxNodeDivToNodeConverter,
						priority: 0,
					};
				}

				return null;
			},
		};
	}

	static override importJSON(
		serializedNode: SerializedBlockNode,
	): WxNodeBlockNode {
		if (serializedNode.version === 1) {
			// Convert (version 1 had the fields wrapped in another, unnecessary data property)
			serializedNode = {
				...serializedNode,
				fields: {
					...serializedNode.fields.data,
				},
				version: 2,
			};
		}
		const node = $createBlockNode(serializedNode.fields);
		node.setFormat(serializedNode.format);
		return node;
	}

	override exportJSON(): SerializedBlockNode {
		return super.exportJSON();
	}

	decorate(): React.ReactElement {
		return <WxNodeBlockComponent fields={this.getFields()} />;
	}
}

interface WxNodeBlockComponentProps {
	fields: BlockFieldsOptionalID;
}

function WxNodeBlockComponent({
	fields,
}: WxNodeBlockComponentProps): React.ReactElement {
	// This is a placeholder component for WxNode blocks
	// The actual rendering will be handled by the block system
	return (
		<div className="wxnode-block" data-wxnode-id={fields.id}>
			<div className="wxnode-placeholder">
				WxNode Block: {fields.id || 'Unknown'}
			</div>
		</div>
	);
}

export function $createBlockNode(
	fields: BlockFieldsOptionalID,
): WxNodeBlockNode {
	return new WxNodeBlockNode({
		fields: {
			...fields,
			id: fields?.id ?? '',
		},
	});
}

export function $isWxNodeBlockNode(
	node: WxNodeBlockNode | LexicalNode | null | undefined,
): node is WxNodeBlockNode {
	return node instanceof WxNodeBlockNode;
}

function $wxNodeDivToNodeConverter(
	domNode: HTMLDivElement,
): DOMConversionOutput {
	const id = domNode.id;
	const configString = domNode.getAttribute('data-config');
	let wxNodeConfig = {};

	// First check if configString exists
	if (!configString) {
		return { node: null };
	}

	const encodedConfigString = Buffer.from(configString, 'base64').toString();

	try {
		wxNodeConfig = JSON.parse(encodedConfigString);
	} catch (error) {
		console.warn(
			'WXNode Conversion Error (likely related to null node in the array): ',
			error,
		);

		return { node: null }; // Return null node on parsing error
	}

	const fields: BlockFieldsOptionalID = {
		id,
		__wxnode: wxNodeConfig,
		...(transformWxNodesToBlockConfig(wxNodeConfig) as BaseBlockFields),
	};

	return {
		node: $createBlockNode(fields),
	};
}
