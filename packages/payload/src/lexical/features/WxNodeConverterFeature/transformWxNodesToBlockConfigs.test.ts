import { describe, test, expect } from 'vitest';
import {
	transformWxNodesToBlockConfig,
	wxNodesToBlockTypes,
} from './transformWxNodesToBlockConfigs';
import VideoBlockConfig from '@repo/payload/blocks/Video/config';
import type { JsonObject } from 'payload';

describe('transformWxNodesToBlockConfigs', () => {
	describe('Video Node Transformations', () => {
		test('transforms a complete video wxNode correctly', () => {
			const mockVideoWxNode: JsonObject = {
				id: 'wxn01',
				type: 'wxnode_video',
				__wxnext: {
					file: 'https://example.com/video.mp4',
					image: 'https://example.com/thumbnail.jpg',
					title: 'Test Video',
					description: 'Test Description',
					tracks: [
						{
							file: 'https://example.com/captions.vtt',
							label: 'English',
							kind: 'captions',
							default: true,
						},
					],
				},
			};

			const result = transformWxNodesToBlockConfig(mockVideoWxNode);

			expect(result).toEqual({
				blockType: VideoBlockConfig.slug,
				blockName: 'wxn01__wxnode_video',
				file: 'https://example.com/video.mp4',
				image: 'https://example.com/thumbnail.jpg',
				title: 'Test Video',
				description: 'Test Description',
				playerSettings: {
					showDescriptions: false,
				},
				tracks: [
					{
						file: 'https://example.com/captions.vtt',
						label: 'English',
						kind: 'captions',
						default: true,
					},
				],
			});
		});

		test('transforms a video wxNode with missing fields', () => {
			const mockVideoWxNode: JsonObject = {
				id: 'wxn02',
				type: 'wxnode_video',
				__wxnext: {
					file: 'https://example.com/video.mp4',
					// Missing image, title, description, etc.
				},
			};

			const result = transformWxNodesToBlockConfig(mockVideoWxNode);

			expect(result).toEqual({
				blockType: VideoBlockConfig.slug,
				blockName: 'wxn02__wxnode_video',
				file: 'https://example.com/video.mp4',
				image: undefined,
				title: undefined,
				description: undefined,
				playlist: undefined,
				tracks: undefined,
				custom: undefined,
				playerSettings: {
					showDescriptions: false,
				},
			});
		});

		test('transforms a video wxNode with empty __wxnext property', () => {
			const mockVideoWxNode: JsonObject = {
				id: 'wxn03',
				type: 'wxnode_video',
				__wxnext: {},
			};

			const result = transformWxNodesToBlockConfig(mockVideoWxNode);

			expect(result).toEqual({
				blockType: VideoBlockConfig.slug,
				blockName: 'wxn03__wxnode_video',
				file: undefined,
				image: undefined,
				title: undefined,
				description: undefined,
				tracks: undefined,
				playlist: undefined,
				custom: undefined,
				playerSettings: {
					showDescriptions: false,
				},
			});
		});

		test('transforms a video wxNode with null __wxnext property', () => {
			const mockVideoWxNode: JsonObject = {
				id: 'wxn04',
				type: 'wxnode_video',
				__wxnext: null,
			};

			const result = transformWxNodesToBlockConfig(mockVideoWxNode);

			expect(result).toEqual({
				blockType: VideoBlockConfig.slug,
				blockName: 'wxn04__wxnode_video',
				file: undefined,
				image: undefined,
				title: undefined,
				description: undefined,
				tracks: undefined,
				playlist: undefined,
				custom: undefined,
				playerSettings: {
					showDescriptions: false,
				},
			});
		});

		test('transforms a video wxNode with tracks data', () => {
			const mockVideoWxNode: JsonObject = {
				id: 'wxn05',
				type: 'wxnode_video',
				__wxnext: {
					file: 'https://example.com/video.mp4',
					tracks: [
						{
							file: 'https://example.com/captions-en.vtt',
							label: 'English',
							kind: 'captions',
							default: true,
						},
						{
							file: 'https://example.com/captions-es.vtt',
							label: 'Spanish',
							kind: 'captions',
							default: false,
						},
					],
				},
			};

			const result = transformWxNodesToBlockConfig(mockVideoWxNode);

			expect(result).toEqual({
				blockType: VideoBlockConfig.slug,
				blockName: 'wxn05__wxnode_video',
				file: 'https://example.com/video.mp4',
				image: undefined,
				title: undefined,
				description: undefined,
				playerSettings: {
					showDescriptions: false,
				},
				tracks: [
					{
						file: 'https://example.com/captions-en.vtt',
						label: 'English',
						kind: 'captions',
						default: true,
					},
					{
						file: 'https://example.com/captions-es.vtt',
						label: 'Spanish',
						kind: 'captions',
						default: false,
					},
				],
				playlist: undefined,
				custom: undefined,
			});
		});

		test('transforms a video wxNode with playlist data', () => {
			const mockVideoWxNode: JsonObject = {
				id: 'wxn06',
				type: 'wxnode_video',
				__wxnext: {
					file: 'https://example.com/video.mp4',
					playlist: [
						{
							file: 'https://example.com/video1.mp4',
							title: 'First Video',
							image: 'https://example.com/thumbnail1.jpg',
						},
						{
							file: 'https://example.com/video2.mp4',
							title: 'Second Video',
							image: 'https://example.com/thumbnail2.jpg',
						},
					],
				},
			};

			const result = transformWxNodesToBlockConfig(mockVideoWxNode);

			expect(result).toEqual({
				blockType: VideoBlockConfig.slug,
				blockName: 'wxn06__wxnode_video',
				file: 'https://example.com/video.mp4',
				image: undefined,
				title: undefined,
				description: undefined,
				tracks: undefined,
				playerSettings: {
					showDescriptions: false,
				},
				playlist: [
					{
						file: 'https://example.com/video1.mp4',
						title: 'First Video',
						image: 'https://example.com/thumbnail1.jpg',
					},
					{
						file: 'https://example.com/video2.mp4',
						title: 'Second Video',
						image: 'https://example.com/thumbnail2.jpg',
					},
				],
				custom: undefined,
			});
		});

		test('transforms a video wxNode with custom properties', () => {
			const mockVideoWxNode: JsonObject = {
				id: 'wxn07',
				type: 'wxnode_video',
				__wxnext: {
					file: 'https://example.com/video.mp4',
					custom: {
						ctx: {
							jwplayer: 'test-player-id',
							adzone: 'weather/news',
							iab: {
								v1: ['IAB15-10_Weather', 'IAB15_Science'],
								v2: ['390_Weather', '464_Science'],
								v3: ['390_Weather', '467_Environment'],
							},
							providername: 'TWC - Digital',
							duration: '00:00:56',
							id: 'test-id-123',
						},
						otherProp: 'test-value',
					},
				},
			};

			const result = transformWxNodesToBlockConfig(mockVideoWxNode);

			expect(result).toEqual({
				blockType: VideoBlockConfig.slug,
				blockName: 'wxn07__wxnode_video',
				file: 'https://example.com/video.mp4',
				image: undefined,
				title: undefined,
				description: undefined,
				tracks: undefined,
				playlist: undefined,
				playerSettings: {
					showDescriptions: false,
				},
				custom: {
					ctx: {
						jwplayer: 'test-player-id',
						adzone: 'weather/news',
						iab: {
							v1: ['IAB15-10_Weather', 'IAB15_Science'],
							v2: ['390_Weather', '464_Science'],
							v3: ['390_Weather', '467_Environment'],
						},
						providername: 'TWC - Digital',
						duration: '00:00:56',
						id: 'test-id-123',
					},
					otherProp: 'test-value',
				},
			});
		});

		test('transforms a video wxNode with ad-specific custom fields', () => {
			const mockVideoWxNode: JsonObject = {
				id: 'wxn08',
				type: 'wxnode_video',
				__wxnext: {
					file: 'https://example.com/video.mp4',
					title: 'Ad-enabled Video',
					custom: {
						ctx: {
							jwplayer: 'custom-player-id',
							adzone: 'weather/video/ads',
							noAds: false,
							helios: {
								enabled: true,
								config: {
									site: 'weather',
									section: 'video',
								},
							},
						},
					},
				},
			};

			const result = transformWxNodesToBlockConfig(mockVideoWxNode);

			expect(result).toEqual({
				blockType: VideoBlockConfig.slug,
				blockName: 'wxn08__wxnode_video',
				file: 'https://example.com/video.mp4',
				title: 'Ad-enabled Video',
				image: undefined,
				description: undefined,
				tracks: undefined,
				playlist: undefined,
				playerSettings: {
					showDescriptions: false,
				},
				custom: {
					ctx: {
						jwplayer: 'custom-player-id',
						adzone: 'weather/video/ads',
						noAds: false,
						helios: {
							enabled: true,
							config: {
								site: 'weather',
								section: 'video',
							},
						},
					},
				},
			});
		});

		test('transforms a video wxNode with complex nested custom fields', () => {
			const mockVideoWxNode: JsonObject = {
				id: 'wxn09',
				type: 'wxnode_video',
				__wxnext: {
					file: 'https://example.com/video.mp4',
					custom: {
						ctx: {
							jwplayer: 'test-player-id',
							adzone: 'weather/news',
							analytics: {
								category: 'video',
								label: 'weather-video',
								metrics: {
									views: 0,
									completions: 0,
									engagement: {
										quartiles: [0, 0, 0, 0],
										average: 0,
									},
								},
								tags: ['weather', 'news', 'video'],
							},
							targeting: {
								demographics: {
									age: ['18-24', '25-34'],
									gender: ['all'],
									interests: ['weather', 'news'],
								},
								geo: {
									country: 'US',
									regions: ['Northeast', 'Midwest'],
								},
							},
						},
					},
				},
			};

			const result = transformWxNodesToBlockConfig(mockVideoWxNode);

			expect(result).toEqual({
				blockType: VideoBlockConfig.slug,
				blockName: 'wxn09__wxnode_video',
				file: 'https://example.com/video.mp4',
				image: undefined,
				title: undefined,
				description: undefined,
				tracks: undefined,
				playlist: undefined,
				playerSettings: {
					showDescriptions: false,
				},
				custom: {
					ctx: {
						jwplayer: 'test-player-id',
						adzone: 'weather/news',
						analytics: {
							category: 'video',
							label: 'weather-video',
							metrics: {
								views: 0,
								completions: 0,
								engagement: {
									quartiles: [0, 0, 0, 0],
									average: 0,
								},
							},
							tags: ['weather', 'news', 'video'],
						},
						targeting: {
							demographics: {
								age: ['18-24', '25-34'],
								gender: ['all'],
								interests: ['weather', 'news'],
							},
							geo: {
								country: 'US',
								regions: ['Northeast', 'Midwest'],
							},
						},
					},
				},
			});
		});

		test('preserves all custom fields in transformation', () => {
			// This test verifies that ALL custom fields are preserved, not just known ones
			const mockVideoWxNode: JsonObject = {
				id: 'wxn10',
				type: 'wxnode_video',
				__wxnext: {
					file: 'https://example.com/video.mp4',
					custom: {
						ctx: {
							jwplayer: 'test-player-id',
							adzone: 'weather/news',
						},
						// Non-standard fields that should still be preserved
						metadata: {
							recordedDate: '2025-01-15',
							location: 'New York, NY',
							weatherConditions: 'Sunny',
							temperature: '72°F',
						},
						permissions: {
							canDownload: false,
							canShare: true,
							expirationDate: '2026-01-01',
						},
						tracking: {
							sourceId: 'SRC-12345',
							campaignId: 'CAMP-6789',
							partnerId: 'PRTNR-101',
						},
					},
					showDescriptions: true,
				},
			};

			const result = transformWxNodesToBlockConfig(mockVideoWxNode);

			// Verify all custom fields are preserved
			expect(result).toEqual({
				blockType: VideoBlockConfig.slug,
				blockName: 'wxn10__wxnode_video',
				file: 'https://example.com/video.mp4',
				image: undefined,
				title: undefined,
				description: undefined,
				tracks: undefined,
				playlist: undefined,
				playerSettings: {
					showDescriptions: true,
				},
				custom: {
					ctx: {
						jwplayer: 'test-player-id',
						adzone: 'weather/news',
					},
					metadata: {
						recordedDate: '2025-01-15',
						location: 'New York, NY',
						weatherConditions: 'Sunny',
						temperature: '72°F',
					},
					permissions: {
						canDownload: false,
						canShare: true,
						expirationDate: '2026-01-01',
					},
					tracking: {
						sourceId: 'SRC-12345',
						campaignId: 'CAMP-6789',
						partnerId: 'PRTNR-101',
					},
				},
			});
		});
	});

	describe('wxnode_youtube', () => {
		test('should transform YouTube wxNode to YouTube block config', () => {
			// Arrange: Create test fixture with sample YouTube data
			const youtubeWxNode: JsonObject = {
				id: 'youtube123',
				type: 'wxnode_youtube',
				src: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
			};

			// Act: Transform the wxNode
			const result = transformWxNodesToBlockConfig(youtubeWxNode);

			// Assert: Verify the transformation
			expect(result).toEqual({
				blockType: wxNodesToBlockTypes['wxnode_youtube'],
				blockName: 'youtube123__wxnode_youtube',
				url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
				size: 'hero',
			});
		});
	});

	describe('wxnode_map', () => {
		test('should transform map wxNode to Image block config', () => {
			// Arrange: Create test fixture with sample map data
			const mapWxNode: JsonObject = {
				id: 'map123',
				type: 'wxnode_map',
				map: {
					imageurl: 'https://example.com/map.jpg',
				},
				description: 'Test map description',
				help_text: 'Test alt text',
			};

			// Act: Transform the wxNode
			const result = transformWxNodesToBlockConfig(mapWxNode);

			// Assert: Verify the transformation
			expect(result).toEqual({
				blockType: wxNodesToBlockTypes['wxnode_map'],
				blockName: 'map123__wxnode_map',
				imageType: 'external',
				isPriorityImage: false,
				externalImageUrl: 'https://example.com/map.jpg',
				controls: {
					showCaption: true,
					caption: 'Test map description',
					width: 'full',
					alignment: 'center',
				},
				altText: 'Test alt text',
			});
		});

		test('should set isPriorityImage to true when _index is 0', () => {
			// Arrange: Create test fixture with _index: 0
			const mapWxNode: JsonObject = {
				id: 'map123',
				type: 'wxnode_map',
				map: {
					imageurl: 'https://example.com/map.jpg',
				},
				description: 'Test map description',
				help_text: 'Test alt text',
				__wxnext: {
					_index: 0,
				},
			};

			// Act: Transform the wxNode
			const result = transformWxNodesToBlockConfig(mapWxNode);

			// Assert: Verify the transformation
			expect(result).toEqual({
				blockType: wxNodesToBlockTypes['wxnode_map'],
				blockName: 'map123__wxnode_map',
				imageType: 'external',
				isPriorityImage: true,
				externalImageUrl: 'https://example.com/map.jpg',
				controls: {
					showCaption: true,
					caption: 'Test map description',
					width: 'full',
					alignment: 'center',
				},
				altText: 'Test alt text',
			});
		});
	});

	describe('wxnode_product', () => {
		test('should transform product wxNode to BuyButton block config', () => {
			// Arrange: Create test fixture with sample product data
			const productWxNode: JsonObject = {
				id: 'product123',
				type: 'wxnode_product',
				title: 'Test Product',
				linkUrl: 'https://example.com/product',
			};

			// Act: Transform the wxNode
			const result = transformWxNodesToBlockConfig(productWxNode);

			// Assert: Verify the transformation
			expect(result).toEqual({
				blockType: wxNodesToBlockTypes['wxnode_product'],
				blockName: 'product123__wxnode_product',
				label: 'Test Product',
				url: 'https://example.com/product',
				variant: 'bg-gray-900',
				inline: false,
				isAffiliate: true,
			});
		});
	});

	describe('wxnode_internal_image', () => {
		test('should transform internal image wxNode to Image block config', () => {
			// Arrange: Create test fixture with sample internal image data
			const internalImageWxNode: JsonObject = {
				id: 'image123',
				type: 'wxnode_internal_image',
				__wxnext: {
					imageUrl: 'https://example.com/image.jpg',
					caption: 'Test image caption',
					credit: 'Test image credit',
					altText: 'Test alt text',
				},
			};

			// Act: Transform the wxNode
			const result = transformWxNodesToBlockConfig(internalImageWxNode);

			// Assert: Verify the transformation
			expect(result).toEqual({
				blockType: wxNodesToBlockTypes['wxnode_internal_image'],
				blockName: 'image123__wxnode_internal_image',
				imageType: 'external',
				isPriorityImage: false,
				linkUrl: '',
				externalImageUrl: 'https://example.com/image.jpg',
				controls: {
					showCaption: true,
					caption: 'Test image caption',
					credit: 'Test image credit',
					width: 'full',
					alignment: 'center',
				},
				altText: 'Test alt text',
			});
		});

		test('should set isPriorityImage to true when priority is true', () => {
			// Arrange: Create test fixture with priority: true
			const internalImageWxNode: JsonObject = {
				id: 'image123',
				type: 'wxnode_internal_image',
				__wxnext: {
					imageUrl: 'https://example.com/image.jpg',
					caption: 'Test image caption',
					credit: 'Test image credit',
					altText: 'Test alt text',
					priority: true,
				},
			};

			// Act: Transform the wxNode
			const result = transformWxNodesToBlockConfig(internalImageWxNode);

			// Assert: Verify the transformation
			expect(result).toEqual({
				blockType: wxNodesToBlockTypes['wxnode_internal_image'],
				blockName: 'image123__wxnode_internal_image',
				imageType: 'external',
				isPriorityImage: true,
				linkUrl: '',
				externalImageUrl: 'https://example.com/image.jpg',
				controls: {
					showCaption: true,
					caption: 'Test image caption',
					credit: 'Test image credit',
					width: 'full',
					alignment: 'center',
				},
				altText: 'Test alt text',
			});
		});
	});

	describe('wxnode_twitter', () => {
		test('should transform Twitter wxNode to Twitter block config', () => {
			// Arrange: Create test fixture with sample Twitter data
			const twitterWxNode: JsonObject = {
				id: 'twitter123',
				type: 'wxnode_twitter',
				twitter_widget: {
					embed_options: {
						tweet_id: '1234567890',
					},
					embed_text: 'https://twitter.com/user/status/1234567890',
				},
			};

			// Act: Transform the wxNode
			const result = transformWxNodesToBlockConfig(twitterWxNode);

			// Assert: Verify the transformation
			expect(result).toEqual({
				blockType: wxNodesToBlockTypes['wxnode_twitter'],
				blockName: 'twitter123__wxnode_twitter',
				tweetId: '1234567890',
				tweetURL: 'https://twitter.com/user/status/1234567890',
				showThread: false,
				entryMethod: 'id',
			});
		});
	});

	describe('wxnode_slideshow', () => {
		test('should transform slideshow wxNode to Slideshow block config', () => {
			// Arrange: Create test fixture with sample slideshow data
			const slideshowWxNode: JsonObject = {
				id: 'slide123',
				type: 'wxnode_slideshow',
				rounded: true,
				__wxnext: {
					images: [
						{
							imageUrl: 'https://example.com/image1.jpg',
							caption: 'Test Caption 1',
							credit: 'Test Credit 1',
							altText: 'Test Alt Text 1',
							id: 'img1',
							title: 'Test Title 1',
						},
						{
							imageUrl: 'https://example.com/image2.jpg',
							caption: 'Test Caption 2',
							credit: 'Test Credit 2',
							altText: 'Test Alt Text 2',
							id: 'img2',
							title: 'Test Title 2',
						},
					],
					title: 'Test Slideshow',
				},
			};

			// Act: Transform the wxNode
			const result = transformWxNodesToBlockConfig(slideshowWxNode);

			// Assert: Verify the transformation
			expect(result).toEqual({
				blockType: wxNodesToBlockTypes['wxnode_slideshow'],
				blockName: 'slide123__wxnode_slideshow',
				slides: [
					{
						imageAsset: { url: 'https://example.com/image1.jpg' },
						imageTitle: 'Test Title 1',
						imageCaption: 'Test Caption 1',
						id: 'img1',
					},
					{
						imageAsset: { url: 'https://example.com/image2.jpg' },
						imageTitle: 'Test Title 2',
						imageCaption: 'Test Caption 2',
						id: 'img2',
					},
				],
				title: 'Test Slideshow',
				settings: {
					rounded: true,
				},
			});
		});

		test('should handle missing images array', () => {
			// Arrange: Create test fixture with missing images array
			const slideshowWxNode: JsonObject = {
				id: 'slide123',
				type: 'wxnode_slideshow',
				__wxnext: {
					title: 'Test Slideshow',
				},
			};

			// Act: Transform the wxNode
			const result = transformWxNodesToBlockConfig(slideshowWxNode);

			// Assert: Verify the transformation
			expect(result).toEqual({
				blockType: wxNodesToBlockTypes['wxnode_slideshow'],
				blockName: 'slide123__wxnode_slideshow',
				slides: [],
				title: 'Test Slideshow',
				settings: {
					rounded: false,
				},
			});
		});

		test('should handle image titles with .jpg extension', () => {
			// Arrange: Create test fixture with image title containing .jpg
			const slideshowWxNode: JsonObject = {
				id: 'slide123',
				type: 'wxnode_slideshow',
				__wxnext: {
					images: [
						{
							imageUrl: 'https://example.com/image1.jpg',
							caption: 'Test Caption 1',
							id: 'img1',
							title: 'image1.jpg',
						},
					],
					title: 'Test Slideshow',
				},
			};

			// Act: Transform the wxNode
			const result = transformWxNodesToBlockConfig(slideshowWxNode);

			// Assert: Verify the transformation - title should be empty string
			expect(result).toEqual({
				blockType: wxNodesToBlockTypes['wxnode_slideshow'],
				blockName: 'slide123__wxnode_slideshow',
				slides: [
					{
						imageAsset: { url: 'https://example.com/image1.jpg' },
						imageTitle: '',
						imageCaption: 'Test Caption 1',
						id: 'img1',
					},
				],
				title: 'Test Slideshow',
				settings: {
					rounded: false,
				},
			});
		});

		test('should handle image titles with DCT_SPECIAL42.jpg pattern', () => {
			// Arrange: Create test fixture with image title matching the special pattern
			const slideshowWxNode: JsonObject = {
				id: 'slide123',
				type: 'wxnode_slideshow',
				__wxnext: {
					images: [
						{
							imageUrl: 'https://example.com/DCT_SPECIAL42.jpg',
							caption: 'Test Caption 1',
							id: 'img1',
							title: 'DCT_SPECIAL42.jpg',
						},
					],
					title: 'Test Slideshow',
				},
			};

			// Act: Transform the wxNode
			const result = transformWxNodesToBlockConfig(slideshowWxNode);

			// Assert: Verify the transformation - title should be empty string
			expect(result).toEqual({
				blockType: wxNodesToBlockTypes['wxnode_slideshow'],
				blockName: 'slide123__wxnode_slideshow',
				slides: [
					{
						imageAsset: { url: 'https://example.com/DCT_SPECIAL42.jpg' },
						imageTitle: '',
						imageCaption: 'Test Caption 1',
						id: 'img1',
					},
				],
				title: 'Test Slideshow',
				settings: {
					rounded: false,
				},
			});
		});

		test('should handle image titles with image-123 pattern', () => {
			// Arrange: Create test fixture with image title matching the numeric pattern
			const slideshowWxNode: JsonObject = {
				id: 'slide123',
				type: 'wxnode_slideshow',
				__wxnext: {
					images: [
						{
							imageUrl: 'https://example.com/image-123.jpg',
							caption: 'Test Caption 1',
							id: 'img1',
							title: 'image-123',
						},
					],
					title: 'Test Slideshow',
				},
			};

			// Act: Transform the wxNode
			const result = transformWxNodesToBlockConfig(slideshowWxNode);

			// Assert: Verify the transformation - title should be empty string
			expect(result).toEqual({
				blockType: wxNodesToBlockTypes['wxnode_slideshow'],
				blockName: 'slide123__wxnode_slideshow',
				slides: [
					{
						imageAsset: { url: 'https://example.com/image-123.jpg' },
						imageTitle: '',
						imageCaption: 'Test Caption 1',
						id: 'img1',
					},
				],
				title: 'Test Slideshow',
				settings: {
					rounded: false,
				},
			});
		});

		test('should handle missing imageUrl in slides', () => {
			// Arrange: Create test fixture with missing imageUrl
			const slideshowWxNode: JsonObject = {
				id: 'slide123',
				type: 'wxnode_slideshow',
				__wxnext: {
					images: [
						{
							caption: 'Test Caption 1',
							id: 'img1',
							title: 'Test Title 1',
						},
					],
					title: 'Test Slideshow',
				},
			};

			// Act: Transform the wxNode
			const result = transformWxNodesToBlockConfig(slideshowWxNode);

			// Assert: Verify the transformation - imageUrl should be empty string
			expect(result).toEqual({
				blockType: wxNodesToBlockTypes['wxnode_slideshow'],
				blockName: 'slide123__wxnode_slideshow',
				slides: [
					{
						imageAsset: { url: '' },
						imageTitle: 'Test Title 1',
						imageCaption: 'Test Caption 1',
						id: 'img1',
					},
				],
				title: 'Test Slideshow',
				settings: {
					rounded: false,
				},
			});
		});

		test('should handle missing id in slides', () => {
			// Arrange: Create test fixture with missing id
			const slideshowWxNode: JsonObject = {
				id: 'slide123',
				type: 'wxnode_slideshow',
				__wxnext: {
					images: [
						{
							imageUrl: 'https://example.com/image1.jpg',
							caption: 'Test Caption 1',
							title: 'Test Title 1',
						},
					],
					title: 'Test Slideshow',
				},
			};

			// Act: Transform the wxNode
			const result = transformWxNodesToBlockConfig(slideshowWxNode);

			// Assert: Verify the transformation - id should be empty string
			expect(result).toEqual({
				blockType: wxNodesToBlockTypes['wxnode_slideshow'],
				blockName: 'slide123__wxnode_slideshow',
				slides: [
					{
						imageAsset: { url: 'https://example.com/image1.jpg' },
						imageTitle: 'Test Title 1',
						imageCaption: 'Test Caption 1',
						id: '',
					},
				],
				title: 'Test Slideshow',
				settings: {
					rounded: false,
				},
			});
		});

		test('should handle missing __wxnext property', () => {
			// Arrange: Create test fixture with missing __wxnext
			const slideshowWxNode: JsonObject = {
				id: 'slide123',
				type: 'wxnode_slideshow',
			};

			// Act: Transform the wxNode
			const result = transformWxNodesToBlockConfig(slideshowWxNode);

			// Assert: Verify the transformation - should have empty slides array
			expect(result).toEqual({
				blockType: wxNodesToBlockTypes['wxnode_slideshow'],
				blockName: 'slide123__wxnode_slideshow',
				slides: [],
				title: '',
				settings: {
					rounded: false,
				},
			});
		});
	});

	describe('unknown wxNode type', () => {
		test('should return empty object for unknown wxNode type', () => {
			// Arrange: Create test fixture with unknown wxNode type
			const unknownWxNode: JsonObject = {
				id: 'unknown123',
				type: 'wxnode_unknown',
			};

			// Act: Transform the wxNode
			const result = transformWxNodesToBlockConfig(unknownWxNode);

			// Assert: Verify the transformation
			expect(result).toEqual({});
		});
	});
});
