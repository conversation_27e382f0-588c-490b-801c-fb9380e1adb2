import { CollectionBeforeChangeHook } from 'payload';

// Define types for our data structure
interface VideoFormatUrl {
	format: string;
	url: string;
}

interface DefaultFormat {
	format: string;
	url?: string;
}

interface ChannelVideo {
	channel: string;
	format: string;
	url?: string;
	[key: string]: unknown;
}

interface VideoData {
	videoFormatUrls?: VideoFormatUrl[];
	defaultFormat?: DefaultFormat;
	channelVideos?: ChannelVideo[];
	[key: string]: unknown;
}

export const populateVideoFormats: CollectionBeforeChangeHook = async ({
	data,
}) => {
	const videoData = data as VideoData;

	// Skip if no videoFormatUrls
	if (!videoData.videoFormatUrls || !Array.isArray(videoData.videoFormatUrls)) {
		return data;
	}

	// Auto-populate defaultFormat.url based on selected format
	if (videoData.defaultFormat && videoData.defaultFormat.format) {
		const matchingFormatUrl = videoData.videoFormatUrls.find(
			(formatUrl: VideoFormatUrl) =>
				formatUrl.format === videoData.defaultFormat?.format,
		);

		if (matchingFormatUrl) {
			videoData.defaultFormat.url = matchingFormatUrl.url;
		}
	}

	// Auto-populate url in channelVideos based on selected format
	if (Array.isArray(videoData.channelVideos)) {
		videoData.channelVideos = videoData.channelVideos.map(
			(channelVideo: ChannelVideo) => {
				if (!channelVideo.format) return channelVideo;

				// Find matching format in videoFormatUrls
				const matchingFormatUrl = videoData.videoFormatUrls?.find(
					(formatUrl: VideoFormatUrl) =>
						formatUrl.format === channelVideo.format,
				);

				if (matchingFormatUrl) {
					return {
						...channelVideo,
						url: matchingFormatUrl.url,
					};
				}

				return channelVideo;
			},
		);
	}

	return data;
};
