import type { SelectField } from 'payload';

// Field config for use in collection configuration
export const mappedFormatField: SelectField = {
	name: 'format',
	label: 'Format',
	type: 'select',
	required: true,
	options: [
		{
			label: 'M3U8',
			value: 'm3u8',
		},
		{
			label: 'MP4',
			value: 'mp4',
		},
	],
	admin: {
		components: {
			Field: {
				path: '@repo/payload/fields/components#MappedFormat',
				clientProps: {},
			},
		},
	},
};
