import type { CollectionConfig } from 'payload';

import { slugField } from '../../fields/slug';
import { publishDateField } from '../../fields/publishDate';
import { categoryTag, defaultContentTags, topicTag } from '../../fields/tags';
import { authors } from '../../fields/authors';
import { revalidateArticle } from '../Articles/hooks/revalidateArticle';

import { title, description, externalAuthors } from './fields';
import {
	seoTab,
	contentTab,
	coreMetadataTab,
	channelOverridesTab,
} from './fields/tabs';
import { createTabs, createCollapsible } from '../../fields/utility';
import { setCreatedBy } from '../../fields/createdBy/hooks/setCreatedBy';
import { setUpdatedBy } from '../../fields/updatedBy/hooks/setUpdatedBy';
import { populateVideoFormats } from './hooks/populateVideoFormats';
import { generatePreviewPath } from '@repo/payload/utils/generatePreviewPath';
import { createCollectionAccess } from '../Roles/utils/hasPermission';

const videoTabs = [contentTab, seoTab, coreMetadataTab, channelOverridesTab];

export const Videos: CollectionConfig = {
	slug: 'videos',
	access: createCollectionAccess('videos'),
	defaultPopulate: {
		authors: true,
		title: true,
		category: true,
		topic: true,
		publishDate: true,
		updatedAt: true,
		createdBy: true,
		seo: {
			title: true,
			description: true,
		},
	},
	admin: {
		useAsTitle: 'title',
		defaultColumns: ['title', 'slug', 'status', 'publishDate', 'updatedAt'],
		livePreview: {
			url: ({ data, locale }) =>
				generatePreviewPath({
					data,
					locale: locale.code,
					collection: 'videos',
					isLivePreview: true,
				}),
		},
		preview: (data, { locale }: { locale: string }) =>
			generatePreviewPath({ data, locale, collection: 'videos' }),
	},
	fields: [
		createCollapsible(
			[title, description, categoryTag, topicTag, authors, externalAuthors],
			'Header',
			{
				admin: { initCollapsed: false },
			},
		),
		createTabs(videoTabs),
		defaultContentTags,
		...publishDateField(),
		...slugField(),
	],
	hooks: {
		afterChange: [revalidateArticle], // Reusing the article revalidation hook
		beforeChange: [setCreatedBy, setUpdatedBy, populateVideoFormats],
	},
	versions: {
		drafts: {
			autosave: {
				interval: 1000, // We set this interval for optimal live preview
			},
			schedulePublish: true,
		},
		maxPerDoc: 50,
	},
};
