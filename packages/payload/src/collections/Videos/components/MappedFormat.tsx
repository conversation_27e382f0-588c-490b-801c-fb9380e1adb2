'use client';

import React, { useEffect, useRef, useMemo } from 'react';
import { reduceFieldsToValues } from 'payload/shared';
import { SelectInput, useField, useFormFields } from '@payloadcms/ui';

// Define the props type for our component
type MappedFormatProps = {
	field: {
		name: string;
		label: string;
		required?: boolean;
		options?: Array<{ label: string; value: string }>;
		[key: string]: unknown;
	};
	path: string;
	[key: string]: unknown;
};

export const MappedFormat: React.FC<MappedFormatProps> = ({
	field,
	path,
	...rest
}) => {
	// Use destructured useField hook
	// const { value, setValue } = useField<string>({ path });
	// console.log('VIDEO: field:', value);
	const { value: fieldValue, setValue: setFieldValue } = useField<
		string | { value: string; label: string }
	>({ path });

	// Extract the actual string value
	const value =
		typeof fieldValue === 'object' && fieldValue !== null
			? fieldValue.value
			: fieldValue;
	console.log('VIDEO: value:', value);

	// Create a custom setValue that ensures we're always setting a string
	const setValue = (val: unknown) => {
		const stringValue =
			typeof val === 'object' && val !== null && 'value' in val
				? val.value
				: val;
		setFieldValue(stringValue);
	};

	// Use useRef for paths that won't change
	const parentPath = useRef(path.split('.').slice(0, -1).join('.'));
	const urlPath = useRef(`${parentPath.current}.url`);

	// Get access to the videoFormatUrls field
	const rawVideoFormatUrls = useFormFields(([fields]) => {
		const formState = reduceFieldsToValues(fields, true);
		return formState?.content?.videoFormatUrls || [];
	});

	// Create a stable string key for the dependency array
	const videoFormatUrlsKey = useMemo(() => {
		try {
			return JSON.stringify(rawVideoFormatUrls);
		} catch (_error) {
			// Fallback in case of circular references
			return '';
		}
	}, [rawVideoFormatUrls]);

	// Memoize videoFormatUrls based on its content
	const videoFormatUrls = useMemo(() => {
		return rawVideoFormatUrls;
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [videoFormatUrlsKey]);

	// Get the URL field setter outside the useEffect
	const { setValue: setUrlValue } = useField<string>({ path: urlPath.current });

	// Update the URL field when the format changes
	useEffect(() => {
		if (value && videoFormatUrls.length > 0) {
			// Find the matching format in videoFormatUrls
			const matchingFormat = videoFormatUrls.find(
				(formatUrl: { format: string; url: string }) =>
					formatUrl && formatUrl.format === value,
			);

			// If a matching format is found, update the URL field
			if (matchingFormat && matchingFormat.url) {
				setUrlValue(matchingFormat.url);
			}
		}
	}, [value, videoFormatUrls, setUrlValue]);
	console.log('VIDEO: options:', field.options);
	console.log('VIDEO: value:', value);

	return (
		<SelectInput
			{...rest}
			path={path}
			name={field.name}
			required={field.required}
			label={field.label}
			options={field.options}
			value={value}
			onChange={setValue}
		/>
	);
};
