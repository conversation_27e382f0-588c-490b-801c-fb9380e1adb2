import { Block } from 'payload';
import TwitterBlockConfig from '../../../blocks/Twitter/config';
import ImageBlockConfig from '../../../blocks/Image/config';
import VideoBlockConfig from '../../../blocks/Video/config';
import ContentMediaBlockConfig from '../../../blocks/ContentMedia/config';
import BuyButtonBlockConfig from '../../../blocks/BuyButton/config';
import YouTubeBlockConfig from '../../../blocks/YouTube/config';
import CallToActionBlockConfig from '../../../blocks/CallToAction/config';
import SlideshowBlockConfig from '../../../blocks/Slideshow/config';
import MorningBriefBlockConfig from '../../../blocks/MorningBrief/config';
import DailyForecastBlockConfig from '../../../blocks/DailyForecast/config';
import CurrentConditionsBlockConfig from '../../../blocks/CurrentConditions/config';
import InstagramBlockConfig from '../../../blocks/Instagram/config';
/*
 * This is the list of blocks that are allowed to be used on liveblog entries
 * Do not modify change the structure of this list without modifying the corresponding
 * scaffolder
 * TODO: perhaps this should not be edited and only generated by the scaffolder
 * */
// prettier-ignore
export const allowedBlocks: Block[] = [
	MorningBriefBlockConfig,
	DailyForecastBlockConfig,
	CurrentConditionsBlockConfig,
	YouTubeBlockConfig,
	BuyButtonBlockConfig,
	ImageBlockConfig,
	VideoBlockConfig,
	ContentMediaBlockConfig,
	TwitterBlockConfig,
	CallToActionBlockConfig,
	SlideshowBlockConfig,
	InstagramBlockConfig,
];
