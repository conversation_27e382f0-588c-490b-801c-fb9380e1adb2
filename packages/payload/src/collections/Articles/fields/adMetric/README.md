# Ad Metric Field

This custom field provides a relationship field for selecting ad metrics in articles, with the ability to automatically derive the ad metric from the article's category.

## Overview

The Ad Metric field is a specialized relationship field that connects articles to ad metrics (which are stored as tags with type 'adMetrics'). It has two modes of operation:

1. **Auto Mode (default)**: The ad metric is automatically derived from the article's category
2. **Manual Mode**: The user can manually select any ad metric from a dropdown

The field includes a toggle button that allows users to switch between these modes.

## Architecture

### File Structure

```text
adMetric/
├── README.md                           # This documentation
├── index.ts                            # Field definition and hooks
└── components/
    ├── AdMetricServerComponent.tsx     # Server component for data fetching
    └── AdMetricComponent.tsx           # Client component for UI rendering
```

### Component Flow

1. **Field Definition (`index.ts`)**:

   - Defines the relationship field and checkbox field
   - Sets up hooks for auto-populating the ad metric from the category
   - Configures the field to use the custom components

2. **Server Component (`AdMetricServerComponent.tsx`)**:

   - Fetches data from the server (category data, ad metric data)
   - Passes the data to the client component as props

3. **Client Component (`AdMetricComponent.tsx`)**:
   - Renders the UI based on the current mode (auto or manual)
   - Handles user interactions (toggling modes, selecting ad metrics)
   - Updates the field value based on category changes or user selections

## State Management

The component uses a dual-state approach:

1. **Local Component State**:

   - `adMetricId`: Tracks the ID of the selected ad metric for internal component logic
   - `adMetricName`: Stores the display name of the selected ad metric

2. **Form Field Value**:
   - Managed by Payload's `useField` hook
   - This is the actual value that gets saved to the database

This approach allows for separation between UI state and form data, making it easier to handle display logic independently from form submission data.

## Auto-Generation Logic

When in auto mode, the ad metric is derived from the category using this process:

1. The component watches for changes to the category field
2. When a category is selected, it fetches the category data
3. If the category has an associated ad metric, it:
   - Updates the local state with the ad metric ID and name
   - Sets the field value to the ad metric ID
4. If the category has no associated ad metric, it:
   - Clears the local state
   - Sets the display text to "No ad metric selected"
   - Sets the field value to null
5. If the category is cleared, it:
   - Clears the local state
   - Sets the field value to null

## Usage

### Adding to a Collection

```typescript
import { adMetricFields } from "@/collections/Articles/fields/adMetric";

const articleFields = [
	// Other fields...
	...adMetricFields(),
];
```

### Customization Options

The `adMetricFields` function accepts an optional `overrides` object:

```typescript
adMetricFields({
	adMetricOverrides: {
		// Override relationship field properties
		required: true,
		admin: {
			description: "Custom description",
		},
	},
	checkboxOverrides: {
		// Override checkbox field properties
		defaultValue: false,
	},
});
```

## UI Behavior

- The field displays a toggle button labeled "Generate from Category" or "Specify Manually"
- In auto mode, it shows a read-only text input with the ad metric name
- In manual mode, it shows a relationship field dropdown for selecting any ad metric
- When a category without an ad metric is selected in auto mode, it displays "No ad metric selected"
- When the category is cleared in auto mode, the ad metric is also cleared

## Implementation Notes

1. The checkbox field is hidden in the admin UI to avoid confusion
2. The relationship field label is removed in manual mode to prevent duplicate labels
3. The component maintains separate state for tracking changes and updating the display name
4. The adMetricLock value is properly synchronized between:
   - The database value in `coreMetadata.adMetricLock`
   - The form field value
   - The UI component state

## Integration with updateAdMetrics Job

The adMetric field works in conjunction with the `updateAdMetrics` job to maintain consistency between categories and articles:

1. When a category's ad metric changes, the `updateAdMetrics` job is triggered
2. The job updates all articles with that category, setting:
   - `coreMetadata.adMetric` to the new ad metric ID
   - `coreMetadata.adMetricLock` to `true` to indicate auto-generation
3. When the article is loaded in the admin UI:
   - The `AdMetricServerComponent` reads the `coreMetadata.adMetricLock` value
   - The `AdMetricComponent` initializes the checkbox field with this value
   - The UI displays the appropriate mode (auto or manual) based on this value

This ensures that articles maintain their ad metric inheritance relationship with categories, while still allowing for manual overrides when needed.
