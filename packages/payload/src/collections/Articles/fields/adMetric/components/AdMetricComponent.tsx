'use client';
import React, {
	useCallback,
	useEffect,
	useState,
	useRef,
	useMemo,
} from 'react';
import { RelationshipFieldClientProps } from 'payload';
import { Tag } from '@repo/payload/payload-types';
import {
	useField,
	Button,
	FieldLabel,
	useFormFields,
	useForm,
	usePayloadAPI,
	RelationshipField,
	TextInput,
} from '@payloadcms/ui';

// Define the structure of relation field values
type RelationFieldValue = {
	relationTo: string;
	value: string;
};

type AdMetricComponentProps = {
	checkboxFieldPath: string;
	categoryId: string | null;
	adMetricId: string | null;
	adMetricName: string;
	adMetrics: Tag[];
	adMetricLock?: boolean;
} & RelationshipFieldClientProps;

export const AdMetricComponent: React.FC<AdMetricComponentProps> = ({
	field,
	checkboxFieldPath,
	path,
	readOnly: readOnlyFromProps,
	adMetricId: initialAdMetricId,
	adMetricName: initialAdMetricName,
	adMetricLock = true,
}) => {
	const { label } = field;

	// State to track fetched data
	// Note: We maintain a separate adMetricId state for internal component logic
	// even though we also have the value from useField. This allows us to track
	// changes and update the display name independently from the form value.
	const [_adMetricId, setAdMetricId] = useState<string | null>(
		initialAdMetricId,
	);
	const [adMetricName, setAdMetricName] = useState<string>(initialAdMetricName);

	// Ref to track previous category ID
	const prevCategoryIdRef = useRef<string | null>(null);

	// Get the value and setValue from the field
	// This is the actual value that will be saved to the database
	const { setValue } = useField<{ id: string } | string>({
		path: path || field.name,
	});

	const { dispatchFields } = useForm();

	// The value of the checkbox - when true, ad metric is auto-generated from category
	const checkboxValue = useFormFields(([fields]) => {
		return fields[checkboxFieldPath]?.value as boolean;
	});

	// Get the current form fields to check if the checkbox value is already set
	const formFields = useFormFields(([fields]) => fields);

	// Initialize the checkbox field with the adMetricLock value
	// But only if it's not already set in the form
	useEffect(() => {
		// Only set the value if it's not already defined in the form
		// This ensures we don't override a user's manual selection
		if (formFields[checkboxFieldPath]?.value === undefined) {
			dispatchFields({
				type: 'UPDATE',
				path: checkboxFieldPath,
				value: adMetricLock,
			});
		}
	}, [adMetricLock, checkboxFieldPath, dispatchFields, formFields]);

	// Watch for changes in category field with proper typing
	const categoryValue = useFormFields(([fields]) => {
		if (!fields.category || !fields.category.value) return '';

		// Type assertion to help TypeScript understand the structure
		const categoryField = fields.category.value as RelationFieldValue;
		return categoryField.value;
	});

	// Use Payload API for category data - always provide a valid URL
	const [{ data: categoryData }, { setParams: setCategoryParams }] =
		usePayloadAPI(
			`/api/payload/tags/${categoryValue || 'placeholder'}?depth=1&draft=false&locale=en-US`,
		);

	// Update ad metric when category data changes or is cleared
	useEffect(() => {
		// If checkbox is checked (auto mode)
		if (checkboxValue) {
			// If category is cleared or doesn't exist
			if (!categoryValue) {
				// Clear the ad metric
				setAdMetricId(null);
				setAdMetricName('No ad metric selected');
				setValue(null);
				return;
			}

			// If category data exists
			if (categoryData) {
				const category = categoryData as Tag;

				// If the category has an associated ad metric
				if (category?.adMetric) {
					let newAdMetricId: string | null = null;

					if (
						typeof category.adMetric === 'object' &&
						category.adMetric !== null
					) {
						// Get the ID from the Tag object
						newAdMetricId = category.adMetric.id;

						// Also update the ad metric name for display
						if (category.adMetric.name) {
							setAdMetricName(category.adMetric.name);
						}
					} else if (typeof category.adMetric === 'string') {
						newAdMetricId = category.adMetric;
					}

					if (newAdMetricId) {
						setAdMetricId(newAdMetricId);

						// Set the field value as an object for relationship fields
						// This is what actually gets saved to the database
						setValue({
							id: newAdMetricId,
						});
					}
				} else {
					// Category exists but has no ad metric
					setAdMetricId(null);
					setAdMetricName('No ad metric found for this category');
					setValue(null);
				}
			}
		}
	}, [categoryData, categoryValue, checkboxValue, setValue]);

	// Trigger refetch when category value changes
	useEffect(() => {
		if (categoryValue && categoryValue !== prevCategoryIdRef.current) {
			setCategoryParams({
				id: categoryValue,
				depth: 1,
				draft: false,
				locale: 'en-US',
			});
			prevCategoryIdRef.current = categoryValue;
		} else if (!categoryValue && prevCategoryIdRef.current) {
			// Category was cleared
			prevCategoryIdRef.current = null;

			// If in auto mode, clear the ad metric
			if (checkboxValue) {
				setAdMetricId(null);
				setAdMetricName('No ad metric selected');
				setValue(null);
			}
		}
	}, [categoryValue, setCategoryParams, checkboxValue, setValue]);

	// Handle lock/unlock button click
	const handleLock = useCallback(
		(e: React.MouseEvent<Element>) => {
			e.preventDefault();

			// Update the checkbox field in the form
			dispatchFields({
				type: 'UPDATE',
				path: checkboxFieldPath,
				value: !checkboxValue,
			});

			// Also update the adMetricLock field in coreMetadata
			// This is necessary because the field is defined in the coreMetadata object
			dispatchFields({
				type: 'UPDATE',
				path: 'coreMetadata.adMetricLock',
				value: !checkboxValue,
			});
		},
		[checkboxValue, checkboxFieldPath, dispatchFields],
	);

	// Memoize the readOnly value
	const readOnly = useMemo(
		() => readOnlyFromProps || checkboxValue,
		[readOnlyFromProps, checkboxValue],
	);

	// Create a modified field with the filter options and no label (to avoid duplicate labels)
	const modifiedField = useMemo(() => {
		return {
			...field,
			label: '', // Remove the label from the RelationshipField to avoid duplication
			filterOptions: {
				type: {
					equals: 'adMetrics',
				},
			},
		};
	}, [field]);

	return (
		<div className="field-type">
			<div className="flex items-center justify-between">
				<FieldLabel htmlFor={`field-${path}`} label={label} />

				<Button
					className="m-0 pb-[0.3125rem]"
					buttonStyle="none"
					onClick={handleLock}
				>
					{checkboxValue ? 'Specify Manually' : 'Generate from Category'}
				</Button>
			</div>

			{checkboxValue ? (
				<TextInput
					path={path}
					value={adMetricName || 'No ad metric selected'}
					readOnly={readOnly}
				/>
			) : (
				<RelationshipField field={modifiedField} path={path} />
			)}
		</div>
	);
};
