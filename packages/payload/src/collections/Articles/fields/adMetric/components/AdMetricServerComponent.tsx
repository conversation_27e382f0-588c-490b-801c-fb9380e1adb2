import React from 'react';
import { RelationshipFieldServerProps } from 'payload';
import { AdMetricComponent } from './AdMetricComponent';
import { Tag } from '@repo/payload/payload-types';

export const AdMetricServerComponent = async ({
	path,
	readOnly,
	payload,
	data,
	clientField,
	permissions,
	schemaPath,
	checkboxFieldPath,
}: RelationshipFieldServerProps & { checkboxFieldPath: string }) => {
	// Extract category ID from the data
	const categoryId = data?.category?.value;

	// Initialize variable to store ad metric ID and name
	let adMetricId = null;
	let adMetricName = '';

	// Get the adMetricLock value from coreMetadata
	const adMetricLock = data?.coreMetadata?.adMetricLock ?? true;

	// Fetch available ad metrics for the dropdown
	let adMetrics: Tag[] = [];
	try {
		const adMetricsResult = await payload.find({
			collection: 'tags',
			where: {
				type: {
					equals: 'adMetrics',
				},
			},
			limit: 100,
		});

		if (adMetricsResult?.docs) {
			adMetrics = adMetricsResult.docs;
		}
	} catch (error) {
		payload.logger.error(`Error fetching ad metrics: ${error}`);
	}

	// Fetch category data if available
	if (categoryId && typeof categoryId === 'string') {
		try {
			const categoryData = await payload.findByID({
				collection: 'tags',
				id: categoryId,
				depth: 1,
			});

			const category = categoryData;

			// If the category has an associated ad metric, get its ID and name
			if (category?.adMetric) {
				if (
					typeof category.adMetric === 'object' &&
					category.adMetric !== null
				) {
					// Get the ID from the Tag object
					adMetricId = category.adMetric.id;

					// Get the name directly from the adMetric object if available
					if (category.adMetric.name) {
						adMetricName = category.adMetric.name;
					}
				} else if (typeof category.adMetric === 'string') {
					adMetricId = category.adMetric;

					// If adMetric is just a string ID, we need to fetch the name
					try {
						const adMetricData = await payload.findByID({
							collection: 'tags',
							id: adMetricId,
						});

						if (adMetricData?.name) {
							adMetricName = adMetricData.name;
						}
					} catch (error) {
						payload.logger.error(`Error fetching ad metric data: ${error}`);
					}
				}
			}
		} catch (error) {
			payload.logger.error(`Error fetching category data: ${error}`);
		}
	}

	// Pass the fetched data to the client component
	return (
		<AdMetricComponent
			field={clientField}
			path={path}
			readOnly={readOnly}
			permissions={permissions}
			schemaPath={schemaPath}
			checkboxFieldPath={checkboxFieldPath}
			categoryId={categoryId}
			adMetricId={adMetricId}
			adMetricName={adMetricName}
			adMetrics={adMetrics}
			adMetricLock={adMetricLock}
		/>
	);
};
