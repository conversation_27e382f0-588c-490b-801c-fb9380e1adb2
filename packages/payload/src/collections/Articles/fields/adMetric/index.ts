import type { RelationshipField, <PERSON><PERSON>Field, FieldHook } from 'payload';
import { Tag } from '@repo/payload/payload-types';

// This hook will eventually be removed as the UI component will handle this logic
export const getAdMetricFromCategory =
	(): FieldHook =>
	async ({ data, req, value }) => {
		// If the value is already set, return it
		if (value) {
			return value;
		}

		// If there's no category, return null
		if (!data?.category) {
			return null;
		}

		// Get the category ID - handle the nested structure with locale
		let categoryId: string | null = null;

		if (typeof data.category === 'object') {
			// Handle localized category field
			if ('en-US' in data.category) {
				const localizedCategory = data.category['en-US'];
				if (
					localizedCategory &&
					typeof localizedCategory === 'object' &&
					'value' in localizedCategory
				) {
					categoryId = String(localizedCategory.value);
				}
			}
			// Handle direct relationship object
			else if ('value' in data.category) {
				categoryId = String(data.category.value);
			}
		} else if (typeof data.category === 'string') {
			categoryId = data.category;
		}

		if (!categoryId) {
			return null;
		}

		try {
			// Find the category tag
			const category = (await req.payload.findByID({
				collection: 'tags',
				id: categoryId,
				depth: 1,
			})) as Tag;

			// If the category has an associated ad metric, return it
			if (category?.adMetric) {
				let adMetricId: string | null = null;

				if (
					typeof category.adMetric === 'object' &&
					category.adMetric !== null
				) {
					// Get the ID from the Tag object
					adMetricId = String(category.adMetric.id);
				} else if (typeof category.adMetric === 'string') {
					adMetricId = category.adMetric;
				}

				if (adMetricId) {
					// Return just the ID as a string
					return String(adMetricId);
				}
			}
		} catch (error) {
			console.error('Error getting ad metric from category:', error);
		}

		return null;
	};

type Overrides = {
	adMetricOverrides?: Partial<RelationshipField>;
	checkboxOverrides?: Partial<CheckboxField>;
};

export const adMetricFields = (
	overrides: Overrides = {},
): [RelationshipField, CheckboxField] => {
	const { adMetricOverrides, checkboxOverrides } = overrides;

	const checkBoxField: CheckboxField = {
		name: 'adMetricLock',
		type: 'checkbox',
		defaultValue: true,
		admin: {
			hidden: true, // Hide the checkbox field in the admin UI
		},
		...checkboxOverrides,
	};

	const adMetricField = {
		name: 'adMetric',
		type: 'relationship',
		relationTo: 'tags',
		hasMany: false,
		required: false,
		label: 'Ad metric for this article',
		admin: {
			description:
				'This field is automatically populated based on the category, but can be overridden if unlocked',
			readOnly: false,
			...(adMetricOverrides?.admin || {}),
			components: {
				Field: {
					path: '@repo/payload/fields/components#AdMetricServerComponent',
					serverProps: {
						checkboxFieldPath: checkBoxField.name,
					},
				},
			},
		},
		hooks: {
			beforeValidate: [getAdMetricFromCategory()],
		},
		filterOptions: {
			type: {
				equals: 'adMetrics',
			},
		},
		...(adMetricOverrides || {}),
	} as RelationshipField;

	return [adMetricField, checkBoxField];
};
