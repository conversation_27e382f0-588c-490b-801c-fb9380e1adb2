import { <PERSON><PERSON>ield, RichTextField } from 'payload';
import type { <PERSON><PERSON>y<PERSON>ield, Field, TextFieldSingleValidation } from 'payload';
import {
	MetaDescriptionField,
	MetaImageField,
	MetaTitleField,
	OverviewField,
} from '@payloadcms/plugin-seo/fields';
import {
	BlocksFeature,
	FixedToolbarFeature,
	HeadingFeature,
	HorizontalRuleFeature,
	LinkFeature,
	InlineToolbarFeature,
	lexicalEditor,
	type LinkFields,
	OrderedListFeature,
} from '@payloadcms/richtext-lexical';
import { allowedBlocks } from './blocks';
import { isValidUrl } from '@repo/payload/utils/isValidUrl';

export const title: TextField = {
	name: 'title',
	label: 'Headline',
	localized: true,
	type: 'text',
	required: true,
};

export const subHeadline: TextField = {
	name: 'subHeadline',
	label: 'Sub Headline',
	localized: true,
	type: 'text',
	required: false,
};

export const headerLayout: Field = {
	name: 'headerLayout',
	label: 'Header Layout',
	type: 'select',
	options: ['default', 'noAuthorImages'],
	required: true,
};

export const partnerByline: Field = {
	name: 'partnerByl<PERSON>',
	label: 'Partner Byline',
	type: 'group',
	localized: true,
	admin: {
		description: 'Can be either rich text or plain text',
	},
	fields: [
		{
			name: 'type',
			label: 'Content Type',
			type: 'select',
			options: [
				{
					label: 'Rich Text',
					value: 'richText',
				},
				{
					label: 'Plain Text',
					value: 'text',
				},
			],
			defaultValue: 'richText',
			required: true,
		},
		{
			name: 'richText',
			label: 'Rich Text Content',
			type: 'richText',
			admin: {
				condition: (data, siblingData) => siblingData?.type === 'richText',
			},
			editor: lexicalEditor({
				features: ({ rootFeatures }) => {
					return [
						...rootFeatures,
						HeadingFeature({
							enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'],
						}),
						BlocksFeature({
							blocks: allowedBlocks ?? [],
						}),
						FixedToolbarFeature(),
						InlineToolbarFeature(),
						OrderedListFeature(),
						HorizontalRuleFeature(),
						LinkFeature({
							enabledCollections: ['pages', 'articles'],
							fields: ({ defaultFields }) => {
								const defaultFieldsWithoutUrl = defaultFields.filter(
									(field) => {
										if ('name' in field && field.name === 'url') return false;
										return true;
									},
								);

								return [
									...defaultFieldsWithoutUrl,
									{
										name: 'url',
										type: 'text',
										admin: {
											condition: (_data, siblingData) =>
												siblingData?.linkType !== 'internal',
										},
										label: ({ t }) => t('fields:enterURL'),
										required: true,
										validate: ((value, options) => {
											if (
												(options?.siblingData as LinkFields)?.linkType ===
												'internal'
											) {
												return true; // no validation needed, as no url should exist for internal links
											}
											return value ? true : 'URL is required';
										}) as TextFieldSingleValidation,
									},
								];
							},
						}),
					];
				},
			}),
		},
		{
			name: 'text',
			label: 'Plain Text Content',
			type: 'text',
			admin: {
				condition: (data, siblingData) => siblingData?.type === 'text',
			},
		},
	],
	required: false,
};

export const body: RichTextField = {
	name: 'body',
	label: '',
	localized: true,
	type: 'richText',
	editor: lexicalEditor({
		features: ({ rootFeatures }) => {
			return [
				...rootFeatures,
				HeadingFeature({
					enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'],
				}),
				BlocksFeature({
					blocks: allowedBlocks ?? [],
				}),
				FixedToolbarFeature(),
				InlineToolbarFeature(),
				OrderedListFeature(),
				HorizontalRuleFeature(),
				LinkFeature({
					enabledCollections: ['pages', 'articles'],
					fields: ({ defaultFields }) => {
						const defaultFieldsWithoutUrl = defaultFields.filter((field) => {
							if ('name' in field && field.name === 'url') return false;
							return true;
						});

						return [
							...defaultFieldsWithoutUrl,
							{
								name: 'url',
								type: 'text',
								admin: {
									condition: (_data, siblingData) =>
										siblingData?.linkType !== 'internal',
								},
								label: ({ t }) => t('fields:enterURL'),
								required: true,
								validate: ((value, options) => {
									if (
										(options?.siblingData as LinkFields)?.linkType ===
										'internal'
									) {
										return true; // no validation needed, as no url should exist for internal links
									}
									return value ? true : 'URL is required';
								}) as TextFieldSingleValidation,
							},
						];
					},
				}),
			];
		},
	}),
	required: true,
};

export const canonicalUrl: TextField = {
	name: 'canonicalUrl',
	label: 'Canonical URL',
	type: 'text',
	admin: {
		description:
			'Used by partners to point to their external URL (e.g. https://example.com)',
	},
	validate: (value) => {
		if (!value) return true; // Field is optional
		if (isValidUrl(value)) return true;

		return 'Please enter a valid URL including the protocol (e.g., https://example.com)';
	},
};

export const seoOverview = OverviewField({
	titlePath: 'meta.title',
	descriptionPath: 'meta.description',
	imagePath: 'meta.image',
});
export const seoMeta = MetaTitleField({
	hasGenerateFn: true,
});
export const seoMetaImage = MetaImageField({
	relationTo: 'images',
});

export const seoMetaDescription = MetaDescriptionField({});

export const channelHeadlines: ArrayField = {
	name: 'channelHeadlines',
	label: 'Channel Headlines',
	type: 'array',
	admin: {
		description: 'Channel headlines override the standard article headline',
	},
	fields: [
		{
			name: 'channel',
			label: 'Channel',
			type: 'relationship',
			relationTo: 'tags',
			required: true,
			hasMany: false,
			filterOptions: {
				type: {
					equals: 'channel',
				},
			},
		},
		{
			name: 'headline',
			label: 'Headline',
			type: 'text',
			required: true,
			localized: true,
		},
	],
};

export const externalAuthors: <AUTHORS>
	name: 'externalAuthors',
	label: 'External Authors',
	type: 'text',
	required: false,
};
