import { describe, it, expect, vi, beforeEach } from 'vitest';
import { canonicalUrl } from '../index';
import * as urlUtils from '@repo/payload/utils/isValidUrl';

describe('canonicalUrl validation', () => {
	// Extract the validation function from the field
	const validateFn = canonicalUrl.validate as (value: string) => true | string;

	// Setup and teardown for mocking
	beforeEach(() => {
		vi.restoreAllMocks();
	});

	it('should return true when isValidUrl returns true', () => {
		vi.spyOn(urlUtils, 'isValidUrl').mockReturnValue(true);
		expect(validateFn('https://example.com')).toBe(true);
	});

	it('should return error message when isValidUrl returns false', () => {
		vi.spyOn(urlUtils, 'isValidUrl').mockReturnValue(false);
		expect(validateFn('invalid-url')).toEqual(
			expect.stringContaining('Please enter a valid URL'),
		);
	});

	it.each([
		['empty string', ''],
		['null', null],
		['undefined', undefined],
	])('should accept %s', (_, value) => {
		// @ts-expect-error Testing with null/undefined which might happen in runtime
		expect(validateFn(value)).toBe(true);
	});
});
