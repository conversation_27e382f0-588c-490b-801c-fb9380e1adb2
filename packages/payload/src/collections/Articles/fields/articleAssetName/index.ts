import { CheckboxField, TextField } from 'payload';

type Overrides = {
	assetNameOverrides?: Partial<TextField>;
	checkboxOverrides?: Partial<CheckboxField>;
};

export const articleAssetNameFields = (
	overrides: Overrides = {},
): [TextField, CheckboxField] => {
	const { assetNameOverrides, checkboxOverrides } = overrides;

	const checkBoxField: CheckboxField = {
		name: 'assetNameLock',
		type: 'checkbox',
		defaultValue: true,
		admin: {
			hidden: true,
			position: 'sidebar',
		},
		...checkboxOverrides,
	};

	// Use a more explicit type definition with 'as TextField'
	const assetNameField = {
		name: 'assetName',
		type: 'text',
		localized: true,
		index: true,
		label: 'Asset Name',
		admin: {
			position: 'sidebar',
			readOnly: false,
			...(assetNameOverrides?.admin || {}),
			components: {
				Field: {
					path: '@repo/payload/fields/components#ArticleAssetNameServerComponent',
					serverProps: {
						checkboxFieldPath: checkBoxField.name,
					},
				},
			},
		},
		...(assetNameOverrides || {}),
	} as TextField;

	return [assetNameField, checkBoxField];
};
