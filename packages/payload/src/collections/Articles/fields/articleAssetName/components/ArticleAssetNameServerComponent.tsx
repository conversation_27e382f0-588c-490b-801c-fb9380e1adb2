import React from 'react';
import { TextFieldServerProps } from 'payload';
import { ArticleAssetNameComponent } from './ArticleAssetNameComponent';
import { formatDate } from '../utils/formatDate';
import { getLocalizedValue } from '@repo/payload/utils/getLocalizedValue';

export const ArticleAssetNameServerComponent = async ({
	path,
	readOnly,
	payload,
	data,
	clientField,
	checkboxFieldPath,
}: TextFieldServerProps & { checkboxFieldPath: string }) => {
	// Extract category and topic IDs from the data
	const categoryId = data?.category?.value;
	const topicId = data?.topic?.value;

	const locale = 'en-US';

	// Initialize variables to store category and topic slugs
	let categorySlug = '';
	let topicSlug = '';

	// Fetch category data if available
	if (categoryId && typeof categoryId === 'string') {
		try {
			const categoryData = await payload.findByID({
				collection: 'tags',
				id: categoryId,
			});

			if (categoryData && categoryData.slug) {
				categorySlug = getLocalizedValue(categoryData.slug, locale) ?? '';
			}
		} catch (error) {
			payload.logger.error(`Error fetching category data: ${error}`);
		}
	}

	// Fetch topic data if available
	if (topicId && typeof topicId === 'string') {
		try {
			const topicData = await payload.findByID({
				collection: 'tags',
				id: topicId,
			});

			if (topicData && topicData.slug) {
				topicSlug = getLocalizedValue(topicData.slug, locale) ?? '';
			}
		} catch (error) {
			payload.logger.error(`Error fetching topic data: ${error}`);
		}
	}

	// Format the publish date if available
	let formattedDate = '';
	if (data?.publishDate) {
		try {
			formattedDate = formatDate(data.publishDate);
		} catch (error) {
			payload.logger.error(`Error formatting date: ${error}`);
		}
	}

	// Generate a sample asset name with a placeholder slug
	const sampleAssetName = generateSampleAssetName({
		locale,
		formattedDate,
		categorySlug,
		topicSlug,
	});

	// Pass the fetched data to the client component
	return (
		<ArticleAssetNameComponent
			field={clientField}
			path={path}
			readOnly={readOnly}
			checkboxFieldPath={checkboxFieldPath}
			categorySlug={categorySlug}
			topicSlug={topicSlug}
			formattedDate={formattedDate}
			initialValue={''}
			sampleAssetName={sampleAssetName}
		/>
	);
};

// Helper function to generate a sample asset name
function generateSampleAssetName({
	locale,
	formattedDate,
	categorySlug,
	topicSlug,
}: {
	locale: string;
	formattedDate: string;
	categorySlug: string;
	topicSlug: string;
}) {
	if (!categorySlug || !formattedDate) return '';

	const pathParts = [formattedDate, categorySlug];
	if (locale !== 'en-US') pathParts.unshift(locale);
	if (topicSlug) pathParts.push(topicSlug);
	pathParts.push('[slug]'); // Placeholder for the slug

	return `/${pathParts.join('/')}`;
}
