/**
 * Formats a date string into YYYY/MM/DD format for asset name paths
 * @param dateString ISO date string
 * @returns Formatted date string in YYYY/MM/DD format
 */
export function formatDate(dateString: string): string {
	const date = new Date(dateString);

	const year = date.getFullYear();
	// Add 1 to month since getMonth() returns 0-11
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');

	return `${year}/${month}/${day}`;
}
