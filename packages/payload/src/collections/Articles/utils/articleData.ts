import { Article, type VideoBlockConfig } from '@repo/payload/payload-types';
import { Metadata } from 'next';
import { PageDebugData } from '../../../components/FrontendAdminHeader/state/types';
import { getServerSideURL } from '@repo/payload/utils/getURL';
import type { DSXCollection } from '@repo/dal/content/collections/types';
import { hasLength } from '@repo/utils/hasLength';

export const getArticlePageDebugData = (article: Article): PageDebugData => {
	return {
		title: article.title,
		description: article.seo?.description || '',
		assetName: article.assetName || '',
		id: article.id,
		status: article._status || 'published',
		collection: 'pages',
		createdAt: article.createdAt,
		updatedAt: article.updatedAt,
		publishedAt: article.publishDate,
		tenant: article.tenant,
		ogTitle: article.seo?.title || '',
		ogDescription: article.seo?.description || '',
		ogImage:
			typeof article.featuredImage === 'string'
				? article.featuredImage
				: article.featuredImage?.url || undefined,
	};
};

export const getArticleDetails = (article: Article) => {
	// Extract author names for metadata
	const authorNames =
		article.authors
			?.map((author) => {
				if (typeof author === 'string') return author;
				return `${author.authorData?.firstName || ''} ${author.authorData?.lastName || ''}`.trim();
			})
			.filter(Boolean) || [];

	// Extract category and topic names
	const categoryName =
		typeof article.category?.value === 'string'
			? article.category.value
			: article.category?.value?.name;

	const topicName =
		article.topic?.value && typeof article.topic.value !== 'string'
			? article.topic.value.name
			: (article.topic?.value as string) || null;

	// Extract tags for keywords
	const tagNames =
		article.tags
			?.map((tag) => {
				if (typeof tag === 'string') return tag;
				return tag.name;
			})
			.filter(Boolean) || [];

	// Combine all relevant tags for keywords
	// TODO: We need to properly add keywords during transform when the seo.keywords field is available.
	const keywords = [categoryName, topicName].filter(Boolean) as string[];

	// Get image URL for OpenGraph/Twitter
	const images: { url: string; alt?: string }[] = [];
	if (article.featuredImage) {
		images.push(
			typeof article.featuredImage === 'string'
				? { url: article.featuredImage }
				: {
						url: article.featuredImage.url || '',
						alt: article.featuredImage.seo?.altText || undefined,
					},
		);
	}

	let video: VideoBlockConfig | undefined = undefined;

	if (hasLength(article?.content?.body?.root?.children)) {
		const firstNode = article.content?.body?.root?.children[0];
		const firstNodeIsVideo =
			firstNode?.type === 'block' &&
			(firstNode.fields as VideoBlockConfig)?.blockType === 'Video';

		if (firstNodeIsVideo) {
			// TODO: maybe use headerBlockk for video
			video = firstNode.fields as VideoBlockConfig;
		}
	}

	const hostname = getServerSideURL();
	const url = hostname + article.assetName;

	return {
		title: article.title || '',
		seoTitle: article.seo?.title || '',
		seoDescription: article.seo?.description || '',
		canonicalUrl: article.seo?.canonicalUrl || url,
		url,
		images,
		createdAt: article.createdAt || undefined,
		datePublished: article.publishDate || undefined,
		dateModified: article.updatedAt,
		authors: authorNames,
		tagNames,
		topicName,
		keywords,
		video,
	};
};

export type ArticleDetails = ReturnType<typeof getArticleDetails>;

export const buildArticlePageMetadata = (article: Article | null): Metadata => {
	if (!article) {
		return {
			title: 'The Weather Channel',
			description: 'The Weather Channel',
		};
	}

	const details = getArticleDetails(article);

	return {
		title: details.seoTitle,
		description: details.seoDescription,
		keywords: details.keywords,
		authors: details.authors.map((name) => ({ name })),
		alternates: {
			canonical: details.canonicalUrl,
			languages: {
				'x-default': details.url,
			},
		},
		openGraph: {
			title: details.seoTitle,
			description: details.seoDescription,
			type: 'article',
			url: details.url,
			publishedTime: details.datePublished,
			modifiedTime: details.dateModified,
			authors: details.authors,
			tags: details.keywords,
			images: details.images,
		},
		twitter: {
			card: 'summary_large_image',
			title: details.seoTitle,
			description: details.seoDescription,
			images: details.images,
		},
	};
};

export interface PageMetadata {
	url: string;
	nextPageUrl?: string;
	previousPageUrl?: string;
}

export const buildArticleIndexesPageMetadata = (
	collection: DSXCollection | null,
	metadata: PageMetadata,
): Metadata => {
	if (!collection) {
		return {
			title: 'The Weather Channel | weather.com',
			description: 'The Weather Channel',
		};
	}

	const { description, seometa, title } = collection;

	const defaultTitle = 'The Weather Channel';

	return {
		title: `${title || defaultTitle} | weather.com`,
		description: description,
		keywords: seometa.keywords,
		openGraph: {
			title: seometa.title || title,
			description:
				seometa.description || seometa['og:description'] || description,
			type: 'article',
			url: metadata.url,
			tags: seometa.keywords,
			images: [{ url: seometa['og:image'], alt: seometa['og:description'] }],
		},
		alternates: {
			canonical: metadata.url,
		},
		pagination: {
			next: metadata.nextPageUrl,
			previous: metadata.previousPageUrl,
		},
	};
};
