import type {
	BasePayload,
	CollectionAfterChangeHook,
	CollectionAfterDeleteHook,
} from 'payload';

import type { Article } from '@repo/payload/payload-types';

/**
 * Revalidate a path using various Next.js revalidation methods
 * This function attempts to use the appropriate method based on the environment
 */
async function revalidatePathCompat(path: string, payload: BasePayload) {
	try {
		// First try to use next/cache methods if available (App Router)
		try {
			const { revalidatePath } = await import('next/cache');
			payload.logger.info(`Revalidating path using App Router method: ${path}`);
			revalidatePath(path);
			return true;
		} catch (_error) {
			// Fall back to using the revalidation API
			payload.logger.info(
				`Falling back to API route revalidation for path: ${path}`,
			);

			// Construct the revalidation URL
			const baseUrl =
				process.env.NEXT_PUBLIC_SERVER_URL || 'https://local.weather.com';
			const revalidationUrl = `${baseUrl}/api/v1/revalidate?path=${encodeURIComponent(path)}&secret=${process.env.PAYLOAD_SECRET}`;

			try {
				// Use fetch API to call the revalidate endpoint with POST method
				const response = await fetch(revalidationUrl, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
				});
				if (!response.ok) {
					payload.logger.warn(`Revalidation API failed for path: ${path}`);
					return false;
				}
				return true;
			} catch (fetchError) {
				payload.logger.error(
					`Failed to call revalidation API for path: ${path}`,
					fetchError,
				);
				return false;
			}
		}
	} catch (mainError) {
		payload.logger.error(
			`Error during revalidation for path: ${path}`,
			mainError,
		);
		return false;
	}
}

/**
 * Revalidate a tag using various Next.js revalidation methods
 * This function attempts to use the appropriate method based on the environment
 */
async function revalidateTagCompat(tag: string, payload: BasePayload) {
	try {
		// First try to use next/cache methods if available (App Router)
		try {
			const { revalidateTag } = await import('next/cache');
			payload.logger.info(`Revalidating tag using App Router method: ${tag}`);
			revalidateTag(tag);
			return true;
		} catch (_error) {
			// Fall back to using the revalidation API
			payload.logger.info(
				`Falling back to API route revalidation for tag: ${tag}`,
			);

			// Construct the revalidation URL
			const baseUrl =
				process.env.NEXT_PUBLIC_SERVER_URL || 'https://local.weather.com';
			const revalidationUrl = `${baseUrl}/api/v1/revalidate?tag=${encodeURIComponent(tag)}&secret=${process.env.PAYLOAD_SECRET}`;

			try {
				// Use fetch API to call the revalidate endpoint with POST method
				const response = await fetch(revalidationUrl, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
				});
				if (!response.ok) {
					payload.logger.warn(`Revalidation API failed for tag: ${tag}`);
					return false;
				}
				return true;
			} catch (fetchError) {
				payload.logger.error(
					`Failed to call revalidation API for tag: ${tag}`,
					fetchError,
				);
				return false;
			}
		}
	} catch (mainError) {
		payload.logger.error(
			`Error during revalidation for tag: ${tag}`,
			mainError,
		);
		return false;
	}
}

export const revalidateArticle: CollectionAfterChangeHook<Article> = async ({
	doc,
	previousDoc,
	req: { payload, context },
}) => {
	if (!context.disableRevalidate) {
		if (doc._status === 'published') {
			const path = doc.assetName;

			if (!path) {
				payload.logger.warn(
					`assetName does not exist before revalidating newpath: ${path}`,
				);
				return doc;
			}

			payload.logger.info(`Revalidating post at path: ${path}`);

			// Replace revalidatePath with our compatible function
			await revalidatePathCompat(path, payload);
			// Replace revalidateTag with our compatible function
			await revalidateTagCompat('articles-sitemap', payload);
		}

		// If the post was previously published, we need to revalidate the old path
		if (previousDoc._status === 'published' && doc._status !== 'published') {
			const oldPath = previousDoc.assetName;

			if (!oldPath) {
				payload.logger.warn(
					`assetName does not exist before revalidating oldPath: ${oldPath}`,
				);
				return doc;
			}

			payload.logger.info(`Revalidating old post at path: ${oldPath}`);

			// Replace revalidatePath with our compatible function
			await revalidatePathCompat(oldPath, payload);
			// Replace revalidateTag with our compatible function
			await revalidateTagCompat('articles-sitemap', payload);
		}
	}
	return doc;
};

export const revalidateDelete: CollectionAfterDeleteHook<Article> = async ({
	doc,
	req: { context, payload },
}) => {
	if (!context.disableRevalidate) {
		const path = `/content/${doc?.slug}`;

		// Replace revalidatePath with our compatible function
		await revalidatePathCompat(path, payload);
		// Replace revalidateTag with our compatible function
		await revalidateTagCompat('articles-sitemap', payload);
	}

	return doc;
};
