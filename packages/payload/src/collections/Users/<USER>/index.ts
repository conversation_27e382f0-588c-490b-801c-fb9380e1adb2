import type { ArrayField, Field, TextField, RelationshipField } from 'payload';
import { tenantsArrayField } from '@payloadcms/plugin-multi-tenant/fields';
import { isAdmin } from '@repo/payload/configs/access';
import { ensureUniqueUsername } from '../hooks/ensureUniqueUsername';

export const name: TextField = {
	name: 'name',
	label: 'Name',
	type: 'text',
	required: true,
};

export const role: RelationshipField = {
	name: 'role',
	label: 'Roles',
	type: 'relationship',
	relationTo: 'roles',
	hasMany: true,
	required: false,
	access: {
		update: () => true,
		create: () => true,
	},
	admin: {
		position: 'sidebar',
		description:
			'Select roles from the Roles collection. All roles created in the Roles collection will appear here.',
		// Ensure the field saves and loads properly
		allowCreate: false, // Prevent creating roles from this field
	},
	// Add filterOptions to ensure we only show active roles
	filterOptions: {
		// You can add filters here if needed
	},
};

export const username: Field = {
	name: 'username',
	type: 'text',
	hooks: {
		beforeValidate: [ensureUniqueUsername],
	},
	index: true,
};

const defaultTenantArrayField = tenantsArrayField({
	tenantsArrayFieldName: 'tenants',
	tenantsArrayTenantFieldName: 'tenant',
	tenantsCollectionSlug: 'tenants',
	arrayFieldAccess: {},
	tenantFieldAccess: {},
	rowFields: [
		{
			name: 'roles',
			type: 'select',
			defaultValue: ['tenant-viewer'],
			hasMany: true,
			options: ['tenant-admin', 'tenant-viewer'],
			required: true,
		},
	],
});

export const tenants: ArrayField = {
	...defaultTenantArrayField,
	access: {
		update: ({ req }) => isAdmin(req.user),
	},
	admin: {
		...(defaultTenantArrayField?.admin || {}),
		position: 'sidebar',
	},
};

export const oktaData: Field = {
	name: '_okta',
	type: 'group',
	label: 'Okta Authentication Data',
	fields: [
		{
			name: 'sub',
			type: 'text',
			label: 'Subject',
			admin: {
				readOnly: true,
				description:
					'The "sub" (subject) claim identifies the principal that is the subject of the JWT',
			},
		},
		{
			name: 'exp',
			type: 'number',
			label: 'Token Expiration',
			admin: {
				readOnly: true,
				description: 'Token expiration timestamp',
			},
		},
		{
			name: 'iss',
			type: 'text',
			label: 'Token Issuer',
			admin: {
				readOnly: true,
				description: 'Okta instance that issued the token',
			},
		},
		{
			name: 'iat',
			type: 'number',
			label: 'Issued At',
			admin: {
				readOnly: true,
				description: 'Token creation timestamp',
			},
		},
		{
			name: 'cid',
			type: 'text',
			label: 'Client ID',
			admin: {
				readOnly: true,
				description: 'OAuth client identifier',
			},
		},
	],
	admin: {
		position: 'sidebar',
		description: 'Okta authentication metadata (automatically managed)',
	},
	access: {
		read: ({ req }) => isAdmin(req.user),
		update: () => false, // Prevent manual updates
	},
};
