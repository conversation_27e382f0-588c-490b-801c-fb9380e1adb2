import type { FieldHook, Where } from 'payload';

import { ValidationError } from 'payload';

import { getUserTenantIDs } from '@repo/payload/utils/getUserTenantIDs';
import { getTenantFromCookie } from '@payloadcms/plugin-multi-tenant/utilities';
import { getCollectionIDType } from '@repo/payload/utils/getCollectionIDType';

export const ensureUniqueUsername: FieldHook = async ({
	originalDoc,
	req,
	value,
}) => {
	// if value is unchanged, skip validation
	if (originalDoc.username === value) {
		return value;
	}

	const constraints: Where[] = [
		{
			username: {
				equals: value,
			},
		},
	];

	const selectedTenant = getTenantFromCookie(
		req.headers,
		getCollectionIDType({ payload: req.payload, collectionSlug: 'tenants' }),
	);

	if (selectedTenant) {
		constraints.push({
			'tenants.tenant': {
				equals: selectedTenant,
			},
		});
	}

	const findDuplicateUsers = await req.payload.find({
		collection: 'users',
		req,
		where: {
			and: constraints,
		},
	});

	if (findDuplicateUsers.docs.length > 0 && req.user) {
		const tenantIDs = getUserTenantIDs(req.user);
		// if the user is an admin or has access to more than 1 tenant
		// provide a more specific error message
		if (req.user.role?.includes('admin') || tenantIDs.length > 1) {
			const attemptedTenantChange = await req.payload.findByID({
				// @ts-expect-error - selectedTenant will match DB ID type
				id: selectedTenant,
				req,
				collection: 'tenants',
			});

			throw new ValidationError({
				errors: [
					{
						message: `The "${attemptedTenantChange.name}" tenant already has a user with the username "${value}". Usernames must be unique per tenant.`,
						path: 'username',
					},
				],
			});
		}

		throw new ValidationError({
			errors: [
				{
					message: `A user with the username ${value} already exists. Usernames must be unique per tenant.`,
					path: 'username',
				},
			],
		});
	}

	return value;
};
