import type { CollectionConfig } from 'payload';
import { role, username, tenants, oktaData } from './fields';
import { externalUsersLogin } from './endpoints/externalUsersLogin';
import { setCookieBasedOnDomain } from './hooks/setCookieBasedOnDomain';
import { isAuthor, authorData } from '../../fields/authors';
import { createCollectionAccess } from '../Roles/utils/hasPermission';

export const Users: CollectionConfig = {
	slug: 'users',
	access: createCollectionAccess('users'),
	admin: {
		useAsTitle: 'username',
	},
	auth: {
		useAPIKey: true,
	},
	endpoints: [externalUsersLogin],
	fields: [role, username, tenants, oktaData, isAuthor, authorData],
	// Ensure role relationship is populated by default
	defaultPopulate: {
		role: true,
	},
	// The following hook sets a cookie based on the domain a user logs in from.
	// It checks the domain and matches it to a tenant in the system, then sets
	// a 'payload-tenant' cookie for that tenant.
	hooks: {
		afterLogin: [setCookieBasedOnDomain],
	},
};
