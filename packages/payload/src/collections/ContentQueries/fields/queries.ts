import { isAdmin } from '@repo/payload/configs/access';
import type { ContentQuery } from '@repo/payload/payload-types';
import { hasLength } from '@repo/utils/hasLength';
import {
	categoryFilterOptions,
	topicFilterOptions,
} from '../../../fields/tags';
import type {
	ArrayFieldValidation,
	ArrayField,
	CheckboxField,
	DateField,
	FilterOptions,
	RelationshipField,
	SelectField,
	TextField,
	Where,
} from 'payload';

export const lastSynced: DateField = {
	name: 'lastSynced',
	label: 'Last Synced',
	type: 'date',
	admin: {
		date: {
			displayFormat: 'PPpp',
		},
		position: 'sidebar',
		readOnly: true,
	},
};

export const syncStatus: SelectField = {
	name: 'syncStatus',
	label: 'Sync Status',
	type: 'select',
	access: {
		update: ({ req }) => isAdmin(req.user),
	},
	defaultValue: 'pending',
	options: [
		{ label: 'Pending', value: 'pending' },
		{ label: 'In Progress', value: 'in-progress' },
		{ label: 'Completed', value: 'completed' },
		{ label: 'Failed', value: 'failed' },
	],
	admin: {
		position: 'sidebar',
	},
};
const contentFilterOptions: FilterOptions = () => {
	const articles: Where = {
		_status: {
			in: ['published', 'changed'],
		},
	};
	return articles;
};

const queryType: SelectField = {
	name: 'queryType',
	label: 'Query Type',
	type: 'select',
	hasMany: false,
	options: [
		{ label: 'By Data', value: 'data' },
		{ label: 'By Payload ID', value: 'id' },
		{ label: 'By Kalliope CMQS ID', value: 'kalliope' },
	],
	required: true,
};

const tags: RelationshipField = {
	name: 'tags',
	label: 'Content Tags',
	type: 'relationship',
	relationTo: 'tags',
	hasMany: true,
	localized: true,
	filterOptions: () => {
		return {
			type: { equals: 'contentTags' },
		};
	},
	admin: {
		condition: (_, siblingData) => siblingData?.queryType === 'data',
		allowCreate: false,
	},
};

const tagsOperator: SelectField = {
	name: 'tagsOperator',
	label: 'Tags Operator',
	type: 'select',
	defaultValue: 'OR',
	options: [
		{ label: 'AND', value: 'AND' },
		{ label: 'OR', value: 'OR' },
	],
	admin: {
		condition: (_, siblingData) =>
			siblingData?.queryType === 'data' &&
			Array.isArray(siblingData?.tags) &&
			siblingData.tags.length > 1,
		description:
			'How to combine multiple tags: AND requires all tags, OR requires any tag',
	},
};

const category: RelationshipField = {
	name: 'category',
	label: 'Category',
	type: 'relationship',
	relationTo: 'tags',
	hasMany: true,
	localized: true,
	admin: {
		condition: (_, siblingData) => siblingData?.queryType === 'data',
		allowCreate: false,
	},
	filterOptions: categoryFilterOptions,
};

const categoryOperator: SelectField = {
	name: 'categoryOperator',
	label: 'Category Operator',
	type: 'select',
	defaultValue: 'OR',
	options: [
		// Removed AND option since articles can only have one category
		{ label: 'OR', value: 'OR' },
	],
	admin: {
		condition: (_, siblingData) =>
			siblingData?.queryType === 'data' &&
			Array.isArray(siblingData?.category) &&
			siblingData.category.length > 1,
		description:
			'How to combine multiple categories: OR requires any category (Note: Articles can only have one category)',
	},
};

const topic: RelationshipField = {
	name: 'topic',
	label: 'Topic',
	type: 'relationship',
	relationTo: 'tags',
	hasMany: true,
	localized: true,
	admin: {
		condition: (_, siblingData) => siblingData?.queryType === 'data',
		allowCreate: false,
	},
	filterOptions: topicFilterOptions,
};

const topicOperator: SelectField = {
	name: 'topicOperator',
	label: 'Topic Operator',
	type: 'select',
	defaultValue: 'OR',
	options: [
		// Removed AND option since articles can only have one topic
		{ label: 'OR', value: 'OR' },
	],
	admin: {
		condition: (_, siblingData) =>
			siblingData?.queryType === 'data' &&
			Array.isArray(siblingData?.topic) &&
			siblingData.topic.length > 1,
		description:
			'How to combine multiple topics: OR requires any topic (Note: Articles can only have one topic)',
	},
};

const groupsOperator: SelectField = {
	name: 'groupsOperator',
	label: 'Groups Operator',
	type: 'select',
	defaultValue: 'AND',
	options: [
		{ label: 'AND', value: 'AND' },
		{ label: 'OR', value: 'OR' },
	],
	admin: {
		condition: (_, siblingData) => {
			if (siblingData?.queryType !== 'data') return false;

			// Show if at least two of tags, category, or topic are non-empty
			let nonEmptyGroups = 0;
			if (Array.isArray(siblingData?.tags) && siblingData.tags.length > 0)
				nonEmptyGroups++;
			if (
				Array.isArray(siblingData?.category) &&
				siblingData.category.length > 0
			)
				nonEmptyGroups++;
			if (Array.isArray(siblingData?.topic) && siblingData.topic.length > 0)
				nonEmptyGroups++;

			return nonEmptyGroups >= 2;
		},
		description:
			'How to combine different field groups (tags, categories, topics): AND requires all conditions, OR requires any condition',
	},
};

export const kalliopeCMQSID: TextField = {
	name: 'kalliopeCMQSID',
	label: 'Kalliope CMQS ID',
	type: 'text',
	admin: {
		condition: (_, siblingData) => siblingData?.queryType === 'kalliope',
		components: {
			Field: {
				path: '@repo/payload/fields/components#KalliopeCMQSSelect',
				clientProps: {},
			},
		},
	},
};

export const _locked: CheckboxField = {
	name: '_locked',
	label: 'Locked',
	type: 'checkbox',
	required: true,
	access: {
		read: ({ req }) => isAdmin(req.user),
	},
	defaultValue: false,
};

export const _lockedByTaskID: TextField = {
	name: '_lockedByTaskID',
	label: 'Locked By Task ID',
	type: 'text',
	access: {
		read: ({ req }) => isAdmin(req.user),
	},
};

const contentId: RelationshipField = {
	name: 'contentId',
	label: 'Wxnext Content ID',
	type: 'relationship',
	relationTo: 'articles',
	hasMany: true,
	admin: {
		condition: (_, siblingData) => siblingData?.queryType === 'id',
		allowCreate: false,
	},
	filterOptions: contentFilterOptions,
};

export const queries: ArrayField = {
	name: 'queries',
	label: 'Queries',
	type: 'array',
	validate: ((value: ContentQuery['queries']) => {
		// If no value or empty array, validation passes
		if (!hasLength(value)) {
			return true;
		}

		// Get all kalliopeCMQSID values that are not empty
		const kalliopeIds = value
			.filter((item) => item.queryType === 'kalliope' && item.kalliopeCMQSID)
			.map((item) => item.kalliopeCMQSID);

		const uniqueIds = new Set(kalliopeIds);

		if (uniqueIds.size !== kalliopeIds.length) {
			return 'Duplicate Kalliope CMQS IDs are not allowed. Please ensure all IDs are unique.';
		}

		// return true by default, put more logic here
		return true;
	}) as ArrayFieldValidation,
	admin: {
		initCollapsed: true,
		components: {
			RowLabel: {
				path: '@repo/payload/fields/components#MergedContentRowLabel',
			},
		},
	},
	fields: [
		queryType,
		tags,
		tagsOperator,
		category,
		categoryOperator,
		topic,
		topicOperator,
		groupsOperator,
		syncStatus,
		lastSynced,
		contentId,
		kalliopeCMQSID,
		_locked,
		_lockedByTaskID,
	],
};
