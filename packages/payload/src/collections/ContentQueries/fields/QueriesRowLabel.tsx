'use client';
import React from 'react';
import { useRowLabel } from '@payloadcms/ui';
import { formatDistanceToNow } from 'date-fns';

export const QueriesRowLabel: React.FC = () => {
	const { data } = useRowLabel<{
		queryType?: 'data' | 'id' | 'kalliope';
		syncStatus?: 'pending' | 'in-progress' | 'completed' | 'failed';
		tags?: Array<{ name?: string }>;
		category?: Array<{ name?: string }>;
		topic?: Array<{ name?: string }>;
		contentId?: Array<{ title?: string }>;
		kalliopeCMQSID?: string;
		lastSynced?: string;
	}>();

	// Get query type
	const queryType = data?.queryType || 'unknown';

	// Get status
	const status = data?.syncStatus || 'pending';

	// Format last synced time
	const getLastSyncedText = () => {
		if (!data?.lastSynced) return null;

		try {
			const lastSyncedDate = new Date(data.lastSynced);
			return formatDistanceToNow(lastSyncedDate, { addSuffix: true });
		} catch (_error) {
			return null;
		}
	};

	const lastSyncedText = getLastSyncedText();

	// Get a summary of the query content based on type
	const getQuerySummary = () => {
		switch (queryType) {
			case 'data': {
				const tagCount = data?.tags?.length || 0;
				const categoryCount = data?.category?.length || 0;
				const topicCount = data?.topic?.length || 0;

				const totalCount = tagCount + categoryCount + topicCount;

				return totalCount > 0
					? `${totalCount} tag${totalCount !== 1 ? 's' : ''} selected`
					: 'No tags selected';
			}
			case 'id': {
				const contentCount = data?.contentId?.length || 0;

				return contentCount > 0
					? `${contentCount} article${contentCount !== 1 ? 's' : ''} selected`
					: 'No content selected';
			}

			case 'kalliope':
				return 'Content Media Query';

			default:
				return 'Unknown query type';
		}
	};

	// Get background color based on status
	const getStatusColor = () => {
		switch (status) {
			case 'pending':
				return 'bg-yellow-100 text-yellow-800';
			case 'in-progress':
				return 'bg-blue-100 text-blue-800';
			case 'completed':
				return 'bg-green-100 text-green-800';
			case 'failed':
				return 'bg-red-100 text-red-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	};

	// Get background color based on query type
	const getTypeColor = () => {
		switch (queryType) {
			case 'data':
				return 'bg-purple-100 text-purple-800';
			case 'id':
				return 'bg-blue-100 text-blue-800';
			case 'kalliope':
				return 'bg-green-100 text-green-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	};

	return (
		<div className="flex items-center gap-3 px-4 py-2">
			<span className={`rounded px-2 py-1 text-xs ${getTypeColor()}`}>
				{queryType === 'kalliope' ? 'Content Media' : queryType.toUpperCase()}
			</span>

			<span className={`rounded px-2 py-1 text-xs ${getStatusColor()}`}>
				{status}
			</span>

			<div className="flex flex-col">
				<span className="truncate text-sm font-medium">
					{getQuerySummary()}
				</span>
				{lastSyncedText && (
					<span className="text-xs text-gray-500">Synced {lastSyncedText}</span>
				)}
			</div>
		</div>
	);
};

export default QueriesRowLabel;
