'use client';
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useField, FieldLabel, TextInput, SelectInput } from '@payloadcms/ui';
import type { TextFieldClientProps } from 'payload';

// Define the structure based on the response.txt format
interface KalliopeCMQ {
	id: string;
	type: string;
	attributes: {
		title: string;
		drupal_internal__nid: string;
		[key: string]: unknown;
	};
}

interface KalliopeApiResponse {
	data: KalliopeCMQ[];
	meta: {
		count: number;
	};
	links: {
		self: { href: string };
		next?: { href: string };
		prev?: { href: string };
		last?: { href: string };
		first?: { href: string };
	};
	jsonapi: {
		version: string;
		meta: {
			links: unknown;
		};
	};
}

export const KalliopeCMQSSelect: React.FC<TextFieldClientProps> = ({
	field,
	path,
}) => {
	const { label, name } = field;
	const { value: docValue, setValue: setDocValue } = useField<string>({
		path: path || name,
	});

	const [cmqs, setCmqs] = useState<KalliopeCMQ[]>([]);
	const [searchTerm, setSearchTerm] = useState('');
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [page, setPage] = useState(1);
	const [totalItems, setTotalItems] = useState(0);
	const [itemsPerPage] = useState(50);
	const [totalPages, setTotalPages] = useState(1);
	const [hasNextPage, setHasNextPage] = useState(false);
	const [hasPrevPage, setHasPrevPage] = useState(false);

	const fetchCMQs = useCallback(
		async (pageNum: number, search: string) => {
			setIsLoading(true);
			setError(null);
			try {
				const queryParams = new URLSearchParams({
					page: pageNum.toString(),
					limit: itemsPerPage.toString(),
				});

				if (search && search.trim()) {
					// Ensure search term is properly encoded
					queryParams.append('search', search.trim());

					// Add a parameter to indicate we want case insensitive search
					queryParams.append('caseInsensitive', 'true');

					// Add a parameter to indicate we want to search by ID as well
					queryParams.append('searchById', 'true');
				}

				const res = await fetch(
					`/api/kalliope/cmqs/all?${queryParams.toString()}`,
					{
						// Add Next.js cache configuration
						next: {
							revalidate: 3600, // Cache for 1 hour
							tags: ['kalliope-cmqs'],
						},
					},
				);

				if (!res.ok) {
					throw new Error(
						`Error fetching CMQs: ${res.status} ${res.statusText}`,
					);
				}

				const response: KalliopeApiResponse = await res.json();

				// Add validation to ensure we have the expected data structure
				if (!response.data) {
					console.debug('Unexpected API response format:', response);
					setCmqs([]);
					setTotalItems(0);
					setTotalPages(1);
					setHasNextPage(false);
					setHasPrevPage(false);
					return;
				}

				// Extract data from the response
				setCmqs(response.data);

				// Ensure we're getting the correct count from the API
				const totalCount =
					response.meta?.count !== undefined
						? response.meta.count
						: response.data.length;

				setTotalItems(totalCount);

				// Calculate total pages based on the total count
				const calculatedTotalPages = Math.max(
					1,
					Math.ceil(totalCount / itemsPerPage),
				);
				setTotalPages(calculatedTotalPages);

				// Check if there are next/prev pages based on links or calculated values
				setHasNextPage(!!response.links.next || pageNum < calculatedTotalPages);
				setHasPrevPage(!!response.links.prev || pageNum > 1);

				// Log pagination data for debugging
				console.debug('Pagination data:', {
					totalItems: totalCount,
					totalPages: calculatedTotalPages,
					currentPage: pageNum,
					hasNext: !!response.links.next || pageNum < calculatedTotalPages,
					hasPrev: !!response.links.prev || pageNum > 1,
				});
			} catch (err) {
				console.debug('Error fetching CMQs:', err);
				setError(err instanceof Error ? err.message : 'Failed to fetch CMQs');
			} finally {
				setIsLoading(false);
			}
		},
		[itemsPerPage],
	);

	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);

	// Improved debounce logic with normalization
	useEffect(() => {
		const timer = setTimeout(() => {
			// Normalize search term by trimming excess whitespace
			const normalizedSearchTerm = searchTerm.trim();
			setDebouncedSearchTerm(normalizedSearchTerm);
			setPage(1); // Reset to first page when search changes
		}, 300); // 300ms delay

		return () => clearTimeout(timer);
	}, [searchTerm]);

	// Memoized fetch function to prevent unnecessary re-renders
	const memoizedFetchCMQs = useMemo(() => {
		return (pageNum: number, search: string) => {
			fetchCMQs(pageNum, search);
		};
	}, [fetchCMQs]);

	// Fetch data when page or search term changes
	useEffect(() => {
		memoizedFetchCMQs(page, debouncedSearchTerm);
	}, [page, debouncedSearchTerm, memoizedFetchCMQs]);

	const filteredOptions = useMemo(() => {
		return cmqs.map((c) => {
			const title = c.attributes?.title || 'Untitled';
			return {
				label: `${title} (ID: ${c.id})`,
				value: c.id,
			};
		});
	}, [cmqs]);

	// Handle page navigation
	const goToPage = (newPage: number) => {
		if (newPage >= 1 && newPage <= totalPages) {
			setPage(newPage);
		}
	};

	return (
		<div className="field-type kalliope-cmq-selector">
			<FieldLabel label={typeof label === 'string' ? label : 'Kalliope CMQ'} />

			<TextInput
				placeholder="Search by title or ID..."
				value={searchTerm}
				path=""
				onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
					const newSearchTerm = e.target.value;
					setSearchTerm(newSearchTerm);
				}}
			/>

			{isLoading && <div className="loading-state">Loading CMQs...</div>}

			{error && <div className="error-state">Error: {error}</div>}

			{!isLoading && !error && (
				<>
					{filteredOptions.length === 0 && (
						<div className="no-results">
							No CMQs found matching &quot;{searchTerm}&quot;
						</div>
					)}

					<SelectInput
						path={path || name}
						value={docValue}
						name={name}
						options={filteredOptions}
						isClearable
						className="py-5"
						onChange={(newVal) => {
							if (!newVal) {
								setDocValue('');
							} else if (Array.isArray(newVal)) {
								setDocValue('');
							} else {
								setDocValue((newVal.value as string) || '');
							}
						}}
					/>

					{/* Pagination controls */}
					{totalItems > 0 && (
						<div className="pagination mt-4 flex items-center justify-between">
							<div className="text-sm text-gray-500">
								{totalItems > 0 ? (
									<>
										Showing{' '}
										{Math.min((page - 1) * itemsPerPage + 1, totalItems)} -{' '}
										{Math.min(page * itemsPerPage, totalItems)} of {totalItems}{' '}
										CMQs
									</>
								) : (
									<>No results found</>
								)}
							</div>
							{totalPages > 1 && (
								<div className="flex gap-2">
									<button
										type="button"
										onClick={() => goToPage(1)}
										disabled={page === 1}
										className="rounded border px-2 py-1 disabled:opacity-50"
									>
										First
									</button>
									<button
										type="button"
										onClick={() => goToPage(page - 1)}
										disabled={!hasPrevPage}
										className="rounded border px-2 py-1 disabled:opacity-50"
									>
										Previous
									</button>
									<span className="px-2 py-1">
										Page {page} of {totalPages}
									</span>
									<button
										type="button"
										onClick={() => goToPage(page + 1)}
										disabled={!hasNextPage}
										className="rounded border px-2 py-1 disabled:opacity-50"
									>
										Next
									</button>
									<button
										type="button"
										onClick={() => goToPage(totalPages)}
										disabled={page === totalPages}
										className="rounded border px-2 py-1 disabled:opacity-50"
									>
										Last
									</button>
								</div>
							)}
						</div>
					)}
				</>
			)}
		</div>
	);
};
