import { ArrayField } from 'payload';

export const mergedContent: ArrayField = {
	name: 'mergedContent',
	label: 'Merged Content',

	type: 'array',
	admin: {
		isSortable: true,
		readOnly: true,
		description: 'Content merged from various sources',
		initCollapsed: true,
		components: {
			RowLabel: {
				path: '@repo/payload/fields/components#MergedContentRowLabel',
			},
		},
		disableBulkEdit: true,
	},
	fields: [
		{
			name: 'id',
			type: 'text',
			required: true,
			admin: {
				readOnly: true,
			},
		},
		{
			name: 'source',
			type: 'select',
			required: true,
			options: [
				{ label: 'Drupal', value: 'drupal' },
				{ label: 'Payload', value: 'payload' },
			],
			admin: {
				readOnly: true,
			},
		},
		{
			name: 'contentType',
			type: 'select',
			required: true,
			options: [
				{ label: 'Article', value: 'article' },
				{ label: 'Image', value: 'image' },
				{ label: 'Video', value: 'video' },
			],
			admin: {
				description: 'Type of content',
				readOnly: true,
			},
		},
		{
			name: 'title',
			type: 'text',
			required: true,
			admin: {
				readOnly: true,
			},
		},
		{
			name: 'overrideTitle',
			type: 'text',
			required: false,
			label: 'Override Title',
		},
		{
			name: 'description',
			type: 'text',
			required: false,
			admin: {
				readOnly: true,
			},
		},
		{
			name: 'overrideDescription',
			type: 'text',
			required: false,
			label: 'Override Description',
		},
		{
			name: 'thumbnail',
			type: 'text',
			required: true,
			admin: {
				readOnly: true,
				components: {
					Field: {
						path: '@repo/payload/fields/components#ThumbnailPreview',
						clientProps: {
							maxWidth: 100,
							maxHeight: 100,
						},
					},
				},
			},
		},
		{
			name: 'overrideThumbnail',
			type: 'text',
			required: false,
			label: 'Override Thumbnail',
		},
		{
			name: 'url',
			type: 'text',
			required: true,
			admin: {
				readOnly: true,
			},
		},
		{
			name: 'pinned',
			type: 'checkbox',
			required: false,
			admin: {
				readOnly: true,
			},
			label: 'Pin this content',
		},
		{
			name: 'sourceQueryID',
			type: 'text',
			required: false,
			admin: {
				readOnly: true,
				description: 'ID of the query that sourced this content',
			},
		},
	],
};
