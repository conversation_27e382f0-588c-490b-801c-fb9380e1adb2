'use client';
import React from 'react';
import { useRowLabel } from '@payloadcms/ui';
import Image from 'next/image';

export const MergedContentRowLabel: React.FC = () => {
	const { data } = useRowLabel<{
		id?: string;
		title?: string;
		overrideTitle?: string;
		source?: string;
		thumbnail?: string;
		pinned?: boolean;
		url?: string;
	}>();

	const title = data?.overrideTitle || data?.title || 'Untitled';
	const source = data?.source || 'unknown';
	const isPinned = data?.pinned ? '📌 ' : '';

	// Build the link URL based on the source
	const getLinkUrl = () => {
		if (!data?.id || !data?.url) return null;

		if (source === 'drupal') {
			return `https://cms.weather.com${data.url}`;
		} else if (source === 'payload') {
			return `/admin/collections/articles/${data.id}`;
		}
		return null;
	};

	const linkUrl = getLinkUrl();

	return (
		<div className="relative flex items-center gap-4 px-4 py-2">
			{data?.thumbnail && (
				<div className="thumbnail-container">
					<Image
						src={data.thumbnail}
						alt={title}
						className="h-8 w-8 rounded object-cover"
						onError={(e) => {
							e.currentTarget.style.display = 'none';
						}}
					/>
				</div>
			)}
			<span className="font-medium">
				{isPinned}
				{title}
			</span>
			<span
				className={`rounded px-2 py-1 text-xs ${
					source === 'payload'
						? 'bg-blue-100 text-blue-600'
						: 'bg-purple-100 text-purple-600'
				}`}
			>
				{source}
			</span>

			{linkUrl && (
				<a
					href={linkUrl}
					target="_blank"
					rel="noopener noreferrer"
					onClick={(e) => e.stopPropagation()}
					className="ml-auto flex items-center gap-1 rounded bg-blue-50 px-2 py-1 text-xs text-blue-500 transition-colors hover:bg-blue-100 hover:text-blue-700"
					title={`Open in new tab: ${linkUrl}`}
				>
					<svg
						xmlns="http://www.w3.org/2000/svg"
						width="10"
						height="10"
						viewBox="0 0 24 24"
						fill="none"
						stroke="currentColor"
						strokeWidth="2"
						strokeLinecap="round"
						strokeLinejoin="round"
					>
						<path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
						<polyline points="15 3 21 3 21 9"></polyline>
						<line x1="10" y1="14" x2="21" y2="3"></line>
					</svg>
					<span className="text-xs">View</span>
				</a>
			)}
		</div>
	);
};

export default MergedContentRowLabel;
