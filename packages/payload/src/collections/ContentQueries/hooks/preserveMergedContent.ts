import type { CollectionBeforeChangeHook } from 'payload';
import type { ContentQuery } from '@repo/payload/payload-types';
import { hasLength } from '@repo/utils/hasLength';

/**
 * This hook ensures that the mergedContent field is preserved when updating a document
 * It prevents the mergedContent from being cleared in these cases:
 * 1. When mergedContent is not provided in the update data
 * 2. When mergedContent is provided but is an empty array
 * 3. When mergedContent is provided but is not an array (e.g., a number)
 *
 * It also cleans up content from queries that have been removed
 */
export const preserveMergedContent: CollectionBeforeChangeHook<
	ContentQuery
> = async ({ data, originalDoc, operation, collection, req }) => {
	if (collection.slug !== 'content-queries') return data;

	// If this is an update operation and originalDoc exists
	if (operation === 'update' && originalDoc) {
		// First, identify any queries that have been removed
		const removedQueryIds = findRemovedQueryIds(
			originalDoc.queries,
			data.queries,
		);

		if (removedQueryIds.length > 0) {
			req?.payload?.logger?.debug(
				`Found ${removedQueryIds.length} removed queries: ${JSON.stringify(removedQueryIds)}`,
			);
		}

		// If we have mergedContent in the original document
		if (hasLength(originalDoc.mergedContent)) {
			req?.payload?.logger?.debug(
				`Original document has ${originalDoc.mergedContent.length} merged content items`,
			);

			// Create a map of all content by ID for efficient lookup
			const contentMap = new Map();

			// First, add all content from the original document to the map
			let itemsWithRemovedQueryId = 0;

			// Use for...of loop instead of forEach
			for (const item of originalDoc.mergedContent) {
				if (item && typeof item === 'object' && item.id) {
					// Log each item's sourceQueryID for debugging
					req?.payload?.logger?.debug(
						`Item ID: ${item.id}, sourceQueryID: ${item.sourceQueryID || 'undefined'}`,
					);

					// Only keep items that don't belong to removed queries
					if (
						!item.sourceQueryID ||
						!removedQueryIds.includes(item.sourceQueryID)
					) {
						contentMap.set(String(item.id), item);
					} else {
						itemsWithRemovedQueryId++;
						req?.payload?.logger?.debug(
							`Removing item with ID ${item.id} from mergedContent (sourceQueryID: ${item.sourceQueryID})`,
						);
					}
				}
			}

			req?.payload?.logger?.debug(
				`Found ${itemsWithRemovedQueryId} items with removed query IDs`,
			);

			// If the update data has mergedContent, add those items too (they'll overwrite any duplicates)
			if (Array.isArray(data.mergedContent) && data.mergedContent.length > 0) {
				req?.payload?.logger?.debug(
					`Update data has ${data.mergedContent.length} merged content items`,
				);

				let updateItemsWithRemovedQueryId = 0;

				// Use for...of loop instead of forEach
				for (const item of data.mergedContent) {
					if (item && typeof item === 'object' && item.id) {
						// Log each item's sourceQueryID for debugging
						req?.payload?.logger?.debug(
							`Update item ID: ${item.id}, sourceQueryID: ${item.sourceQueryID || 'undefined'}`,
						);

						// Only keep items that don't belong to removed queries
						if (
							!item.sourceQueryID ||
							!removedQueryIds.includes(item.sourceQueryID)
						) {
							contentMap.set(String(item.id), item);
						} else {
							updateItemsWithRemovedQueryId++;
							req?.payload?.logger?.debug(
								`Removing item with ID ${item.id} from update data (sourceQueryID: ${item.sourceQueryID})`,
							);
						}
					}
				}

				req?.payload?.logger?.debug(
					`Found ${updateItemsWithRemovedQueryId} items with removed query IDs in update data`,
				);
			}

			// Convert the map back to an array
			const mergedContent = Array.from(contentMap.values());

			// Set the merged content in the update data
			data.mergedContent = mergedContent;

			req?.payload?.logger?.debug(
				`Final mergedContent count after merging and filtering: ${mergedContent.length}`,
			);

			return data;
		} else {
			req?.payload?.logger?.debug(
				`Original document has no mergedContent items`,
			);
		}
		// Add a debug log to show what's being returned
		req?.payload?.logger?.debug(
			`Returning data with mergedContent length: ${Array.isArray(data.mergedContent) ? data.mergedContent.length : 'not an array'}`,
		);
	}

	return data;
};

/**
 * Identifies query IDs that have been removed from the document
 */
function findRemovedQueryIds(
	originalQueries: ContentQuery['queries'] | undefined,
	updatedQueries: ContentQuery['queries'] | undefined,
): string[] {
	if (!originalQueries) return [];
	if (!updatedQueries)
		return originalQueries.map((q) => q.id).filter(Boolean) as string[];

	// Get all query IDs from the original document
	const originalIds = new Set(
		originalQueries.map((q) => q.id).filter(Boolean) as string[],
	);

	// Get all query IDs from the updated document
	const updatedIds = new Set(
		updatedQueries.map((q) => q.id).filter(Boolean) as string[],
	);

	// Find IDs that exist in original but not in updated
	return Array.from(originalIds).filter((id) => !updatedIds.has(id));
}
