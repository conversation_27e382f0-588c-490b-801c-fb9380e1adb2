# Content Queries

## Overview

Content Queries is a system for dynamically fetching and aggregating content from multiple sources. It allows users to define queries that pull content from:

1. **Payload CMS** - Internal articles by tags, categories, or topics
2. **Payload CMS** - Internal articles by specific IDs
3. **Kalliope CMS** - External content from the Drupal-based Kalliope system

The system uses a job queue to process queries asynchronously, ensuring that content is fetched efficiently without blocking the user interface.

## How It Works

### Query Document Structure

Each Content Query document contains:

- **name**: A descriptive name for the query
- **queries**: An array of individual query configurations
- **mergedContent**: The aggregated content from all queries

### Query Types

1. **Data Queries** (`queryType: 'data'`)

   - Fetch content by tags, categories, and topics
   - Support for AND/OR operators between filter groups

2. **ID Queries** (`queryType: 'id'`)

   - Fetch content by specific article IDs
   - Direct lookup of known content

3. **Kalliope Queries** (`queryType: 'kalliope'`)
   - Fetch content from the external Kalliope CMS
   - Uses the Kalliope CMQS ID to identify content sets

### Content Processing Flow

1. **Query Creation/Update**: When a query is created or updated, the `queueContentQueryJobsAfterChange` hook identifies new or modified queries and queues appropriate jobs.

2. **Job Processing**: Background jobs fetch content based on query parameters:

   - `getContentByTags` for data queries
   - `getContentByIds` for ID queries
   - `getCMAssetByID` for Kalliope queries

3. **Content Merging**: Each job processes its results and merges them into the document's `mergedContent` array, preserving content from other queries.

4. **Query Status**: Each query tracks its sync status (`pending`, `in-progress`, `completed`, `failed`) and when it was last synced.

### Content Preservation

The system includes safeguards to ensure content is properly preserved:

- `preserveMergedContent` hook prevents accidental clearing of merged content
- Content from removed queries is automatically filtered out
- User customizations (like pinned status or overrides) are preserved across updates

## Usage

1. Create a new Content Query document
2. Add one or more queries of different types
3. Save the document to trigger content fetching
4. View and use the aggregated content in the `mergedContent` field

## Technical Details

See the [Jobs README](../jobs/README.md) for detailed information on the job processing system and content merging logic.
