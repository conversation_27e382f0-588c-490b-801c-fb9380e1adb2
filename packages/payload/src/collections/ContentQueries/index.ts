import type { CollectionConfig } from 'payload';
import { name } from './fields';
import { queries } from './fields/queries';
import { mergedContent } from './fields/mergedContent';
import { queueContentQueryJobsAfterChange } from './hooks/queueContentQueryJobsAfterChange';
import { preserveMergedContent } from './hooks/preserveMergedContent';
import { createCollectionAccess } from '../Roles/utils/hasPermission';

export const ContentQueries: CollectionConfig = {
	slug: 'content-queries',
	labels: {
		singular: 'Content Query',
		plural: 'Content Queries',
	},
	admin: {
		useAsTitle: 'name',
		defaultColumns: ['name', 'id'],
	},
	defaultSort: '-name',
	versions: false,
	access: createCollectionAccess('content-queries'),
	fields: [name, queries, mergedContent],
	hooks: {
		beforeChange: [preserveMergedContent],
		afterChange: [queueContentQueryJobsAfterChange],
	},
};
