'use client';
import React from 'react';
import Image from 'next/image';
import { useField } from '@payloadcms/ui';

interface ThumbnailPreviewProps {
	path: string;
	maxWidth?: number;
	maxHeight?: number;
}

export const ThumbnailPreview: React.FC<ThumbnailPreviewProps> = ({
	path,
	maxWidth = 100,
	maxHeight = 100,
}) => {
	const { value } = useField<string>({ path });

	if (!value) return null;

	return (
		<div>
			<Image
				src={value}
				alt="Thumbnail"
				width={maxWidth}
				height={maxHeight}
				style={{
					maxWidth: `${maxWidth}px`,
					maxHeight: `${maxHeight}px`,
					objectFit: 'cover',
				}}
				onError={(e) => {
					e.currentTarget.style.display = 'none';
				}}
			/>
			<div className="mt-1 truncate text-xs text-gray-500">{value}</div>
		</div>
	);
};

export default ThumbnailPreview;
