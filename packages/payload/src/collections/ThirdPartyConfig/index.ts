import type { CollectionConfig } from 'payload';
import { fields } from './fields';
import { createCollectionAccess } from '../Roles/utils/hasPermission';

export const ThirdPartyConfig: CollectionConfig = {
	slug: 'third-party-config',
	admin: {
		useAsTitle: 'name',
		group: 'Advertising',
		defaultColumns: ['name', 'type', 'token'],
	},
	access: createCollectionAccess('third-party-config'),
	fields,
	versions: {
		drafts: true,
	},
};
