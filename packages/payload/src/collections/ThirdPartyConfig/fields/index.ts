import { Field } from 'payload';

export const fields: Field[] = [
	{
		name: 'name',
		type: 'text',
		label: 'Name',
		required: true,
		admin: {
			description: 'The name of the third party configuration',
		},
	},
	{
		name: 'token',
		type: 'text',
		label: 'Token',
		required: true,
		admin: {
			description: 'Authentication token for the third party service',
		},
	},
	{
		name: 'type',
		type: 'text',
		label: 'Type',
		required: true,
		admin: {
			description: 'The type of third party service',
		},
	},
	{
		name: 'thirdPartyConfigJson',
		type: 'json',
		label: 'Third Party Config JSON',
		required: true,
		admin: {
			description: 'JSON configuration for the third party service',
		},
	},
];
