import { SeoMeta } from './fields/seo';
import { createCollapsible } from '@repo/payload/fields/utility';
import type { CollectionConfig, Field } from 'payload';
import { defaultContentTags } from '../../fields/tags';
import { regenerateImageSizes } from './hooks/regenerateImageSizes';
import { createCollectionAccess } from '../Roles/utils/hasPermission';

const collapsedLabel = 'Metadata';
const collapsedFields: Field[] = [SeoMeta];

export const Images: CollectionConfig = {
	slug: 'images',
	access: createCollectionAccess('images'),
	fields: [
		createCollapsible(collapsedFields, collapsedLabel),
		defaultContentTags,
	],
	hooks: {
		afterChange: [regenerateImageSizes],
	},
	upload: {
		mimeTypes: ['image/*'],
		imageSizes: [
			{
				name: 'small',
				fit: 'cover',
				height: 300,
				width: 900,
			},
			{
				name: 'large',
				fit: 'cover',
				height: 600,
				width: 1800,
			},
		],
	},
};
