import type { CollectionAfterChangeHook } from 'payload';
import type { Image, ImagesSelect } from '@repo/payload/payload-types';
import { detectImageChanges } from '../utils/imageChangeDetection';

/**
 * Simple hook to re-upload image when focal point changes
 * This triggers Payload to regenerate all image sizes with the new focal point
 */
export const regenerateImageSizes: CollectionAfterChangeHook<Image> = async ({
	doc,
	previousDoc,
	req,
	operation,
	context,
}) => {
	// Skip if this update was triggered by our own regeneration process
	if (context?.regeneratingImageSizes === true) {
		return doc;
	}
	// Only process updates, not creates
	if (operation !== 'update') {
		return doc;
	}
	// Check if focal point has changed or if this is a manual crop/edit
	const { focalPointChanged, dimensionsChanged } = detectImageChanges(
		doc,
		previousDoc,
	);
	// If focal point changed or dimensions changed and we have a file, trigger re-upload
	if ((focalPointChanged || dimensionsChanged) && doc.filename && doc.url) {
		// Validate that all required properties exist before attempting update
		if (!doc.seo || !doc.seo.altText || !doc.seo.caption) {
			req.payload.logger.error(
				`Cannot regenerate image sizes for ${doc.filename}: missing required seo properties (altText, caption)`,
			);
			return doc;
		}

		try {
			// Download the original image from S3
			const imageResponse = await fetch(doc.url);
			if (!imageResponse.ok) {
				throw new Error(
					`Failed to fetch original image: ${imageResponse.statusText}`,
				);
			}
			const imageBuffer = Buffer.from(await imageResponse.arrayBuffer());
			const responseMimeType = imageResponse.headers.get('content-type');

			// Validate MIME type - must be an image type
			const mimeType = doc.mimeType || responseMimeType;
			if (!mimeType || !mimeType.startsWith('image/')) {
				throw new Error(
					`Invalid MIME type for image regeneration: ${mimeType}. Expected image/* type.`,
				);
			}

			// Create a file object that mimics a fresh upload with the SAME filename
			const fileData = {
				data: imageBuffer,
				mimetype: mimeType,
				name: doc.filename, // Keep the exact same filename
				size: imageBuffer.length,
			};
			// Re-upload the file with the new focal point, keeping the same filename
			// This will trigger Payload to regenerate all image sizes automatically
			doc = await req.payload.update<'images', ImagesSelect>({
				collection: 'images',
				id: doc.id,
				data: {
					// Keep all existing data to prevent filename changes
					filename: doc.filename, // Explicitly keep the same filename
					focalX: doc.focalX,
					focalY: doc.focalY,
					// Keep other metadata to prevent changes
					width: doc.width,
					height: doc.height,
					filesize: doc.filesize,
					mimeType: doc.mimeType,
					// Include required seo property
					seo: doc.seo,
				},
				file: fileData,
				req,
				// Prevent infinite loop by adding context flag
				context: {
					regeneratingImageSizes: true,
				},
			});
			req.payload.logger.info(
				`Successfully re-uploaded image ${doc.filename} with new focal point`,
			);
		} catch (error) {
			req.payload.logger.error(
				`Failed to re-upload image ${doc.filename}:`,
				error,
			);
		}
	}
	return doc;
};
