import { describe, it, expect } from 'vitest';
import { detectImageChanges } from './imageChangeDetection';
import type { Image } from '@repo/payload/payload-types';

// Mock image document factory
const createMockImage = (overrides: Partial<Image> = {}): Image => ({
	id: '1',
	filename: 'test-image.jpg',
	url: 'https://example.com/test-image.jpg',
	width: 1920,
	height: 1080,
	focalX: 50,
	focalY: 50,
	filesize: 1024000,
	mimeType: 'image/jpeg',
	sizes: {},
	seo: {
		altText: 'Test image alt text',
		caption: 'Test image caption',
	},
	updatedAt: '2023-01-01T00:00:00.000Z',
	createdAt: '2023-01-01T00:00:00.000Z',
	...overrides,
});

describe('detectImageChanges', () => {
	describe('when previousDoc is null or undefined', () => {
		it('should return false for all changes when previousDoc is null', () => {
			const currentDoc = createMockImage();
			const result = detectImageChanges(currentDoc, null);

			expect(result.focalPointChanged).toBe(false);
			expect(result.dimensionsChanged).toBe(false);
		});

		it('should return false for all changes when previousDoc is undefined', () => {
			const currentDoc = createMockImage();
			const result = detectImageChanges(currentDoc, undefined);

			expect(result.focalPointChanged).toBe(false);
			expect(result.dimensionsChanged).toBe(false);
		});
	});

	describe('focal point changes', () => {
		it('should detect focalX change', () => {
			const previousDoc = createMockImage({ focalX: 30, focalY: 50 });
			const currentDoc = createMockImage({ focalX: 70, focalY: 50 });
			const result = detectImageChanges(currentDoc, previousDoc);

			expect(result.focalPointChanged).toBe(true);
			expect(result.dimensionsChanged).toBe(false);
		});

		it('should detect focalY change', () => {
			const previousDoc = createMockImage({ focalX: 50, focalY: 30 });
			const currentDoc = createMockImage({ focalX: 50, focalY: 70 });
			const result = detectImageChanges(currentDoc, previousDoc);

			expect(result.focalPointChanged).toBe(true);
			expect(result.dimensionsChanged).toBe(false);
		});

		it('should detect both focalX and focalY changes', () => {
			const previousDoc = createMockImage({ focalX: 30, focalY: 30 });
			const currentDoc = createMockImage({ focalX: 70, focalY: 70 });
			const result = detectImageChanges(currentDoc, previousDoc);

			expect(result.focalPointChanged).toBe(true);
			expect(result.dimensionsChanged).toBe(false);
		});

		it('should not detect change when focal points are the same', () => {
			const previousDoc = createMockImage({ focalX: 50, focalY: 50 });
			const currentDoc = createMockImage({ focalX: 50, focalY: 50 });
			const result = detectImageChanges(currentDoc, previousDoc);

			expect(result.focalPointChanged).toBe(false);
		});
	});

	describe('dimension changes', () => {
		it('should detect width change', () => {
			const previousDoc = createMockImage({ width: 1920, height: 1080 });
			const currentDoc = createMockImage({ width: 1280, height: 1080 });
			const result = detectImageChanges(currentDoc, previousDoc);

			expect(result.dimensionsChanged).toBe(true);
			expect(result.focalPointChanged).toBe(false);
		});

		it('should detect height change', () => {
			const previousDoc = createMockImage({ width: 1920, height: 1080 });
			const currentDoc = createMockImage({ width: 1920, height: 720 });
			const result = detectImageChanges(currentDoc, previousDoc);

			expect(result.dimensionsChanged).toBe(true);
			expect(result.focalPointChanged).toBe(false);
		});

		it('should detect both width and height changes', () => {
			const previousDoc = createMockImage({ width: 1920, height: 1080 });
			const currentDoc = createMockImage({ width: 1280, height: 720 });
			const result = detectImageChanges(currentDoc, previousDoc);

			expect(result.dimensionsChanged).toBe(true);
			expect(result.focalPointChanged).toBe(false);
		});

		it('should not detect change when dimensions are the same', () => {
			const previousDoc = createMockImage({ width: 1920, height: 1080 });
			const currentDoc = createMockImage({ width: 1920, height: 1080 });
			const result = detectImageChanges(currentDoc, previousDoc);

			expect(result.dimensionsChanged).toBe(false);
		});
	});

	describe('combined changes', () => {
		it('should detect both focal point and dimension changes', () => {
			const previousDoc = createMockImage({
				focalX: 30,
				focalY: 30,
				width: 1920,
				height: 1080,
			});
			const currentDoc = createMockImage({
				focalX: 70,
				focalY: 70,
				width: 1280,
				height: 720,
			});
			const result = detectImageChanges(currentDoc, previousDoc);

			expect(result.focalPointChanged).toBe(true);
			expect(result.dimensionsChanged).toBe(true);
		});

		it('should return false for both when no changes detected', () => {
			const previousDoc = createMockImage({
				focalX: 50,
				focalY: 50,
				width: 1920,
				height: 1080,
			});
			const currentDoc = createMockImage({
				focalX: 50,
				focalY: 50,
				width: 1920,
				height: 1080,
			});
			const result = detectImageChanges(currentDoc, previousDoc);

			expect(result.focalPointChanged).toBe(false);
			expect(result.dimensionsChanged).toBe(false);
		});
	});

	describe('edge cases', () => {
		it('should handle null focal points in previousDoc', () => {
			const previousDoc = createMockImage({
				focalX: null,
				focalY: null,
			} as any);
			const currentDoc = createMockImage({ focalX: 50, focalY: 50 });
			const result = detectImageChanges(currentDoc, previousDoc);

			expect(result.focalPointChanged).toBe(true);
		});

		it('should handle null focal points in currentDoc', () => {
			const previousDoc = createMockImage({ focalX: 50, focalY: 50 });
			const currentDoc = createMockImage({ focalX: null, focalY: null } as any);
			const result = detectImageChanges(currentDoc, previousDoc);

			expect(result.focalPointChanged).toBe(true);
		});

		it('should handle null dimensions in previousDoc', () => {
			const previousDoc = createMockImage({ width: null, height: null } as any);
			const currentDoc = createMockImage({ width: 1920, height: 1080 });
			const result = detectImageChanges(currentDoc, previousDoc);

			expect(result.dimensionsChanged).toBe(true);
		});

		it('should handle null dimensions in currentDoc', () => {
			const previousDoc = createMockImage({ width: 1920, height: 1080 });
			const currentDoc = createMockImage({ width: null, height: null } as any);
			const result = detectImageChanges(currentDoc, previousDoc);

			expect(result.dimensionsChanged).toBe(true);
		});

		it('should handle undefined focal points', () => {
			const previousDoc = createMockImage({
				focalX: undefined,
				focalY: undefined,
			} as any);
			const currentDoc = createMockImage({ focalX: 50, focalY: 50 });
			const result = detectImageChanges(currentDoc, previousDoc);

			expect(result.focalPointChanged).toBe(true);
		});

		it('should handle zero values correctly', () => {
			const previousDoc = createMockImage({
				focalX: 0,
				focalY: 0,
				width: 0,
				height: 0,
			});
			const currentDoc = createMockImage({
				focalX: 50,
				focalY: 50,
				width: 1920,
				height: 1080,
			});
			const result = detectImageChanges(currentDoc, previousDoc);

			expect(result.focalPointChanged).toBe(true);
			expect(result.dimensionsChanged).toBe(true);
		});

		it('should handle same zero values correctly', () => {
			const previousDoc = createMockImage({
				focalX: 0,
				focalY: 0,
				width: 0,
				height: 0,
			});
			const currentDoc = createMockImage({
				focalX: 0,
				focalY: 0,
				width: 0,
				height: 0,
			});
			const result = detectImageChanges(currentDoc, previousDoc);

			expect(result.focalPointChanged).toBe(false);
			expect(result.dimensionsChanged).toBe(false);
		});
	});

	describe('return value structure', () => {
		it('should return an object with focalPointChanged and dimensionsChanged properties', () => {
			const currentDoc = createMockImage();
			const previousDoc = createMockImage();
			const result = detectImageChanges(currentDoc, previousDoc);

			expect(result).toHaveProperty('focalPointChanged');
			expect(result).toHaveProperty('dimensionsChanged');
			expect(typeof result.focalPointChanged).toBe('boolean');
			expect(typeof result.dimensionsChanged).toBe('boolean');
		});
	});
});
