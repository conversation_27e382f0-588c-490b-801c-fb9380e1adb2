import type { Image } from '@repo/payload/payload-types';

/**
 * Detects if an image has changes that require regeneration
 * @param doc - Current image document
 * @param previousDoc - Previous image document
 * @returns Object with change detection results
 */
export function detectImageChanges(doc: Image, previousDoc?: Image | null) {
	// If there's no previous document, there can't be any changes
	if (!previousDoc) {
		return {
			focalPointChanged: false,
			dimensionsChanged: false,
		};
	}

	const focalPointChanged =
		doc.focalX !== previousDoc.focalX || doc.focalY !== previousDoc.focalY;
	// Also check if image dimensions changed (which might indicate cropping)
	const dimensionsChanged =
		doc.width !== previousDoc.width || doc.height !== previousDoc.height;

	return {
		focalPointChanged,
		dimensionsChanged,
	};
}
