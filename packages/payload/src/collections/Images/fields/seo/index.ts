import { createGroup } from '@repo/payload/fields/utility';
import type { Field } from 'payload';

export const altText: Field = {
	name: 'altText',
	label: 'Alt Text',
	localized: true,
	type: 'text',
	required: true,
};

export const caption: Field = {
	name: 'caption',
	label: 'Caption',
	localized: true,
	type: 'text',
	required: true,
};

export const credit: Field = {
	name: 'credit',
	label: 'Credit',
	localized: true,
	type: 'text',
	required: false,
	admin: {
		description: 'Attribution for the image (e.g., photographer, source)',
	},
};

const groupName = 'seo';
const groupLabel = 'SEO';
const groupedFields: Field[] = [altText, caption, credit];

export const SeoMeta = createGroup(groupedFields, groupName, groupLabel);
