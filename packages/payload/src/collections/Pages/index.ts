import type {
	CollectionConfig,
	GeneratePreviewURL,
	LivePreviewConfig,
} from 'payload';
import { title, variants } from './fields';
import { generatePreviewPath } from '@repo/payload/utils/generatePreviewPath';
import { createCollectionAccess } from '../Roles/utils/hasPermission';
import { contentTab, seoTab, adModuleConfigTab } from './fields/tabs';
import { createdBy } from '../../fields/createdBy';
import { updatedBy } from '../../fields/updatedBy';
import { createTabs } from '../../fields/utility';
import { variantNameField } from './fields/variantName';
import { contextParameters } from './fields/contextParameters';
import { pageAssetNameFields } from './fields/pageAssetName';
import { afterOperationHook } from './hooks/afterOperationHook';
import { beforeValidateHook } from './hooks/beforeValidateHook';
import { createAssetNameChangeHook } from '../../plugins/redirects';

export const Pages: CollectionConfig = {
	slug: 'pages',
	access: createCollectionAccess('pages'),
	admin: {
		useAsTitle: 'title',
		defaultColumns: [
			'title',
			'assetName',
			'variantName',
			'publishDate',
			'updatedAt',
		],
		livePreview: {
			url: ({ data }) =>
				generatePreviewPath({
					data,
					collection: 'pages',
					isLivePreview: true,
				}),
		} as LivePreviewConfig,
		preview: ((data) =>
			generatePreviewPath({ data, collection: 'pages' })) as GeneratePreviewURL,
	},
	hooks: {
		beforeValidate: [beforeValidateHook],
		afterOperation: [afterOperationHook],
		afterChange: [createAssetNameChangeHook()],
	},
	fields: [
		title,
		...pageAssetNameFields(),
		variantNameField,
		variants,
		createdBy,
		updatedBy,
		createTabs([contentTab, seoTab, adModuleConfigTab]),
		contextParameters,
	],
	versions: {
		drafts: {
			autosave: {
				interval: 1000, // We set this interval for optimal live preview
			},
			schedulePublish: true,
		},
		maxPerDoc: 50,
	},
};
