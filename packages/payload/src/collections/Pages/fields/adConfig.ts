import type { Field, GroupField } from 'payload';
import { createParameterFields } from './contextParameters';

export const adsData: GroupField = {
	name: 'adsData',
	type: 'group',
	label: 'Ads Data',
	admin: {
		description: 'Configure ad settings for this page',
	},
	fields: [
		{
			name: 'networkCode',
			type: 'text',
			label: 'Network Code',
			admin: {
				description: 'The ad network code for this page',
			},
		},
		{
			name: 'adZone',
			type: 'text',
			label: 'Ad Zone',
			admin: {
				description: 'The ad zone for this page',
			},
		},
		{
			name: 'engine',
			type: 'text',
			label: 'Engine',
			admin: {
				description: 'The ad engine for this page',
			},
		},
		{
			name: 'videoPrerollBidders',
			type: 'json',
			label: 'Video Preroll Bidders',
			admin: {
				description: 'JSON configuration for video preroll bidders',
			},
		},
		{
			name: 'contextParameterOverrides',
			type: 'group',
			admin: {
				description: 'Override or extend page context parameters for ads data',
			},
			fields: [
				{
					name: 'enabled',
					type: 'checkbox',
					defaultValue: false,
					admin: {
						description: 'Enable context parameter overrides for ads data',
					},
				},
				{
					name: 'parameters',
					type: 'group',
					admin: {
						condition: (data, siblingData) => siblingData?.enabled === true,
						description:
							'Configure context parameters for ads data. Multiple values for the same parameter create an OR relationship.',
					},
					fields: createParameterFields(),
				},
				{
					name: 'visibilityRule',
					type: 'select',
					defaultValue: 'show_always',
					options: [
						{ label: 'Always Show', value: 'show_always' },
						{ label: 'Show Only When Matching', value: 'show_matching' },
						{ label: 'Hide When Matching', value: 'hide_matching' },
					],
					admin: {
						description: 'Determine when these ads should be visible',
						condition: (data, siblingData) => siblingData?.enabled === true,
					},
				},
			],
		},
	],
};

export const thirdpartyConfigs: Field = {
	name: 'thirdpartyConfigs',
	type: 'array',
	label: 'Third-party Configurations',
	admin: {
		description: 'Add multiple third-party ad configurations for this page',
	},
	fields: [
		{
			name: 'thirdPartyConfig',
			type: 'relationship',
			relationTo: 'third-party-config',
			label: '3rd Party Ad Config',
			required: true,
			admin: {
				description: 'Select a third-party ad configuration',
			},
		},
		{
			name: 'contextParameterOverrides',
			type: 'group',
			admin: {
				description:
					'Override or extend page context parameters for this third-party config',
			},
			fields: [
				{
					name: 'enabled',
					type: 'checkbox',
					defaultValue: false,
					admin: {
						description:
							'Enable context parameter overrides for this third-party config',
					},
				},
				{
					name: 'parameters',
					type: 'group',
					admin: {
						condition: (data, siblingData) => siblingData?.enabled === true,
						description:
							'Configure context parameters for this third-party config. Multiple values for the same parameter create an OR relationship.',
					},
					fields: createParameterFields(),
				},
				{
					name: 'visibilityRule',
					type: 'select',
					defaultValue: 'show_always',
					options: [
						{ label: 'Always Show', value: 'show_always' },
						{ label: 'Show Only When Matching', value: 'show_matching' },
						{ label: 'Hide When Matching', value: 'hide_matching' },
					],
					admin: {
						description:
							'Determine when this third-party config should be visible',
						condition: (data, siblingData) => siblingData?.enabled === true,
					},
				},
			],
		},
	],
};
