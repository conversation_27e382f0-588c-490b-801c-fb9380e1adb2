'use client';
import React, { useEffect } from 'react';
import { FormState, TextFieldClientProps } from 'payload';
import { reduceFieldsToValues } from 'payload/shared';
import { useField, TextInput, FieldLabel, useFormFields } from '@payloadcms/ui';
import { formatVariantNamesFromContextParameters } from '.';

export const VariantName: React.FC<TextFieldClientProps> = ({
	field,
	path,
}) => {
	const { label } = field;
	const { value, setValue } = useField<string>({ path: path || field.name });

	// Define a type for the context parameter entries that matches what's expected
	type ContextParameterEntry = {
		name?: string | null;
		value?: string | null;
		id?: string | null;
	};

	const contextParametersFieldValue = useFormFields(([fields]) => {
		const formState: FormState = reduceFieldsToValues(
			fields as FormState,
			true,
		);

		// Ensure we return an array of the expected type or empty array if not available
		const contextParams = formState.contextParameters ?? [];
		// Cast to the expected type since we know the form structure
		return Array.isArray(contextParams)
			? (contextParams as ContextParameterEntry[])
			: [];
	});

	useEffect(() => {
		if (contextParametersFieldValue.length > 0) {
			// Cast to any to bypass type checking since we've already ensured it's the right structure
			const formattedVariantName = formatVariantNamesFromContextParameters(
				/* eslint-disable-next-line @typescript-eslint/no-explicit-any */
				contextParametersFieldValue as any,
			);

			if (value !== formattedVariantName) setValue(formattedVariantName);
		} else {
			if (value !== '') setValue('');
		}
	}, [value, setValue, contextParametersFieldValue]);

	return (
		<div className="field-type">
			{/*
				This component ux can be better.
			 	Currently unlocking means that its disconnected from title.
			 	What we really want is to indicate specifying separate auto vs manual
			*/}
			<FieldLabel htmlFor={`field-${path}`} label={label} />
			<TextInput value={value} path={path || field.name} readOnly />
		</div>
	);
};
