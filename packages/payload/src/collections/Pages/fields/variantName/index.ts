import type { TextField, FieldHook } from 'payload';
import { Page } from '@repo/payload/payload-types';

export const formatVariantNamesFromContextParameters = (
	contextParameters: Page['contextParameters'],
) => {
	if (!contextParameters) return '';

	// filter-out null parameter values
	const validParameters = Object.entries(contextParameters).filter(
		([, value]) => {
			if (!value || value.length < 1) return false;
			return true;
		},
	);

	return validParameters.reduce(
		(formattedString: string, contextParam, index: number) => {
			const [name, value] = contextParam;
			const separator = index === 0 ? '' : '; ';
			return `${formattedString}${separator}${name}: ${value}`;
		},
		'',
	);
};

export const formatVariantNamesHook: FieldHook = ({
	data,
	operation,
	value,
}) => {
	if (operation === 'create' || !data?.variantName) {
		const fallbackData = data?.contextParameters;

		if (fallbackData) {
			return formatVariantNamesFromContextParameters(fallbackData);
		}
	}

	return value;
};

export const variantNameField: TextField = {
	name: 'variantName',
	type: 'text',
	index: true,
	label: 'Variant Name',
	hooks: {
		beforeValidate: [formatVariantNamesHook],
	},
	admin: {
		position: 'sidebar',
		components: {
			Field: {
				path: '@repo/payload/fields/components#VariantName',
			},
		},
	},
};
