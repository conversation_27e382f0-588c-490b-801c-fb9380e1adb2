'use client';
import React, { useCallback, useEffect } from 'react';
import { TextFieldClientProps } from 'payload';
import {
	useField,
	Button,
	TextInput,
	FieldLabel,
	useFormFields,
	useForm,
} from '@payloadcms/ui';
import { formatAssetName } from '../../utils/formatAssetName';
import './index.scss';

type AssetNameComponentProps = {
	fieldToUse: string;
	checkboxFieldPath: string;
} & TextFieldClientProps;

export const AssetNameComponent: React.FC<AssetNameComponentProps> = ({
	field,
	fieldToUse,
	checkboxFieldPath: checkboxFieldPathFromProps,
	path,
	readOnly: readOnlyFromProps,
}) => {
	const { label } = field;

	const checkboxFieldPath = path?.includes('.')
		? `${path}.${checkboxFieldPathFromProps}`
		: checkboxFieldPathFromProps;

	const { value, setValue } = useField<string>({ path: path || field.name });

	const { dispatchFields } = useForm();

	// The value of the checkbox
	// We're using separate useFormFields to minimise re-renders
	const checkboxValue = useFormFields(([fields]) => {
		return fields[checkboxFieldPath]?.value as string;
	});

	// The value of the field we're listening to for the assetName
	const targetFieldValue = useFormFields(([fields]) => {
		return fields[fieldToUse]?.value as string;
	});

	useEffect(() => {
		if (checkboxValue) {
			if (targetFieldValue) {
				const formattedSlug = formatAssetName(targetFieldValue);

				if (value !== formattedSlug) setValue(formattedSlug);
			} else {
				if (value !== '') setValue('');
			}
		}
	}, [targetFieldValue, checkboxValue, setValue, value]);

	const handleLock = useCallback(
		(e: React.MouseEvent<Element>) => {
			e.preventDefault();

			dispatchFields({
				type: 'UPDATE',
				path: checkboxFieldPath,
				value: !checkboxValue,
			});
		},
		[checkboxValue, checkboxFieldPath, dispatchFields],
	);

	const readOnly = readOnlyFromProps || checkboxValue;

	return (
		<div className="field-type assetName-field-component">
			{/* this component ux can be better.
			 currently unlocking means that its disconnected from title.
			 what we really want is to indicate specifying separate auto vs manual*/}
			<div className="label-wrapper">
				<FieldLabel htmlFor={`field-${path}`} label={label} />

				<Button className="lock-button" buttonStyle="none" onClick={handleLock}>
					{checkboxValue ? 'Specify Manually' : 'Generate from Title'}
				</Button>
			</div>

			<TextInput
				value={value}
				onChange={setValue}
				path={path || field.name}
				readOnly={Boolean(readOnly)}
			/>
		</div>
	);
};
