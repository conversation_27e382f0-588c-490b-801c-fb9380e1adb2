import type { CheckboxField, TextField, FieldHook } from 'payload';
import { formatAssetName } from '../../utils/formatAssetName';

export const formatSlugHook =
	(fallback: string): FieldHook =>
	({ data, operation, value }) => {
		if (typeof value === 'string') {
			return formatAssetName(value);
		}

		if (operation === 'create' || !data?.assetName) {
			const fallbackData = data?.[fallback] || data?.[fallback];

			if (fallbackData && typeof fallbackData === 'string') {
				return formatAssetName(fallbackData);
			}
		}

		return value;
	};

type Overrides = {
	pageAssetNameOverrides?: Partial<TextField>;
	checkboxOverrides?: Partial<CheckboxField>;
};

type PageAssetNameFields = (
	fieldToUse?: string,
	overrides?: Overrides,
) => [TextField, CheckboxField];

export const pageAssetNameFields: PageAssetNameFields = (
	fieldToUse = 'title',
	overrides = {},
) => {
	const { pageAssetNameOverrides, checkboxOverrides } = overrides;

	const checkBoxField: CheckboxField = {
		name: 'assetNameLock',
		type: 'checkbox',
		defaultValue: true,
		admin: {
			hidden: true,
			position: 'sidebar',
		},
		...checkboxOverrides,
	};

	// @ts-expect-error - ts mismatch Partial<TextField> with TextField
	const pageAssetNameField: TextField = {
		name: 'assetName',
		type: 'text',
		required: true,
		//localized: true,
		index: true,
		label: 'Asset Name',
		...(pageAssetNameOverrides || {}),
		hooks: {
			// Kept this in for hook or API based updates
			beforeValidate: [formatSlugHook(fieldToUse)],
		},
		admin: {
			position: 'sidebar',
			...(pageAssetNameOverrides?.admin || {}),
			components: {
				Field: {
					path: '@repo/payload/fields/components#AssetNameComponent',
					clientProps: {
						fieldToUse,
						checkboxFieldPath: checkBoxField.name,
					},
				},
			},
		},
	};

	return [pageAssetNameField, checkBoxField];
};
