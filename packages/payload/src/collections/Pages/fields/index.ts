import {
	MetaDescriptionField,
	MetaImageField,
	MetaTitleField,
	OverviewField,
	PreviewField,
} from '@payloadcms/plugin-seo/fields';
import type { ArrayField, TextField, UIField } from 'payload';
import { createParameterFields } from './contextParameters';
// No longer need to import validation functions

import { allowedBlocks } from './blocks';

export const title: TextField = {
	name: 'title',
	label: 'Title',
	type: 'text',
	required: true,
};

export const variants: UIField = {
	name: 'variants',
	type: 'ui',
	admin: {
		position: 'sidebar',
		components: {
			Field: {
				path: '@repo/payload/fields/components#VariantsAdminFieldServerComponent',
			},
		},
	},
};

export const layout: ArrayField = {
	name: 'layout',
	labels: {
		singular: 'Layout',
		plural: 'Layouts',
	},
	required: true,
	type: 'array',
	fields: [
		{
			name: 'region',
			type: 'select',
			required: true,
			options: [
				{
					label: 'Main Layout Area',
					value: 'main',
				},
				{
					label: 'Sidebar Area',
					value: 'sidebar',
				},
			],
		},
		{
			name: 'blocks',
			type: 'blocks',
			blocks: allowedBlocks ?? [],
		},
		{
			name: 'contextParameterOverrides',
			type: 'group',
			admin: {
				description:
					'Override or extend page context parameters for this layout',
			},
			fields: [
				{
					name: 'enabled',
					type: 'checkbox',
					defaultValue: false,
					admin: {
						description: 'Enable context parameter overrides for this layout',
					},
				},
				{
					name: 'parameters',
					type: 'group',
					admin: {
						condition: (data, siblingData) => siblingData?.enabled === true,
						description:
							'Configure context parameters for this layout. Multiple values for the same parameter create an OR relationship.',
					},
					fields: createParameterFields(),
				},
				{
					name: 'visibilityRule',
					type: 'select',
					defaultValue: 'show_always',
					options: [
						{ label: 'Always Show', value: 'show_always' },
						{ label: 'Show Only When Matching', value: 'show_matching' },
						{ label: 'Hide When Matching', value: 'hide_matching' },
					],
					admin: {
						description: 'Determine when this layout should be visible',
						condition: (data, siblingData) => siblingData?.enabled === true,
					},
				},
			],
		},
	],
};

export const seoOverview = OverviewField({
	titlePath: 'meta.title',
	descriptionPath: 'meta.description',
	imagePath: 'meta.image',
});

export const seoMeta = MetaTitleField({
	hasGenerateFn: true,
});

export const seoMetaImage = MetaImageField({
	relationTo: 'images',
});

export const seoMetaDescription = MetaDescriptionField({});
export const seoPreview = PreviewField({
	// if the `generateUrl` function is configured
	hasGenerateFn: true,

	// field paths to match the target field for data
	titlePath: 'meta.title',
	descriptionPath: 'meta.description',
});
