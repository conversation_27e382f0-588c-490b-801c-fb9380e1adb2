import type { Field } from 'payload';
import { validConfigs, validNames } from '../utils/validConfigs';

/**
 * Creates fields for each valid parameter name
 * Uses select fields with hasMany: true for parameters with predefined values
 * This enables OR relationships by allowing multiple selections
 */
export const createParameterFields = (): Field[] => {
	// ignore pageKey, as that's handled by the asset name
	const names = validNames.filter((name) => name !== 'pageKey');
	return names.map((name) => {
		const validValues = validConfigs[name] || [];

		// For parameters that can have any value (like pageKey), use a text field
		if (validValues.length === 0) {
			return {
				name,
				type: 'text',
				admin: {
					description: `Value for ${name} parameter`,
				},
			};
		}

		// For parameters with predefined values, use a select field with multiple option
		return {
			name,
			type: 'select',
			hasMany: true, // This enables OR relationship by allowing multiple selections
			admin: {
				description: `Value(s) for ${name} parameter. Multiple selections create an OR relationship.`,
			},
			options: validValues.map((value) => ({
				label: value,
				value,
			})),
		};
	});
};

/**
 * Context parameters field for Pages collection
 * Uses built-in Payload field UIs with select fields that support multiple selections
 * This enables OR relationships when duplicate context parameter key names are present
 */
export const contextParameters: Field = {
	name: 'contextParameters',
	type: 'group',
	admin: {
		position: 'sidebar',
		description:
			'Configure context parameters for this page. Multiple values for the same parameter create an OR relationship.',
	},
	fields: createParameterFields(),
};
