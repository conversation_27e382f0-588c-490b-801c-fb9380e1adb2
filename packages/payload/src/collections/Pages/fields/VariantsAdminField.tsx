import type React from 'react';
import type { UIFieldServerComponent, UIFieldServerProps } from 'payload';
import type { PagesSelect } from '@repo/payload/payload-types';

export const VariantsAdminFieldServerComponent: UIFieldServerComponent = async (
	props: UIFieldServerProps,
) => {
	const { data, payload } = props;

	const result = await payload.find<'pages', PagesSelect>({
		collection: 'pages',
		// draft,
		depth: 2,
		overrideAccess: true,
		pagination: false,
		where: {
			and: [
				{
					assetName: {
						equals: data.assetName,
					},
				},
				// {
				// 	id: {
				// 		not_equals: data.id,
				// 	},
				// },
			],
		},
	});

	return (
		<div>
			<p>relationships</p>
			<ul className="list-disc pl-6">
				{result?.docs?.map((page) => (
					<li key={page.id} className={page.id === data.id ? 'font-bold' : ''}>
						{page.variantName} {page.id === data.id ? '(current)' : ''}
					</li>
				))}
			</ul>
		</div>
	);
};
