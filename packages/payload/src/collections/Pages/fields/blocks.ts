import { Block } from 'payload';
import LiveblogEntriesBlock from '../../../blocks/LiveblogEntries/config';
import VideoBlock from '../../../blocks/Video/config';
import ContentMediaBlock from '../../../blocks/ContentMedia/config';
import AdBlock from '../../../blocks/Ad/config';
import ImageBlock from '../../../blocks/Image/config';
import MorningBriefBlock from '../../../blocks/MorningBrief/config';
import DailyForecastBlock from '../../../blocks/DailyForecast/config';
import CurrentConditionsBlock from '../../../blocks/CurrentConditions/config';
import CallToActionBlockConfig from '../../../blocks/CallToAction/config';
import SlideshowBlock from '../../../blocks/Slideshow/config';
import PromoDriverBlock from '../../../blocks/PromoDriver/config';

/*
 * This is the list of blocks that are allowed to be used on liveblog entries
 * Do not modify change the structure of this list without modifying the corresponding
 * scaffolder
 * TODO: perhaps this should not be edited and only generated by the scaffolder
 * */
// prettier-ignore
export const allowedBlocks: Block[] = [
	MorningBriefBlock,
	DailyForecastBlock,
	CurrentConditionsBlock,
	AdBlock,
	ImageBlock,
	VideoBlock,
	ContentMediaBlock,
	LiveblogEntriesBlock,
	CallToActionBlockConfig,
	SlideshowBlock,
	PromoDriverBlock,
];
