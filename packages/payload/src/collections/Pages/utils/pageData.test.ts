import { describe, expect, it, vi } from 'vitest';
import { buildPageMetadata, buildPageData } from './pageData';
// We're mocking this module but not directly using its exports
import type { Page } from '@repo/payload/payload-types';

describe('pageData', () => {
	describe('buildPageMetadata', () => {
		it('should return default metadata when page is null', async () => {
			const metadata = await buildPageMetadata(
				null,
				undefined,
				'https://weather.com/test',
			);

			expect(metadata.title).toBe('The Weather Channel');
			expect(metadata.description).toBe('The Weather Channel');
			expect(metadata.alternates?.canonical).toBe('https://weather.com/test');
			expect(metadata).toMatchSnapshot();
		});

		it('should use page SEO data when available', async () => {
			const page = {
				seo: {
					title: 'Test Page Title',
					description: 'Test page description',
					image: 'https://weather.com/test-image.jpg',
				},
			} as Page;

			const metadata = await buildPageMetadata(
				page,
				undefined,
				'https://weather.com/test-page',
			);

			expect(metadata.title).toBe('Test Page Title');
			expect(metadata.description).toBe('Test page description');
			expect(metadata.openGraph?.title).toBe('Test Page Title');
			expect(metadata.openGraph?.url).toBe('https://weather.com/test-page');
			expect(metadata.alternates?.canonical).toBe(
				'https://weather.com/test-page',
			);
			expect(metadata.openGraph?.images).toEqual([
				{ url: 'https://weather.com/test-image.jpg' },
			]);
			// Check Twitter metadata exists but don't assert on specific properties
			// that might change in the Twitter metadata type
			expect(metadata.twitter).toBeDefined();
		});

		it('should handle complex image object', async () => {
			const page = {
				seo: {
					title: 'Test Page Title',
					description: 'Test page description',
					image: {
						url: 'https://weather.com/complex-image.jpg',
					},
				},
			} as Page;

			const metadata = await buildPageMetadata(page);

			expect(metadata.openGraph?.images).toEqual([
				{ url: 'https://weather.com/complex-image.jpg' },
			]);
		});
	});

	describe('buildPageData', () => {
		it('should return page debug data with page values', () => {
			const page = {
				id: 'page-123',
				seo: {
					title: 'Test Page Title',
					description: 'Test page description',
					image: 'https://weather.com/test-image.jpg',
				},
				assetName: 'test-page',
				_status: 'published',
				createdAt: '2023-01-01T00:00:00.000Z',
				updatedAt: '2023-01-02T00:00:00.000Z',
				tenant: 'test-tenant',
				content: { blocks: [] },
			} as unknown as Page;

			const locale = 'en-US';

			const result = buildPageData(page, locale);

			expect(result.title).toBe('Test Page Title');
			expect(result.id).toBe('page-123');
			expect(result.assetName).toBe('test-page');
			expect(result.status).toBe('published');
			expect(result.seo?.ogImage?.url).toBe(
				'https://weather.com/test-image.jpg',
			);
			expect(result.locale).toBe('en-US');
		});

		it('should use fallback values when page is null', () => {
			const fallback = {
				title: 'Fallback Title',
				description: 'Fallback description',
				id: 'fallback-id',
				assetName: 'fallback-asset',
				status: 'draft',
				collection: 'custom-collection',
				tenant: 'fallback-tenant',
			};

			const result = buildPageData(null, 'fr-FR', fallback);

			expect(result.title).toBe('Fallback Title');
			expect(result.id).toBe('fallback-id');
			expect(result.assetName).toBe('fallback-asset');
			expect(result.status).toBe('draft');
			expect(result.collection).toBe('custom-collection');
			expect(result.locale).toBe('fr-FR');
		});

		it('should use default values when page and fallback are minimal', () => {
			const result = buildPageData(null);

			expect(result.title).toBe('The Weather Channel');
			expect(result.description).toBe('The Weather Channel');
			expect(result.assetName).toBe('home');
			expect(result.status).toBe('published');
			expect(result.collection).toBe('pages');
			expect(result.seo?.ogImage?.url).toBe('https://weather.com/favicon.ico');
		});
	});
});
