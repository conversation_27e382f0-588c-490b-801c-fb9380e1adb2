// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`pageData > buildPageMetadata > should return default metadata when page is null 1`] = `
{
  "alternates": {
    "canonical": "https://weather.com/test",
  },
  "description": "The Weather Channel",
  "keywords": [],
  "openGraph": {
    "description": "The Weather Channel",
    "images": undefined,
    "title": "The Weather Channel",
    "type": "website",
    "url": "https://weather.com/test",
  },
  "title": "The Weather Channel",
  "twitter": {
    "card": "summary_large_image",
    "description": "The Weather Channel",
    "images": undefined,
    "title": "The Weather Channel",
  },
}
`;
