import type { Page } from '@repo/payload/payload-types';
import { Metadata } from 'next';
import { PageDebugData } from '../../../components/FrontendAdminHeader/state/types';

interface FallbackPageMetadata {
	title?: string;
	description?: string;
}

export const buildPageMetadata = async (
	page: Page | null | undefined,
	fallback?: FallbackPageMetadata,
	url: string = 'https://weather.com',
): Promise<Metadata> => {
	const pageSeo = page?.seo;

	const title = pageSeo?.title || fallback?.title || 'The Weather Channel';
	const description =
		pageSeo?.description || fallback?.description || 'The Weather Channel';
	const imageUrl =
		typeof pageSeo?.image === 'string' ? pageSeo?.image : pageSeo?.image?.url;

	return {
		title,
		description,
		keywords: [],
		alternates: {
			canonical: url,
		},
		openGraph: {
			title,
			description,
			url,
			type: 'website',
			images: imageUrl ? [{ url: imageUrl }] : undefined,
		},
		twitter: {
			card: 'summary_large_image',
			title,
			description,
			images: imageUrl ? [{ url: imageUrl }] : undefined,
		},
	};
};

interface FallbackPageData {
	title?: string;
	description?: string;
	id?: string;
	assetName?: string;
	status?: string;
	collection?: string;
	tenant?: string;
}

export const buildPageData = (
	page: Page | null,
	locale?: string,
	fallback?: FallbackPageData,
): PageDebugData => {
	const title = page?.seo?.title || fallback?.title || 'The Weather Channel';
	const description =
		page?.seo?.description || fallback?.description || 'The Weather Channel';
	const imageUrl =
		typeof page?.seo?.image === 'string'
			? page?.seo?.image
			: page?.seo?.image?.url || 'https://weather.com/favicon.ico';

	return {
		title,
		id: page?.id || fallback?.id,
		description,
		assetName: page?.assetName || fallback?.assetName || 'home',
		status: page?._status || fallback?.status || 'published',
		collection: fallback?.collection || 'pages',
		createdAt: page?.createdAt,
		updatedAt: page?.updatedAt,
		seo: {
			title,
			description,
			ogImage: { url: imageUrl },
		},
		tenant: page?.tenant || fallback?.tenant,
		locale,
		content: page?.content || null,
	};
};
