export const formatAssetName = (val: string): string => {
	// Format the string, preserving slashes
	const formatted = val
		.replace(/ /g, '-')
		.replace(/[^\w\-\\/]+/g, '') // Modified regex to preserve slashes
		.replace(/\/+/g, '/') // Replace multiple consecutive slashes with single slash
		.toLowerCase();

	// Ensure it starts with a slash and doesn't have multiple consecutive slashes
	const formattedWithSlash = formatted.startsWith('/')
		? formatted
		: `/${formatted}`;
	return formattedWithSlash.replace(/\/+/g, '/');
};
