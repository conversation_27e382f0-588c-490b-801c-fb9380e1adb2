import { PRIVACY_REGIMES } from '@repo/privacy/constants';

// Valid names and their allowed values
// TODO we can probably drive this from Global Config
export const validConfigs: Record<string, string[]> = {
	// empty array implies it can be any value
	pageKey: [],
	deviceClass: ['desktop', 'mobile'],
	partner: ['samsung', 'oppo', 'apple'],
	weatherMode: ['active', 'normal'],
	siteMode: ['normal', 'severe', 'hybrid'],
	privacyRegime: [...PRIVACY_REGIMES],
	// locale: ['en-US', 'de-DE', 'fr-FR', 'es-ES'],
};

export const validNames = Object.keys(validConfigs);
