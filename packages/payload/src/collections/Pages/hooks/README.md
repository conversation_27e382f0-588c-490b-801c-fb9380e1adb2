# Page Collection Hooks

This directory contains hooks for the Pages collection that handle various aspects of page management, particularly related to context parameters.

## Hooks Overview

### `afterOperationHook.ts`

This hook is executed after a Pages operation (create, update, delete) is completed. It handles:

1. Creating or updating ContextParameters documents based on the page's context parameters
2. Setting relationships between Pages and ContextParameters:
   - `page` field for top-level parameters (direct relationship to one page)
   - `layouts` field for layout-level parameters (array of pages using this parameter in layouts)
   - `blocks` field for block-level parameters (array of pages using this parameter in blocks)
3. Cleaning up orphaned ContextParameters when a page is deleted

### `beforeValidateHook.ts`

This hook is executed before a Pages document is validated. It ensures that:

1. A Page's context parameters don't conflict with existing ContextParameters that already have a page relationship
2. Each context parameter combination (identified by its hash) can only be related to one page

## Context Parameters Validation

The `beforeValidateHook` implements a critical validation rule: **a context parameter combination can only be related to one page**. This prevents conflicts where multiple pages might match the same context parameters.

### Validation Process

1. When a Page is created or updated, the hook calculates the hash for its context parameters
2. It checks if a ContextParameters document with the same hash already exists
3. If one exists and has a different page in its `page` field, the validation fails
4. The operation is prevented with a detailed error message that includes:
   - The conflicting hash
   - The title and ID of the page that already uses this context parameter combination

### Error Example

```txt
Context parameters conflict: The combination of context parameters would create a hash (abc123)
that already exists with a page relationship to "Home Page" (ID: 123456).
Each context parameter combination can only be related to one page.
```

## Implementation Details

The validation works by:

1. Transforming the page's context parameters to a standardized JSON format
2. Adding the `pageKey` parameter based on the page's `assetName`
3. Generating a hash from the combined parameters
4. Querying the ContextParameters collection for documents with the same hash
5. Checking if any matching document has a different page relationship

This ensures that each unique combination of context parameters can only be associated with one page, which is essential for the routing system to work correctly.
