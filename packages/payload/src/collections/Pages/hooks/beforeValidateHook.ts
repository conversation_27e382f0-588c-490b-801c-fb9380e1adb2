import type { CollectionBeforeValidateHook } from 'payload';
import { hashParameters } from '@repo/payload/contextParameters/generateCombinations';
import { Page } from '@repo/payload/payload-types';
import { transformContextParametersToJson } from '@repo/payload/contextParameters/transformContextParametersToJson';

/**
 * This hook is called before a Pages document is validated.
 * It checks if the context parameters would create a hash that already exists with a different page relationship.
 */
export const beforeValidateHook: CollectionBeforeValidateHook = async ({
	data,
	originalDoc,
	req,
	operation,
}) => {
	// If data is undefined, return it as is
	if (!data) {
		return data;
	}

	// Only validate on create or update operations
	if (operation !== 'create' && operation !== 'update') {
		return data;
	}

	// Only validate if context parameters are provided
	if (
		!data.contextParameters ||
		!Array.isArray(data.contextParameters) ||
		data.contextParameters.length === 0
	) {
		return data;
	}

	// Get the page ID (will be undefined for create operations)
	const pageId = (data as Page).id || (originalDoc as Page).id;

	// Transform context parameters to JSON with pageKey
	const pageKey = data.assetName;
	if (!pageKey) {
		// If assetName is not provided, we can't create a hash
		return data;
	}

	// Define a type for the context parameter entries
	type ContextParameterEntry = {
		name: string;
		value: string;
	};

	// Create an array of context parameter entries
	const contextParamEntries: ContextParameterEntry[] = [
		{ name: 'pageKey', value: pageKey },
		...(Array.isArray(data.contextParameters) ? data.contextParameters : []),
	];

	// Transform the array to the expected format
	// Use 'as any' to bypass type checking since we know the structure is compatible
	const convertedContextParameters =
		transformContextParametersToJson(contextParamEntries);
	const hash = hashParameters(convertedContextParameters);

	// Check if a context parameter with the same hash already exists
	const existingContextParams = await req.payload.find({
		collection: 'context-parameters',
		where: {
			hash: {
				equals: hash,
			},
		},
	});

	// If a context parameter with the same hash exists
	if (existingContextParams.docs && existingContextParams.docs.length > 0) {
		const existingContextParam = existingContextParams.docs[0];

		// If the existing context parameter has a page relationship
		if (existingContextParam && existingContextParam.page) {
			// If the page relationship is different from the current page
			const existingPageId =
				typeof existingContextParam.page === 'object'
					? existingContextParam.page.id
					: existingContextParam.page;

			if (existingPageId !== pageId) {
				// Get the related page details for a more informative error message
				let relatedPageTitle = 'another page';
				try {
					const relatedPage = await req.payload.findByID({
						collection: 'pages',
						id: existingPageId,
					});

					if (relatedPage && relatedPage.title) {
						relatedPageTitle = `"${relatedPage.title}" (ID: ${existingPageId})`;
					}
				} catch {
					// If we can't get the related page details, just use the ID
					relatedPageTitle = `page with ID ${existingPageId}`;
				}

				// Throw an error to prevent the operation
				throw new Error(
					`Context parameters conflict: The combination of context parameters would create a hash (${hash}) that already exists with a page relationship to ${relatedPageTitle}. Each context parameter combination can only be related to one page.`,
				);
			}
		}
	}

	// If no conflict is found, return the data
	return data;
};
