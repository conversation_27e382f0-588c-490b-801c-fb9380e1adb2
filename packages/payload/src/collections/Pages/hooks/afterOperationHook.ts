import type {
	ContextParameter,
	Page,
	ContextParametersSelect,
} from '@repo/payload/payload-types';
import {
	hashParameters,
	expandParameterArrays,
	generateParameterHashes,
	type GeneratedParameterHash,
} from '@repo/payload/contextParameters/generateCombinations';
import { generateStandardizedName } from '@repo/payload/contextParameters/standardizedName';
import { transformContextParametersToJson } from '@repo/payload/contextParameters/transformContextParametersToJson';
import {
	EnhancedBlock,
	EnhancedLayout,
} from '@repo/payload/contextParameters/types';
import type { CollectionAfterOperationHook, PayloadRequest } from 'payload';
import { validConfigs, validNames } from '../utils/validConfigs';

/**
 * This hook is called after a Pages operation (create, update, delete) is completed.
 * It only updates ContextParameters when a Pages configuration is published.
 */
export const afterOperationHook: CollectionAfterOperationHook<'pages'> = async (
	args,
) => {
	const { operation, req, result } = args;

	// Handle different operations
	if (
		operation === 'create' ||
		operation === 'update' ||
		operation === 'updateByID'
	) {
		// For create and update operations, we get the document from args.result
		const doc = args.result as Page;

		// Only process when a document is published
		if (doc._status === 'published') {
			await processPublishedPage(doc, req);
		}
	} else if (operation === 'delete' || operation === 'deleteByID') {
		// For delete operations, we need to clean up context parameters
		// In delete operations, we need to handle the document differently
		// since it might be a bulk operation or a single document operation
		try {
			if (args.result && typeof args.result === 'object') {
				if ('docs' in args.result && Array.isArray(args.result.docs)) {
					// Bulk delete operation
					for (const doc of args.result.docs) {
						// We only need the ID for cleanup
						if (doc && typeof doc === 'object' && 'id' in doc) {
							await cleanupContextParameters({ id: doc.id as string }, req);
						}
					}
				} else {
					// Single delete operation
					// We only need the ID for cleanup
					const docId = (args.result as Page).id;
					if (docId) {
						await cleanupContextParameters({ id: docId } as Page, req);
					}
				}
			}
		} catch (err) {
			req.payload.logger.error(
				`Error handling delete operation: ${(err as Error).message}`,
			);
		}
	}

	return result;
};

/**
 * Process a published page to update or create context parameters
 */
async function processPublishedPage(doc: Page, req: PayloadRequest) {
	const pageKey = doc.assetName;
	// context parameters configured at the page-level; default to at least
	// including a pageKey
	let pageLevelContextParams: Record<string, string | string[]> = { pageKey };

	// Process page-level context parameters
	if (doc.contextParameters) {
		// Transform context parameters to JSON with pageKey
		// Handle both old array format and new object format
		if (Array.isArray(doc.contextParameters)) {
			// If it's an array, convert the array of {name, value} objects to a single object
			const paramsObject: Record<string, string> = {};
			doc.contextParameters.forEach((param) => {
				if (param.name && param.value !== undefined) {
					paramsObject[param.name] = param.value;
				}
			});
			// Add pageKey and transform
			paramsObject.pageKey = pageKey;
			pageLevelContextParams = transformContextParametersToJson(paramsObject);
		} else if (
			doc.contextParameters &&
			typeof doc.contextParameters === 'object'
		) {
			// If it's already an object, transform it and add pageKey
			pageLevelContextParams = {
				...transformContextParametersToJson(doc.contextParameters),
				pageKey,
			};
		} else {
			// Fallback if contextParameters is null or undefined
			pageLevelContextParams = { pageKey };
		}

		// Check for invalid parameters
		for (const [name, value] of Object.entries(pageLevelContextParams)) {
			// Check for invalid parameter names (ignoring pageKey)
			if (name !== 'pageKey' && !validNames.includes(name)) {
				req.payload.logger.error(
					`Invalid parameter name: "${name}". Skipping context parameter creation.`,
				);
				return;
			}

			// Check for invalid parameter values (skip check if validConfigs[name] is empty array = any value allowed)
			if (value && validConfigs[name] && validConfigs[name].length > 0) {
				// Handle both string and array values
				const valueArray = Array.isArray(value) ? value : [value];

				// Check if all values are valid
				const validValuesForName = validConfigs[name] || [];
				const invalidValues = valueArray.filter(
					(val) => !validValuesForName.includes(val),
				);

				if (invalidValues.length > 0) {
					req.payload.logger.error(
						`afterOperationHook: Invalid value(s) for ${name}: "${invalidValues.join(', ')}". Must be one of: ${validConfigs[name].join(', ')}. Skipping context parameter creation.`,
					);
					return;
				}
			}
		}

		try {
			// Check if any parameter has array values (indicating an OR relationship)
			const hasArrayValues = Object.values(pageLevelContextParams).some(
				Array.isArray,
			);

			if (hasArrayValues) {
				// Generate all possible combinations of parameter values
				const paramCombinations = expandParameterArrays(pageLevelContextParams);

				// Process each combination
				for (const combination of paramCombinations) {
					// Generate hash for this specific combination
					const hash = hashParameters(combination);

					// Generate standardized name based on parameters
					const standardizedName = generateStandardizedName(combination);

					// Check if a context parameter with the same hash already exists (regardless of page)
					const existingContextParams = await req.payload.find<
						'context-parameters',
						ContextParametersSelect
					>({
						collection: 'context-parameters',
						where: {
							hash: {
								equals: hash,
							},
						},
					});

					if (
						existingContextParams.docs &&
						existingContextParams.docs.length > 0
					) {
						// A context parameter with the same hash exists
						const existingContextParam = existingContextParams.docs[0];

						if (existingContextParam) {
							// Check if the page field is already set to a different page
							const existingPageId = existingContextParam.page
								? typeof existingContextParam.page === 'object'
									? existingContextParam.page.id
									: existingContextParam.page
								: null;

							// Only update the page field if it's not already set to a different page
							const updateData: Partial<ContextParameter> = {
								name: standardizedName,
								hash,
								parameters: combination,
							};

							// If page field is not set or is set to this page, update it
							if (!existingPageId || existingPageId === doc.id) {
								updateData.page = doc.id;
							}

							// Update the existing context parameter
							await req.payload.update<
								'context-parameters',
								ContextParametersSelect
							>({
								collection: 'context-parameters',
								id: existingContextParam.id,
								data: updateData,
							});

							req.payload.logger.info(
								`Updated context parameter ${existingContextParam.id} for page ${doc.id} (combination: ${JSON.stringify(combination)})`,
							);
						}
					} else {
						// No context parameter with this hash exists, create a new one
						await req.payload.create<
							'context-parameters',
							ContextParametersSelect
						>({
							collection: 'context-parameters',
							data: {
								name: standardizedName,
								hash,
								parameters: combination,
								page: doc.id, // Set the page field for top-level parameters
								weight: 0,
							},
						});

						req.payload.logger.info(
							`Created new context parameter for page ${doc.id} (combination: ${JSON.stringify(combination)})`,
						);
					}
				}
			} else {
				// No OR relationships, process as a single parameter set
				const hash = hashParameters(pageLevelContextParams);

				// Generate standardized name based on parameters
				const standardizedName = generateStandardizedName(
					pageLevelContextParams,
				);

				// Check if a context parameter with the same hash already exists (regardless of page)
				const existingContextParams = await req.payload.find<
					'context-parameters',
					ContextParametersSelect
				>({
					collection: 'context-parameters',
					where: {
						hash: {
							equals: hash,
						},
					},
				});

				if (
					existingContextParams.docs &&
					existingContextParams.docs.length > 0
				) {
					// A context parameter with the same hash exists
					const existingContextParam = existingContextParams.docs[0];

					if (existingContextParam) {
						// Check if the page field is already set to a different page
						const existingPageId = existingContextParam.page
							? typeof existingContextParam.page === 'object'
								? existingContextParam.page.id
								: existingContextParam.page
							: null;

						// Only update the page field if it's not already set to a different page
						const updateData: Partial<ContextParameter> = {
							name: standardizedName,
							hash,
							parameters: pageLevelContextParams,
						};

						// If page field is not set or is set to this page, update it
						if (!existingPageId || existingPageId === doc.id) {
							updateData.page = doc.id;
						}

						// Update the existing context parameter
						await req.payload.update<
							'context-parameters',
							ContextParametersSelect
						>({
							collection: 'context-parameters',
							id: existingContextParam.id,
							data: updateData,
						});

						req.payload.logger.info(
							`Updated context parameter ${existingContextParam.id} for page ${doc.id}`,
						);
					}
				} else {
					// No context parameter with this hash exists, create a new one
					await req.payload.create<
						'context-parameters',
						ContextParametersSelect
					>({
						collection: 'context-parameters',
						data: {
							name: standardizedName,
							hash,
							parameters: pageLevelContextParams,
							page: doc.id, // Set the page field for top-level parameters
							weight: 0,
						},
					});

					req.payload.logger.info(
						`Created new context parameter for page ${doc.id}`,
					);
				}
			}
		} catch (err) {
			req.payload.logger.error(
				`Error updating context parameters for page ${doc.id}: ${(err as Error).message}`,
			);
		}
	}

	// Check for linked ContextParameter documents with mismatched hashes
	// This should be done after processing the page-level context parameters
	const linkedContextParams = await req.payload.find<
		'context-parameters',
		ContextParametersSelect
	>({
		collection: 'context-parameters',
		where: {
			page: {
				equals: doc.id,
			},
		},
	});
	if (linkedContextParams.docs && linkedContextParams.docs.length > 0) {
		try {
			// Get the current page's context parameters hashes
			let currentPageHashes: GeneratedParameterHash[] = [];
			if (doc.contextParameters) {
				const hasArrayValues = Object.values(pageLevelContextParams).some(
					Array.isArray,
				);
				if (hasArrayValues) {
					// Generate all possible combinations of parameter values
					const paramCombinations = expandParameterArrays(
						pageLevelContextParams,
					);

					// Process each combination
					for (const combination of paramCombinations) {
						// Generate hash for this specific combination
						const hash = hashParameters(combination);
						currentPageHashes.push({
							combination,
							hash,
						});
					}
				} else {
					currentPageHashes = generateParameterHashes(pageLevelContextParams);
				}

				currentPageHashes = currentPageHashes.filter((hashCombo) => {
					const keysCount = Object.keys(hashCombo.combination).length;
					const convertedKeysCount = Object.keys(pageLevelContextParams).length;
					return keysCount === convertedKeysCount;
				});
			}

			// Check each linked context parameter
			for (const contextParam of linkedContextParams.docs) {
				const matchesThisPage = currentPageHashes.some(
					(hashCombo) => hashCombo.hash === contextParam.hash,
				);
				// If the hash doesn't match one of the current page's hashes, delete the context parameter
				if (!matchesThisPage) {
					await req.payload.delete<
						'context-parameters',
						ContextParametersSelect
					>({
						collection: 'context-parameters',
						id: contextParam.id,
					});

					req.payload.logger.info(
						`Deleted context parameter ${contextParam.id} with mismatched hash for page ${doc.id}`,
					);
				}
			}
		} catch (err) {
			req.payload.logger.error(
				`Error checking for mismatched context parameter hashes for page ${doc.id}: ${(err as Error).message}`,
			);
		}
	}

	// return early if no layout configured
	if (!doc.content?.layout) return;

	// Process layouts and blocks
	let layoutAndPageParameters: ContextParameter['parameters'] = {
		...pageLevelContextParams,
	};

	// grab all layout-related context parameter docs
	const linkedLayoutContextParameters = await req.payload.find<
		'context-parameters',
		ContextParametersSelect
	>({
		collection: 'context-parameters',
		where: {
			layouts: {
				contains: doc.id,
			},
		},
	});
	// grab all blocks-related context parameter docs
	const linkedBlockContextParameters = await req.payload.find<
		'context-parameters',
		ContextParametersSelect
	>({
		collection: 'context-parameters',
		where: {
			blocks: {
				contains: doc.id,
			},
		},
	});

	/**
	 * This serves to keep track of which layout-related Context Parameter docs
	 * need to be updated or deleted, due to them no longer being used by the current page.
	 */
	const linkedLayoutContextParamsToUpdateOrDelete =
		linkedLayoutContextParameters.docs?.reduce(
			(accum, curr) => {
				accum[curr.hash!] = curr;
				return accum;
			},
			{} as Record<string, ContextParameter>,
		);

	/**
	 * This serves to keep track of which block-related Context Parameter docs
	 * need to be updated or deleted, due to them no longer being used by the current page.
	 */
	const linkedBlockContextParamsToUpdateOrDelete =
		linkedBlockContextParameters.docs?.reduce(
			(accum, curr) => {
				accum[curr.hash!] = curr;
				return accum;
			},
			{} as Record<string, ContextParameter>,
		);

	// Process each layout
	for (
		let layoutIndex = 0;
		layoutIndex < doc.content.layout.length;
		layoutIndex++
	) {
		const layout = doc.content.layout[layoutIndex] as unknown as EnhancedLayout;

		// Process layout context parameter overrides
		if (
			layout.contextParameterOverrides?.enabled &&
			layout.contextParameterOverrides.parameters
		) {
			// Merge with page parameters (page parameters take precedence)
			layoutAndPageParameters = {
				...transformContextParametersToJson(
					layout.contextParameterOverrides.parameters,
				),
				...pageLevelContextParams,
			};

			// Check if any parameter has array values (indicating an OR relationship)
			const hasArrayValues = Object.values(layoutAndPageParameters).some(
				Array.isArray,
			);

			try {
				if (hasArrayValues) {
					// Generate all possible combinations of parameter values
					const paramCombinations = expandParameterArrays(
						layoutAndPageParameters,
					);

					// Process each combination
					for (const combination of paramCombinations) {
						// Generate hash for this specific combination
						const hash = hashParameters(combination);

						// remove current hash from linked layout context parameter docs to update or delete
						delete linkedLayoutContextParamsToUpdateOrDelete[hash];

						// Generate standardized name for layout parameters
						const layoutStandardizedName =
							generateStandardizedName(combination);

						// Check if a context parameter with the same hash already exists
						const layoutExistingParams = await req.payload.find<
							'context-parameters',
							ContextParametersSelect
						>({
							collection: 'context-parameters',
							where: {
								hash: {
									equals: hash,
								},
							},
						});

						if (
							layoutExistingParams.docs &&
							layoutExistingParams.docs.length > 0
						) {
							// A context parameter with the same hash exists
							const layoutExistingParam = layoutExistingParams.docs[0];

							if (layoutExistingParam) {
								// Get current layouts array or initialize empty array
								const currentLayouts = layoutExistingParam.layouts || [];

								// Check if this page is already in the layouts array
								const layoutExists =
									Array.isArray(currentLayouts) &&
									currentLayouts.some(
										(page) => typeof page === 'object' && page.id === doc.id,
									);

								if (!layoutExists) {
									// Add this page to the layouts array if it doesn't exist
									const updatedLayouts = [...currentLayouts, doc.id];

									// Update the existing context parameter
									await req.payload.update<
										'context-parameters',
										ContextParametersSelect
									>({
										collection: 'context-parameters',
										id: layoutExistingParam.id,
										data: {
											name: layoutStandardizedName,
											hash,
											parameters: combination,
											layouts: updatedLayouts,
										},
									});

									req.payload.logger.info(
										`Added page ${doc.id} to existing layout context parameter ${layoutExistingParam.id} (combination: ${JSON.stringify(combination)})`,
									);
								}
							}
						} else {
							// No context parameter with this hash exists, create a new one
							await req.payload.create<
								'context-parameters',
								ContextParametersSelect
							>({
								collection: 'context-parameters',
								data: {
									name: layoutStandardizedName,
									parameters: combination,
									hash,
									layouts: [doc.id], // Add to layouts array for layout-level parameters
								},
							});

							req.payload.logger.info(
								`Created new layout context parameter for page ${doc.id} (combination: ${JSON.stringify(combination)})`,
							);
						}
					}
				} else {
					// No OR relationships, process as a single parameter set
					const hash = hashParameters(layoutAndPageParameters);

					// remove current hash from linked layout context parameter docs to update or delete
					delete linkedLayoutContextParamsToUpdateOrDelete[hash];

					// Generate standardized name for layout parameters
					const layoutStandardizedName = generateStandardizedName(
						layoutAndPageParameters,
					);

					// Check if a context parameter with the same hash already exists
					const layoutExistingParams = await req.payload.find<
						'context-parameters',
						ContextParametersSelect
					>({
						collection: 'context-parameters',
						where: {
							hash: {
								equals: hash,
							},
						},
					});

					if (
						layoutExistingParams.docs &&
						layoutExistingParams.docs.length > 0
					) {
						// A context parameter with the same hash exists
						const layoutExistingParam = layoutExistingParams.docs[0];

						if (layoutExistingParam) {
							// Get current layouts array or initialize empty array
							const currentLayouts = layoutExistingParam.layouts || [];

							// Check if this page is already in the layouts array
							const layoutExists =
								Array.isArray(currentLayouts) &&
								currentLayouts.some(
									(page) => typeof page === 'object' && page.id === doc.id,
								);

							if (!layoutExists) {
								// Add this page to the layouts array if it doesn't exist
								const updatedLayouts = [...currentLayouts, doc.id];

								// Update the existing context parameter
								await req.payload.update<
									'context-parameters',
									ContextParametersSelect
								>({
									collection: 'context-parameters',
									id: layoutExistingParam.id,
									data: {
										name: layoutStandardizedName,
										hash,
										parameters: layoutAndPageParameters,
										layouts: updatedLayouts,
									},
								});

								req.payload.logger.info(
									`Added page ${doc.id} to existing layout context parameter ${layoutExistingParam.id}`,
								);
							}
						}
					} else {
						// No context parameter with this hash exists, create a new one
						await req.payload.create<
							'context-parameters',
							ContextParametersSelect
						>({
							collection: 'context-parameters',
							data: {
								name: layoutStandardizedName,
								parameters: layoutAndPageParameters,
								hash,
								layouts: [doc.id], // Add to layouts array for layout-level parameters
							},
						});

						req.payload.logger.info(
							`Created new layout context parameter for page ${doc.id}`,
						);
					}
				}
			} catch (err) {
				req.payload.logger.error(
					`Error updating layout context parameters for page ${doc.id}, layout ${layoutIndex}: ${(err as Error).message}`,
				);
			}
		}

		// Process blocks within this layout
		if (layout.blocks && Array.isArray(layout.blocks)) {
			for (
				let blockIndex = 0;
				blockIndex < layout.blocks.length;
				blockIndex++
			) {
				const block = layout.blocks[blockIndex] as unknown as EnhancedBlock;

				// Skip if no context parameter overrides or not enabled
				if (
					!block.contextParameterOverrides?.enabled ||
					!block.contextParameterOverrides.parameters
				) {
					continue;
				}

				// Get the block ID or name
				const blockId = block.id || block.blockName || `block-${blockIndex}`;

				// Transform block context parameters to object
				// Handle both old array format and new object format
				/* eslint-disable-next-line @typescript-eslint/no-explicit-any */
				let blockContextParametersObj: Record<string, any> = {};

				if (Array.isArray(block.contextParameterOverrides.parameters)) {
					// Convert array of {name, value} objects to a single object
					/* eslint-disable-next-line @typescript-eslint/no-explicit-any */
					const paramsObject: Record<string, any> = {};
					block.contextParameterOverrides.parameters.forEach((param) => {
						if (param.name && param.value !== undefined) {
							paramsObject[param.name] = param.value;
						}
					});
					blockContextParametersObj =
						transformContextParametersToJson(paramsObject);
				} else if (
					typeof block.contextParameterOverrides.parameters === 'object'
				) {
					// If it's already an object, transform it directly
					blockContextParametersObj = transformContextParametersToJson(
						block.contextParameterOverrides.parameters || {},
					);
				}

				// Merge parameters with precedence: page > layout > block
				const mergedContextParameters = {
					...blockContextParametersObj,
					...layoutAndPageParameters,
				};

				// Check if any parameter has array values (indicating an OR relationship)
				const hasArrayValues = Object.values(mergedContextParameters).some(
					Array.isArray,
				);

				try {
					if (hasArrayValues) {
						// Generate all possible combinations of parameter values
						const paramCombinations = expandParameterArrays(
							mergedContextParameters,
						);

						// Process each combination
						for (const combination of paramCombinations) {
							// Generate hash for this specific combination
							const hash = hashParameters(combination);

							// remove current hash from linked block context parameter docs to update or delete
							delete linkedBlockContextParamsToUpdateOrDelete[hash];

							// Generate standardized name for block parameters
							const blockStandardizedName =
								generateStandardizedName(combination);

							// Check if a context parameter with the same hash already exists
							const blockExistingParams = await req.payload.find<
								'context-parameters',
								ContextParametersSelect
							>({
								collection: 'context-parameters',
								where: {
									hash: {
										equals: hash,
									},
								},
							});

							if (
								blockExistingParams.docs &&
								blockExistingParams.docs.length > 0
							) {
								// A context parameter with the same hash exists
								const blockExistingParam = blockExistingParams.docs[0];

								if (blockExistingParam) {
									// Get current blocks array or initialize empty array
									const currentBlocks = blockExistingParam.blocks || [];

									// Check if this page is already in the blocks array
									const blockExists =
										Array.isArray(currentBlocks) &&
										currentBlocks.some(
											(page) => typeof page === 'object' && page.id === doc.id,
										);

									if (!blockExists) {
										// Add this page to the blocks array if it doesn't exist
										const updatedBlocks = [...currentBlocks, doc.id];

										// Update the existing context parameter
										await req.payload.update<
											'context-parameters',
											ContextParametersSelect
										>({
											collection: 'context-parameters',
											id: blockExistingParam.id,
											data: {
												name: blockStandardizedName,
												hash,
												parameters: combination,
												blocks: updatedBlocks,
											},
										});

										req.payload.logger.info(
											`Added page ${doc.id} to existing block context parameter ${blockExistingParam.id} (combination: ${JSON.stringify(combination)})`,
										);
									}
								}
							} else {
								// No context parameter with this hash exists, create a new one
								await req.payload.create<
									'context-parameters',
									ContextParametersSelect
								>({
									collection: 'context-parameters',
									data: {
										name: blockStandardizedName,
										parameters: combination,
										hash,
										blocks: [doc.id], // Add to blocks array for block-level parameters
									},
								});

								req.payload.logger.info(
									`Created new block context parameter for page ${doc.id} (combination: ${JSON.stringify(combination)})`,
								);
							}
						}
					} else {
						// No OR relationships, process as a single parameter set
						const hash = hashParameters(mergedContextParameters);

						// remove current hash from linked block context parameter docs to update or delete
						delete linkedBlockContextParamsToUpdateOrDelete[hash];

						// Generate standardized name for block parameters
						const blockStandardizedName = generateStandardizedName(
							mergedContextParameters,
						);

						// Check if a context parameter with the same hash already exists
						const blockExistingParams = await req.payload.find<
							'context-parameters',
							ContextParametersSelect
						>({
							collection: 'context-parameters',
							where: {
								hash: {
									equals: hash,
								},
							},
						});

						if (
							blockExistingParams.docs &&
							blockExistingParams.docs.length > 0
						) {
							// A context parameter with the same hash exists
							const blockExistingParam = blockExistingParams.docs[0];

							if (blockExistingParam) {
								// Get current blocks array or initialize empty array
								const currentBlocks = blockExistingParam.blocks || [];

								// Check if this page is already in the blocks array
								const blockExists =
									Array.isArray(currentBlocks) &&
									currentBlocks.some(
										(page) => typeof page === 'object' && page.id === doc.id,
									);

								if (!blockExists) {
									// Add this page to the blocks array if it doesn't exist
									const updatedBlocks = [...currentBlocks, doc.id];

									// Update the existing context parameter
									await req.payload.update<
										'context-parameters',
										ContextParametersSelect
									>({
										collection: 'context-parameters',
										id: blockExistingParam.id,
										data: {
											name: blockStandardizedName,
											hash,
											parameters: mergedContextParameters,
											blocks: updatedBlocks,
										},
									});

									req.payload.logger.info(
										`Added page ${doc.id} to existing block context parameter ${blockExistingParam.id}`,
									);
								}
							}
						} else {
							// No context parameter with this hash exists, create a new one
							await req.payload.create<
								'context-parameters',
								ContextParametersSelect
							>({
								collection: 'context-parameters',
								data: {
									name: blockStandardizedName,
									parameters: mergedContextParameters,
									hash,
									blocks: [doc.id], // Add to blocks array for block-level parameters
								},
							});

							req.payload.logger.info(
								`Created new block context parameter for page ${doc.id}`,
							);
						}
					}
				} catch (err) {
					req.payload.logger.error(
						`Error updating block context parameters for page ${doc.id}, layout ${layoutIndex}, block ${blockId}: ${(err as Error).message}`,
					);
				}
			}
		}
	}

	// update/delete linked layout and block context param docs

	// update/delete layout-related context parameters
	for (const contextParam of Object.values(
		linkedLayoutContextParamsToUpdateOrDelete,
	)) {
		const currentLayouts = contextParam.layouts || [];
		// Filter out the deleted page from all arrays
		const updatedLayouts = currentLayouts.filter((page) =>
			typeof page === 'object' ? page.id !== doc.id : page !== doc.id,
		);
		const hasPage = !!contextParam.page;
		const hasBlocks =
			Array.isArray(contextParam.blocks) && contextParam.blocks.length > 0;
		const hasPageOrBlocks = hasPage || hasBlocks;

		if (updatedLayouts.length === 0 && !hasPageOrBlocks) {
			// no other linked pages, delete the orphaned context param
			await req.payload.delete<'context-parameters', ContextParametersSelect>({
				collection: 'context-parameters',
				id: contextParam.id,
			});
		} else {
			// context param is linked to one or more other pages, update it
			await req.payload.update<'context-parameters', ContextParametersSelect>({
				collection: 'context-parameters',
				id: contextParam.id,
				data: {
					layouts: updatedLayouts,
				},
			});
		}
	}

	// update/delete block-related context parameters
	for (const contextParam of Object.values(
		linkedBlockContextParamsToUpdateOrDelete,
	)) {
		const currentBlocks = contextParam.blocks || [];
		// Filter out the deleted page from all arrays
		const updatedBlocks = currentBlocks.filter((page) =>
			typeof page === 'object' ? page.id !== doc.id : page !== doc.id,
		);
		const hasPage = !!contextParam.page;
		const hasLayouts =
			Array.isArray(contextParam.layouts) && contextParam.layouts.length > 0;
		const hasPageOrLayouts = hasPage || hasLayouts;

		if (updatedBlocks.length === 0 && !hasPageOrLayouts) {
			// no other linked pages, delete the orphaned context param
			await req.payload.delete<'context-parameters', ContextParametersSelect>({
				collection: 'context-parameters',
				id: contextParam.id,
			});
		} else {
			// context param is linked to one or more other pages, update it
			await req.payload.update<'context-parameters', ContextParametersSelect>({
				collection: 'context-parameters',
				id: contextParam.id,
				data: {
					blocks: updatedBlocks,
				},
			});
		}
	}
}

/**
 * Clean up context parameters when a page is deleted
 * Note: For delete operations, we only need the page ID
 */
async function cleanupContextParameters(
	doc: { id: string },
	req: PayloadRequest,
) {
	try {
		// Find all context parameters that reference this page in any field
		const contextParams = await req.payload.find<
			'context-parameters',
			ContextParametersSelect
		>({
			collection: 'context-parameters',
			where: {
				or: [
					{
						page: {
							equals: doc.id,
						},
					},
					{
						layouts: {
							contains: doc.id,
						},
					},
					{
						blocks: {
							contains: doc.id,
						},
					},
				],
			},
		});

		if (contextParams.docs && contextParams.docs.length > 0) {
			req.payload.logger.info(
				`Found ${contextParams.docs.length} context parameters referencing page ${doc.id}`,
			);

			// Process each context parameter
			for (const contextParam of contextParams.docs) {
				// Get the current arrays
				const currentLayouts = contextParam.layouts || [];
				const currentBlocks = contextParam.blocks || [];

				// Filter out the deleted page from all arrays
				const updatedLayouts = currentLayouts.filter((page) =>
					typeof page === 'object' ? page.id !== doc.id : page !== doc.id,
				);
				const updatedBlocks = currentBlocks.filter((page) =>
					typeof page === 'object' ? page.id !== doc.id : page !== doc.id,
				);

				// Check if page field matches the deleted page
				const updatePageField =
					contextParam.page === doc.id ||
					(typeof contextParam.page === 'object' &&
						contextParam.page?.id === doc.id)
						? null
						: contextParam.page;

				// If no references in any fields, delete the context parameter
				if (
					updatedLayouts.length === 0 &&
					updatedBlocks.length === 0 &&
					!updatePageField
				) {
					await req.payload.delete<
						'context-parameters',
						ContextParametersSelect
					>({
						collection: 'context-parameters',
						id: contextParam.id,
					});

					req.payload.logger.info(
						`Deleted orphaned context parameter ${contextParam.id}`,
					);
				} else {
					// Otherwise, update the context parameter with the modified arrays
					await req.payload.update<
						'context-parameters',
						ContextParametersSelect
					>({
						collection: 'context-parameters',
						id: contextParam.id,
						data: {
							layouts: updatedLayouts,
							blocks: updatedBlocks,
							page: updatePageField,
						},
					});

					req.payload.logger.info(
						`Removed page ${doc.id} from context parameter ${contextParam.id}`,
					);
				}
			}
		}
	} catch (err) {
		req.payload.logger.error(
			`Error cleaning up context parameters for page ${doc.id}: ${(err as Error).message}`,
		);
	}
}
