import { Field } from 'payload';

export const fields: Field[] = [
	{
		name: 'name',
		type: 'text',
		label: 'Name',
		required: true,
		admin: {
			description: 'The name of the ad_config entity',
		},
	},
	{
		name: 'slotTarget',
		type: 'text',
		label: 'Slot Target',
		required: true,
		admin: {
			description: 'The target slot for this ad configuration',
		},
	},
	{
		name: 'adPosition',
		type: 'text',
		label: 'Ad Position',
		required: true,
		admin: {
			description: 'The position of the ad',
		},
	},
	{
		name: 'adDevices',
		type: 'select',
		label: 'Ad Devices',
		required: true,
		hasMany: true,
		options: [
			{
				label: 'Mobile',
				value: 'mobile',
			},
			{
				label: 'Tablet',
				value: 'tablet',
			},
			{
				label: 'Desktop',
				value: 'desktop',
			},
		],
		admin: {
			description: 'The devices this ad configuration applies to',
		},
	},
	{
		name: 'adSizes',
		type: 'array',
		label: 'Ad Sizes',
		required: true,
		admin: {
			description: 'The sizes for this ad configuration',
		},
		fields: [
			{
				name: 'width',
				type: 'number',
				label: 'Width',
				required: true,
			},
			{
				name: 'height',
				type: 'number',
				label: 'Height',
				required: true,
			},
		],
	},
	{
		name: 'sequentialGroup',
		type: 'select',
		label: 'Sequential Group',
		required: true,
		options: [
			{
				label: 'Above the Fold (ATF)',
				value: 'ATF',
			},
			{
				label: 'Below the Fold (BTF)',
				value: 'BTF',
			},
		],
		admin: {
			description: 'The sequential group for this ad configuration',
		},
	},
	{
		name: 'adKeyword',
		type: 'text',
		label: 'Ad Keyword',
		admin: {
			description: 'Keyword for this ad configuration',
		},
	},
	{
		name: 'customJson',
		type: 'json',
		label: 'Custom JSON',
		admin: {
			description: 'Custom JSON configuration for this ad slot',
		},
	},
	{
		name: 'liteAdPosition',
		type: 'checkbox',
		label: 'Lite Ad Position',
		defaultValue: false,
		admin: {
			description: 'Display this Ad Configuration if Lite Ads are enabled',
		},
	},
];
