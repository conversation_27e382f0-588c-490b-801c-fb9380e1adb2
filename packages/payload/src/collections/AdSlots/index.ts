import type { CollectionConfig } from 'payload';
import { fields } from './fields';
import { createCollectionAccess } from '../Roles/utils/hasPermission';

export const AdSlots: CollectionConfig = {
	slug: 'ad-slots',
	admin: {
		useAsTitle: 'name',
		group: 'Advertising',
		defaultColumns: ['name', 'slotTarget', 'adPosition', 'sequentialGroup'],
	},
	access: createCollectionAccess('ad-slots'),
	fields,
	versions: {
		drafts: true,
	},
};
