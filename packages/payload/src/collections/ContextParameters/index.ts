import type { CollectionConfig } from 'payload';
import { createCollectionAccess } from '../Roles/utils/hasPermission';
import { hashParameters } from '@repo/payload/contextParameters/generateCombinations';
import { validConfigs, validNames } from '../Pages/utils/validConfigs';

// Create a field for each valid parameter name
const createParameterFields = () => {
	return validNames.map((name: string) => {
		const validValues = validConfigs[name] || [];

		// For parameters that can have any value (like pageKey), use a text field
		if (validValues.length === 0) {
			return {
				name,
				type: 'text',
				admin: {
					description: `Value for ${name} parameter`,
				},
			} as const;
		}

		// For parameters with predefined values, use a select field with multiple option
		return {
			name,
			type: 'select',
			hasMany: false,
			admin: {
				description: `Value for ${name} parameter.`,
			},
			options: validValues.map((value: string) => ({
				label: value,
				value,
			})),
		} as const;
	});
};

export const ContextParameters: CollectionConfig = {
	slug: 'context-parameters',
	access: createCollectionAccess('context-parameters'),
	admin: {
		useAsTitle: 'name',
		defaultColumns: ['name', 'page', 'weight', 'hash'],
		description: 'Context parameter combinations for page routing',
	},
	fields: [
		{
			name: 'name',
			type: 'text',
			required: true,
			admin: {
				description: 'Name to identify this parameter combination',
			},
		},
		{
			name: 'hash',
			type: 'text',
			required: false,
			unique: false,
			admin: {
				description:
					'Automatically generated unique hash representing this parameter combination',
				readOnly: true,
			},
			hooks: {
				beforeValidate: [
					({ data }) => {
						if (data?.parameters) {
							return hashParameters(data.parameters);
						}
						return undefined;
					},
				],
			},
		},
		{
			name: 'parameters',
			label: 'Parameters',
			type: 'group',
			admin: {
				description: 'The parameter key-value pairs. Must include "pageKey".',
			},
			fields: createParameterFields(),
			validate: (value) => {
				// Check if value is an object and not null
				if (typeof value !== 'object' || value === null) {
					return 'Parameters must be an object';
				}

				// Check if pageKey is present
				const params = value as Record<string, unknown>;
				if (!params.pageKey) {
					return 'Parameters must include "pageKey"';
				}

				return true; // Validation passed
			},
		},
		{
			name: 'page',
			type: 'relationship',
			relationTo: 'pages',
			hasMany: false,
			required: false,
			admin: {
				description:
					'The page this parameter combination is used at the top level',
			},
		},
		{
			name: 'layouts',
			type: 'relationship',
			relationTo: 'pages',
			hasMany: true,
			required: false,
			admin: {
				description:
					'Pages where this parameter combination is used in layouts',
			},
		},
		{
			name: 'blocks',
			type: 'relationship',
			relationTo: 'pages',
			hasMany: true,
			required: false,
			admin: {
				description: 'Pages where this parameter combination is used in blocks',
			},
		},
		{
			name: 'weight',
			type: 'number',
			min: 0,
			max: 1000,
			defaultValue: 0,
			admin: {
				description:
					'Priority weight (0-1000) for resolving conflicts when multiple partial matches have the same parameter count. Example: {"pageKey": "home", "deviceClass": "desktop", "weatherMode": "active"} and {"pageKey": "home", "deviceClass": "desktop", "siteMode": "severe"} have the same number of parameters - whichever has the higher weight will be used.',
			},
		},
		{
			name: 'description',
			type: 'text',
			admin: {
				description:
					'Optional description to help identify this parameter combination',
			},
		},
	],
	timestamps: true,
};
