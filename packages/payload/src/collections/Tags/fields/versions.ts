import { SelectField } from 'payload';

export const versionsField: SelectField = {
	name: 'versions',
	type: 'select',
	hasMany: true,
	options: [
		{ label: 'Version 1', value: 'v1' },
		{ label: 'Version 2', value: 'v2' },
		{ label: 'Version 3', value: 'v3' },
	],
	required: true,
	admin: {
		condition: (data) => data.type === 'iabTags',
		description: 'Select one or more versions that apply to this IAB Tag',
		isClearable: false,
		isSortable: false,
	},
	validate: (value) => {
		if (!value || (Array.isArray(value) && value.length === 0)) {
			return 'At least one version must be selected';
		}
		return true;
	},
};
