import { describe, it, expect, vi, beforeEach } from 'vitest';
import { parent } from '../index';

describe('parent field validation', () => {
	// Mock payload and request objects
	const mockPayload = {
		findByID: vi.fn(),
	};

	const mockReq = {
		payload: mockPayload,
	};

	// Extract the validation function with a more permissive type assertion
	// Using any for test purposes since we're testing behavior, not types
	const validateFn = parent.validate as unknown as (
		value: unknown,
		options: unknown,
	) => Promise<boolean | string>;

	beforeEach(() => {
		vi.resetAllMocks();
	});

	it('should return true when no parent is selected', async () => {
		const result = await validateFn(null, {
			data: { type: 'contentTags' },
			req: mockReq,
			// Add required properties with default values
			path: '',
			siblingData: {},
			blockData: {},
			preferences: {},
		});
		expect(result).toBe(true);
		expect(mockPayload.findByID).not.toHaveBeenCalled();
	});

	it('should reject self-reference', async () => {
		const id = '123';
		const result = await validateFn(id, {
			id,
			data: { type: 'contentTags' },
			req: mockReq,
			path: '',
			siblingData: {},
			blockData: {},
			preferences: {},
		});
		expect(result).toEqual('A tag cannot be its own parent!');
		expect(mockPayload.findByID).not.toHaveBeenCalled();
	});

	it('should validate channel type cannot have a parent', async () => {
		mockPayload.findByID.mockResolvedValue({ type: 'contentTags' });

		const result = await validateFn('456', {
			id: '123',
			data: { type: 'channel' },
			req: mockReq,
			path: '',
			siblingData: {},
			blockData: {},
			preferences: {},
		});

		expect(result).toEqual(
			expect.stringContaining('Valid parent types are: none'),
		);
	});

	it('should validate contentTags can only have contentTags as parents', async () => {
		mockPayload.findByID.mockResolvedValue({ type: 'adZones' });

		const result = await validateFn('456', {
			id: '123',
			data: { type: 'contentTags' },
			req: mockReq,
			path: '',
			siblingData: {},
			blockData: {},
			preferences: {},
		});

		expect(result).toEqual(
			expect.stringContaining('Valid parent types are: contentTags'),
		);
	});

	it('should accept valid parent-child relationship', async () => {
		mockPayload.findByID.mockResolvedValue({
			type: 'contentTags',
			parent: null,
		});

		const result = await validateFn('456', {
			id: '123',
			data: { type: 'contentTags' },
			req: mockReq,
			path: '',
			siblingData: {},
			blockData: {},
			preferences: {},
		});

		expect(result).toBe(true);
	});

	it('should detect circular references', async () => {
		// Create a circular reference: A -> B -> C -> A
		mockPayload.findByID.mockImplementation(async (params) => {
			const id = params.id;
			if (id === '456') return { type: 'contentTags', parent: '789' }; // B points to C
			if (id === '789') return { type: 'contentTags', parent: '123' }; // C points to A
			return null;
		});

		const result = await validateFn('456', {
			id: '123', // A
			data: { type: 'contentTags' },
			req: mockReq,
			path: '',
			siblingData: {},
			blockData: {},
			preferences: {},
		});

		expect(result).toEqual(
			expect.stringContaining('Circular parent reference detected'),
		);
	});
});
