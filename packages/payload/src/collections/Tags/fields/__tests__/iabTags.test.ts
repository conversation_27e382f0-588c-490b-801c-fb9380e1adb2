import { describe, it, expect } from 'vitest';
import { iabTagsField } from '../iabTags';

describe('iabTags field', () => {
	it('should only be visible for analyticsTag type', () => {
		// Test the condition function using type assertion for test purposes
		const condition = iabTagsField.admin?.condition as unknown as (
			data: any,
		) => boolean;

		// Should be visible for analyticsTag
		expect(condition({ type: 'analyticsTag' })).toBe(true);

		// Should not be visible for other types
		expect(condition({ type: 'iabTags' })).toBe(false);
		expect(condition({ type: 'contentTags' })).toBe(false);
		expect(condition({})).toBe(false);
	});

	it('should only show iabTags in filter options', () => {
		// Test that filterOptions only shows iabTags
		expect(iabTagsField.filterOptions).toEqual({
			type: { equals: 'iabTags' },
		});
	});

	it('should allow multiple IAB tags to be selected', () => {
		// Test that hasMany is true
		expect(iabTagsField.hasMany).toBe(true);
	});
});
