import { describe, test, expect, vi, beforeEach } from 'vitest';
import { queueAdMetricUpdateJobs } from './queueAdMetricUpdateJobs';
import { BasePayload, PayloadRequest } from 'payload';

describe('queueAdMetricUpdateJobs', () => {
	// Mock data
	const mockCategoryId = 'category-123';
	const mockAdMetricId = 'admetric-456';

	// Mock payload
	const mockPayload = {
		find: vi.fn(),
		delete: vi.fn(),
		logger: {
			debug: vi.fn(),
			error: vi.fn(),
		},
		jobs: {
			queue: vi.fn(),
		},
	};

	// Mock request object
	const mockReq: Partial<PayloadRequest> = {
		payload: mockPayload as unknown as BasePayload,
		user: { id: 'user-123' } as unknown as PayloadRequest['user'],
	};

	beforeEach(() => {
		// Reset all mocks before each test
		vi.resetAllMocks();

		// Mock getUserLocale function
		vi.mock('@repo/payload/utils/getUserLocale', () => ({
			getUserLocale: () => 'en-US',
		}));
	});

	test('should queue job when adMetric changes', async () => {
		// Setup
		mockPayload.find.mockResolvedValue({ docs: [] }); // No existing job

		const doc = {
			id: mockCategoryId,
			name: 'Test Category',
			type: 'categoryTags',
			adMetric: {
				relationTo: 'tags',
				value: mockAdMetricId,
			},
		};

		const previousDoc = {
			id: mockCategoryId,
			name: 'Test Category',
			type: 'categoryTags',
			adMetric: {
				relationTo: 'tags',
				value: 'old-admetric-id', // Different adMetric
			},
		};

		// Execute
		await queueAdMetricUpdateJobs({
			// @ts-expect-error - Mocking doc for testing
			doc,
			// @ts-expect-error - Mocking previousDoc for testing
			previousDoc,
			// @ts-expect-error - Mocking req for testing
			req: mockReq,
		});

		// Verify
		expect(mockPayload.find).toHaveBeenCalled();
		expect(mockPayload.jobs.queue).toHaveBeenCalledWith({
			task: 'updateAdMetrics',
			req: mockReq,
			input: expect.objectContaining({
				categoryId: mockCategoryId,
				adMetricId: mockAdMetricId,
			}),
			queue: 'ad-metrics',
		});
	});

	test('should not queue job if adMetric has not changed', async () => {
		// Setup
		const doc = {
			id: mockCategoryId,
			name: 'Test Category',
			type: 'categoryTags',
			adMetric: {
				relationTo: 'tags',
				value: mockAdMetricId,
			},
		};

		const previousDoc = {
			id: mockCategoryId,
			name: 'Test Category',
			type: 'categoryTags',
			adMetric: {
				relationTo: 'tags',
				value: mockAdMetricId, // Same adMetric
			},
		};

		// Execute
		await queueAdMetricUpdateJobs({
			// @ts-expect-error - Mocking doc for testing
			doc,
			// @ts-expect-error - Mocking previousDoc for testing
			previousDoc,
			// @ts-expect-error - Mocking req for testing
			req: mockReq,
		});

		// Verify
		expect(mockPayload.jobs.queue).not.toHaveBeenCalled();
	});

	test('should delete existing job if not processing', async () => {
		// Setup
		mockPayload.find.mockResolvedValue({
			docs: [
				{
					id: 'job-123',
					processing: false, // Not processing
				},
			],
		});

		const doc = {
			id: mockCategoryId,
			name: 'Test Category',
			type: 'categoryTags',
			adMetric: {
				relationTo: 'tags',
				value: mockAdMetricId,
			},
		};

		const previousDoc = {
			id: mockCategoryId,
			name: 'Test Category',
			type: 'categoryTags',
			adMetric: {
				relationTo: 'tags',
				value: 'old-admetric-id', // Different adMetric
			},
		};

		// Execute
		await queueAdMetricUpdateJobs({
			// @ts-expect-error - Mocking doc for testing
			doc,
			// @ts-expect-error - Mocking previousDoc for testing
			previousDoc,
			// @ts-expect-error - Mocking req for testing
			req: mockReq,
		});

		// Verify
		expect(mockPayload.delete).toHaveBeenCalledWith({
			collection: 'payload-jobs',
			req: mockReq,
			id: 'job-123',
		});

		expect(mockPayload.jobs.queue).toHaveBeenCalled();
	});
});
