import { CollectionAfterChangeHook } from 'payload';
import { Tag } from '@repo/payload/payload-types';
import { getUserLocale } from '@repo/payload/utils/getUserLocale';

export const queueAdMetricUpdateJobs: CollectionAfterChangeHook<Tag> = async ({
	doc,
	previousDoc,
	req,
	context,
}) => {
	// Skip if this update was triggered by a job
	if (context?.skipQueueJobs === true) {
		req.payload.logger.debug(
			`Skipping job queue for tag ID: ${doc.id} (context flag set)`,
		);
		return doc;
	}

	// Only proceed if this is a categoryTag and the adMetric has changed
	if (
		doc.type !== 'categoryTags' ||
		JSON.stringify(doc.adMetric) === JSON.stringify(previousDoc?.adMetric)
	) {
		return doc;
	}

	// Extract adMetricId from the adMetric field
	let adMetricId: string | null = null;

	if (doc.adMetric) {
		if (typeof doc.adMetric === 'object' && doc.adMetric !== null) {
			// Check if it's a Tag object with an id property
			if ('id' in doc.adMetric) {
				adMetricId = String((doc.adMetric as Tag).id);
			}
			// Check if it's a relationship object with value property
			else if ('value' in doc.adMetric) {
				// eslint-disable-next-line @typescript-eslint/no-explicit-any
				adMetricId = String((doc.adMetric as any).value);
			}
		} else if (typeof doc.adMetric === 'string') {
			adMetricId = doc.adMetric;
		}
	}

	req.payload.logger.debug(
		`Queueing updateAdMetrics job for category ID: ${doc.id}, adMetricId: ${adMetricId || 'null'}`,
	);

	try {
		// Check if a job already exists for this category
		const jobExists = await req.payload.find({
			collection: 'payload-jobs',
			req,
			where: {
				'input.categoryId': {
					equals: doc.id,
				},
				taskSlug: {
					equals: 'updateAdMetrics',
				},
			},
		});

		// If a job exists and is not processing, delete it
		const existingJob = jobExists?.docs?.[0];
		if (existingJob && !existingJob?.processing) {
			await req.payload.delete({
				collection: 'payload-jobs',
				req,
				id: existingJob.id,
			});
		}

		// Queue the job to update ad metrics
		await req.payload.jobs.queue({
			task: 'updateAdMetrics',
			req,
			input: {
				categoryId: doc.id,
				adMetricId,
				batchSize: 100,
				skipIds: [],
				locale: getUserLocale(req),
			},
			queue: 'ad-metrics',
		});

		return doc;
	} catch (error) {
		req.payload.logger.error(
			`[queueAdMetricUpdateJobs] Error queueing job: ${error}`,
		);
		return doc;
	}
};
