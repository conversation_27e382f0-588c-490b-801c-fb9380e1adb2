# Tags Collection

## Overview

This document outlines the structure, usage patterns, and best practices for the Tags collection in our PayloadCMS implementation. The Tags collection serves as a unified taxonomy system that supports hierarchical relationships for content organization, ad zones, and metrics.

## Structure

### Core Fields

- **name**: Display name of the tag
- **slug**: URL-friendly version of the name (auto-generated)
- **type**: Categorizes tags into different vocabularies (categoryTags, adZones, adMetrics)
- **parent**: Establishes hierarchical relationships between tags
- **fullPath**: Auto-generated breadcrumb-style path showing the full hierarchy

### Tag Types

1. **categoryTags**: Used for content categorization and topics
2. **adZones**: Represents advertising placement areas
3. **adMetrics**: Metrics associated with specific ad zones

## Hierarchical Relationships

The Tags collection uses a parent-child relationship model to create hierarchies within each tag type:

### Content Tags Hierarchy

- **Categories**: Top-level categoryTags (no parent)
- **Topics**: Child categoryTags (have a parent)

Example structure:

```text
Home-and-Garden (category, type: categoryTags, no parent)
└── Grilling (topic, type: categoryTags, parent: Home-and-Garden)
└── Landscaping (topic, type: categoryTags, parent: Home-and-Garden)

Weather (category, type: categoryTags, no parent)
└── Storms (topic, type: categoryTags, parent: Weather)
└── Forecasts (topic, type: categoryTags, parent: Weather)
```

### Ad Zones and Metrics Hierarchy

- **Ad Zones**: Top-level tags of type adZones (no parent)
- **Ad Metrics**: Tags of type adMetrics with an ad zone parent

Example structure:

```text
Homepage (type: adZones, no parent)
└── Homepage Banner (type: adMetrics, parent: Homepage)
└── Homepage Sidebar (type: adMetrics, parent: Homepage)

Weather Section (type: adZones, no parent)
└── Weather Leaderboard (type: adMetrics, parent: Weather Section)
```

## Usage Guidelines

### Creating New Tags

1. **Determine the appropriate type**:

   - Use `categoryTags` for editorial content organization
   - Use `adZones` for advertising placement areas
   - Use `adMetrics` for specific metrics tied to ad zones

2. **Establish proper hierarchy**:

   - Categories should be top-level categoryTags (no parent)
   - Topics should be child categoryTags (with a parent)
   - Ad zones should have no parent
   - Ad metrics should have an ad zone as parent

3. **Naming conventions**:
   - Use clear, concise names
   - Be consistent with capitalization
   - Avoid special characters when possible

### Filtering Tags

Use the provided filter functions to retrieve specific types of tags:

```typescript
// Get categories (top-level categoryTags)
const categories = await payload.find({
	collection: "tags",
	where: {
		and: [{ type: { equals: "categoryTags" } }, { parent: { exists: false } }],
	},
});

// Get topics for a specific category
const topics = await payload.find({
	collection: "tags",
	where: {
		and: [
			{ type: { equals: "categoryTags" } },
			{ parent: { equals: categoryId } },
		],
	},
});

// Get all ad zones
const adZones = await payload.find({
	collection: "tags",
	where: {
		type: { equals: "adZones" },
	},
});

// Get ad metrics for a specific zone
const adMetrics = await payload.find({
	collection: "tags",
	where: {
		and: [{ type: { equals: "adMetrics" } }, { parent: { equals: adZoneId } }],
	},
});
```

## Validation Rules

The Tags collection includes several validation rules to maintain data integrity:

1. **Circular reference prevention**: Tags cannot create circular parent-child relationships
2. **Self-reference prevention**: A tag cannot be its own parent
3. **Type-specific validation** (recommended):
   - Ad metrics should have an ad zone parent
   - Ad zones should not have parents

## Best Practices

1. **Maintain clear hierarchies**: Don't create overly deep hierarchies (more than 3-4 levels)
2. **Use consistent typing**: Don't mix different tag types in the same hierarchy
3. **Leverage the fullPath**: Use the auto-generated fullPath for UI breadcrumbs and navigation
4. **Index for performance**: Create database indexes on frequently queried fields (type, parent)
5. **Validate relationships**: Ensure proper parent-child relationships between different tag types

## Migration Considerations

This taxonomy structure is designed to be compatible with Drupal's taxonomy system:

- The `type` field corresponds to Drupal's "vocabulary" concept
- The parent-child relationships correspond to Drupal's term hierarchy

When migrating from Drupal:

1. Map Drupal vocabularies to tag types
2. Preserve the hierarchical relationships
3. Generate slugs from term names
4. Maintain any existing term references

## Implementation Details

### fullPath Generation

The `fullPath` field is automatically generated using a `beforeChange` hook that:

1. Uses just the tag name if there's no parent
2. Combines the parent's fullPath with the tag name if there is a parent
3. Creates a breadcrumb-style path (e.g., "Parent > Child > Grandchild")

### Circular Reference Prevention

The parent field includes validation that:

1. Prevents a tag from being its own parent
2. Traverses the entire parent chain to detect cycles
3. Provides clear error messages when circular references are detected

## Examples

### Creating a Category and Topics

```typescript
// Create a category
const category = await payload.create({
	collection: "tags",
	data: {
		name: "Home and Garden",
		type: "categoryTags",
		// No parent for categories
	},
});

// Create a topic under that category
const topic = await payload.create({
	collection: "tags",
	data: {
		name: "Grilling",
		type: "categoryTags",
		parent: category.id,
	},
});
```

### Creating Ad Zones and Metrics

```typescript
// Create an ad zone
const adZone = await payload.create({
	collection: "tags",
	data: {
		name: "Homepage",
		type: "adZones",
		// No parent for ad zones
	},
});

// Create an ad metric for that zone
const adMetric = await payload.create({
	collection: "tags",
	data: {
		name: "Homepage Banner",
		type: "adMetrics",
		parent: adZone.id,
	},
});
```

### Using Tags in Content

```typescript
// Assign a category to an article
const article = await payload.create({
	collection: "articles",
	data: {
		title: "Grilling Tips for Summer",
		category: {
			relationTo: "tags",
			value: categoryId,
		},
		// Other article fields...
	},
});
```

### Assigning Multiple Tags

```typescript
// Assign multiple tags to an article
const articleWithTags = await payload.create({
	collection: "articles",
	data: {
		title: "Best Grills of 2025",
		tags: [
			{
				relationTo: "tags",
				value: grillingTopicId,
			},
			{
				relationTo: "tags",
				value: reviewsTopicId,
			},
		],
		// Other article fields...
	},
});
```
