import { CollectionConfig, CollectionSlug } from 'payload';
import { contentTab } from '../Articles/fields/tabs';
import { createTabs } from '@repo/payload/fields/utility';
import { title } from './fields';
import { createCollectionAccess } from '../Roles/utils/hasPermission';

const collectionPrefixMap: Partial<Record<CollectionSlug, string>> = {
	liveblogs: '/content/liveblogs',
};

export const Liveblogs: CollectionConfig = {
	slug: 'liveblogs',
	access: createCollectionAccess('liveblogs'),
	admin: {
		defaultColumns: ['title', 'slug', 'updatedAt'],
		livePreview: {
			url: ({ data }) => {
				const slug = typeof data?.slug === 'string' ? data.slug : '';
				const collection = 'liveblogs';
				const encodedParams = new URLSearchParams({
					slug,
					collection,
					path: `${collectionPrefixMap[collection]}/${slug}`,
					previewSecret: process.env.PREVIEW_SECRET || '',
				});

				return `/api/payload/v1/preview?${encodedParams.toString()}`;
			},
		},
	},
	fields: [title, createTabs([contentTab])],
	versions: {
		drafts: {
			autosave: {
				interval: 1000, // We set this interval for optimal live preview
			},
			schedulePublish: true,
		},
		maxPerDoc: 50,
	},
};
