import { DateField, TextField, RichTextField } from 'payload';
import type { TextFieldSingleValidation } from 'payload';
import {
	BlocksFeature,
	FixedToolbarFeature,
	HeadingFeature,
	HorizontalRuleFeature,
	LinkFeature,
	InlineToolbarFeature,
	lexicalEditor,
	type LinkFields,
} from '@payloadcms/richtext-lexical';
import { allowedBlocks } from './blocks';

export const eventStartTime: DateField = {
	name: 'eventStartTime',
	label: 'Event Start Time',
	localized: true,
	type: 'date',
	required: true,
};

export const eventEndTime: DateField = {
	name: 'eventStartTime',
	label: 'Event Start Time',
	localized: true,
	type: 'date',
	required: true,
};

export const title: TextField = {
	name: 'title',
	label: 'Title',
	localized: true,
	type: 'text',
	required: true,
};

export const body: RichTextField = {
	name: 'body',
	label: '',
	type: 'richText',
	localized: true,
	editor: lexicalEditor({
		features: ({ rootFeatures }) => {
			return [
				...rootFeatures,
				HeadingFeature({
					enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'],
				}),
				BlocksFeature({
					blocks: allowedBlocks ?? [],
				}),
				FixedToolbarFeature(),
				InlineToolbarFeature(),
				HorizontalRuleFeature(),
				LinkFeature({
					enabledCollections: ['pages', 'articles'],
					fields: ({ defaultFields }) => {
						const defaultFieldsWithoutUrl = defaultFields.filter((field) => {
							if ('name' in field && field.name === 'url') return false;
							return true;
						});

						return [
							...defaultFieldsWithoutUrl,
							{
								name: 'url',
								type: 'text',
								admin: {
									condition: (_data, siblingData) =>
										siblingData?.linkType !== 'internal',
								},
								label: ({ t }) => t('fields:enterURL'),
								required: true,
								validate: ((value, options) => {
									if (
										(options?.siblingData as LinkFields)?.linkType ===
										'internal'
									) {
										return true; // no validation needed, as no url should exist for internal links
									}
									return value ? true : 'URL is required';
								}) as TextFieldSingleValidation,
							},
						];
					},
				}),
			];
		},
	}),
	required: true,
};
