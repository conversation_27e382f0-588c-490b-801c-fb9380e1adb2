import type { CollectionConfig } from 'payload';
import { slugField } from '../../fields/slug';
import { title, permissions, isAdmin } from './fields';
import { createCollectionAccess } from './utils/hasPermission';

export const Roles: CollectionConfig = {
	slug: 'roles',
	labels: {
		singular: 'Role',
		plural: 'Roles',
	},
	admin: {
		useAsTitle: 'title',
		defaultColumns: ['title', 'slug'],
		description: 'Manage user roles and their permissions',
	},
	access: createCollectionAccess('roles'),
	fields: [
		title,
		isAdmin,
		permissions,
		...slugField('title', {
			slugOverrides: {
				admin: {
					description:
						'URL-friendly version of the title. Use "admin" for admin roles.',
				},
			},
			checkboxOverrides: {
				defaultValue: false, // Allow manual editing by default
			},
		}),
	],
};
