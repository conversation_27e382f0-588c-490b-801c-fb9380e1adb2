// Types for access permission system
export type AccessPermission = 'none' | 'view' | 'edit' | 'full';
export type CollectionSlug = string;
import type { PayloadRequest } from 'payload'

/**
 * Check if a user has the required permission for a specific collection
 *
 * @param req - PayloadRequest containing user information
 * @param collectionId - The slug of the collection to check access for
 * @param access - The level of access required ('none' | 'view' | 'edit' | 'full')
 * @returns boolean - true if user has the required access, false otherwise
 *
 * Permission levels:
 * - 'none': Collection is hidden (always returns false)
 * - 'view': Requires 'read' permission
 * - 'edit': Requires 'read', 'create', and 'update' permissions
 * - 'full': Requires 'read', 'create', 'update', and 'delete' permissions
 */
export function hasPermission(
	req: PayloadRequest, // PayloadRequest type
	collectionId: CollectionSlug,
	access: AccessPermission,
): boolean {
	console.log(req);
	// If no user is logged in, deny access
	if (!req.user) {
		return false;
	}

	// Super admin always has full access (backward compatibility)
	// Handle both old string array format and new relationship format
	const userRoles = req.user.role || [];

	// Check if user has admin role (need to check by fetching role data since we have IDs)
	let hasAdminRole = false;

	if (Array.isArray(userRoles)) {
		// If roles are objects with isAdmin property
		if (
			userRoles.length > 0 &&
			typeof userRoles[0] === 'object' &&
			userRoles[0].hasOwnProperty('isAdmin')
		) {
			hasAdminRole = userRoles.some((role: any) => role.isAdmin === true);
		}
	}

	if (hasAdminRole) {
		return access !== 'none';
	}

	// If access level is 'none', always deny
	if (access === 'none') {
		return false;
	}

	try {
		// Get user's roles (handle both old and new format)
		const userRoles = req.user.role || [];
		const collectionKey = collectionId.replace(/-/g, '');
		// Check each role for permissions
		for (const role of userRoles) {
			let rolePermissions;
			// Handle relationship format (role is an object with permissions)
			if (
				typeof role === 'object' &&
				role !== null &&
				'permissions' in role
			) {
				// Nested group structure - permissions are stored as nested objects
				const roleWithPermissions = role as { permissions: Record<string, unknown> };
				rolePermissions = (roleWithPermissions.permissions as Record<string, unknown>)[collectionKey];
			}
			// Handle old string format - would need to fetch from database
			else if (typeof role === 'string') {
				// For backward compatibility, you might want to fetch role data here
				// For now, skip string-based roles - use hasPermissionAsync for database lookup
				continue;
			}

			if (rolePermissions && typeof rolePermissions === 'object') {
				const permissions = rolePermissions as {
					read?: boolean;
					create?: boolean;
					update?: boolean;
					delete?: boolean;
				};

				// Check permissions based on access level
				switch (access) {
					case 'view':
						if (permissions.read === true) {
							return true;
						}
						break;

					case 'edit':
						if (
							permissions.read === true &&
							permissions.create === true &&
							permissions.update === true
						) {
							return true;
						}
						break;

					case 'full':
						if (
							permissions.read === true &&
							permissions.create === true &&
							permissions.update === true &&
							permissions.delete === true
						) {
							return true;
						}
						break;
				}
			}
		}
		return false;
	} catch (error) {
		console.error(`Error checking permissions for ${collectionId}:`, error);
		return false;
	}
}

/**
 * Alternative version that fetches role permissions from the database
 * Use this if permissions are stored in the Roles collection rather than directly on the user
 */
export async function hasPermissionAsync(
	req: PayloadRequest, // PayloadRequest type
	collectionId: CollectionSlug,
	access: AccessPermission,
): Promise<boolean> {
	// If no user is logged in, deny access
	if (!req.user) {
		return false;
	}
	// Super admin always has full access - check by fetching role data
	const userRoleKeys = req.user.role || [];
	// Fetch roles to check for admin
	// Check if user has any role with isAdmin checkbox checked
	for (const roleId of userRoleKeys) {
		if (typeof roleId === 'string') {
			try {
				const role = await req.payload.findByID({
					collection: 'roles',
					id: roleId,
				});
				if (
					role &&
					typeof role === 'object' &&
					'isAdmin' in role &&
					(role as { isAdmin: boolean }).isAdmin === true
				) {
					return access !== 'none';
				}
			} catch (_error) {
				console.warn(`Failed to fetch role ${roleId}`);
			}
		}
	}
	// If access level is 'none', always deny
	if (access === 'none') {
		return false;
	}
	try {
		// Get user's role keys
		const userRoleKeys = req.user.role || [];
		// Fetch role documents from the database using IDs.
		const rolePromises = userRoleKeys
			.filter((roleId): roleId is string => typeof roleId === 'string')
			.map(async (roleId: string) => {
				try {
					const roleDoc = await req.payload.findByID({
						collection: 'roles',
						id: roleId,
					});
					return roleDoc;
				} catch (error) {
					console.warn(`Failed to fetch role ${roleId}:`, error);
					return null;
				}
			});
		const roles = (await Promise.all(rolePromises)).filter(Boolean);

		// Check each role's permissions for this collection
		for (const role of roles) {
			if (!role || typeof role !== 'object') continue;

			let rolePermissions;

			if ('permissions' in role) {
				// Nested group structure - permissions are stored as nested objects
				const collectionKey = collectionId.replace(/-/g, '');
				const roleWithPermissions = role as { permissions: Record<string, unknown> };
				rolePermissions = (roleWithPermissions.permissions as Record<string, unknown>)[collectionKey];
			}

			if (rolePermissions) {
				// Check permissions based on access level
				switch (access) {
					case 'view':
						if (rolePermissions.read === true) {
							return true;
						}
						break;
					case 'edit':
						if (
							rolePermissions.read === true &&
							rolePermissions.create === true &&
							rolePermissions.update === true
						) {
							return true;
						}
						break;
					case 'full':
						if (
							rolePermissions.read === true &&
							rolePermissions.create === true &&
							rolePermissions.update === true &&
							rolePermissions.delete === true
						) {
							return true;
						}
						break;
				}
			}
		}
		return false;
	} catch (error) {
		console.error(`Error checking permissions for ${collectionId}:`, error);
		return false;
	}
}

/**
 * Utility functions to create access control functions for collections
 * These can be used directly in collection configurations
 */

/**
 * Create a read access function for a collection
 */
export function createReadAccess(collectionId: CollectionSlug) {
	return async ({ req }: { req: PayloadRequest }) => {
		// Try async version first (handles role ID lookup)
		try {
			return await hasPermissionAsync(req, collectionId, 'view');
		} catch (error) {
			console.warn(
				`Async permission check failed, falling back to sync:`,
				error,
			);
			return hasPermission(req, collectionId, 'view');
		}
	};
}

/**
 * Create a create access function for a collection
 */
export function createCreateAccess(collectionId: CollectionSlug) {
	return async ({ req }: { req: PayloadRequest }) => {
		try {
			return await hasPermissionAsync(req, collectionId, 'edit');
		} catch (error) {
			console.warn(
				`Async permission check failed, falling back to sync:`,
				error,
			);
			return hasPermission(req, collectionId, 'edit');
		}
	};
}

/**
 * Create an update access function for a collection
 */
export function createUpdateAccess(collectionId: CollectionSlug) {
	return async ({ req }: { req: PayloadRequest }) => {
		try {
			return await hasPermissionAsync(req, collectionId, 'edit');
		} catch (error) {
			console.warn(
				`Async permission check failed, falling back to sync:`,
				error,
			);
			return hasPermission(req, collectionId, 'edit');
		}
	};
}

/**
 * Create a delete access function for a collection
 */
export function createDeleteAccess(collectionId: CollectionSlug) {
	return async ({ req }: { req: PayloadRequest }) => {
		try {
			return await hasPermissionAsync(req, collectionId, 'full');
		} catch (error) {
			console.warn(
				`Async permission check failed, falling back to sync:`,
				error,
			);
			return hasPermission(req, collectionId, 'full');
		}
	};
}

/**
 * Create all CRUD access functions for a collection at once
 */
export function createCollectionAccess(collectionId: CollectionSlug) {
	return {
		read: createReadAccess(collectionId),
		create: createCreateAccess(collectionId),
		update: createUpdateAccess(collectionId),
		delete: createDeleteAccess(collectionId),
	};
}
