import type { <PERSON><PERSON>ield, Field } from 'payload';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Dynamically discover all collections by scanning the collections directory
 * This runs at runtime and automatically includes any new collections
 */
function discoverCollections(): string[] {
	try {
		const collectionsDir = path.join(__dirname, '..');
		const collections: string[] = [];

		// Read all directories in the collections folder
		const items = fs.readdirSync(collectionsDir, { withFileTypes: true });

		for (const item of items) {
			if (item.isDirectory()) {
				const collectionPath = path.join(collectionsDir, item.name, 'index.ts');

				// Check if the collection has an index.ts file
				if (fs.existsSync(collectionPath)) {
					try {
						const content = fs.readFileSync(collectionPath, 'utf8');

						// Look for slug definition in the file
						const slugMatch = content.match(/slug:\s*['"`]([^'"`]+)['"`]/);
						if (slugMatch && slugMatch[1]) {
							collections.push(slugMatch[1]);
						} else {
							// Fallback: use directory name converted to kebab-case
							const slug = item.name
								.replace(/([A-Z])/g, '-$1')
								.toLowerCase()
								.replace(/^-/, '');
							collections.push(slug);
						}
					} catch (error) {
						console.warn(
							`Could not read collection file: ${collectionPath}`,
							error,
						);
					}
				}
			}
		}

		return collections.sort();
	} catch (error) {
		console.error('Error discovering collections:', error);
		// Fallback to empty array - this will result in no permissions being generated
		return [];
	}
}

/**
 * Get all collection slugs dynamically
 * This discovers collections at runtime, so new collections are automatically included
 */
function getCollectionSlugs(): string[] {
	return discoverCollections();
}

/**
 * Generate permissions as individual fields for each collection
 * This creates a table-like structure with all collections displayed automatically
 */
function generatePermissionsTable(): GroupField {
	const collections = getCollectionSlugs();
	const fields: Field[] = [];

	collections.forEach((collection) => {
		const collectionName = collection
			.split('-')
			.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
			.join(' ');

		// Create a group for each collection with its permissions
		fields.push({
			name: collection.replace(/-/g, ''),
			type: 'group',
			label: collectionName,
			admin: {
				style: {
					border: '1px solid #e0e0e0',
					borderRadius: '4px',
					padding: '15px',
					marginBottom: '10px',
					display: 'grid',
					gridTemplateColumns: 'repeat(4, 1fr)',
					gap: '15px',
				},
			},
			fields: [
				{
					name: 'read',
					type: 'checkbox',
					label: 'Read',
				},
				{
					name: 'create',
					type: 'checkbox',
					label: 'Create',
				},
				{
					name: 'update',
					type: 'checkbox',
					label: 'Update',
				},
				{
					name: 'delete',
					type: 'checkbox',
					label: 'Delete',
				},
			],
		} as GroupField);
	});

	return {
		name: 'permissions',
		type: 'group',
		label: 'Collection Permissions',
		admin: {
			description:
				'Set permissions for each collection. All collections are displayed below with their respective permissions.',
		},
		fields,
	};
}

/**
 * Permissions field configuration for roles
 * This creates a table-like interface with collections as rows and CRUD as columns
 */
export const rolePermissions = generatePermissionsTable();
