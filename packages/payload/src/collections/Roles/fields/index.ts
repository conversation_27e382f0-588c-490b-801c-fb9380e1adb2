import type { TextField, CheckboxField } from 'payload';
import { rolePermissions } from '../permissions';

export const title: TextField = {
	name: 'title',
	label: 'Title',
	type: 'text',
	required: true,
};

export const isAdmin: CheckboxField = {
	name: 'isAdmin',
	type: 'checkbox',
	label: 'Admin Role',
	defaultValue: false,
	admin: {
		position: 'sidebar',
		description:
			'Check this to make this role have full admin access to all collections.',
	},
};

export const permissions = rolePermissions;
