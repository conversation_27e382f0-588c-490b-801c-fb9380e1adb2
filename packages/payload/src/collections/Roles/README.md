# Roles Collection

This directory contains the Roles collection configuration and automated permission management.

## Automated Collection Discovery

The permissions system automatically discovers all collections in your Payload configuration and generates CRUD permissions for each one.

### Files

- **`permissions.ts`** - Contains the auto-generated collection registry and permission definitions
- **`updateCollectionRegistry.js`** - <PERSON><PERSON><PERSON> to automatically scan and update the collection registry
- **`fields/index.ts`** - Field definitions for the Roles collection

### How It Works

1. **Collection Registry**: The `COLLECTION_REGISTRY` array in `permissions.ts` contains all collection slugs
2. **Auto-Generation**: Permissions are automatically generated for each collection with CRUD operations (read, create, update, delete)
3. **Dynamic Updates**: Run the update script whenever you add/remove collections

### Updating the Collection Registry

When you add or remove collections, run this command to automatically update the permissions:

```bash
cd packages/payload/src/collections/Roles
node updateCollectionRegistry.js
```

This will:
- Scan all collection directories
- Extract collection slugs from their configuration files
- Update the `COLLECTION_REGISTRY` array in `permissions.ts`
- Regenerate all permission checkboxes

### Generated Permissions

For each collection, the following permissions are automatically created:
- **Read [Collection]** - View/read access
- **Create [Collection]** - Create new items
- **Update [Collection]** - Edit existing items  
- **Delete [Collection]** - Remove items

### Example

If you have collections: `articles`, `pages`, `users`

The system generates:
- ☐ Read Articles, ☐ Create Articles, ☐ Update Articles, ☐ Delete Articles
- ☐ Read Pages, ☐ Create Pages, ☐ Update Pages, ☐ Delete Pages  
- ☐ Read Users, ☐ Create Users, ☐ Update Users, ☐ Delete Users

### Current Collections

The system currently manages permissions for these collections:
- ad-slots
- articles  
- content-queries
- context-parameters
- images
- liveblogs
- locations
- pages
- permissions
- roles
- tags
- tenants
- third-party-config
- users
- videos

Total: **60 permission checkboxes** (15 collections × 4 operations)
