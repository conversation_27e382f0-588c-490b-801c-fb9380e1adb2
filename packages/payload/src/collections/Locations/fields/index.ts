import { TextField, SelectField } from 'payload';
import { locationPointField } from './locationPoint';

export const legacyLocID: TextField = {
	name: 'locID',
	label: 'Legacy Loc ID',
	type: 'text',
	admin: {
		description:
			'Legacy TWC location identifier corresponding to a unique location record.  This location type is provided to ensure compatibility with legacy queries.',
	},
};

// Basic identification fields
export const displayName: TextField = {
	name: 'displayName',
	label: 'Display Name',
	type: 'text',
	required: true,
	admin: {
		description: 'The common display name for this location',
	},
};

// Geographic hierarchy fields
export const city: TextField = {
	name: 'city',
	label: 'City',
	localized: true,
	type: 'text',
	index: true,
};

export const adminDistrictCode: TextField = {
	name: 'adminDistrictCode',
	label: 'Admin District Code',
	type: 'text',
	admin: {
		description: "State/province/region code (e.g., 'AL', 'CA')",
	},
};

export const adminDistrict: TextField = {
	name: 'adminDistrict',
	label: 'Admin District',
	localized: true,
	type: 'text',
	index: true,
	admin: {
		description: 'Full state/province/region name',
	},
};

export const country: TextField = {
	name: 'country',
	label: 'Country',
	localized: true,
	type: 'text',
	index: true,
};

export const countryCode: TextField = {
	name: 'countryCode',
	label: 'Country Code',
	localized: true,
	type: 'text',
	index: true,
	admin: {
		description: 'ISO 3166-1 alpha-2 country code',
	},
};

export const locationPoint = locationPointField();

// URL and identification
export const canonicalID: TextField = {
	name: 'canonicalID',
	label: 'Canonical ID',
	type: 'text',
	unique: true,
	localized: true,
	index: true,
	admin: {
		description:
			'Canonical ID stored as a partial path for this location on weather.com',
	},
};

export const placeID: TextField = {
	name: 'placeID',
	label: 'Place ID',
	type: 'text',
	unique: true,
	index: true,
	admin: {
		description: 'Hash of geocode and location type from SUN',
	},
};

// Location type - only poi and airport for now
export const locationType: SelectField = {
	name: 'locationType',
	label: 'Location Type',
	type: 'select',
	required: true,
	index: true,
	options: [
		{ label: 'Point of Interest', value: 'poi' },
		{ label: 'Airport', value: 'airport' },
	],
	admin: {
		description: 'Type of location - currently supports POI and Airport',
	},
};
