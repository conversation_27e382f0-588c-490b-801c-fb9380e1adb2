# LocationPoint Field

A custom PayloadCMS field for handling geographic coordinates with robust validation and a user-friendly admin interface.

## Overview

The `locationPoint` field is designed to store and validate geographic coordinates in the wx-next location data store. It supports both simple array format `[longitude, latitude]` and GeoJSON Point format, with comprehensive validation to ensure data integrity.

## Features

- **Dual Format Support**: Accepts both `[lng, lat]` arrays and GeoJSON Point objects
- **Robust Validation**: Comprehensive coordinate validation with clear error messages
- **Custom Admin UI**: User-friendly coordinate input component with real-time validation
- **Type Safety**: Full TypeScript support with proper type definitions
- **Flexible Configuration**: Customizable field options and admin overrides

## Usage

### Basic Usage

```typescript
import { locationPointField } from './fields/locationPoint';

// Add to your collection fields
const MyCollection = {
  fields: [
    locationPointField(),
    // other fields...
  ],
};
```

### With Custom Configuration

```typescript
import { locationPointField } from './fields/locationPoint';

const MyCollection = {
  fields: [
    locationPointField({
      locationPointOverrides: {
        label: 'Custom Location Label',
        required: false,
        admin: {
          description: 'Enter the geographic coordinates for this location',
          position: 'sidebar',
        },
      },
    }),
  ],
};
```

## Data Formats

### Simple Array Format (Recommended)

```typescript
// [longitude, latitude]
[-85.7664, 33.8203] // JSU Stadium, Alabama
```

### GeoJSON Point Format

```typescript
{
  type: 'Point',
  coordinates: [-85.7664, 33.8203]
}
```

## Validation Rules

The field validates coordinates according to standard geographic constraints:

### Latitude Validation
- **Range**: -90 to 90 degrees
- **Examples**: 
  - ✅ `90` (North Pole)
  - ✅ `-90` (South Pole)
  - ❌ `91` (Invalid - exceeds range)

### Longitude Validation
- **Range**: -180 to 180 degrees
- **Examples**:
  - ✅ `180` (International Date Line)
  - ✅ `-180` (International Date Line)
  - ❌ `181` (Invalid - exceeds range)

### Additional Validation
- **Required**: Field is required by default
- **Numeric**: Coordinates must be valid numbers (not NaN or Infinity)
- **Format**: Must be either array format or valid GeoJSON Point

## Error Messages

The field provides clear, specific error messages:

- `"Location point is required"` - When field is empty and required
- `"Invalid location point format"` - When format doesn't match expected patterns
- `"Latitude must be between -90 and 90 degrees"` - When latitude is out of range
- `"Longitude must be between -180 and 180 degrees"` - When longitude is out of range
- `"Coordinates must be valid numbers"` - When coordinates contain NaN or invalid values

## Admin Interface

The field includes a custom admin component (`CoordinateInput`) that provides:

- **Separate Input Fields**: Individual inputs for longitude and latitude
- **Real-time Validation**: Immediate feedback on coordinate validity
- **Clear Labels**: Intuitive field labels and placeholders
- **Error Display**: Contextual error messages
- **Responsive Design**: Works well across different screen sizes

## API Reference

### `locationPointField(options?)`

Creates a locationPoint field configuration for PayloadCMS.

#### Parameters

- `options` (optional): Configuration object
  - `locationPointOverrides`: Partial field configuration to override defaults

#### Returns

PayloadCMS field configuration object with:
- `name: 'locationPoint'`
- `type: 'point'`
- `label: 'Location Point'`
- `required: true`
- Custom validation function
- Custom admin component

### Type Definitions

```typescript
type LocationPointValue = 
  | [number, number]  // [longitude, latitude]
  | {
      type: 'Point';
      coordinates: [number, number];
    };

interface LocationPointFieldOptions {
  locationPointOverrides?: Partial<PointField>;
}
```

## Implementation Details

### File Structure

```text
locationPoint/
├── README.md                    # This documentation
├── index.ts                     # Main field export
├── components/
│   └── CoordinateInput.tsx      # Custom admin component
└── __tests__/
    └── locationPoint.test.ts    # Comprehensive test suite
```

### Key Functions

#### `isPointValue(value: unknown): boolean`
Type guard function that validates if a value matches the expected coordinate formats.

#### `validateLocationPoint(value: unknown): true | string`
Main validation function that performs comprehensive coordinate validation and returns either `true` for valid coordinates or an error message string.

## Testing

The field includes comprehensive tests covering:

- **Valid Coordinates**: Various valid coordinate formats and edge cases
- **Invalid Coordinates**: All types of invalid inputs with appropriate error messages
- **Field Configuration**: Proper field setup and override functionality
- **Real-World Examples**: Tests with actual geographic coordinates

Run tests with:
```bash
pnpm --filter web test collections/Locations/fields/locationPoint/__tests__/locationPoint.test.ts
```

## Integration with Location Store

This field is specifically designed for the wx-next location data store and integrates with:

- **Location Collection**: Primary storage for location data
- **Canonical URL Generation**: Coordinates used for generating weather.com URLs
- **Vector Search**: Enables location-based content association
- **Weather API Integration**: Coordinates passed to weather data services

## Examples

### Real-World Coordinates

```typescript
// Famous locations with their coordinates
const examples = [
  { name: 'JSU Stadium (Alabama)', coords: [-85.7664, 33.8203] },
  { name: 'Eiffel Tower (Paris)', coords: [2.2944, 48.8584] },
  { name: 'Sydney Opera House', coords: [151.2153, -33.8568] },
  { name: 'Mount Everest', coords: [86.9250, 27.9881] },
  { name: 'Statue of Liberty', coords: [-74.0445, 40.6892] },
];
```

### Usage in Location Data Structure

```typescript
{
  "language": "en",
  "displayName": "JSU Stadium",
  "city": "Jacksonville",
  "adminDistrictCode": "AL",
  "adminDistrict": "Alabama",
  "country": "United States",
  "countryCode": "US",
  "locationPoint": [-85.7664, 33.8203], // [longitude, latitude] - This field
  "canonicalUrl": "some string",
  "placeID": "some uuid",
  "legacy": {
    "locID": "..."
  },
  "locationData": {
    "{locationType}": {...}
  }
  // ... other fields
}
```

## Contributing

When modifying this field:

1. **Update Tests**: Ensure all tests pass and add new tests for new functionality
2. **Update Documentation**: Keep this README current with any changes
3. **Validate Integration**: Test with the full location collection
4. **Check Admin UI**: Verify the admin interface works correctly

## Related Files

- `apps/web/collections/Locations/index.ts` - Main location collection
- `apps/web/collections/Locations/fields/index.ts` - Field exports
- Location Service API documentation (referenced in project brief)
