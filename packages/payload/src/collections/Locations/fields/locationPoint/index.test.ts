import { describe, it, expect } from 'vitest';
import { locationPointField } from './index';

describe('locationPoint field', () => {
	const field = locationPointField();
	const validateFn = field.validate as (value: unknown) => true | string;

	describe('validation function', () => {
		describe('valid coordinates', () => {
			it('should accept valid simple array format [lng, lat]', () => {
				expect(validateFn([-85.7664, 33.8203])).toBe(true);
			});

			it('should accept valid GeoJSON Point format', () => {
				const geoJsonPoint = {
					type: 'Point',
					coordinates: [-85.7664, 33.8203],
				};
				expect(validateFn(geoJsonPoint)).toBe(true);
			});

			it('should accept boundary latitude values', () => {
				expect(validateFn([0, 90])).toBe(true); // North pole
				expect(validateFn([0, -90])).toBe(true); // South pole
			});

			it('should accept boundary longitude values', () => {
				expect(validateFn([180, 0])).toBe(true); // International date line
				expect(validateFn([-180, 0])).toBe(true); // International date line
			});

			it('should accept zero coordinates', () => {
				expect(validateFn([0, 0])).toBe(true); // Null Island
			});
		});

		describe('invalid coordinates', () => {
			it('should reject null/undefined values', () => {
				expect(validateFn(null)).toBe('Location point is required');
				expect(validateFn(undefined)).toBe('Location point is required');
			});

			it('should reject empty values', () => {
				expect(validateFn('')).toBe('Location point is required');
				expect(validateFn([])).toBe('Invalid location point format');
			});

			it('should reject invalid array formats', () => {
				expect(validateFn([1])).toBe('Invalid location point format'); // Single value
				expect(validateFn([1, 2, 3])).toBe('Invalid location point format'); // Too many values
				expect(validateFn(['1', '2'])).toBe('Invalid location point format'); // String values
			});

			it('should reject invalid GeoJSON formats', () => {
				expect(validateFn({ type: 'Point' })).toBe(
					'Invalid location point format',
				); // Missing coordinates
				expect(validateFn({ coordinates: [1, 2] })).toBe(
					'Invalid location point format',
				); // Missing type
				expect(validateFn({ type: 'Polygon', coordinates: [1, 2] })).toBe(
					'Invalid location point format',
				); // Wrong type
			});

			it('should reject out-of-range latitude values', () => {
				expect(validateFn([0, 91])).toBe(
					'Latitude must be between -90 and 90 degrees',
				);
				expect(validateFn([0, -91])).toBe(
					'Latitude must be between -90 and 90 degrees',
				);
				expect(validateFn([0, 180])).toBe(
					'Latitude must be between -90 and 90 degrees',
				);
			});

			it('should reject out-of-range longitude values', () => {
				expect(validateFn([181, 0])).toBe(
					'Longitude must be between -180 and 180 degrees',
				);
				expect(validateFn([-181, 0])).toBe(
					'Longitude must be between -180 and 180 degrees',
				);
				expect(validateFn([360, 0])).toBe(
					'Longitude must be between -180 and 180 degrees',
				);
			});

			it('should reject NaN values', () => {
				expect(validateFn([NaN, 0])).toBe('Coordinates must be valid numbers');
				expect(validateFn([0, NaN])).toBe('Coordinates must be valid numbers');
				expect(validateFn([NaN, NaN])).toBe(
					'Coordinates must be valid numbers',
				);
			});

			it('should reject non-numeric values', () => {
				expect(validateFn([null, 0])).toBe('Invalid location point format');
				expect(validateFn([0, null])).toBe('Invalid location point format');
				expect(validateFn([undefined, 0])).toBe(
					'Invalid location point format',
				);
			});

			it('should reject invalid object types', () => {
				expect(validateFn({})).toBe('Invalid location point format');
				expect(validateFn('invalid')).toBe('Invalid location point format');
				expect(validateFn(123)).toBe('Invalid location point format');
				expect(validateFn(true)).toBe('Invalid location point format');
			});
		});

		describe('edge cases', () => {
			it('should handle floating point precision', () => {
				expect(validateFn([-85.7664123456789, 33.8203123456789])).toBe(true);
			});

			it('should handle very small numbers', () => {
				expect(validateFn([0.000001, 0.000001])).toBe(true);
			});

			it('should handle negative zero', () => {
				expect(validateFn([-0, -0])).toBe(true);
			});

			it('should handle Infinity values', () => {
				expect(validateFn([Infinity, 0])).toBe(
					'Longitude must be between -180 and 180 degrees',
				);
				expect(validateFn([0, Infinity])).toBe(
					'Latitude must be between -90 and 90 degrees',
				);
				expect(validateFn([-Infinity, 0])).toBe(
					'Longitude must be between -180 and 180 degrees',
				);
			});
		});
	});

	describe('field configuration', () => {
		it('should have correct field properties', () => {
			expect(field.name).toBe('locationPoint');
			expect(field.type).toBe('point');
			expect(field.label).toBe('Location Point');
			expect(field.required).toBe(true);
		});

		it('should have custom component configuration', () => {
			expect(field.admin?.components?.Field).toBeDefined();
		});

		it('should accept overrides', () => {
			const customField = locationPointField({
				locationPointOverrides: {
					label: 'Custom Location',
					required: false,
				},
			});

			expect(customField.label).toBe('Custom Location');
			expect(customField.required).toBe(false);
		});

		it('should merge admin overrides correctly', () => {
			const customField = locationPointField({
				locationPointOverrides: {
					admin: {
						description: 'Custom description',
						position: 'sidebar',
					},
				},
			});

			expect(customField.admin?.description).toBe('Custom description');
			expect(customField.admin?.position).toBe('sidebar');
		});
	});

	describe('real-world coordinate examples', () => {
		const realWorldCoordinates = [
			{ name: 'JSU Stadium (Alabama)', coords: [-85.7664, 33.8203] },
			{ name: 'Eiffel Tower (Paris)', coords: [2.2944, 48.8584] },
			{ name: 'Sydney Opera House', coords: [151.2153, -33.8568] },
			{ name: 'Mount Everest', coords: [86.925, 27.9881] },
			{ name: 'Statue of Liberty', coords: [-74.0445, 40.6892] },
		];

		it.each(realWorldCoordinates)(
			'should accept real-world coordinates for $name',
			({ coords }) => {
				expect(validateFn(coords)).toBe(true);
			},
		);
	});
});
