import { createGroup } from '@repo/payload/fields/utility';
import type { TextField } from 'payload';

const icaoCode: TextField = {
	name: 'icaoCode',
	type: 'text',
	label: 'ICAO Code',
	admin: {
		description: '4-digit airport code',
	},
};

const iataCode: TextField = {
	name: 'iataCode',
	type: 'text',
	label: 'IATA Code',
	admin: {
		description: '3-digit airport code',
	},
};

export const airport = createGroup([icaoCode, iataCode], 'airport', 'Airport', {
	admin: {
		condition: (data, _siblingData) => data.locationType === 'airport',
	},
});
