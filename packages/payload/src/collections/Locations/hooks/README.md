# Locations Collection Hooks

This directory contains PayloadCMS hooks for the Locations collection, specifically for generating canonical IDs for location records.

## Overview

The main functionality provided by these hooks is the automatic generation of canonical IDs for location records when they are created or updated in the CMS.

## Files

- `index.ts` - Main hook implementation and utility functions
- `index.test.ts` - Comprehensive test suite
- `README.md` - This documentation file

## Functions

### `generateCanonicalID(data, locale)`

Generates a canonical ID for a location based on its properties.

**Parameters:**

- `data` - Location data object containing location properties
- `locale` - Locale string (e.g., 'en-US', 'fr-FR')

**Returns:**

- String representing the canonical ID

**Format:**

```text
{city}_{adminDistrictCode}_{countryCode}_{locale}
```

**Example:**

```typescript
const canonicalID = generateCanonicalID({
  city: 'New York City',
  adminDistrictCode: 'NY',
  countryCode: 'US',
  displayName: 'New York City'
}, 'en-US');
// Returns: "new-york-city_ny_us_en"
```

**Key Features:**

- Converts text to lowercase and replaces spaces/special characters with hyphens
- Uses `displayName` as fallback when `city` is missing
- Uses `adminDistrict` as fallback when `adminDistrictCode` is missing
- Extracts first two letters from locale (e.g., 'en-US' → 'en')
- Handles missing or empty values gracefully

### `generateCanonicalIDHook`

PayloadCMS hook that automatically generates canonical IDs for location records.

**Trigger Conditions:**

- Collection: 'locations'
- Operations: 'create' and 'update'
- Requirement: `displayName` must be present and non-empty

**Behavior:**

- Automatically generates and sets the `canonicalID` field
- Overwrites existing canonical IDs to ensure consistency
- Uses the request locale for ID generation
- Preserves all other data in the record

## Usage

The hook is automatically registered with PayloadCMS and will run whenever location records are created or updated through the admin interface or API.

### Manual Usage

You can also use the `generateCanonicalID` function directly:

```typescript
import { generateCanonicalID } from './collections/Locations/hooks';

const locationData = {
  city: 'Paris',
  adminDistrictCode: '75',
  countryCode: 'FR',
  displayName: 'Paris'
};

const canonicalID = generateCanonicalID(locationData, 'fr-FR');
console.log(canonicalID); // "paris_75_fr_fr"
```

## String Manipulation Example

To get the first two letters from a locale string like 'en-US':

```typescript
const locale = 'en-US';
const firstTwoLetters = locale.substring(0, 2); // Returns 'en'
```

This is used internally to extract the language code from the full locale string.

## Testing

The hooks are thoroughly tested with 25 test cases covering:

- Basic functionality with all required fields
- Handling of missing optional fields
- Edge cases (empty strings, null values, special characters)
- Locale handling and extraction
- Real-world location examples
- Hook execution conditions
- Data preservation

Run tests with:

```bash
cd apps/web && pnpm test collections/Locations/hooks/__tests__/index.test.ts
```

## Error Handling

The implementation includes robust error handling:

- Gracefully handles missing or undefined values
- Provides fallback values for optional fields
- Maintains data integrity by preserving original record data
- Logs errors appropriately for debugging

## Dependencies

- PayloadCMS hook system
- `getUserLocale` utility function for locale handling
