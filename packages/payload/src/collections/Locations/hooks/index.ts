import type { Location } from '@repo/payload/payload-types';
import { formatSlug } from '../../../hooks/formatSlug';
import { CollectionBeforeChangeHook } from 'payload';
import { getLocale } from '@repo/payload/utils/getSupportedLocale';

export const generateCanonicalIDHook: CollectionBeforeChangeHook<
	Location
> = async ({ data, req, operation, collection }) => {
	if (
		(operation !== 'update' && operation !== 'create') ||
		collection.slug !== 'locations'
	)
		return data;

	const locationData = data;

	if (locationData.displayName) {
		const fullLocale = getLocale(req.locale);
		const locale = fullLocale.substring(0, 2);

		const canonicalID = generateCanonicalID(data, locale);

		// canonicalID is a simple string field, not localized
		data.canonicalID = canonicalID;
		return data;
	}

	return data;
};

/**
 * Generate canonical ID using the pattern: ${language-code}/${country-code}/${state}/${locationType}/${city}/${displayName}
 * All components are URL-safe (lowercase, spaces replaced with hyphens, special chars removed)
 */
export const generateCanonicalID = (
	data: Partial<Location>,
	locale: string,
): string => {
	const displayName = formatSlug(String(data.displayName || ''));
	// Extract first 2 characters from locale (e.g., 'en-US' -> 'en')
	const languageCode = (locale || 'en').substring(0, 2);
	const countryCode = formatSlug(String(data.countryCode || ''));
	const state = formatSlug(
		String(data.adminDistrictCode || data.adminDistrict || ''),
	);
	const locationTypeValue = formatSlug(String(data.locationType || ''));
	const cityValue = formatSlug(String(data.city || data.displayName || ''));

	// Filter out empty components
	const components = [
		languageCode,
		countryCode,
		state,
		locationTypeValue,
		cityValue,
		displayName,
	].filter(Boolean);

	return components.join('/');
};
