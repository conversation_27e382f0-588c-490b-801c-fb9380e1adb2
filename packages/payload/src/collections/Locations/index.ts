import type { CollectionConfig } from 'payload';
import {
	displayName,
	city,
	adminDistrictCode,
	adminDistrict,
	country,
	countryCode,
	locationPoint,
	canonicalID,
	placeID,
	legacyLocID,
	locationType,
} from './fields';
import { locationData } from './fields/locationData/locationData';
import { createCollectionAccess } from '../Roles/utils/hasPermission';
import { generateCanonicalIDHook } from './hooks';

export const Locations: CollectionConfig = {
	slug: 'locations',
	labels: {
		singular: 'Location',
		plural: 'Locations',
	},
	admin: {
		useAsTitle: 'displayName',
		defaultColumns: [
			'displayName',
			'city',
			'adminDistrict',
			'country',
			'locationType',
		],
		description: 'Location data store, for canonical and search',
	},
	access: createCollectionAccess('locations'),
	hooks: {
		beforeChange: [generateCanonicalIDHook],
	},
	fields: [
		displayName,
		locationType,
		locationData,
		canonicalID,
		placeID,
		legacyLocID,
		city,
		adminDistrictCode,
		adminDistrict,
		country,
		countryCode,
		locationPoint,
	],
};
