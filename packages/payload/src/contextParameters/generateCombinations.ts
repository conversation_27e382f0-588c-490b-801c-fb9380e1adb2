import crypto from 'crypto';
import type { ContextParameter } from '@repo/payload/payload-types';
import type { ContextParameters } from './extractParameters';

/**
 * Generates all possible combinations of the provided parameters
 *
 * @param params The context parameters
 * @returns Array of parameter combinations
 */
export const generateParameterCombinations = (
	params: ContextParameters,
): { [k: string]: unknown }[] => {
	if (!params) return [];

	const isOk =
		params !== null && typeof params === 'object' && !Array.isArray(params);
	if (!isOk) return [];

	const keys = Object.keys(params).filter(
		(key) => params[key as keyof typeof params] !== undefined,
	);
	const combinations: { [k: string]: unknown }[] = [];

	// Generate all possible combinations using binary counting
	// For n parameters, there are 2^n - 1 possible combinations (excluding empty set)
	const n = keys.length;
	const combinationCount = Math.pow(2, n) - 1;

	for (let i = 1; i <= combinationCount; i++) {
		const combination: { [k: string]: unknown } = {};

		// Check which bits are set in the binary representation of i
		for (let j = 0; j < n; j++) {
			if ((i & (1 << j)) !== 0) {
				// This key has not yet been accounted for in this iteration of combinations
				const key = keys[j] as keyof typeof params;
				if (key && params[key] !== undefined) {
					combination[key] = params[key];
				}
			}
		}

		combinations.push(combination);
	}

	// Sort combinations by number of parameters (descending)
	// This ensures that combinations with more parameters are checked first
	combinations.sort((a, b) => Object.keys(b).length - Object.keys(a).length);

	return combinations;
};

/**
 * Expands parameters with array values into all possible combinations
 *
 * @param params Parameters with potential array values
 * @returns Array of parameter combinations with single values
 */
export const expandParameterArrays = (
	params: Record<string, unknown>,
): Record<string, unknown>[] => {
	const keys = Object.keys(params);
	const result: Record<string, unknown>[] = [{}];

	for (const key of keys) {
		const value = params[key];
		const newResult: Record<string, unknown>[] = [];

		if (Array.isArray(value)) {
			// use consistent order of items
			value.sort();
			// For array values, create a combination for each value
			for (const item of value) {
				for (const existing of result) {
					newResult.push({ ...existing, [key]: item });
				}
			}
		} else {
			// For single values, just add to all existing combinations
			for (const existing of result) {
				newResult.push({ ...existing, [key]: value });
			}
		}

		result.length = 0;
		result.push(...newResult);
	}

	return result;
};

/**
 * Creates a hash for a parameter combination
 *
 * @param params The parameter combination
 * @returns Array of hash strings (multiple if arrays are present)
 */
export const hashParameters = (
	params: ContextParameter['parameters'],
): string => {
	if (!params) return '';

	// sort and format params string to be consistent and deterministic
	const keys = Object.keys(params).sort();
	const paramString = keys
		.map((key) => {
			// Use type assertion to safely access the property
			const value = params[key as keyof typeof params];
			return `${key}:${value}`;
		})
		.join('|');
	const hash = crypto.createHash('sha256').update(paramString).digest('hex');
	return hash;
};

export interface GeneratedParameterHash {
	combination: { [k: string]: unknown };
	hash: string;
}

/**
 * Generates hashes for all combinations of the provided parameters
 *
 * @param params The context parameters
 * @returns Array of objects containing the combination and its hash
 */
export const generateParameterHashes = (
	params: ContextParameters,
): GeneratedParameterHash[] => {
	const combinations = generateParameterCombinations(params);

	return combinations.map((combination) => ({
		combination,
		hash: hashParameters(combination),
	}));
};

/**
 * Checks if a parameter value matches another value, supporting arrays for OR relationships
 *
 * @param paramValue Value from stored parameters (may be array for OR)
 * @param requestedValue Value from requested parameters
 * @returns True if values match
 */
export const parameterValueMatches = (
	paramValue: unknown,
	requestedValue: unknown,
): boolean => {
	// If the parameter value is an array, check if any value matches (OR relationship)
	if (Array.isArray(paramValue)) {
		return paramValue.some((value) => value === requestedValue);
	}

	// Otherwise, do a direct comparison
	return paramValue === requestedValue;
};
