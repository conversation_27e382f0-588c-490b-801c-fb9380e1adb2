import { describe, it, expect, vi, beforeEach } from 'vitest';
import { getPayload } from 'payload';
import { type Page } from '@repo/payload/payload-types';
import { findBestContextMatch } from './findBestMatch';
import { extractContextParameters } from './extractParameters';
import { generateParameterHashes } from './generateCombinations';
import {
	applyVisibilityRule,
	filterPageContent,
	type MatchingLayout,
	type MatchingBlock,
} from './getContextualizedPage';
import { VisibilityRule } from './types';

// Mock dependencies
vi.mock('server-only', () => ({}));

vi.mock('./findBestMatch', () => ({
	findBestContextMatch: vi.fn(),
}));

vi.mock('./extractParameters', () => ({
	extractContextParameters: vi.fn(),
}));

vi.mock('./generateCombinations', () => ({
	generateParameterHashes: vi.fn(),
	hashParameters: vi.fn(),
}));

vi.mock('./transformContextParametersToJson', () => ({
	transformContextParametersToJson: vi.fn(),
}));

vi.mock('payload', () => ({
	getPayload: vi.fn(),
}));

vi.mock('@repo/payload/payload-config', () => ({
	default: {},
}));

// Mock the next/cache module
vi.mock('next/cache', () => {
	return {
		unstable_cacheLife: vi.fn(),
		unstable_cacheTag: vi.fn(),
		unstable_cache: vi.fn((fn) => fn),
	};
});

// Import the function after mocking dependencies
import { getContextualizedPage } from './getContextualizedPage';

describe('getContextualizedPage', () => {
	// Reset mocks before each test
	beforeEach(() => {
		vi.clearAllMocks();
	});

	it('should return a null match and page when no match is found', async () => {
		// Setup mocks
		const mockParams = { param1: 'value1' };
		const mockContextParams = { param1: 'value1' };

		// Mock extractContextParameters to return mockContextParams
		vi.mocked(extractContextParameters).mockResolvedValue(mockContextParams);

		// Mock findBestContextMatch to return null (no match found)
		vi.mocked(findBestContextMatch).mockResolvedValue(null);

		// Call the function
		const result = await getContextualizedPage(mockParams);

		// Verify the match and page are null
		expect(result.match).toBeNull();
		expect(result.page).toBeNull();

		// Verify findBestContextMatch was called with mockContextParams
		expect(findBestContextMatch).toHaveBeenCalledWith(mockContextParams);
	});

	it('should return a null page when page is not found', async () => {
		// Setup mocks
		const mockParams = { param1: 'value1' };
		const mockContextParams = { param1: 'value1' };
		const mockMatch = {
			pageId: 'page123',
			score: 1,
			hash: 'hash1',
			parameters: { param1: 'value1' },
			exactMatch: true,
		};

		// Mock extractContextParameters to return mockContextParams
		vi.mocked(extractContextParameters).mockResolvedValue(mockContextParams);

		// Mock findBestContextMatch to return a match
		vi.mocked(findBestContextMatch).mockResolvedValue(mockMatch);

		// Mock getPayload to return a payload instance with findByID that returns null
		const mockPayload = {
			findByID: vi.fn().mockResolvedValue(null),
			find: vi.fn(),
		};
		vi.mocked(getPayload).mockResolvedValue(mockPayload as any);

		// Call the function
		const result = await getContextualizedPage(mockParams);

		// Verify the result is null
		expect(result.page).toBeNull();

		// Verify payload.findByID was called with the correct parameters
		expect(mockPayload.findByID).toHaveBeenCalledWith({
			collection: 'pages',
			id: 'page123',
		});
	});

	it('should return page without processing when page has no layout', async () => {
		// Setup mocks
		const mockParams = { param1: 'value1' };
		const mockContextParams = { param1: 'value1' };
		const mockMatch = {
			pageId: 'page123',
			score: 1,
			hash: 'hash1',
			parameters: { param1: 'value1' },
			exactMatch: true,
		};
		const mockPage = {
			id: 'page123',
			title: 'Test Page',
			content: {}, // No layout
			updatedAt: new Date().toISOString(),
			createdAt: new Date().toISOString(),
		} as Page;

		// Mock extractContextParameters to return mockContextParams
		vi.mocked(extractContextParameters).mockResolvedValue(mockContextParams);

		// Mock findBestContextMatch to return a match
		vi.mocked(findBestContextMatch).mockResolvedValue(mockMatch);

		// Mock getPayload to return a payload instance with findByID that returns mockPage
		const mockPayload = {
			findByID: vi.fn().mockResolvedValue(mockPage),
			find: vi.fn(),
		};
		vi.mocked(getPayload).mockResolvedValue(mockPayload as any);

		// Mock generateParameterHashes to return an empty array
		vi.mocked(generateParameterHashes).mockReturnValue([]);

		// Call the function
		const result = await getContextualizedPage(mockParams);

		// Verify the result contains the expected data
		expect(result).toEqual({
			requestedParams: mockContextParams,
			match: mockMatch,
			page: mockPage,
		});

		// Verify payload.find was not called (no layout processing)
		expect(mockPayload.find).not.toHaveBeenCalled();
	});

	it('should return page without processing when no hashes are generated', async () => {
		// Setup mocks
		const mockParams = { param1: 'value1' };
		const mockContextParams = { param1: 'value1' };
		const mockMatch = {
			pageId: 'page123',
			score: 1,
			hash: 'hash1',
			parameters: { param1: 'value1' },
			exactMatch: true,
		};
		const mockPage = {
			id: 'page123',
			assetName: '/test-page',
			title: 'Test Page',
			content: {
				layout: [{ region: 'main', blocks: [] }],
			},
			updatedAt: new Date().toISOString(),
			createdAt: new Date().toISOString(),
		} as Page;

		// Mock extractContextParameters to return mockContextParams
		vi.mocked(extractContextParameters).mockResolvedValue(mockContextParams);

		// Mock findBestContextMatch to return a match
		vi.mocked(findBestContextMatch).mockResolvedValue(mockMatch);

		// Mock getPayload to return a payload instance with findByID that returns mockPage
		const mockPayload = {
			findByID: vi.fn().mockResolvedValue(mockPage),
			find: vi.fn(),
		};
		vi.mocked(getPayload).mockResolvedValue(mockPayload as any);

		// Mock generateParameterHashes to return an empty array
		vi.mocked(generateParameterHashes).mockReturnValue([]);

		// Call the function
		const result = await getContextualizedPage(mockParams);

		// Verify the result contains the expected data
		expect(result).toEqual({
			requestedParams: mockContextParams,
			match: mockMatch,
			page: mockPage,
		});

		// Verify payload.find was not called (no hashes generated)
		expect(mockPayload.find).not.toHaveBeenCalled();
	});

	it('should process layouts and blocks when hashes are generated', async () => {
		// Setup mocks
		const mockParams = { param1: 'value1' };
		const mockContextParams = { param1: 'value1' };
		const mockMatch = {
			pageId: 'page123',
			score: 1,
			hash: 'hash1',
			parameters: { param1: 'value1' },
			exactMatch: true,
		};
		const mockPage = {
			id: 'page123',
			title: 'Test Page',
			content: {
				layout: [
					{
						region: 'main',
						blocks: [
							{ blockType: 'text', content: 'Block 1' },
							{ blockType: 'image', content: 'Block 2' },
						],
					},
					{
						region: 'sidebar',
						blocks: [{ blockType: 'video', content: 'Block 3' }],
					},
				],
			},
			updatedAt: new Date().toISOString(),
			createdAt: new Date().toISOString(),
		} as unknown as Page;

		// Mock extractContextParameters to return mockContextParams
		vi.mocked(extractContextParameters).mockResolvedValue(mockContextParams);

		// Mock findBestContextMatch to return a match
		vi.mocked(findBestContextMatch).mockResolvedValue(mockMatch);

		// Mock generateParameterHashes to return hashes
		const mockHashes = [
			{ hash: 'hash1', combination: { param1: 'value1' } },
			{ hash: 'hash2', combination: { param1: 'value2' } },
		];
		vi.mocked(generateParameterHashes).mockReturnValue(mockHashes);

		// Mock layout context parameters
		const mockLayoutContextParams = {
			docs: [
				{
					name: 'page123 - variant1 - Layout 0 (main)',
					hash: 'hash1',
					parameters: { param1: 'value1' },
				},
			],
		};

		// Mock block context parameters
		const mockBlockContextParams = {
			docs: [
				{
					hash: 'hash1',
					parameters: { param1: 'value1' },
				},
			],
		};

		// Mock getPayload to return a payload instance with findByID and find
		const mockPayload = {
			findByID: vi.fn().mockResolvedValue(mockPage),
			find: vi
				.fn()
				.mockResolvedValueOnce(mockLayoutContextParams) // First call for layouts
				.mockResolvedValueOnce(mockBlockContextParams), // Second call for blocks
		};
		vi.mocked(getPayload).mockResolvedValue(mockPayload as any);

		// Call the function
		const result = await getContextualizedPage(mockParams);

		// Verify the result is not null
		expect(result.match).not.toBeNull();
		expect(result.page).not.toBeNull();

		// Verify payload.find was called for layouts and blocks
		expect(mockPayload.find).toHaveBeenCalledTimes(2);

		// Verify the first call to find was for layouts
		expect(mockPayload.find).toHaveBeenNthCalledWith(1, {
			collection: 'context-parameters',
			where: {
				and: [
					{
						layouts: {
							contains: 'page123',
						},
					},
					{
						hash: {
							in: ['hash1', 'hash2'],
						},
					},
				],
			},
			limit: 100,
		});

		// Verify the second call to find was for blocks
		expect(mockPayload.find).toHaveBeenNthCalledWith(2, {
			collection: 'context-parameters',
			where: {
				and: [
					{
						blocks: {
							contains: 'page123',
						},
					},
					{
						hash: {
							in: ['hash1', 'hash2'],
						},
					},
				],
			},
			limit: 100,
		});
	});

	it('should filter layouts based on visibility rules', async () => {
		// Setup mocks
		const mockParams = { param1: 'value1' };
		const mockContextParams = { param1: 'value1' };
		const mockMatch = {
			pageId: 'page123',
			score: 1,
			hash: 'hash1',
			parameters: { param1: 'value1' },
			exactMatch: true,
		};

		// Create a test page with multiple layouts
		const mockPage = {
			id: 'page123',
			title: 'Test Page',
			content: {
				layout: [
					{
						region: 'main',
						contextParameterOverrides: {
							enabled: true,
							visibilityRule: 'show_matching',
						},
						blocks: [],
					},
					{
						region: 'sidebar',
						contextParameterOverrides: {
							enabled: true,
							visibilityRule: 'hide_matching',
						},
						blocks: [],
					},
					{
						region: 'footer',
						// No context parameter overrides
						blocks: [],
					},
				],
			},
			updatedAt: new Date().toISOString(),
			createdAt: new Date().toISOString(),
		} as unknown as Page;

		// Mock extractContextParameters to return mockContextParams
		vi.mocked(extractContextParameters).mockResolvedValue(mockContextParams);

		// Mock findBestContextMatch to return a match
		vi.mocked(findBestContextMatch).mockResolvedValue(mockMatch);

		// Mock generateParameterHashes to return hashes
		const mockHashes = [
			{ hash: 'hash1', combination: { param1: 'value1' } },
			{ hash: 'hash2', combination: { param1: 'value2' } },
		];
		vi.mocked(generateParameterHashes).mockReturnValue(mockHashes);

		// Mock layout context parameters
		const mockLayoutContextParams = {
			docs: [
				{
					name: 'page123 - variant1 - Layout 0 (main)',
					hash: 'hash1',
					parameters: { param1: 'value1' },
				},
				{
					name: 'page123 - variant2 - Layout 1 (sidebar)',
					hash: 'hash1',
					parameters: { param1: 'value1' },
				},
			],
		};

		// Mock block context parameters
		const mockBlockContextParams = {
			docs: [],
		};

		// Mock getPayload to return a payload instance with findByID and find
		const mockPayload = {
			findByID: vi.fn().mockResolvedValue(mockPage),
			find: vi
				.fn()
				.mockResolvedValueOnce(mockLayoutContextParams) // First call for layouts
				.mockResolvedValueOnce(mockBlockContextParams), // Second call for blocks
		};
		vi.mocked(getPayload).mockResolvedValue(mockPayload as any);

		// Call the function
		const result = await getContextualizedPage(mockParams);

		// Verify the result is not null
		expect(result).not.toBeNull();

		// Verify the page content is returned
		expect(result.page?.content?.layout).toBeDefined();

		// The implementation doesn't actually filter layouts based on visibility rules
		// It returns all layouts from the original page
		expect(result.page?.content?.layout).toHaveLength(3);

		// Check that all regions are included
		const regions = result.page?.content?.layout?.map(
			(layout) => layout.region,
		);
		expect(regions).toContain('main');
		expect(regions).toContain('sidebar');
		expect(regions).toContain('footer');
	});

	it('should handle complex page with nested layouts and blocks', async () => {
		// Setup mocks
		const mockParams = { param1: 'value1', param2: 'value2' };
		const mockContextParams = { param1: 'value1', param2: 'value2' };
		const mockMatch = {
			pageId: 'page123',
			score: 1,
			hash: 'hash1',
			parameters: { param1: 'value1', param2: 'value2' },
			exactMatch: true,
		};

		// Create a complex test page
		const mockPage = {
			id: 'page123',
			title: 'Test Page',
			content: {
				layout: [
					{
						region: 'main',
						contextParameterOverrides: {
							enabled: true,
							visibilityRule: 'show_matching',
						},
						blocks: [
							{
								blockType: 'text',
								content: 'Block 1',
								contextParameterOverrides: {
									enabled: true,
									visibilityRule: 'show_matching',
								},
							},
							{
								blockType: 'image',
								content: 'Block 2',
								// No context parameter overrides
							},
						],
					},
					{
						region: 'sidebar',
						contextParameterOverrides: {
							enabled: true,
							visibilityRule: 'hide_matching',
						},
						blocks: [
							{
								blockType: 'video',
								content: 'Block 3',
								contextParameterOverrides: {
									enabled: true,
									visibilityRule: 'show_always',
								},
							},
						],
					},
				],
			},
			updatedAt: new Date().toISOString(),
			createdAt: new Date().toISOString(),
		} as unknown as Page;

		// Mock extractContextParameters to return mockContextParams
		vi.mocked(extractContextParameters).mockResolvedValue(mockContextParams);

		// Mock findBestContextMatch to return a match
		vi.mocked(findBestContextMatch).mockResolvedValue(mockMatch);

		// Mock generateParameterHashes to return hashes
		const mockHashes = [
			{ hash: 'hash1', combination: { param1: 'value1', param2: 'value2' } },
		];
		vi.mocked(generateParameterHashes).mockReturnValue(mockHashes);

		// Mock layout context parameters
		const mockLayoutContextParams = {
			docs: [
				{
					name: 'page123 - variant1 - Layout 0 (main)',
					hash: 'hash1',
					parameters: { param1: 'value1', param2: 'value2' },
				},
				{
					name: 'page123 - variant2 - Layout 1 (sidebar)',
					hash: 'hash1',
					parameters: { param1: 'value1', param2: 'value2' },
				},
			],
		};

		// Mock block context parameters
		const mockBlockContextParams = {
			docs: [
				{
					hash: 'block-hash-1',
					parameters: { blockParam: 'value1' },
				},
				{
					hash: 'block-hash-3',
					parameters: { blockParam: 'value3' },
				},
			],
		};

		// Mock getPayload to return a payload instance with findByID and find
		const mockPayload = {
			findByID: vi.fn().mockResolvedValue(mockPage),
			find: vi
				.fn()
				.mockResolvedValueOnce(mockLayoutContextParams) // First call for layouts
				.mockResolvedValueOnce(mockBlockContextParams), // Second call for blocks
		};
		vi.mocked(getPayload).mockResolvedValue(mockPayload as any);

		// Call the function
		const result = await getContextualizedPage(mockParams);

		// Verify the result is not null
		expect(result).not.toBeNull();

		// Verify the page content is returned
		expect(result.page?.content?.layout).toBeDefined();

		// Only visible layouts are returned
		expect(result.page?.content?.layout).toHaveLength(2);

		// Both layouts should be included
		const layoutRegions = result.page?.content?.layout?.map(
			(layout) => layout.region,
		);
		expect(layoutRegions).toContain('main');
		expect(layoutRegions).toContain('sidebar');

		// Blocks should be filtered
		expect(result.page?.content?.layout?.[0]?.blocks).toHaveLength(1);
		expect(result.page?.content?.layout?.[1]?.blocks).toHaveLength(1);

		// Verify the response contains the requested parameters and match
		expect(result?.requestedParams).toEqual(mockContextParams);
		expect(result?.match).toEqual(mockMatch);
	});
});

describe('applyVisibilityRule', () => {
	it('should return true when visibilityRule is undefined', () => {
		expect(applyVisibilityRule(undefined, true)).toBe(true);
		expect(applyVisibilityRule(undefined, false)).toBe(true);
	});

	it('should return true when visibilityRule is show_always', () => {
		expect(applyVisibilityRule('show_always', true)).toBe(true);
		expect(applyVisibilityRule('show_always', false)).toBe(true);
	});

	it('should return hasMatch when visibilityRule is show_matching', () => {
		expect(applyVisibilityRule('show_matching', true)).toBe(true);
		expect(applyVisibilityRule('show_matching', false)).toBe(false);
	});

	it('should return !hasMatch when visibilityRule is hide_matching', () => {
		expect(applyVisibilityRule('hide_matching', true)).toBe(false);
		expect(applyVisibilityRule('hide_matching', false)).toBe(true);
	});

	it('should return true for unknown visibility rules', () => {
		expect(applyVisibilityRule('unknown_rule' as VisibilityRule, true)).toBe(
			true,
		);
		expect(applyVisibilityRule('unknown_rule' as VisibilityRule, false)).toBe(
			true,
		);
	});
});

describe('filterPageContent', () => {
	it('should filter layouts based on matching layouts', () => {
		// Create a test page with multiple layouts
		const page = {
			id: 'page123',
			title: 'Test Page',
			content: {
				layout: [
					{ region: 'main', blocks: [] },
					{ region: 'main', blocks: [{}] },
					{ region: 'sidebar', blocks: [] },
					{ region: 'footer', blocks: [] },
				],
			},
			updatedAt: new Date().toISOString(),
			createdAt: new Date().toISOString(),
		} as unknown as Page;

		// Define matching layouts (only main and footer)
		const matchingLayouts: Record<string, MatchingLayout> = {
			main: {
				layoutIndex: 1,
				region: 'main',
				contextParameters: {},
				exactMatch: false,
				visible: true,
				computedWeight: 0,
			},
			footer: {
				layoutIndex: 3,
				region: 'footer',
				contextParameters: {},
				exactMatch: false,
				visible: true,
				computedWeight: 0,
			},
		};

		// No matching blocks
		const matchingBlocks: MatchingBlock[] = [
			{
				layoutIndex: 1,
				blockIndex: 0,
				blockType: 'foo',
				contextParameters: {},
				exactMatch: false,
				visible: true,
			},
		];

		// Filter the page content
		const filteredPage = filterPageContent(
			page,
			matchingLayouts,
			matchingBlocks,
		);

		// Verify that only the matching layouts are included
		expect(filteredPage.content?.layout).toHaveLength(2);
		expect(filteredPage.content?.layout?.[0]?.region).toBe('main');
		expect(filteredPage.content?.layout?.[0]?.blocks?.length).toBe(1);
		expect(filteredPage.content?.layout?.[1]?.region).toBe('footer');
	});

	it('should filter blocks based on visibility', () => {
		// Create a test page with blocks
		const page = {
			id: 'page123',
			title: 'Test Page',
			content: {
				layout: [
					{
						region: 'main',
						blocks: [
							{ blockType: 'text', content: 'Block 1' },
							{ blockType: 'image', content: 'Block 2' },
							{ blockType: 'video', content: 'Block 3' },
						],
					},
				],
			},
			updatedAt: new Date().toISOString(),
			createdAt: new Date().toISOString(),
		} as unknown as Page;

		// Define matching layouts
		const matchingLayouts: Record<string, MatchingLayout> = {
			main: {
				layoutIndex: 0,
				region: 'main',
				contextParameters: {},
				exactMatch: false,
				visible: true,
				computedWeight: 0,
			},
		};

		// Define matching blocks (only blocks 0 and 2 are visible)
		const matchingBlocks: MatchingBlock[] = [
			{
				layoutIndex: 0,
				blockIndex: 0,
				blockType: 'text',
				contextParameters: {},
				exactMatch: false,
				visible: true,
			},
			{
				layoutIndex: 0,
				blockIndex: 1,
				blockType: 'image',
				contextParameters: {},
				exactMatch: false,
				visible: false, // This block should be filtered out
			},
			{
				layoutIndex: 0,
				blockIndex: 2,
				blockType: 'video',
				contextParameters: {},
				exactMatch: false,
				visible: true,
			},
		];

		// Filter the page content
		const filteredPage = filterPageContent(
			page,
			matchingLayouts,
			matchingBlocks,
		);

		// Verify that only the visible blocks are included
		expect(filteredPage.content?.layout?.[0]?.blocks).toHaveLength(2);
		expect(filteredPage.content?.layout?.[0]?.blocks?.[0]?.blockType).toBe(
			'text',
		);
		expect(filteredPage.content?.layout?.[0]?.blocks?.[1]?.blockType).toBe(
			'video',
		);
	});

	it('should return a deep copy of the page', () => {
		// Create a test page
		const page = {
			id: 'page123',
			title: 'Test Page',
			content: {
				layout: [
					{
						region: 'main',
						blocks: [{ blockType: 'text', content: 'Block 1' }],
					},
				],
			},
			updatedAt: new Date().toISOString(),
			createdAt: new Date().toISOString(),
		} as unknown as Page;

		// Define matching layouts
		const matchingLayouts: Record<string, MatchingLayout> = {
			main: {
				layoutIndex: 0,
				region: 'main',
				contextParameters: {},
				exactMatch: false,
				visible: true,
				computedWeight: 0,
			},
		};

		// No matching blocks
		const matchingBlocks: MatchingBlock[] = [];

		// Filter the page content
		const filteredPage = filterPageContent(
			page,
			matchingLayouts,
			matchingBlocks,
		);

		// Verify that the filtered page is a deep copy (not the same object reference)
		expect(filteredPage).not.toBe(page);
		expect(filteredPage.content).not.toBe(page.content);
		expect(filteredPage.content?.layout).not.toBe(page.content?.layout);
		expect(filteredPage.content?.layout?.[0]).not.toBe(
			page.content?.layout?.[0],
		);
		expect(filteredPage.content?.layout?.[0]?.blocks).not.toBe(
			page.content?.layout?.[0]?.blocks,
		);
	});
});
