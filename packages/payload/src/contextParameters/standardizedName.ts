/**
 * Generates a standardized name for a context parameter based on its parameters.
 * The name follows the format: 'nameOfParameter:valueOfParameter,nameOfAnotherParameter:valueOfAnotherParameter'
 * Parameters are sorted alphabetically by name.
 *
 * @param parameters The parameters object to generate a name from
 * @returns A standardized name string
 */
export function generateStandardizedName(
	parameters: Record<string, unknown>,
): string {
	// Get all parameter entries
	const entries = Object.entries(parameters);

	// Find the pageKey entry if it exists
	const pageKeyIndex = entries.findIndex(([name]) => name === 'pageKey');
	const hasPageKey = pageKeyIndex !== -1;

	// If pageKey exists, remove it from the entries array
	const pageKeyEntry = hasPageKey ? entries.splice(pageKeyIndex, 1)[0] : null;

	// Sort the remaining entries alphabetically by name
	const sortedEntries = entries.sort(([nameA], [nameB]) => {
		return nameA.localeCompare(nameB);
	});

	// If pageKey exists, add it back at the beginning of the array
	if (hasPageKey && pageKeyEntry) {
		sortedEntries.unshift(pageKeyEntry);
	}

	// Format each entry as 'name:value' and join with commas
	const formattedEntries = sortedEntries.map(([name, value]) => {
		// Handle different value types
		let formattedValue: string;

		if (value === null || value === undefined) {
			formattedValue = 'null';
		} else if (typeof value === 'object') {
			// For objects, use JSON.stringify to create a consistent string representation
			formattedValue = JSON.stringify(value);
		} else {
			formattedValue = String(value);
		}

		return `${name}:${formattedValue}`;
	});

	return formattedEntries.join(',');
}
