import { describe, it, expect, vi, beforeEach } from 'vitest';
import { findBestContextMatch } from './findBestMatch';
import * as payload from 'payload';
import { hashParameters } from './generateCombinations';
import { ContextParameters } from './extractParameters';

// Mock the payload module
vi.mock('payload', async (importOriginal) => {
	const actual: Record<string, unknown> = await importOriginal();
	return {
		...actual,
		getPayload: vi.fn(),
	};
});

// Mock the @repo/payload/payload-config module
vi.mock('@repo/payload/payload-config', () => {
	return {
		default: {},
	};
});

// Mock the next/cache module
vi.mock('next/cache', () => {
	return {
		unstable_cacheLife: vi.fn(),
		unstable_cacheTag: vi.fn(),
		unstable_cache: vi.fn((fn) => fn),
	};
});

describe('findBestContextMatch', () => {
	// Test data
	const testParams: ContextParameters = {
		deviceClass: 'desktop',
		weatherMode: 'severe',
	};

	// Generate real hashes for our test parameters
	const exactMatchHash = hashParameters(testParams);
	const partialHash1 = hashParameters({ deviceClass: 'desktop' });
	const partialHash2 = hashParameters({ weatherMode: 'active' });

	// Create test data with real hashes
	const exactMatch = {
		id: 'exact-match-id',
		hash: exactMatchHash,
		parameters: { deviceClass: 'desktop', weatherMode: 'active' },
		page: 'exact-page-id',
		weight: 10,
	};

	const partialMatch1 = {
		id: 'partial-match-1-id',
		hash: partialHash1,
		parameters: { deviceClass: 'desktop' },
		page: 'partial-page-1-id',
		weight: 5,
	};

	const partialMatch2 = {
		id: 'partial-match-2-id',
		hash: partialHash2,
		parameters: { weatherMode: 'active' },
		page: 'partial-page-2-id',
		weight: 8,
	};

	// Mock payload object with find method
	const mockPayload = {
		find: vi.fn(),
	};

	beforeEach(() => {
		// Reset mocks before each test
		vi.resetAllMocks();

		// Setup default mock implementations with proper type assertion
		// We need to use unknown first to avoid direct any usage
		vi.mocked(payload.getPayload).mockResolvedValue(
			mockPayload as unknown as Awaited<ReturnType<typeof payload.getPayload>>,
		);
	});

	it('should return an exact match when one exists', async () => {
		// Setup mock to return an exact match
		mockPayload.find.mockResolvedValueOnce({
			docs: [exactMatch],
			totalDocs: 1,
			limit: 100,
			totalPages: 1,
			page: 1,
			pagingCounter: 1,
			hasPrevPage: false,
			hasNextPage: false,
			prevPage: null,
			nextPage: null,
		});

		const result = await findBestContextMatch(testParams);

		// Verify the payload.find was called with a collection and where clause
		expect(mockPayload.find).toHaveBeenCalledWith(
			expect.objectContaining({
				collection: 'context-parameters',
				where: expect.objectContaining({
					and: expect.arrayContaining([
						expect.objectContaining({
							hash: expect.objectContaining({
								in: expect.arrayContaining([exactMatchHash]),
							}),
						}),
						expect.objectContaining({
							page: expect.objectContaining({
								exists: true,
							}),
						}),
					]),
				}),
				limit: 100,
			}),
		);

		// Verify the result is the exact match
		expect(result).toEqual({
			pageId: 'exact-page-id',
			hash: exactMatchHash,
			parameters: { deviceClass: 'desktop', weatherMode: 'active' },
			exactMatch: true,
		});
	});

	it('should return the best partial match when no exact match exists', async () => {
		// Setup mock to return only partial matches
		mockPayload.find.mockResolvedValueOnce({
			docs: [partialMatch1, partialMatch2],
			totalDocs: 2,
			limit: 100,
			totalPages: 1,
			page: 1,
			pagingCounter: 1,
			hasPrevPage: false,
			hasNextPage: false,
			prevPage: null,
			nextPage: null,
		});

		const result = await findBestContextMatch(testParams);

		// Both partial matches have the same parameter count (1),
		// so it should return the one with the highest weight (partialMatch2)
		expect(result).toEqual({
			pageId: 'partial-page-2-id',
			hash: partialHash2,
			parameters: { weatherMode: 'active' },
			exactMatch: false,
		});
	});

	it('should return the match with highest parameter count when multiple partial matches exist', async () => {
		// Create a test case with more parameters
		const partialHash3 = hashParameters({
			deviceClass: 'desktop',
			partner: 'apple',
		});
		const partialMatchWithMoreParams = {
			id: 'partial-match-3-id',
			hash: partialHash3,
			parameters: { deviceClass: 'desktop', partner: 'apple' },
			page: 'partial-page-3-id',
			weight: 3, // Lower weight than others
		};

		// Setup mock to return matches with different parameter counts
		mockPayload.find.mockResolvedValueOnce({
			docs: [partialMatch1, partialMatch2, partialMatchWithMoreParams],
			totalDocs: 3,
			limit: 100,
			totalPages: 1,
			page: 1,
			pagingCounter: 1,
			hasPrevPage: false,
			hasNextPage: false,
			prevPage: null,
			nextPage: null,
		});

		const result = await findBestContextMatch(testParams);

		// Should return partialMatchWithMoreParams because it has more parameters (2 vs 1)
		// even though it has a lower weight
		expect(result).toEqual({
			pageId: 'partial-page-3-id',
			hash: partialHash3,
			parameters: { deviceClass: 'desktop', partner: 'apple' },
			exactMatch: false,
		});
	});

	it('should return null when no matches are found', async () => {
		// Setup mock to return empty results
		mockPayload.find.mockResolvedValueOnce({
			docs: [],
			totalDocs: 0,
			limit: 100,
			totalPages: 0,
			page: 1,
			pagingCounter: 1,
			hasPrevPage: false,
			hasNextPage: false,
			prevPage: null,
			nextPage: null,
		});

		const result = await findBestContextMatch(testParams);

		expect(result).toBeNull();
	});

	it('should return null when no parameter hashes are generated', async () => {
		// Use empty parameters to generate no hashes
		const emptyParams = {};
		const result = await findBestContextMatch(emptyParams);

		// Should return null without calling payload.find
		expect(result).toBeNull();
		expect(mockPayload.find).not.toHaveBeenCalled();
	});

	it('should handle page as an object with id property', async () => {
		// Test with page as an object
		const matchWithPageObject = {
			id: 'match-id',
			hash: exactMatchHash,
			parameters: { deviceClass: 'desktop', weatherMode: 'active' },
			page: { id: 'page-object-id' },
			weight: 10,
		};

		mockPayload.find.mockResolvedValueOnce({
			docs: [matchWithPageObject],
			totalDocs: 1,
			limit: 100,
			totalPages: 1,
			page: 1,
			pagingCounter: 1,
			hasPrevPage: false,
			hasNextPage: false,
			prevPage: null,
			nextPage: null,
		});

		const result = await findBestContextMatch(testParams);

		expect(result).toEqual({
			pageId: 'page-object-id',
			hash: exactMatchHash,
			parameters: { deviceClass: 'desktop', weatherMode: 'active' },
			exactMatch: true,
		});
	});

	it('should handle multiple matches with the same parameter count and choose the one with highest weight', async () => {
		// Create two matches with the same parameter count but different weights
		const hash1 = hashParameters({
			deviceClass: 'desktop',
			partner: 'samsung',
		});
		const hash2 = hashParameters({ deviceClass: 'desktop', partner: 'apple' });

		const match1 = {
			id: 'match-1-id',
			hash: hash1,
			parameters: { deviceClass: 'desktop', partner: 'samsung' },
			page: 'page-1-id',
			weight: 5,
		};

		const match2 = {
			id: 'match-2-id',
			hash: hash2,
			parameters: { deviceClass: 'desktop', partner: 'apple' },
			page: 'page-2-id',
			weight: 10, // Higher weight
		};

		mockPayload.find.mockResolvedValueOnce({
			docs: [match1, match2],
			totalDocs: 2,
			limit: 100,
			totalPages: 1,
			page: 1,
			pagingCounter: 1,
			hasPrevPage: false,
			hasNextPage: false,
			prevPage: null,
			nextPage: null,
		});

		const result = await findBestContextMatch(testParams);

		// Should return match2 because it has the higher weight
		expect(result).toEqual({
			pageId: 'page-2-id',
			hash: hash2,
			parameters: { deviceClass: 'desktop', partner: 'apple' },
			exactMatch: false,
		});
	});
});
