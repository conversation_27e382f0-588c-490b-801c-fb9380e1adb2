import 'server-only';
import { getPayload } from 'payload';
import config from '@repo/payload/payload-config';
import { type ContextParameter, type Page } from '@repo/payload/payload-types';
import { unstable_cache } from 'next/cache';
import { type ContextMatch, findBestContextMatch } from './findBestMatch';
import { generateParameterHashes } from './generateCombinations';
import { type ContextParameters } from './extractParameters';
import {
	EnhancedLayout,
	EnhancedBlock,
	VisibilityRule,
	ContextParameterOverride,
} from './types';

/**
 * Information about a matching layout
 */
export interface MatchingLayout {
	/** Index of the layout in the page.content.layout array */
	layoutIndex: number;
	/** Region the layout belongs to (e.g., 'main', 'sidebar') */
	region: string;
	/** Context parameters associated with this layout */
	contextParameters: ContextParameters;
	/** Whether this layout is an exact match for the requested parameters */
	exactMatch: boolean;
	/** Whether this layout should be visible based on visibility rules */
	visible: boolean;
	/** Weight of layout (context params weight plus the count of context params) */
	computedWeight: number;
}

/**
 * Information about a matching block
 */
export interface MatchingBlock {
	/** Index of the layout containing this block */
	layoutIndex: number;
	/** Index of the block within its layout */
	blockIndex: number;
	/** Type of the block */
	blockType: string;
	/** Context parameters associated with this block */
	contextParameters: ContextParameters;
	/** Whether this block is an exact match for the requested parameters */
	exactMatch: boolean;
	/** Whether this block should be visible based on visibility rules */
	visible: boolean;
}

/**
 * Response type for getContextualizedPage function
 */
export type ContextualizedPageResponse = {
	/** The original context parameters from the request */
	requestedParams: ContextParameters;
	/** The best matching context parameter combination */
	match: ContextMatch | null;
	/** The page that matches the context parameters */
	page: Page | null;
};

export const unstableCacheRevalidateSeconds = 60 * 5; // 5 minutes

/**
 * Applies a visibility rule to determine if an element should be visible
 *
 * @param visibilityRule The rule to apply
 * @param hasMatch Whether there's a context parameter match
 * @returns Whether the element should be visible
 */
export function applyVisibilityRule(
	visibilityRule: VisibilityRule | null | undefined,
	hasMatch: boolean,
): boolean {
	if (!visibilityRule) {
		return true; // Default to visible if no rule specified
	}

	switch (visibilityRule) {
		case 'show_always':
			return true;
		case 'show_matching':
			return hasMatch;
		case 'hide_matching':
			return !hasMatch;
		default:
			return true; // Default to visible for unknown rules
	}
}

function getParamCount(
	parameters: ContextParameterOverride['parameters'],
): number {
	if (!parameters) return 0;

	let count = 0;
	for (const value of Object.values(parameters)) {
		if (!value) continue;
		if (Array.isArray(value)) {
			if (value.length > 0) {
				count++;
				continue;
			}
		}
		count++;
	}

	return count;
}

async function findLayoutContextParams(pageId: string, allHashes: string[]) {
	const payload = await getPayload({ config });
	const layoutContextParams = await payload.find({
		collection: 'context-parameters',
		where: {
			and: [
				{
					layouts: {
						contains: pageId,
					},
				},
				{
					hash: {
						in: allHashes.length > 0 ? allHashes : [''],
					},
				},
			],
		},
		limit: 100,
	});
	return layoutContextParams;
}

const findLayoutContextParamsCached = unstable_cache(
	async (pageId: string, allHashes: string[]) =>
		findLayoutContextParams(pageId, allHashes),
	['context-parameters-layouts'],
	{
		// tags for cache invalidation
		tags: ['context-params'],
		// revalidate after a minute
		revalidate: unstableCacheRevalidateSeconds,
	},
);

/**
 * Finds the best matching layout for each region in the page based on context parameters
 *
 * @param payload Payload CMS instance
 * @param page The page to process
 * @param pageId The page ID
 * @param allHashes Array of parameter hashes to match against
 * @param exactMatchHash The hash for an exact match
 * @returns Record of region names to matching layouts
 */
async function findMatchingLayout(
	page: Page,
	pageId: string,
	allHashes: string[],
	exactMatchHash: string,
): Promise<Record<string, MatchingLayout>> {
	const matchingLayouts: Record<string, MatchingLayout> = {};

	// If page has no layouts, return empty record
	if (!page.content?.layout || page.content.layout.length === 0) {
		return matchingLayouts;
	}

	// Get all context parameters for this page's layouts
	const layoutContextParams = await findLayoutContextParamsCached(
		pageId,
		allHashes,
	);

	// Group layouts by region to track layouts with context parameter overrides
	const layoutsByRegion: Record<string, MatchingLayout[]> = {};

	// Process each layout in the page
	for (
		let layoutIndex = 0;
		layoutIndex < page.content.layout.length;
		layoutIndex++
	) {
		const layout = page.content.layout[
			layoutIndex
		] as unknown as EnhancedLayout;

		const region = layout.region || 'main';

		// Initialize region array if it doesn't exist
		if (!layoutsByRegion[region]) {
			layoutsByRegion[region] = [];
		}

		// If layout has context parameter overrides enabled
		if (layout.contextParameterOverrides?.enabled) {
			// Find matching context parameter for this layout
			const layoutCP = layoutContextParams.docs.find((cp: ContextParameter) => {
				const layoutParamHashes = generateParameterHashes({
					...layout.contextParameterOverrides?.parameters,
					pageKey: page.assetName,
				});
				return layoutParamHashes.some(({ hash }) => hash === cp.hash);
			});

			// Determine if layout matches the requested parameters
			const layoutMatches = !!layoutCP;

			// Apply visibility rule
			const layoutVisible = applyVisibilityRule(
				layout.contextParameterOverrides.visibilityRule,
				layoutMatches,
			);

			// Add to matching layouts if there's a match
			if (layoutCP) {
				const paramCount = getParamCount(
					layout.contextParameterOverrides
						.parameters as ContextParameterOverride['parameters'],
				);
				const hash = layoutCP.hash || '';
				const layoutToPush: MatchingLayout = {
					layoutIndex,
					region,
					contextParameters: layoutCP.parameters as ContextParameters,
					exactMatch: hash === exactMatchHash,
					visible: layoutVisible,
					computedWeight: (layoutCP.weight || 0) + paramCount,
				};
				layoutsByRegion[region].push(layoutToPush);
			}
		} else {
			// For layouts without context parameter overrides, add a default entry
			// Only if we don't already have a match for this region
			if (layoutsByRegion[region].length === 0) {
				layoutsByRegion[region].push({
					layoutIndex,
					region,
					contextParameters: {},
					exactMatch: false,
					visible: true, // Always visible since it has no context parameter overrides
					computedWeight: 0,
				});
			}
		}
	}

	// For each region, find the best matching layout
	for (const region in layoutsByRegion) {
		const layouts = layoutsByRegion[region];

		if (layouts && layouts.length > 0) {
			// First, try to find an exact match that's visible
			const exactMatch = layouts.find(
				(layout) => layout.exactMatch && layout.visible,
			);
			if (exactMatch) {
				matchingLayouts[region] = exactMatch;
				continue;
			}

			const layoutsByWeight = layouts.sort(
				(a, b) => b.computedWeight - a.computedWeight,
			);

			// Next, try to find any visible match
			const visibleLayouts = layoutsByWeight.filter(({ visible }) => visible);

			if (visibleLayouts.length > 0 && visibleLayouts[0]) {
				matchingLayouts[region] = visibleLayouts[0];
				continue;
			}

			// If no visible matches, use the first match
			if (layoutsByWeight[0]) {
				matchingLayouts[region] = layoutsByWeight[0];
			}
		}
	}

	// If no matching layouts were found but the page has layouts,
	// add the first layout of each region as a default
	if (
		Object.keys(matchingLayouts).length === 0 &&
		page.content.layout.length > 0
	) {
		// Group layouts by region
		const regionMap: Record<string, number> = {};

		for (let i = 0; i < page.content.layout.length; i++) {
			const layout = page.content.layout[i] as unknown as EnhancedLayout;
			const region = layout.region || 'main';

			// Only add the first layout for each region
			if (regionMap[region] === undefined) {
				regionMap[region] = i;

				matchingLayouts[region] = {
					layoutIndex: i,
					region,
					contextParameters: {},
					exactMatch: false,
					visible: true, // Always visible since it has no context parameter overrides
					computedWeight: 0,
				};
			}
		}
	}

	return matchingLayouts;
}

async function findBlockContextParams(pageId: string, allHashes: string[]) {
	const payload = await getPayload({ config });
	const blockContextParams = await payload.find({
		collection: 'context-parameters',
		where: {
			and: [
				{
					blocks: {
						contains: pageId,
					},
				},
				{
					hash: {
						in: allHashes.length > 0 ? allHashes : [''],
					},
				},
			],
		},
		limit: 100,
	});
	return blockContextParams;
}

const findBlockContextParamsCached = unstable_cache(
	async (pageId: string, allHashes: string[]) =>
		findBlockContextParams(pageId, allHashes),
	['context-parameters-blocks'],
	{
		// tags for cache invalidation
		tags: ['context-params'],
		// revalidate after a minute
		revalidate: unstableCacheRevalidateSeconds,
	},
);

/**
 * Finds matching context parameters for blocks in a page
 *
 * @param payload Payload CMS instance
 * @param page The page to process
 * @param pageId The page ID
 * @param allHashes Array of parameter hashes to match against
 * @param exactMatchHash The hash for an exact match
 * @param matchingLayouts Record of region names to matching layouts
 * @returns Array of matching blocks
 */
async function findMatchingBlocks(
	page: Page,
	pageId: string,
	allHashes: string[],
	exactMatchHash: string,
	matchingLayouts: Record<string, MatchingLayout>,
): Promise<MatchingBlock[]> {
	const matchingBlocks: MatchingBlock[] = [];

	// Bail if there are no layouts
	if (!page.content?.layout) return matchingBlocks;

	// Get all context parameters for this page's blocks
	const blockContextParams = await findBlockContextParamsCached(
		pageId,
		allHashes,
	);

	// Create a set of layout indices that are matched
	const matchedLayoutIndices = new Set<number>();
	Object.values(matchingLayouts).forEach((layout) => {
		matchedLayoutIndices.add(layout.layoutIndex);
	});

	// Process each layout in the page
	for (
		let layoutIndex = 0;
		layoutIndex < page.content.layout.length;
		layoutIndex++
	) {
		const layout = page.content.layout[
			layoutIndex
		] as unknown as EnhancedLayout;

		// Skip if this layout is not in the matched layouts
		if (!matchedLayoutIndices.has(layoutIndex)) {
			continue;
		}

		// Skip if this layout does not have blocks
		if (!layout.blocks || !Array.isArray(layout.blocks)) {
			continue;
		}

		// Process blocks within this layout
		for (let blockIndex = 0; blockIndex < layout.blocks.length; blockIndex++) {
			const block = layout.blocks[blockIndex] as unknown as EnhancedBlock;

			if (block.contextParameterOverrides?.enabled) {
				// Find matching context parameter for this block
				const blockCP = blockContextParams.docs.find((cp: ContextParameter) => {
					// Calculate the hashes for this block's parameters
					const blockParamHashes = generateParameterHashes({
						...block.contextParameterOverrides?.parameters,
						pageKey: page.assetName,
					});
					// Return true if at least one hash matches the context parameter hash
					return blockParamHashes.some(({ hash }) => hash === cp.hash);
				});

				// Determine if block matches the requested parameters
				const blockMatches = !!blockCP;

				// Apply visibility rule
				const blockVisible = applyVisibilityRule(
					block.contextParameterOverrides.visibilityRule,
					blockMatches,
				);

				// Add to matching blocks if there's a match
				const blockHash = blockCP?.hash || '';
				matchingBlocks.push({
					layoutIndex,
					blockIndex,
					blockType: block.blockType,
					contextParameters: (blockCP?.parameters as ContextParameters) || null,
					exactMatch: blockHash === exactMatchHash,
					visible: blockVisible,
				});
			} else {
				// no context parameter overrides, push the block
				matchingBlocks.push({
					layoutIndex,
					blockIndex,
					blockType: block.blockType,
					contextParameters: null,
					exactMatch: false,
					visible: true,
				});
			}
		}
	}

	return matchingBlocks;
}

/**
 * Filters a page's content based on visibility rules
 *
 * @param page The page to filter
 * @param matchingLayouts Record of region names to matching layouts
 * @param matchingBlocks Array of matching blocks
 * @returns A filtered copy of the page
 */
export function filterPageContent(
	page: Page,
	matchingLayouts: Record<string, MatchingLayout>,
	matchingBlocks: MatchingBlock[],
): Page {
	// Create a deep copy of the page to avoid modifying the original
	const filteredPage = JSON.parse(JSON.stringify(page)) as Page;

	if (!filteredPage.content?.layout) return filteredPage;

	// Create a set of layout indices that are matched
	const matchedLayoutIndices = new Set<number>();
	Object.values(matchingLayouts).forEach((layout) => {
		matchedLayoutIndices.add(layout.layoutIndex);
	});

	// Filter blocks within layouts based on visibility
	filteredPage.content.layout.forEach((layout, layoutIndex) => {
		// skip unmatched layouts
		if (!matchedLayoutIndices.has(layoutIndex)) return;

		// no blocks to check
		if (!(layout.blocks && Array.isArray(layout.blocks))) return;

		layout.blocks = layout.blocks.filter((block, blockIndex) => {
			const blockMatch = matchingBlocks.find(
				(mb) => mb.layoutIndex === layoutIndex && mb.blockIndex === blockIndex,
			);

			// If there's a match, use its visibility; otherwise, discard the block
			return blockMatch ? blockMatch.visible : false;
		});
	});

	// Filter-out unmatched layouts
	filteredPage.content.layout = filteredPage.content.layout.filter(
		(layout, layoutIndex) => {
			return matchedLayoutIndices.has(layoutIndex);
		},
	);

	return filteredPage;
}

async function findPageById(pageId: string) {
	const payload = await getPayload({ config });
	const page = await payload.findByID({
		collection: 'pages',
		id: pageId,
	});
	return page;
}

const findPageByIdCached = unstable_cache(
	async (pageId: string) => findPageById(pageId),
	['context-parameters-page-by-id'],
	{
		// tags for cache invalidation
		tags: ['context-params'],
		// revalidate after a minute
		revalidate: unstableCacheRevalidateSeconds,
	},
);

/**
 * Utility for getting data about matched context params.
 *
 * This function finds the best matching page for a set of context parameters,
 * then determines which layouts and blocks within that page should be visible
 * based on their visibility rules and context parameter matches.
 *
 * @param params The context parameters from the request
 * @returns Information about the matching page, layouts, and blocks
 */
export const getContextualizedPage = async (
	params: Record<string, string | string[] | undefined>,
): Promise<ContextualizedPageResponse> => {
	let page: Page | null = null;
	// Find the best matching context parameter combination
	const match = await findBestContextMatch(params);
	if (!match) {
		return {
			requestedParams: params,
			match,
			page,
		};
	}

	// Get the page data for the matching page
	page = await findPageByIdCached(match.pageId);

	if (!page) {
		return {
			requestedParams: params,
			match,
			page,
		};
	}

	// Generate parameter hashes for the requested context parameters
	const parameterHashes = generateParameterHashes(params);
	const allHashes = parameterHashes
		.map((ph) => ph.hash)
		.filter((hash): hash is string => Boolean(hash));
	const exactMatchHash = parameterHashes[0]?.hash || '';

	// Skip processing if page has no layout or no hashes were generated
	if (!page.content?.layout || allHashes.length === 0) {
		return {
			requestedParams: params,
			match,
			page,
		};
	}

	// Find matching layouts and blocks
	const pageId = page.id || '';
	const matchingLayouts = await findMatchingLayout(
		page,
		pageId,
		allHashes,
		exactMatchHash,
	);

	const matchingBlocks = await findMatchingBlocks(
		page,
		pageId,
		allHashes,
		exactMatchHash,
		matchingLayouts,
	);

	// Filter page content based on visibility rules
	const filteredPage = filterPageContent(page, matchingLayouts, matchingBlocks);

	// Return the response
	return {
		requestedParams: params,
		match,
		page: filteredPage,
	};
};
