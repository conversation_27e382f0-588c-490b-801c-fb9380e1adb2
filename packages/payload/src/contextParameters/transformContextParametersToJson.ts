type ContextParameterArray = Array<{
	name?: string | null;
	value?: string | null;
	id?: string | null;
}>;

type ContextParameterObject = Record<
	string,
	string | string[] | null | undefined
>;

type TransformedParameters = {
	[key: string]: string | string[];
};

/**
 * Transforms context parameters into a JSON object
 * Supports OR relationships by handling arrays of values
 *
 * @param contextParameters Context parameters from a Page or other source
 * @returns JSON object with parameter names as keys and values as either strings or arrays of strings
 */
export const transformContextParametersToJson = (
	contextParameters:
		| ContextParameterArray
		| ContextParameterObject
		| null
		| undefined,
): TransformedParameters => {
	// Handle empty parameters
	if (!contextParameters) {
		return {};
	}

	// Handle array format (old format)
	if (Array.isArray(contextParameters)) {
		// Group parameters by name to handle multiple values for the same parameter
		const groupedParams: Record<string, string[]> = {};

		// Filter out entries with empty values and group by name
		contextParameters.forEach((param) => {
			const name = param.name;
			const value = param.value;

			if (!name || !value) return;

			if (!groupedParams[name]) {
				groupedParams[name] = [];
			}

			// Check if the value contains commas, which indicates an OR relationship
			if (value.includes(',')) {
				// Split the comma-separated values and add each one
				const splitValues = value
					.split(',')
					.map((v) => v.trim())
					.filter(Boolean);
				groupedParams[name].push(...splitValues);
			} else {
				groupedParams[name].push(value);
			}
		});

		// Convert to final format - single values stay as strings, multiple become arrays
		const result: TransformedParameters = {};

		for (const [name, values] of Object.entries(groupedParams)) {
			// Only add non-empty values
			if (values.length === 1) {
				result[name] = values[0]!;
			} else if (values.length > 1) {
				result[name] = values;
			}
		}

		return result;
	}

	// Handle object format (new format)
	const result = {} as TransformedParameters;

	// Process each parameter
	for (const [key, value] of Object.entries(contextParameters)) {
		// Skip empty values
		if (value === undefined || value === null || value === '') {
			continue;
		}

		// Handle array values (OR relationship)
		if (Array.isArray(value)) {
			// Filter out any null or undefined values
			const filteredValues = value.filter(Boolean) as string[];

			if (filteredValues.length === 1) {
				result[key] = filteredValues[0]!;
			} else if (filteredValues.length > 1) {
				result[key] = filteredValues;
			}
		} else if (typeof value === 'string' && value !== '') {
			// Handle string values
			result[key] = value;
		}
	}

	return result;
};
