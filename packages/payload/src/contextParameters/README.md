# Context Parameter System

The Context Parameter System allows pages to be queried based on the best-matching page configuration. This system works by generating hashed representations of context parameter combinations, finding the best match based on these hashes, and returning the appropriate page content based on the match.

## OR Relationship Support

The Context Parameter System now supports OR relationships among parameters. This means that a single parameter can have multiple values, and any of those values can match the requested parameter.

### How OR Relationships Work

1. **In the Admin UI**: When adding context parameters to a page, you can add multiple values for the same parameter. The ContextParameterField component will automatically group them and allow you to toggle between AND and OR relationships.

2. **In the Database**: Parameters with OR relationships are stored with comma-separated values or as arrays of values.

3. **In URL Parameters**: You can use comma-separated values in URL parameters to specify OR relationships. For example, `/page?deviceClass=desktop,tablet` will match pages with either `deviceClass=desktop` or `deviceClass=tablet`.

4. **In Matching Logic**: When matching parameters, the system will check if any of the values in an OR relationship match the requested parameter.

### Example Usage

#### Creating Parameters with OR Relationships

In the admin UI, you can add multiple values for the same parameter:

1. Add a parameter with name "deviceClass" and value "desktop"
2. Add another parameter with name "deviceClass" and value "tablet"
3. The ContextParameterField component will group these parameters and show a toggle button to switch between AND and OR relationships
4. Select "OR" to create an OR relationship

This will be stored in the database as:

```json
{
	"pageKey": "home",
	"deviceClass": ["desktop", "tablet"],
	"weatherMode": "active"
}
```

#### Matching Parameters with OR Relationships

When a request comes in with parameters:

```json
{
	"pageKey": "home",
	"deviceClass": "desktop",
	"weatherMode": "active"
}
```

It will match the stored parameters because "desktop" is one of the values in the OR relationship for "deviceClass".

Similarly, a request with:

```json
{
	"pageKey": "home",
	"deviceClass": "tablet",
	"weatherMode": "active"
}
```

Will also match the same stored parameters.

#### URL Parameters with OR Relationships

You can use comma-separated values in URL parameters to specify OR relationships:

```txt
/page?deviceClass=desktop,tablet&weatherMode=active
```

This will be extracted as:

```json
{
	"deviceClass": ["desktop", "tablet"],
	"weatherMode": "active"
}
```

And will match any page that has either "desktop" or "tablet" as the value for "deviceClass".

## Implementation Details

The OR relationship support is implemented in the following files:

- `transformContextParametersToJson.ts`: Transforms an array of context parameters into a JSON object, handling multiple values for the same parameter.
- `generateCombinations.ts`: Generates all possible combinations of parameters, handling arrays of values.
- `parameterValueMatches`: Checks if a parameter value matches another value, supporting arrays for OR relationships.
- `ContextParameterField`: Custom field component that allows users to toggle between AND and OR relationships.

## Testing

The OR relationship functionality is tested in:

- `orRelationship.test.ts`: Tests the basic functionality of OR relationships.
- `examples/orRelationship.test.ts`: More comprehensive tests with examples of how to use OR relationships.

## Benefits of OR Relationships

1. **Simplified Configuration**: Editors can specify multiple values for a parameter without creating duplicate configurations.
2. **Reduced Database Size**: Fewer context parameter documents needed to represent the same logic.
3. **Improved Performance**: Fewer database queries needed to find matching context parameters.
4. **More Flexible Matching**: Pages can match a wider range of requests with fewer configurations.
