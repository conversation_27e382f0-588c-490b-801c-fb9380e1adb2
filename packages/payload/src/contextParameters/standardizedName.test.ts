import { describe, it, expect } from 'vitest';
import { generateStandardizedName } from './standardizedName';

describe('generateStandardizedName', () => {
	it('should place pageKey as the first parameter', () => {
		const parameters = {
			zParam: 'z',
			aParam: 'a',
			pageKey: 'home',
			mParam: 'm',
		};

		const result = generateStandardizedName(parameters);

		// pageKey should be the first parameter
		expect(result.startsWith('pageKey:home')).toBe(true);

		// The rest should be in alphabetical order
		expect(result).toBe('pageKey:home,aParam:a,mParam:m,zParam:z');
	});

	it('should sort all parameters alphabetically when pageKey is not present', () => {
		const parameters = {
			zParam: 'z',
			aParam: 'a',
			mParam: 'm',
		};

		const result = generateStandardizedName(parameters);

		// All parameters should be in alphabetical order
		expect(result).toBe('aParam:a,mParam:m,zParam:z');
	});

	it('should handle different value types correctly', () => {
		const parameters = {
			pageKey: 'home',
			nullValue: null,
			undefinedValue: undefined,
			objectValue: { key: 'value' },
			numberValue: 42,
			booleanValue: true,
		};

		const result = generateStandardizedName(parameters);

		// pageKey should be first, then the rest in alphabetical order
		expect(result).toBe(
			'pageKey:home,booleanValue:true,nullValue:null,numberValue:42,objectValue:{"key":"value"},undefinedValue:null',
		);
	});

	it('should handle empty parameters object', () => {
		const parameters = {};
		const result = generateStandardizedName(parameters);
		expect(result).toBe('');
	});
});
