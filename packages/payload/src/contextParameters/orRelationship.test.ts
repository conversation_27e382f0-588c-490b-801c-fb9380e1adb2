import { describe, it, expect } from 'vitest';
import { transformContextParametersToJson } from './transformContextParametersToJson';
import { parameterValueMatches } from './generateCombinations';

describe('OR Relationship in Context Parameters', () => {
	describe('transformContextParametersToJson', () => {
		it('should handle single values', () => {
			const params = [
				{ name: 'pageKey', value: 'home' },
				{ name: 'deviceClass', value: 'desktop' },
			];

			const result = transformContextParametersToJson(params);

			expect(result).toEqual({
				pageKey: 'home',
				deviceClass: 'desktop',
			});
		});

		it('should handle multiple values for the same parameter (OR relationship)', () => {
			const params = [
				{ name: 'pageKey', value: 'home' },
				{ name: 'deviceClass', value: 'desktop' },
				{ name: 'deviceClass', value: 'tablet' },
			];

			const result = transformContextParametersToJson(params);

			expect(result).toEqual({
				pageKey: 'home',
				deviceClass: ['desktop', 'tablet'],
			});
		});

		it('should handle comma-separated values as OR relationship', () => {
			const params = [
				{ name: 'pageKey', value: 'home' },
				{ name: 'deviceClass', value: 'desktop,tablet' },
			];

			const result = transformContextParametersToJson(params);

			expect(result).toEqual({
				pageKey: 'home',
				deviceClass: ['desktop', 'tablet'],
			});
		});

		it('should handle empty values', () => {
			const params = [
				{ name: 'pageKey', value: 'home' },
				{ name: 'deviceClass', value: '' },
				{ name: '', value: 'something' },
			];

			const result = transformContextParametersToJson(params);

			expect(result).toEqual({
				pageKey: 'home',
			});
		});
	});

	describe('parameterValueMatches', () => {
		it('should match exact values', () => {
			expect(parameterValueMatches('desktop', 'desktop')).toBe(true);
			expect(parameterValueMatches('desktop', 'mobile')).toBe(false);
		});

		it('should match any value in an array (OR relationship)', () => {
			expect(parameterValueMatches(['desktop', 'tablet'], 'desktop')).toBe(
				true,
			);
			expect(parameterValueMatches(['desktop', 'tablet'], 'tablet')).toBe(true);
			expect(parameterValueMatches(['desktop', 'tablet'], 'mobile')).toBe(
				false,
			);
		});

		it('should handle edge cases', () => {
			expect(parameterValueMatches([], 'desktop')).toBe(false);
			expect(parameterValueMatches(null, 'desktop')).toBe(false);
			expect(parameterValueMatches(undefined, 'desktop')).toBe(false);
		});
	});

	describe('Creating context parameters with OR relationships', () => {
		it('should transform parameters with OR relationships correctly', () => {
			// In the admin UI, you would add multiple values for the same parameter
			// The ContextParameterField component will automatically group them
			// and allow you to toggle between AND and OR relationships

			// Example of parameters with OR relationship:
			// - pageKey: 'home'
			// - deviceClass: 'desktop' OR 'tablet'
			// - weatherMode: 'active' OR 'severe'

			// This would be stored in the database as:
			const storedParameters = [
				{ name: 'pageKey', value: 'home' },
				{ name: 'deviceClass', value: 'desktop,tablet' }, // Comma-separated for OR
				{ name: 'weatherMode', value: 'active,severe' }, // Comma-separated for OR
			];

			// When transformed to JSON, it becomes:
			const transformedParams =
				transformContextParametersToJson(storedParameters);

			expect(transformedParams).toEqual({
				pageKey: 'home',
				deviceClass: ['desktop', 'tablet'],
				weatherMode: ['active', 'severe'],
			});
		});
	});
});
