import { describe, it, expect } from 'vitest';
import type { ContextParameter } from '@repo/payload/payload-types';
import {
	generateParameterCombinations,
	hashParameters,
	generateParameterHashes,
} from './generateCombinations';

describe('generateParameterCombinations', () => {
	it('should return an empty array for non-object parameters', () => {
		expect(generateParameterCombinations(null)).toEqual([]);
		expect(generateParameterCombinations(undefined)).toEqual([]);
		expect(generateParameterCombinations('string')).toEqual([]);
		expect(generateParameterCombinations(123)).toEqual([]);
		expect(generateParameterCombinations(['foo', 'bar'])).toEqual([]);
	});

	it('should return an empty array for an empty object', () => {
		expect(generateParameterCombinations({})).toEqual([]);
	});

	it('should generate a single combination for an object with one parameter', () => {
		const params = { a: 1 };
		const result = generateParameterCombinations(params);

		expect(result).toHaveLength(1);
		expect(result).toEqual([{ a: 1 }]);
	});

	it('should generate all possible combinations for an object with two parameters', () => {
		const params = { a: 1, b: 2 };
		const result = generateParameterCombinations(params);

		expect(result).toHaveLength(3); // 2^2 - 1 = 3 combinations
		expect(result).toContainEqual({ a: 1, b: 2 });
		expect(result).toContainEqual({ a: 1 });
		expect(result).toContainEqual({ b: 2 });
	});

	it('should generate all possible combinations for an object with three parameters', () => {
		const params = { a: 1, b: 2, c: 3 };
		const result = generateParameterCombinations(params);

		expect(result).toHaveLength(7); // 2^3 - 1 = 7 combinations
		expect(result).toContainEqual({ a: 1, b: 2, c: 3 });
		expect(result).toContainEqual({ a: 1, b: 2 });
		expect(result).toContainEqual({ a: 1, c: 3 });
		expect(result).toContainEqual({ b: 2, c: 3 });
		expect(result).toContainEqual({ a: 1 });
		expect(result).toContainEqual({ b: 2 });
		expect(result).toContainEqual({ c: 3 });
	});

	it('should sort combinations by number of parameters in descending order', () => {
		const params = { a: 1, b: 2, c: 3 };
		const result = generateParameterCombinations(params);

		// Make sure we have results before testing
		expect(result.length).toBeGreaterThan(0);

		// First combination should have the most parameters
		expect(Object.keys(result[0] as Record<string, unknown>).length).toBe(3);

		// Check that combinations are sorted by number of parameters
		for (let i = 0; i < result.length - 1; i++) {
			const current = result[i] as Record<string, unknown>;
			const next = result[i + 1] as Record<string, unknown>;
			expect(Object.keys(current).length >= Object.keys(next).length).toBe(
				true,
			);
		}
	});

	it('should filter out undefined values', () => {
		const params = { a: 1, b: undefined, c: 3 };
		const result = generateParameterCombinations(params);

		expect(result).toHaveLength(3); // Only combinations of a and c
		expect(result).toContainEqual({ a: 1, c: 3 });
		expect(result).toContainEqual({ a: 1 });
		expect(result).toContainEqual({ c: 3 });

		// No combination should contain key 'b'
		result.forEach((combo) => {
			expect(combo).not.toHaveProperty('b');
		});
	});

	it('should handle complex object values', () => {
		const params = {
			obj: { nested: 'value' },
			arr: [1, 2, 3],
			func: () => 'hello',
		};
		const result = generateParameterCombinations(params);

		expect(result).toHaveLength(7); // 2^3 - 1 = 7 combinations
		expect(result).toContainEqual({
			obj: { nested: 'value' },
			arr: [1, 2, 3],
			func: params.func,
		});
	});
});

describe('hashParameters', () => {
	it('should generate consistent hashes for the same parameters', () => {
		const params = {
			pageKey: '/foobar',
			deviceClass: 'desktop',
			siteMode: 'severe',
		} as ContextParameter['parameters'];
		const hash1 = hashParameters(params);
		const hash2 = hashParameters(params);

		expect(hash1).toBe(hash2);
	});

	it('should generate different hashes for different parameters', () => {
		const params1 = {
			pageKey: '/foobar',
			deviceClass: 'desktop',
			siteMode: 'severe',
		} as ContextParameter['parameters'];
		const params2 = {
			pageKey: '/foobar',
			deviceClass: 'mobile',
			siteMode: 'severe',
		} as ContextParameter['parameters'];

		expect(hashParameters(params1)).not.toBe(hashParameters(params2));
	});
});

describe('generateParameterHashes', () => {
	it('should generate hashes for all combinations', () => {
		const params = { a: 1, b: 2 };
		const result = generateParameterHashes(params);

		expect(result).toHaveLength(3); // 2^2 - 1 = 3 combinations

		// Each result should have a combination and a hash
		result.forEach((item) => {
			expect(item).toHaveProperty('combination');
			expect(item).toHaveProperty('hash');
			expect(typeof item.hash).toBe('string');
		});
	});
});
