import { Page } from '@repo/payload/payload-types';

/**
 * Visibility rule options for context parameter overrides
 *
 * - show_always: Always show the layout/block regardless of context parameters
 * - show_matching: Only show the layout/block when context parameters match
 * - hide_matching: Hide the layout/block when context parameters match
 */
export type VisibilityRule = 'show_always' | 'show_matching' | 'hide_matching';

/**
 * Context parameter override configuration
 *
 * This interface defines the structure for context parameter overrides
 * that can be applied to layouts and blocks.
 */
export interface ContextParameterOverride {
	/** Whether context parameter overrides are enabled for this layout/block */
	enabled: boolean;

	/**
	 * Object containing parameter key-value pairs
	 * Values can be strings or arrays of strings (for OR relationships)
	 */
	parameters?: Record<string, string | string[]>;

	/** Rule determining visibility based on context parameter matching */
	visibilityRule?: VisibilityRule;
}

/**
 * Enhanced layout type with context parameter overrides
 *
 * This extends the base layout type from PayloadCMS with additional
 * properties for context parameter-based visibility control.
 */
export interface EnhancedLayout {
	/** The region this layout belongs to (e.g., 'main', 'sidebar') */
	region?: string;

	/** Context parameter override configuration for this layout */
	contextParameterOverrides?: Page['content']['layout'][number]['contextParameterOverrides'];

	/** Blocks contained within this layout */
	blocks?: EnhancedBlock[];
}

/**
 * Enhanced block type with context parameter overrides
 *
 * This extends the base block type from PayloadCMS with additional
 * properties for context parameter-based visibility control.
 */
export interface EnhancedBlock {
	/** Optional unique identifier for the block */
	id?: string;

	/** The type of block (e.g., 'CurrentConditions', 'DailyForecast') */
	blockType: string;

	/** Optional name for the block for easier identification */
	blockName?: string;

	/** Context parameter override configuration for this block */
	contextParameterOverrides?: ContextParameterOverride;
}
