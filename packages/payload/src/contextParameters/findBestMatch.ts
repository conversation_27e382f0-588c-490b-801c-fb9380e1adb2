import { getPayload, PaginatedDocs } from 'payload';
import config from '@repo/payload/payload-config';
import type { ContextParameter } from '@repo/payload/payload-types';
import { unstable_cache } from 'next/cache';
import { ContextParameters } from './extractParameters';
import { generateParameterHashes } from './generateCombinations';

/**
 * Result of a context parameter match
 */
export interface ContextMatch {
	pageId: string;
	hash: string;
	parameters: ContextParameters;
	exactMatch: boolean;
}

/**
 * Extracts a page ID from a ContextParameter, prioritizing the new 'page' field
 * and falling back to the legacy 'pages' array if needed.
 *
 * @param contextParam The context parameter to extract the page ID from
 * @returns The page ID as a string, or null if no valid page reference is found
 */
function extractPageId(contextParam: ContextParameter): string | null {
	// First try to get pageId from the page field (new structure)
	if (contextParam.page) {
		return typeof contextParam.page === 'string'
			? contextParam.page
			: contextParam.page.id;
	}

	// No valid page reference found
	return null;
}

export const unstableCacheRevalidateSeconds = 60 * 5; // 5 minutes

/**
 * Creates a ContextMatch object from a ContextParameter if it has a valid page reference
 *
 * @param contextParam The context parameter to create a match from
 * @param isExactMatch Whether this is an exact match or a partial match
 * @returns A ContextMatch object, or null if no valid page reference is found
 */
function createContextMatch(
	contextParam: ContextParameter,
	isExactMatch: boolean,
): ContextMatch | null {
	// Skip if no hash
	if (!contextParam.hash) {
		return null;
	}

	// Extract page ID
	const pageId = extractPageId(contextParam);
	if (!pageId) {
		return null;
	}

	// Create and return the match object
	return {
		pageId,
		hash: contextParam.hash,
		parameters: contextParam.parameters as ContextParameters,
		exactMatch: isExactMatch,
	};
}

async function findMatchingContextParams(allHashes: string[]) {
	const payload = await getPayload({ config });
	const matches: PaginatedDocs<ContextParameter> = await payload.find({
		collection: 'context-parameters',
		where: {
			and: [
				{
					hash: {
						in: allHashes,
					},
				},
				{
					page: {
						exists: true,
					},
				},
			],
		},
		limit: 100, // Reasonable limit for matches
	});
	return matches;
}

const findMatchingContextParamsCached = unstable_cache(
	async (allHashes: string[]) => findMatchingContextParams(allHashes),
	['context-parameters-matches'],
	{
		// tags for cache invalidation
		tags: ['context-params'],
		// revalidate after a minute
		revalidate: unstableCacheRevalidateSeconds,
	},
);

/**
 * Finds the best matching context parameter combination for the given parameters.
 *
 * @param params The context parameters to match
 * @returns The best matching context parameter combination
 */
export const findBestContextMatch = async (
	params: ContextParameters,
): Promise<ContextMatch | null> => {
	// Generate all possible parameter combinations and their hashes
	const parameterHashes = generateParameterHashes(params);

	// If no hashes were generated, return null
	if (parameterHashes.length === 0) {
		return null;
	}

	// Get all hashes to query for
	const allHashes = parameterHashes.map((ph) => ph.hash).filter(Boolean);

	// If no valid hashes, return null
	if (allHashes.length === 0) {
		return null;
	}

	// The exact match hash is the first one (with all parameters)
	const exactMatchHash = parameterHashes[0]?.hash;

	// If no exact match hash, return null
	if (!exactMatchHash) {
		return null;
	}

	// Make a single query to get all potential matches
	const matches = await findMatchingContextParamsCached(allHashes);

	// If no matches found, return null
	if (matches.docs.length === 0) {
		return null;
	}

	// First check if there's an exact match
	const exactMatch = matches.docs.find(
		(match) => match.hash === exactMatchHash,
	);

	if (exactMatch) {
		return createContextMatch(exactMatch, true);
	}

	// If no exact match, process partial matches
	// Group matches by parameter count
	const matchesByParamCount: Record<number, ContextParameter[]> = {};

	for (const match of matches.docs) {
		// Skip matches without hash
		if (!match.hash) continue;

		const paramCount = Object.keys(
			match.parameters as Record<string, string>,
		).length;

		if (!matchesByParamCount[paramCount]) {
			matchesByParamCount[paramCount] = [];
		}

		matchesByParamCount[paramCount].push(match);
	}

	// Find the highest parameter count that has matches
	const paramCounts = Object.keys(matchesByParamCount)
		.map(Number)
		.sort((a, b) => b - a);

	if (paramCounts.length === 0) {
		return null;
	}

	const highestParamCount = paramCounts[0];
	// Ensure highestParamCount is defined before using it as an index
	const highestParamMatches =
		highestParamCount !== undefined
			? matchesByParamCount[highestParamCount] || []
			: [];

	// If there's only one match with the highest parameter count, return it
	if (highestParamMatches.length === 1 && highestParamMatches[0]) {
		return createContextMatch(highestParamMatches[0], false);
	}

	// If there are multiple matches with the same parameter count,
	// select the one with the highest weight
	highestParamMatches.sort(
		(a: ContextParameter, b: ContextParameter) =>
			(b.weight || 0) - (a.weight || 0),
	);

	const bestMatch = highestParamMatches[0];
	if (!bestMatch) {
		return null;
	}

	return createContextMatch(bestMatch, false);
};
