# Jobs

## Overview

This folder contains job handlers for various asynchronous processing tasks in the system. Jobs are used to handle operations that may take time to complete or need to be processed in the background.

## Job Types

### Content Query Jobs

The Content Query system uses a job queue to process queries asynchronously:

1. `contentbytags` - Fetches content by tags, categories, and topics
2. `contentbyids` - Fetches content by specific article IDs
3. `contentmedia` - Fetches content from the external Kalliope CMS

### Ad Metrics Jobs

The Ad Metrics system uses jobs to update articles when category ad metrics change:

1. `updateAdMetrics` - Updates articles' ad metrics when a category's ad metric changes

For detailed information about the Ad Metrics job, see the [updateAdMetrics README](./updateAdMetrics/README.md) and the [Tags collection README](../collections/Tags/README.md).

## Processing Flow Content Query Jobs

### 1. Job Queueing

Jobs are queued in the `queueContentQueryJobsAfterChange` hook when:

- A new query is created
- An existing query is modified
- A query with failed status is updated

The hook identifies which queries need processing and queues appropriate jobs with the necessary parameters.

### 2. Query Locking

When a job starts processing, it:

1. Finds the query document by ID
2. Locates the specific query to update
3. Locks the query to prevent concurrent processing
4. Updates the query status to `in-progress`

This locking mechanism prevents race conditions when multiple jobs are running simultaneously.

### 3. Content Fetching

Each job type fetches content differently:

- **contentbytags**: Builds a MongoDB query based on tags, categories, and topics, then fetches matching articles
- **contentbyids**: Directly fetches articles by their IDs
- **contentmedia**: Makes an API call to the Kalliope CMS to fetch external content

### 4. Content Merging

After fetching content, each job:

1. Transforms the content into a standardized format
2. Preserves user customizations from existing content
3. Merges the new content with existing content from other queries
4. Updates the document with the merged content

The merging process ensures that:

- Content from different queries coexists in the `mergedContent` array
- Duplicate content is handled properly
- User customizations are preserved
- Content from removed queries is filtered out

### 5. Query Unlocking

After processing, the job:

1. Updates the query status to `completed` or `failed`
2. Sets the `lastSynced` timestamp
3. Unlocks the query for future processing

### Error Handling

If an error occurs during job processing:

1. The error is logged
2. The query is unlocked
3. The query status is set to `failed`
4. The job returns a failed state with an error message

### Utility Functions

The system includes several utility functions in `queryHandlers.ts`:

- `findQueryAndLock` - Finds a query document and locks a specific query
- `findQueryByKalliopeIdAndLock` - Similar to above but for Kalliope queries
- `unlockAndCompleteQuery` - Unlocks a query and marks it as completed
- `updateQueryWithContent` - Updates a query with new content
- `handleTaskError` - Handles errors and unlocks queries
- `buildWhereConditions` - Builds MongoDB query conditions for content queries

### Content Transformation

Each job type has its own `mergeContent` function that transforms fetched content into a standardized format:
