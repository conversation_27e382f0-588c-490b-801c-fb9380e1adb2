import { Article, ContentQuery, Image } from '@repo/payload/payload-types';
import {
	isImageObject,
	isArticleObject,
	getContentType,
} from '@repo/payload/utils/blockGuards';
import { PaginatedDocs, PayloadRequest } from 'payload';
import { getLocalizedValue } from '@repo/payload/utils/getLocalizedValue';
import { getLocale } from '@repo/payload/utils/getSupportedLocale';

type MergedContentItem = NonNullable<
	NonNullable<ContentQuery['mergedContent']>[number]
>;

interface MergedContentProps {
	newContent: PaginatedDocs<Article | Image>;
	req: PayloadRequest;
	queryId: string;
	existingContent: NonNullable<ContentQuery['mergedContent']>;
	locale?: string;
}

/**
 * Extract content metadata from an Article
 */
function extractArticleMetadata(article: Article, locale: string) {
	const validLocale = getLocale(locale);
	return {
		title: article.title ? getLocalizedValue(article.title, validLocale) : null,
		seoTitle: article.seo?.title
			? getLocalizedValue(article.seo.title, validLocale)
			: null,
		seoDescription: article.seo?.description
			? getLocalizedValue(article.seo.description, validLocale)
			: '',
		thumbnail: isImageObject(article.featuredImage)
			? article.featuredImage.url
			: null,
		assetName: article.assetName || article.slug,
		contentType: getContentType(article),
	};
}

/**
 * Extract content metadata from an Image
 */
function extractImageMetadata(image: Image, locale: string) {
	const validLocale = getLocale(locale);
	return {
		title: '',
		seoTitle: image.seo?.altText
			? getLocalizedValue(image.seo.altText, validLocale)
			: null,
		seoDescription: image.seo?.caption
			? getLocalizedValue(image.seo.caption, validLocale)
			: '',
		thumbnail: image.url,
		assetName: image.filename || image.id,
		contentType: getContentType(image),
	};
}

export const mergeContent = ({
	newContent,
	req,
	queryId,
	existingContent,
	locale = 'en-US',
}: MergedContentProps): NonNullable<ContentQuery['mergedContent']> => {
	// Create a map of existing content items for this query by ID for quick lookup
	const existingItemsMap = new Map();

	// Filter out items from other queries (keep them unchanged)
	const otherQueriesContent = existingContent.filter((item) => {
		if (item.sourceQueryID !== queryId) {
			return true; // Keep items from other queries
		}

		// Store items from this query in the map for later comparison
		// Ensure we're using string IDs for consistent comparison
		existingItemsMap.set(String(item.id), item);
		return false; // Filter out items from this query (we'll add back the ones that still exist)
	});

	// Track processed IDs to avoid duplicates
	const processedIds = new Set<string>();
	const updatedItems: MergedContentItem[] = [];

	// Process new content
	for (const item of newContent.docs) {
		const { id } = item;

		if (!id) {
			continue;
		}

		// Convert ID to string for consistent comparison
		const stringId = String(id);

		// Skip if we've already processed this ID
		if (processedIds.has(stringId)) {
			req.payload.logger.debug(
				`[contentByTagsHandler] Skipping duplicate item with ID: ${stringId}`,
			);
			continue;
		}

		// Mark this ID as processed
		processedIds.add(stringId);
		// Extract metadata based on content type
		let metadata;
		if (isArticleObject(item)) {
			metadata = extractArticleMetadata(item, locale);
		} else if (isImageObject(item)) {
			metadata = extractImageMetadata(item, locale);
		} else {
			continue; // Skip unknown content types
		}

		// Merge properties with fallbacks
		const mergedTitle =
			metadata.title || metadata.seoTitle || `Untitled ${metadata.contentType}`;
		const mergedDescription = metadata.seoDescription || '';
		const thumb = metadata.thumbnail;
		const assetName = metadata.assetName;
		const contentType = getContentType(metadata);

		// Validate required fields
		if (!thumb) {
			req.payload.logger.debug(
				`[contentByTagsHandler] Required thumbnail missing for ${metadata.contentType} with ID: ${stringId}`,
			);
			continue; // Skip items without thumbnails
		}

		// Check if this item already exists in our content
		const existingItem = existingItemsMap.get(stringId);

		if (existingItem) {
			// Update existing item with new data but preserve user customizations
			updatedItems.push({
				...existingItem, // Keep existing properties (including pinned status and overrides)
				// Update with new data (but don't override user customizations)
				title: existingItem.overrideTitle || mergedTitle,
				overrideTitle: '',
				description: existingItem.overrideDescription || mergedDescription,
				thumbnail: existingItem.overrideThumbnail || thumb,
				url: assetName,
				// These fields always get updated
				source: 'payload',
				sourceQueryID: queryId,
				contentType, // Add content type for UI differentiation
			});
		} else {
			// Add new item
			updatedItems.push({
				id: stringId,
				source: 'payload',
				title: mergedTitle,
				overrideTitle: '',
				description: mergedDescription,
				overrideDescription: '',
				thumbnail: thumb,
				overrideThumbnail: '',
				url: assetName || '',
				sourceQueryID: queryId,
				pinned: false,
				contentType,
			});
		}
	}

	// Combine other queries' content with our updated content
	return [...otherQueriesContent, ...updatedItems];
};
