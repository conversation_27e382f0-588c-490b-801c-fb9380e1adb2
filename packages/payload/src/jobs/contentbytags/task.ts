import { hasLength } from '@repo/utils/hasLength';
import { TaskHandlerArgs, TaskHandlerResult } from 'payload';
import {
	buildWhereConditions,
	findQueryAndLock,
	handleTaskError,
	unlockAndCompleteQuery,
	updateQueryWithContent,
} from '@repo/payload/utils/jobs/queryHandlers';
import { mergeContent } from './mergeContent';
import { getUserLocale } from '@repo/payload/utils/getUserLocale';
import { TaskGetContentByTags } from '@repo/payload/payload-types';

/**
 * Helper function to process arrays of tags, categories, or topics
 * Converts each item to a string ID regardless of whether it's a string or object
 */
function processTagArray(
	items: TaskGetContentByTags['input']['tags'],
): string[] | undefined {
	if (!items || !Array.isArray(items)) return undefined;
	return items.map((item) => (typeof item === 'string' ? item : item.id));
}

/**
 * This handler fetches content by tags, categories, and topics and updates the content query
 * 1. Get the query doc by ID
 * 2. Find the query to update
 * 3. Update the query to in-progress
 * 4. Build the where clause based on tags, categories, and topics
 * 5. Get the articles and images from Payload
 * 6. Merge the content with the query
 * 7. Update the query doc with the new content
 */
export const getContentByTagsHandler = async ({
	input,
	job,
	req,
}: TaskHandlerArgs<'getContentByTags'>): Promise<
	TaskHandlerResult<'getContentByTags'>
> => {
	const {
		queryId,
		queryDocID,
		tags,
		tagsOperator,
		category,
		categoryOperator,
		topic,
		topicOperator,
		groupsOperator,
	} = input;
	const payload = req.payload;

	if (!payload) {
		return {
			state: 'failed',
			errorMessage: 'Payload not found',
		};
	}

	// Use the locale from input or get it from the request
	const userLocale = getUserLocale(req);
	payload.logger.debug(
		`Starting getContentByTagsHandler for queryId: ${queryId}, queryDocID: ${queryDocID}, locale: ${userLocale}`,
	);

	try {
		// 1. Find the query document and the specific query to update
		const { queryDoc, queryToUpdate, existingContent } = await findQueryAndLock(
			{
				payload,
				req,
				queryDocID,
				queryId,
				jobId: job.id,
			},
		);

		if (!queryDoc || !queryToUpdate) {
			return { state: 'succeeded', output: [] };
		}

		// 2. Process input arrays to string arrays using the helper function
		const processedTags = processTagArray(tags);
		const processedCategory = processTagArray(category);
		const processedTopic = processTagArray(topic);

		payload.logger.debug(
			`Processing query with tags: ${JSON.stringify(processedTags)}, categories: ${JSON.stringify(processedCategory)}, topics: ${JSON.stringify(processedTopic)}`,
		);

		// 3. Build the where clause based on tags, categories, and topics
		const whereConditions = buildWhereConditions({
			tags: processedTags,
			tagsOperator: tagsOperator ?? '',
			category: processedCategory,
			categoryOperator: categoryOperator ?? '',
			topic: processedTopic,
			topicOperator: topicOperator ?? '',
			locale: userLocale,
		});

		// Skip if no conditions were created
		if (whereConditions.length === 0) {
			payload.logger.debug(`No search conditions for query ${queryId}`);

			// Unlock + mark completed even though no conditions were found
			await unlockAndCompleteQuery({
				payload,
				queryDoc,
				queryToUpdate,
				queries: queryDoc.queries,
				req,
			});

			return { state: 'succeeded', output: [] };
		}

		// 4. Combine all conditions based on groupsOperator
		const finalWhere =
			whereConditions.length > 1
				? { [groupsOperator === 'AND' ? 'and' : 'or']: whereConditions }
				: whereConditions[0] || {};

		payload.logger.debug(
			`Querying articles with where clause: ${JSON.stringify(finalWhere)}`,
		);

		// 5a. Query the articles collection with the constructed where clause
		const articles = await payload.find({
			collection: 'articles',
			where: finalWhere,
			limit: 100,
			depth: 1, // Set depth to 1 to properly populate relationships
			locale: userLocale, // Pass the locale to get properly localized content
			req,
		});

		// 5b. Query the images collection with the same where clause.
		const images = await payload.find({
			collection: 'images',
			where: finalWhere,
			limit: 100,
			depth: 1,
			locale: userLocale,
			req,
		});

		// If no content found in either collection, mark as complete and return
		if (!hasLength(articles.docs) && !hasLength(images.docs)) {
			await unlockAndCompleteQuery({
				payload,
				queryDoc,
				queryToUpdate,
				queries: queryDoc.queries,
				req,
			});

			return { state: 'succeeded', output: [] };
		}

		payload.logger.debug(
			`Found ${articles.docs.length} articles matching the criteria for query ${queryId}`,
		);

		if (!queryToUpdate.id) {
			payload.logger.error(`Query with ID ${queryId} has no ID`);
			return {
				state: 'failed',
				errorMessage: `Query with ID ${queryId} has no ID`,
			};
		}

		// 6a. Process the articles using the task-specific mergeContent function
		let newContent = existingContent;

		if (hasLength(articles.docs)) {
			newContent = mergeContent({
				newContent: articles,
				req,
				queryId: queryToUpdate.id,
				existingContent: newContent || [],
				locale: userLocale,
			});
		}

		// 6b. Process the images using the same mergeContent function
		if (hasLength(images.docs)) {
			newContent = mergeContent({
				newContent: images,
				req,
				queryId: queryToUpdate.id,
				existingContent: newContent || [],
				locale: userLocale,
			});
		}

		// 7. Update the query with the new content
		await updateQueryWithContent({
			payload,
			queryDoc,
			queryToUpdate,
			newContent,
			existingContent,
			queryId,
			req,
		});
		return { state: 'succeeded', output: [] };
	} catch (e) {
		const error = e as Error;

		// Handle error and unlock query
		await handleTaskError({
			payload,
			queryDocID,
			queryId,
			jobId: job.id,
			error,
			req,
		});

		return {
			state: 'failed',
			errorMessage: `Job ID:${job.id} failed trying to process query with ID: ${queryId} \n Error: ${error.message}`,
		};
	}
};
