import { TaskConfig } from 'payload';
import { getContentByTagsHandler } from './task';

export const getContentByTags: TaskConfig = {
	retries: 2,
	slug: 'getContentByTags',
	inputSchema: [
		{ name: 'queryId', type: 'text', required: true },
		{ name: 'queryDocID', type: 'text', required: true },
		{
			name: 'tags',
			type: 'array',
			required: false,
			fields: [{ name: 'id', type: 'text', required: true }],
		},
		{ name: 'tagsOperator', type: 'text', required: false },
		{
			name: 'category',
			type: 'array',
			required: false,
			fields: [{ name: 'id', type: 'text', required: true }],
		},
		{ name: 'categoryOperator', type: 'text', required: false },
		{
			name: 'topic',
			type: 'array',
			required: false,
			fields: [{ name: 'id', type: 'text', required: true }],
		},
		{ name: 'topicOperator', type: 'text', required: false },
		{ name: 'groupsOperator', type: 'text', required: false },
		{ name: 'lastSynced', type: 'text', required: false },
	],
	outputSchema: [],
	onSuccess: async () => {
		console.log('getContentByTags task completed');
	},
	handler: getContentByTagsHandler,
};
