import { TaskH<PERSON>lerArgs, TaskHandlerResult } from 'payload';
import { hasLength } from '@repo/utils/hasLength';
import { mergeContent } from './mergeContent';
import {
	findQueryAndLock,
	handleTaskError,
	unlockAndCompleteQuery,
	updateQueryWithContent,
} from '@repo/payload/utils/jobs/queryHandlers';
import { getUserLocale } from '@repo/payload/utils/getUserLocale';

/**
 * This handler fetches content by specific IDs and updates the content query
 * 1. Get the query doc by ID
 * 2. Find the query to update
 * 3. Update the query to in-progress
 * 4. Get the articles from Payload
 * 5. Merge the articles with the query
 * 6. Update the query doc with the new articles
 */
export const getContentByIdsHandler = async ({
	input,
	job,
	req,
}: TaskHandlerArgs<'getContentByIds'>): Promise<
	TaskHandlerResult<'getContentByIds'>
> => {
	const { queryId, queryDocID, contentIds } = input;
	const payload = req.payload;

	const locale = getUserLocale(req);

	if (!payload) {
		return {
			state: 'failed',
			errorMessage: 'Payload not found',
		};
	}

	// Use the locale from input or get it from the request
	const userLocale = locale || getUserLocale(req);

	payload.logger.debug(
		`Starting getContentByIdsHandler for queryId: ${queryId}, queryDocID: ${queryDocID}, locale: ${userLocale}`,
	);
	payload.logger.debug(`Content IDs to fetch: ${JSON.stringify(contentIds)}`);

	try {
		// 1. Find the query document and the specific query to update
		const { queryDoc, queryToUpdate, existingContent } = await findQueryAndLock(
			{
				payload,
				req,
				queryDocID,
				queryId,
				jobId: job.id,
			},
		);

		if (!queryDoc || !queryToUpdate) {
			return { state: 'succeeded', output: [] };
		}

		// Check if contentIds is valid
		if (!hasLength(contentIds)) {
			payload.logger.debug(`No content IDs provided for query ${queryId}`);

			await unlockAndCompleteQuery({
				payload,
				queryDoc,
				queryToUpdate,
				queries: queryDoc.queries,
				req,
			});

			return { state: 'succeeded', output: [] };
		}

		// Extract just the ID strings from the contentIds array
		const idStrings = contentIds.map((item) =>
			typeof item === 'string' ? item : item.value,
		);

		payload.logger.debug(
			`Querying articles with IDs: ${JSON.stringify(idStrings)}`,
		);

		// Query the articles collection with the provided IDs
		const articles = await payload.find({
			collection: 'articles',
			req,
			where: {
				id: {
					in: idStrings,
				},
			},
			limit: idStrings.length,
			depth: 1,
			locale: userLocale, // Pass the locale to get properly localized content
		});

		if (!hasLength(articles.docs)) {
			payload.logger.debug(
				`No articles found with the provided IDs for query ${queryId}`,
			);

			await unlockAndCompleteQuery({
				payload,
				queryDoc,
				queryToUpdate,
				queries: queryDoc.queries,
				req,
			});

			return { state: 'succeeded', output: [] };
		}

		if (!queryToUpdate.id) {
			payload.logger.error(`Query with ID ${queryId} has no ID`);
			return {
				state: 'failed',
				errorMessage: `Query with ID ${queryId} has no ID`,
			};
		}

		// Process the articles using the task-specific mergeContent function
		const newContent = mergeContent({
			newContent: articles,
			req,
			queryId: queryToUpdate.id,
			existingContent,
			locale: userLocale,
		});

		// Update the query with the new content
		await updateQueryWithContent({
			payload,
			queryDoc,
			queryToUpdate,
			newContent,
			existingContent,
			queryId,
			req,
		});

		return { state: 'succeeded', output: [] };
	} catch (e) {
		const error = e as Error;

		// Handle error and unlock query
		await handleTaskError({
			payload,
			queryDocID,
			queryId,
			jobId: job.id,
			error,
			req,
		});

		return {
			state: 'failed',
			errorMessage: `Job ID:${job.id} failed trying to process query with ID: ${queryId} \n Error: ${error.message}`,
		};
	}
};
