import { TaskConfig } from 'payload';
import { getContentByIdsHandler } from './task';

export const getContentByIds: TaskConfig = {
	retries: 2,
	slug: 'getContentByIds',
	inputSchema: [
		{ name: 'queryId', type: 'text', required: true },
		{ name: 'queryDocID', type: 'text', required: true },
		{
			name: 'contentIds',
			type: 'array',
			required: true,
			fields: [{ name: 'value', type: 'text', required: true }],
		},
		{ name: 'lastSynced', type: 'text', required: false },
	],
	outputSchema: [],
	onSuccess: async () => {
		console.log('getContentByIds task completed');
	},
	handler: getContentByIdsHandler,
};
