import { Article, ContentQuery } from '@repo/payload/payload-types';
import { getContentType, isImageObject } from '@repo/payload/utils/blockGuards';
import { PaginatedDocs, PayloadRequest } from 'payload';
import { getLocalizedValue } from '@repo/payload/utils/getLocalizedValue';
import { getLocale } from '@repo/payload/utils/getSupportedLocale';

type MergedContentItem = NonNullable<
	NonNullable<ContentQuery['mergedContent']>[number]
>;

interface MergedContentProps {
	newContent: PaginatedDocs<Article>;
	req: PayloadRequest;
	queryId: string;
	existingContent: NonNullable<ContentQuery['mergedContent']>;
	locale?: string;
}
export const mergeContent = ({
	newContent,
	req,
	queryId,
	existingContent,
	locale = 'en-US',
}: MergedContentProps): NonNullable<ContentQuery['mergedContent']> => {
	const validLocale = getLocale(locale);
	// Create a map of existing content items for this query by ID for quick lookup
	const existingItemsMap = new Map();

	// Filter out items from other queries (keep them unchanged)
	const otherQueriesContent = existingContent.filter((item) => {
		if (item.sourceQueryID !== queryId) {
			return true; // Keep items from other queries
		}

		// Store items from this query in the map for later comparison
		// Ensure we're using string IDs for consistent comparison
		existingItemsMap.set(String(item.id), item);
		return false; // Filter out items from this query (we'll add back the ones that still exist)
	});

	// Track IDs we've processed to avoid duplicates
	const processedIds = new Set();

	// Process new content
	const updatedItems: MergedContentItem[] = [];

	// Process new content
	for (const item of newContent.docs) {
		const { id, title, seo, featuredImage, assetName } = item;

		if (!id) continue;

		// Convert ID to string for consistent comparison
		const stringId = String(id);

		// Skip if we've already processed this ID
		if (processedIds.has(stringId)) {
			req.payload.logger.debug(
				`[contentByIdsHandler] Skipping duplicate item with ID: ${stringId}`,
			);
			continue;
		}

		// Mark this ID as processed
		processedIds.add(stringId);

		// Get localized values
		const localizedTitle = getLocalizedValue(title, validLocale);
		const localizedSeoTitle = seo?.title
			? getLocalizedValue(seo.title, validLocale)
			: null;
		const localizedSeoDescription = seo?.description
			? getLocalizedValue(seo.description, validLocale)
			: '';

		// Merge properties with fallbacks
		const mergedTitle = localizedTitle || localizedSeoTitle;
		const mergedDescription = localizedSeoDescription || '';
		const thumb = isImageObject(featuredImage) ? featuredImage.url : null;

		// Validate required fields
		if (!assetName || !thumb || !mergedTitle) {
			req.payload.logger.debug(
				`[contentByIdsHandler] Required metadata missing for content with ID: ${stringId}`,
			);
			continue; // Skip invalid items
		}
		const contentType = getContentType(item);

		// Check if this item already exists in our content
		const existingItem = existingItemsMap.get(stringId);

		if (existingItem) {
			// Update existing item with new data but preserve user customizations
			updatedItems.push({
				...existingItem, // Keep existing properties (including pinned status and overrides)
				// Update with new data (but don't override user customizations)
				title: existingItem.overrideTitle || mergedTitle,
				description: existingItem.overrideDescription || mergedDescription,
				thumbnail: existingItem.overrideThumbnail || thumb,
				url: assetName,
				// These fields always get updated
				source: 'payload',
				sourceQueryID: queryId,
			});
		} else {
			// Add new item
			updatedItems.push({
				id: stringId,
				source: 'payload',
				title: mergedTitle,
				overrideTitle: '',
				description: mergedDescription,
				overrideDescription: '',
				thumbnail: thumb,
				overrideThumbnail: '',
				url: assetName,
				sourceQueryID: queryId,
				pinned: false,
				contentType: contentType,
			});
		}
	}

	// Combine other queries' content with our updated content
	return [...otherQueriesContent, ...updatedItems];
};
