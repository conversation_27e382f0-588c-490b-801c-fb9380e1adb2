export interface YouTubeRssFeed {
	'?xml': string;
	feed: {
		link: string[];
		id: string;
		'yt:channelId': string;
		title: string;
		author: {
			name: string;
			uri: string;
		};
		published: string;
		entry: YouTubeRssEntry[];
	};
}

export interface YouTubeRssEntry {
	id: string;
	'yt:videoId': string;
	'yt:channelId': string;
	title: string;
	link: {
		'@_rel': string;
		'@_href': string;
	};
	author: {
		name: string;
		uri: string;
	};
	published: string;
	updated: string;
	'media:group': {
		'media:title': string;
		'media:content': {
			'@_url': string;
			'@_type': string;
			'@_width': string;
			'@_height': string;
		};
		'media:thumbnail': {
			'@_url': string;
			'@_width': string;
			'@_height': string;
		};
		'media:description': string;
	};
}

export interface ConfigsYouTubeRssIngest {
	kalliopeCollectionId: string;
	kalliopePlaylistId: string;
	kalliopeInterestsId: string;
	kalliopeProviderId: string;
	kalliopeForecastKeywordId: string;
	kalliopeWeatherKeywordId: string;
}
