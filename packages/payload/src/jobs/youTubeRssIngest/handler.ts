import type { <PERSON><PERSON><PERSON><PERSON> } from 'payload';
import { ingest } from './ingest';
import { logger } from './utils';

export const handler: TaskHandler<'youTubeRssIngest'> = async ({
	input,
	job,
}) => {
	try {
		logger.info(`Starting YouTube RSS Ingest task with job ID: ${job.id}`);

		const result = await ingest({
			input: {
				sources: input.sources,
				configs: input.configs,
			},
		});

		return {
			state: 'succeeded',
			output: {
				result,
			},
		};
	} catch (error) {
		if (error instanceof Error) {
			return {
				state: 'failed',
				errorMessage: error.message,
			};
		}

		return {
			state: 'failed',
			errorMessage: `An unexpected error occurred: ${String(error)}`,
		};
	}
};
