import { XMLParser } from 'fast-xml-parser';
import { YouTubeRssFeed } from './types';
import { processRssFeed } from './cms';
import { TaskYouTubeRssIngest } from '@repo/payload/payload-types';
import { logger } from './utils';

const parser = new XMLParser({
	ignoreAttributes: false,
});

async function fetchChannelRssFeed(channelId: string) {
	const url = `https://www.youtube.com/feeds/videos.xml?channel_id=${channelId}`;

	const response = await fetch(url, {
		cache: process.env.NODE_ENV === 'production' ? 'no-cache' : 'default',
	});

	if (!response.ok) {
		throw new Error(`[${response.status}] Failed to fetch RSS feed`);
	}

	const xml = await response.text();

	return parser.parse(xml) as YouTubeRssFeed;
}

interface FetchYouTubeVideos {
	input: TaskYouTubeRssIngest['input'];
}

export async function ingest({
	input: { sources, configs },
}: FetchYouTubeVideos) {
	logger.info('Starting YouTube ingestor');

	const results: Record<string, unknown> = {};

	// Using a Promise.all causes a 504 error when multiple videos are
	// being added in parallel.
	// So we process each video sequentially.
	for (let i = 0; i < sources.length; i += 1) {
		const source = sources[i]!;
		try {
			const { channelId, name } = source;

			logger.info(`Fetching feed for channel: ${name}`);

			const data = await fetchChannelRssFeed(channelId);

			const result = await processRssFeed({ source, data, configs });

			results[channelId] = result;
		} catch (error) {
			logger.info(
				`Error processing source ${source.name}:`,
				(error as Error).message,
			);

			results[source.channelId] = {
				error: (error as Error).message,
			};
		}
	}

	logger.info('YouTube ingestor completed');

	return results;
}
