import {
	ConfigsYouTubeRssIngest,
	YouTubeRssEntry,
	YouTubeRssFeed,
} from './types';
import { TaskYouTubeRssIngest } from '@repo/payload/payload-types';
import { logger } from './utils';
import {
	createKalliopeVideo,
	findKalliopeVideoByMediaId,
	type VideoData,
} from '@repo/dal/content/videos/kalliope/queries';
import {
	createKalliopeMediaImage,
	uploadKalliopeImage,
} from '@repo/dal/content/videos/kalliope/uploadImage';
import {
	FileData,
	MediaImageData,
} from '@repo/dal/content/videos/kalliope/types';

type YouTubeSource = TaskYouTubeRssIngest['input']['sources'][number];

interface BuildVideoData {
	video: YouTubeRssEntry;
	source: YouTubeSource;
	id?: string;
	mediaImage?: MediaImageData;
	fileData?: FileData | null;
	configs: ConfigsYouTubeRssIngest;
}

const buildVideoData = ({
	id,
	video,
	source,
	fileData,
	mediaImage,
	configs,
}: BuildVideoData): VideoData => {
	const { link, title, author, published, updated } = video;

	const {
		kalliopeForecastKeywordId,
		kalliopeWeatherKeywordId,
		kalliopeCollectionId,
		kalliopePlaylistId,
		kalliopeInterestsId,
		kalliopeProviderId,
	} = configs;

	const { name, type } = source;

	let kalliopeKeywordId;

	if (type === 'forecast') {
		kalliopeKeywordId = kalliopeForecastKeywordId;
	} else if (type === 'weather') {
		kalliopeKeywordId = kalliopeWeatherKeywordId;
	} else {
		throw new Error(`Unknown source type: ${type}`);
	}

	const videoData: VideoData = {
		data: {
			type: 'node--video',
			attributes: {
				status: true,
				title,
				langcode: ['ko-KR'],
				field_teaser_title: title,
				field_alt_author: author.name,
				field_seo_title: title,
				field_sub_headline: title,
				field_canonical_url: link['@_href'],
				field_source_name: name,
				field_publish_date: published,
				field_ingest_date: updated,
				field_media_id: video['yt:videoId'],
				field_mobile_teaser_headline: title,
				field_seo_url: video['yt:videoId'].toLowerCase(),
				field_video_source_url: link['@_href'],
				field_video_variants: [`youtube|${link['@_href']}`],
				field_format_urls: [`youtube|${link['@_href']}`],
				field_meta_tags: {
					value: {
						keywords: name,
					},
				},
				body: video['media:group']['media:description'],
			},
			relationships: {
				field_pcoll: {
					data: {
						type: 'taxonomy_term--collection',
						id: kalliopeCollectionId,
					},
				},
				field_provider: {
					data: {
						type: 'taxonomy_term--provider',
						id: kalliopeProviderId,
					},
				},
				field_interests: {
					data: {
						type: 'taxonomy_term--interests',
						id: kalliopeInterestsId,
					},
				},
				field_playlists: {
					data: {
						type: 'taxonomy_term--playlists',
						id: kalliopePlaylistId,
					},
				},
				field_alternate_video_thumb: {
					data: {
						type: 'media--image',
					},
				},
				field_keywords: {
					data: {
						type: 'taxonomy_term--tags',
						id: kalliopeKeywordId,
					},
				},
			},
		},
	};

	if (fileData) {
		videoData.data.attributes.field_video_thumb_url =
			fileData.attributes.uri.dsx_image_cut;
	}

	if (mediaImage && videoData.data.relationships) {
		videoData.data.relationships.field_alternate_video_thumb = {
			data: {
				type: 'media--image',
				id: mediaImage.id,
			},
		};
	}

	if (id) {
		videoData.data.id = id;
	}

	return videoData;
};

async function processEntry(
	video: YouTubeRssEntry,
	source: ProcessRssFeed['source'],
	configs: ConfigsYouTubeRssIngest,
) {
	logger.info(`Processing video: ${video.title} (${video['yt:videoId']})`);

	logger.info(
		`Checking for existing video by media ID: ${video['yt:videoId']}`,
	);
	const existingVideo = await findKalliopeVideoByMediaId(video['yt:videoId']);

	/**
	 * PATCH method is not allowed for updating existing videos.
	 * DELETE method throws an 'Access denied | Kalliope" error.
	 *
	 * So we throw an error if the video already exists in the CMS.
	 */
	if (existingVideo) {
		logger.info(
			`Video with media ID ${video['yt:videoId']} already exists in CMS`,
		);
		return;
	}

	logger.info(
		`Creating new video in CMS with media ID: ${video['yt:videoId']}`,
	);

	const fileData = await uploadKalliopeImage({
		url: video['media:group']['media:thumbnail']['@_url'],
		slug: video['yt:videoId'],
	});

	let mediaImage: MediaImageData | undefined;

	if (fileData) {
		mediaImage = await createKalliopeMediaImage(fileData);
	}

	const videoData = buildVideoData({
		video,
		source,
		mediaImage,
		fileData,
		configs,
	});

	const response = await createKalliopeVideo({ data: videoData });

	logger.info(`Video ${video['yt:videoId']} created successfully in CMS`);

	return response;
}

function filterVideos(
	entries: YouTubeRssEntry[],
	source: YouTubeSource,
): YouTubeRssEntry[] {
	const { inclusionKeywords, exclusionKeywords } = source;
	const videos: YouTubeRssEntry[] = [];

	for (const entry of entries) {
		if (Array.isArray(exclusionKeywords)) {
			const excludedKeywords = (exclusionKeywords as string[]).filter(
				(keyword) => entry.title.includes(keyword),
			);

			if (excludedKeywords.length) {
				logger.info(
					`Excluding video "${entry.title}" due to exclusion keywords: ${excludedKeywords}`,
				);
				continue;
			}
		}

		if (Array.isArray(inclusionKeywords)) {
			const includedKeywords = (inclusionKeywords as string[]).filter(
				(keyword) => entry.title.includes(keyword),
			);

			if (!includedKeywords.length) {
				logger.info(
					`Excluding video "${entry.title}" due to lack of inclusion keywords`,
				);
				continue;
			}
		}

		logger.info(`Including video "${entry.title}"`);
		videos.push(entry);
	}

	return videos;
}

interface ProcessRssFeed {
	source: YouTubeSource;
	data: YouTubeRssFeed;
	configs: ConfigsYouTubeRssIngest;
}

export async function processRssFeed({
	data,
	source,
	configs,
}: ProcessRssFeed) {
	const {
		feed: { entry },
	} = data;

	const successIds: string[] = [];
	const failedIds: string[] = [];

	const videos = filterVideos(entry, source);

	// Using a Promise.all causes a 504 error when multiple videos are
	// being added in parallel.
	// So we process each video sequentially.
	for (let i = 0; i < videos.length; i += 1) {
		const video = videos[i]!;
		try {
			const result = await processEntry(video, source, configs);
			if (result?.data) {
				successIds.push(video['yt:videoId']);
			} else if (result?.errors) {
				failedIds.push(video['yt:videoId']);
			}
		} catch (error) {
			logger.info(
				`Error processing video ${video['yt:videoId']}:`,
				(error as Error).message,
			);
			failedIds.push(video['yt:videoId']);
		}
	}

	return {
		successIds,
		failedIds,
	};
}
