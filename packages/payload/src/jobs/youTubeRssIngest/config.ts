import { TaskConfig } from 'payload';
import { handler } from './handler';
import { logger } from './utils';

export const youTubeRssIngest: TaskConfig<'youTubeRssIngest'> = {
	retries: 2,
	slug: 'youTubeRssIngest',
	inputSchema: [
		{
			name: 'configs',
			type: 'group',
			fields: [
				{
					name: 'kalliopeCollectionId',
					type: 'text',
					required: true,
				},
				{
					name: 'kalliopePlaylistId',
					type: 'text',
					required: true,
				},
				{
					name: 'kalliopeInterestsId',
					type: 'text',
					required: true,
				},
				{
					name: 'kalliopeProviderId',
					type: 'text',
					required: true,
				},
				{
					name: 'kalliopeForecastKeywordId',
					type: 'text',
					required: true,
				},
				{
					name: 'kalliopeWeatherKeywordId',
					type: 'text',
					required: true,
				},
			],
		},
		{
			name: 'sources',
			type: 'array',
			fields: [
				{ name: 'name', type: 'text', required: true },
				{ name: 'channelId', type: 'text', required: true },
				{
					name: 'type',
					type: 'select',
					options: ['forecast', 'weather'],
					required: true,
				},
				{ name: 'inclusionKeywords', type: 'json' },
				{ name: 'exclusionKeywords', type: 'json' },
			],
			required: true,
		},
	],
	outputSchema: [
		{
			type: 'json',
			name: 'result',
		},
	],
	onSuccess: async () => {
		logger.info('youTubeRssIngest task completed');
	},
	onFail: async () => {
		logger.info('youTubeRssIngest task failed:');
	},
	handler,
};
