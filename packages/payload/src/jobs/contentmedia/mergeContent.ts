import { ContentQuery } from '@repo/payload/payload-types';
import { getContentType } from '@repo/payload/utils/blockGuards';
import type { ContentMediaResponse } from '@repo/dal/content/media/types';
import { PayloadRequest } from 'payload';

type MergedContentItem = NonNullable<
	NonNullable<ContentQuery['mergedContent']>[number]
>;

interface MergedContentProps {
	newContent: ContentMediaResponse;
	req: PayloadRequest;
	queryId: string;
	existingContent: NonNullable<ContentQuery['mergedContent']>;
}
export const mergeContent = ({
	newContent,
	req,
	queryId,
	existingContent,
}: MergedContentProps): NonNullable<ContentQuery['mergedContent']> => {
	// Create a map of existing content items for this query by ID for quick lookup
	const existingItemsMap = new Map();

	// Filter out items from other queries (keep them unchanged)
	const otherQueriesContent = existingContent.filter((item) => {
		if (item.sourceQueryID !== queryId) {
			return true; // Keep items from other queries
		}

		// Store items from this query in the map for later comparison
		// Ensure we're using string IDs for consistent comparison
		existingItemsMap.set(String(item.id), item);
		return false; // Filter out items from this query (we'll add back the ones that still exist)
	});

	// Track IDs we've processed to avoid duplicates
	const processedIds = new Set();

	// Process new content
	const updatedItems: MergedContentItem[] = [];

	for (const item of newContent) {
		const { id, assetId, caption, src, url, assetName, headline, teaserTitle } =
			item;

		// Merge properties with fallbacks
		const mergedId = id ?? assetId;

		if (!mergedId) {
			req.payload.logger.debug(
				`[getCMAssetHandler] Item missing ID: ${JSON.stringify(item)}`,
			);
			continue; // Skip items without ID
		}

		// Convert ID to string for consistent comparison
		const stringId = String(mergedId);

		// Skip if we've already processed this ID
		if (processedIds.has(stringId)) {
			req.payload.logger.debug(
				`[getCMAssetHandler] Skipping duplicate item with ID: ${stringId}`,
			);
			continue;
		}

		// Mark this ID as processed
		processedIds.add(stringId);

		const mergedTitle = headline ?? teaserTitle;
		const mergedUrl = url ?? assetName;
		const mergedDescription = caption ?? '';

		// Validate required fields
		if (!mergedTitle || !mergedUrl || !src) {
			req.payload.logger.debug(
				`[getCMAssetHandler] Required metadata missing for content with ID: ${stringId}`,
			);
			continue; // Skip invalid items
		}
		const contentType = getContentType(item);

		// Check if this item already exists in our content
		const existingItem = existingItemsMap.get(stringId);

		if (existingItem) {
			// Update existing item with new data but preserve user customizations
			updatedItems.push({
				...existingItem, // Keep existing properties (including pinned status and overrides)
				// Update with new data (but don't override user customizations)
				title: existingItem.overrideTitle || mergedTitle,
				description: existingItem.overrideDescription || mergedDescription,
				thumbnail: existingItem.overrideThumbnail || src,
				url: mergedUrl,
				// These fields always get updated
				source: 'drupal',
				sourceQueryID: queryId,
			});
		} else {
			// Add new item
			updatedItems.push({
				id: stringId,
				source: 'drupal',
				title: mergedTitle,
				overrideTitle: '',
				description: mergedDescription,
				overrideDescription: '',
				thumbnail: src,
				overrideThumbnail: '',
				url: mergedUrl,
				sourceQueryID: queryId,
				pinned: false,
				contentType: contentType,
			});
		}
	}

	return [...otherQueriesContent, ...updatedItems];
};
