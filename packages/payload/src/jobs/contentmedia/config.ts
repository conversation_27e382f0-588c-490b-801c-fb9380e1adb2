import { TaskConfig } from 'payload';
import { getCMAssetsHandler } from './task';

export const getCMAssetByID: TaskConfig = {
	retries: 2,
	slug: 'getCMAssetByID',
	inputSchema: [
		{ name: 'kalliopeCMQID', type: 'text', required: true },
		{ name: 'queryDocID', type: 'text', required: true },
		{ name: 'lastSynced', type: 'text', required: true },
	],
	outputSchema: [],
	onSuccess: async () => {
		console.log('getCMAssetById task completed');
	},
	handler: getCMAssetsHandler,
};
