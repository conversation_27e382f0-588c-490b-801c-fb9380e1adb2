import type { TaskHandlerArgs, TaskHandlerResult } from 'payload';
import { getMediaAssetsById } from '@repo/dal/content/media/getMediaAssetsById';
import { mergeContent } from './mergeContent';
import { hasLength } from '@repo/utils/hasLength';
import {
	findQueryByKalliopeIdAndLock,
	handleTaskError,
	unlockAndCompleteQuery,
	updateQueryWithContent,
} from '@repo/payload/utils/jobs/queryHandlers';

/**
 * This handler requires a few loops to get the job done
 * 1. Get the query doc by ID
 * 2. Find the query to update
 * 3. Update the query to in-progress
 * 4. Get the assets from the CM API
 * 5. Merge the assets with the query
 * 6. Update the query doc with the new assets
 *
 * NOTE: We perform mutation of the query doc in place for performance
 */
export const getCMAssetsHandler = async ({
	input,
	job,
	req,
}: TaskHandlerArgs<'getCMAssetByID'>): Promise<
	TaskHandlerResult<'getCMAssetByID'>
> => {
	const { queryDocID, kalliopeCMQID } = input;
	const payload = req.payload;

	if (!payload) {
		return {
			state: 'failed',
			errorMessage: 'Payload not found',
		};
	}

	try {
		// 1. Find the query document and the specific query to update by Kalliope ID
		const { queryDoc, queryToUpdate, existingContent } =
			await findQueryByKalliopeIdAndLock({
				payload,
				req,
				queryDocID,
				kalliopeCMQID,
				jobId: job.id,
			});

		if (!queryDoc || !queryToUpdate) {
			return { state: 'succeeded', output: [] };
		}

		// 2. Get media assets from the CM API
		const response = await getMediaAssetsById(kalliopeCMQID);

		if (!hasLength(response)) {
			payload.logger.debug(
				`No response from CM API for query with kalliopeCMQSID ${kalliopeCMQID}`,
			);

			await unlockAndCompleteQuery({
				payload,
				queryDoc,
				queryToUpdate,
				queries: queryDoc.queries,
				req,
			});

			return { state: 'succeeded', output: [] };
		}

		if (!queryToUpdate.id) {
			payload.logger.error(`Content Query with ID ${queryDocID} has no ID`);
			return {
				state: 'failed',
				errorMessage: `Content Query with ID ${queryDocID} has no ID`,
			};
		}

		// 3. Process the media assets using the task-specific mergeContent function
		const newContent = mergeContent({
			newContent: response,
			req,
			queryId: queryToUpdate.id,
			existingContent,
		});

		// 4. Update the query with the new content
		await updateQueryWithContent({
			payload,
			queryDoc,
			queryToUpdate,
			newContent,
			existingContent,
			queryId: queryToUpdate.id,
			req,
		});

		return { state: 'succeeded', output: [] };
	} catch (e) {
		const error = e as Error;

		// Handle error and unlock query
		await handleTaskError({
			payload,
			queryDocID,
			queryId: kalliopeCMQID,
			jobId: job.id,
			error,
			isKalliopeId: true,
			req,
		});

		return {
			state: 'failed',
			errorMessage: `Job ID:${job.id} failed trying to process query with kalliopeCMQID: ${kalliopeCMQID} \n Error: ${error.message}`,
		};
	}
};
