import { TaskConfig } from 'payload';
import { recommendArticlesHandler } from './task';

export const recommendArticles: TaskConfig = {
	retries: 2,
	slug: 'aiRecommendArticles',
	inputSchema: [
		{ name: 'articleContent', type: 'text', required: true },
		{ name: 'count', type: 'number', required: true },
	],
	outputSchema: [{ name: 'recommendations', type: 'json', required: true }],
	onSuccess: async () => {
		console.log('AI recommend articles task completed');
	},
	handler: recommendArticlesHandler,
};
