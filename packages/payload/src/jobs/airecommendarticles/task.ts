import { TaskHandlerArgs, TaskHandlerResult } from 'payload';
import { createLogger } from '@repo/logger';
import { LiteLLMProvider } from '../../plugins/ai/providers/LiteLLMProvider';

const logger = createLogger('ai-recommend-articles-job');

export const recommendArticlesHandler = async ({
	input,
	job,
	req,
}: TaskHandlerArgs<'aiRecommendArticles'>): Promise<
	TaskHandlerResult<'aiRecommendArticles'>
> => {
	const { articleContent, count } = input;

	logger.info(`Running recommend articles job for job ${job.id}`);

	try {
		// Use the appropriate provider
		const aiProvider = new LiteLLMProvider(req.payload);

		// Call the provider's recommend articles method directly
		const result = await aiProvider.recommendArticles({
			articleContent,
			count,
		});

		return {
			state: 'succeeded',
			output: {
				recommendations: result.articleRecommendations.recommendations,
			},
		};
	} catch (error) {
		logger.error(`Recommend articles job failed: ${error}`);
		return {
			state: 'failed',
			errorMessage:
				error instanceof Error ? error.message : 'Unknown error occurred',
		};
	}
};
