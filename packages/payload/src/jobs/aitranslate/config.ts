import { TaskConfig } from 'payload';
import { translate<PERSON><PERSON><PERSON> } from './task';

export const translate: TaskConfig = {
	retries: 2,
	slug: 'aiTranslate',
	inputSchema: [
		{ name: 'text', type: 'text', required: true },
		{ name: 'sourceLanguage', type: 'text', required: true },
		{ name: 'targetLanguage', type: 'text', required: true },
		{ name: 'options', type: 'json', required: false },
	],
	outputSchema: [
		{ name: 'translatedText', type: 'text', required: true },
		{ name: 'detectedSourceLanguage', type: 'text', required: false },
	],
	onSuccess: async () => {
		console.log('AI translate task completed');
	},
	handler: translateHand<PERSON>,
};
