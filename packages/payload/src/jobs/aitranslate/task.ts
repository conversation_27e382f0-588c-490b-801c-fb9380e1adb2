import { TaskH<PERSON>lerArgs, TaskHandlerResult } from 'payload';
import { createLogger } from '@repo/logger';
import { MercuryProvider } from '../../plugins/ai/providers/MercuryProvider';
import { SupportedLanguage } from '../../plugins/ai/types/AIProvider';

const logger = createLogger('ai-translate-job');

export const translateHandler = async ({
	input,
	job,
	req,
}: TaskHandlerArgs<'aiTranslate'>): Promise<
	TaskHandlerResult<'aiTranslate'>
> => {
	const { text, sourceLanguage, targetLanguage, options } = input;

	logger.info(`Running translate job for job ${job.id}`);

	try {
		// Use the appropriate provider
		const aiProvider = new MercuryProvider(req.payload);

		// Call the provider's translate method directly
		const result = await aiProvider.translate(
			text,
			sourceLanguage as SupportedLanguage,
			targetLanguage as SupportedLanguage,
			options as Record<string, unknown> | undefined,
		);

		return {
			state: 'succeeded',
			output: {
				translatedText: result.translation.translatedText,
				detectedSourceLanguage: result.translation.detectedSourceLanguage,
			},
		};
	} catch (error) {
		logger.error(`Translate job failed: ${error}`);
		return {
			state: 'failed',
			errorMessage:
				error instanceof Error ? error.message : 'Unknown error occurred',
		};
	}
};
