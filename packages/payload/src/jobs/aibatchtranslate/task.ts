import { TaskHandlerArgs, TaskHandlerResult } from 'payload';
import { createLogger } from '@repo/logger';
import { MercuryProvider } from '../../plugins/ai/providers/MercuryProvider';
import { SupportedLanguage } from '../../plugins/ai/types/AIProvider';

const logger = createLogger('ai-batch-translate-job');

export const batchTranslateHandler = async ({
	input,
	job,
	req,
}: TaskHandlerArgs<'aiBatchTranslate'>): Promise<
	TaskHandlerResult<'aiBatchTranslate'>
> => {
	const { text, sourceLanguage, targetLanguages, options } = input;

	logger.info(`Running batch translate job for job ${job.id}`);

	try {
		// Use the appropriate provider
		const aiProvider = new MercuryProvider(req.payload);

		// Extract language codes from the targetLanguages array
		// targetLanguages is now a JSON field, so we need to handle it as an array of objects
		const languages = Array.isArray(targetLanguages)
			? targetLanguages.map((lang: unknown) =>
					typeof lang === 'string' ? lang : (lang as { value: string }).value,
				)
			: [];

		// Call the provider's batch translate method directly
		const result = await aiProvider.batchTranslate(
			text,
			sourceLanguage as SupportedLanguage,
			languages as SupportedLanguage[],
			options as Record<string, unknown> | undefined,
		);

		return {
			state: 'succeeded',
			output: {
				translations: result.batchTranslation.translations,
			},
		};
	} catch (error) {
		logger.error(`Batch translate job failed: ${error}`);
		return {
			state: 'failed',
			errorMessage:
				error instanceof Error ? error.message : 'Unknown error occurred',
		};
	}
};
