import { TaskConfig } from 'payload';
import { batchTranslateHandler } from './task';

export const batchTranslate: TaskConfig = {
	retries: 2,
	slug: 'aiBatchTranslate',
	inputSchema: [
		{ name: 'text', type: 'text', required: true },
		{ name: 'sourceLanguage', type: 'text', required: true },
		{ name: 'targetLanguages', type: 'json', required: true },
		{ name: 'options', type: 'json', required: false },
	],
	outputSchema: [{ name: 'translations', type: 'json', required: true }],
	onSuccess: async () => {
		console.log('AI batch translate task completed');
	},
	handler: batchTranslateHandler,
};
