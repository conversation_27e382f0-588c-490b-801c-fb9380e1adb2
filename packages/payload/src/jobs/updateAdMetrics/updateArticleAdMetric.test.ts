import { describe, test, expect, vi } from 'vitest';
import { updateArticleAdMetric } from './updateArticleAdMetric';

describe('updateArticleAdMetric', () => {
	// Mock payload
	const mockPayload = {
		findByID: vi.fn(),
		update: vi.fn(),
		jobs: {
			queue: vi.fn(),
		},
		logger: {
			debug: vi.fn(),
			error: vi.fn(),
		},
	};

	// Mock request object
	const mockReq = {
		payload: mockPayload,
	};

	test('should update articles with the given ad metric', async () => {
		// Setup
		const input = {
			categoryId: 'category-123',
			adMetricId: 'admetric-456',
			articleIds: ['article-1', 'article-2'],
			hasMore: false,
			skipIds: [],
			totalCount: 2,
		};

		// Mock article data
		const mockArticle1 = {
			id: 'article-1',
			coreMetadata: {
				entitlements: ['free'],
				createdBy: 'user1',
				updatedBy: 'user2',
			},
		};

		const mockArticle2 = {
			id: 'article-2',
			coreMetadata: {
				entitlements: ['premium'],
				createdBy: 'user3',
				updatedBy: 'user4',
			},
		};

		// Setup findByID to return different articles
		mockPayload.findByID.mockImplementation((options) => {
			if (options.id === 'article-1') return mockArticle1;
			if (options.id === 'article-2') return mockArticle2;
			return null;
		});

		// Execute
		const result = await updateArticleAdMetric({
			input,
			// @ts-expect-error - Mocking req object for testing
			req: mockReq,
			// @ts-expect-error - Mocking payload object for testing
			payload: mockPayload,
		});

		// Verify
		expect(mockPayload.findByID).toHaveBeenCalledTimes(2);
		expect(mockPayload.update).toHaveBeenCalledTimes(2);

		// Check that update was called with the correct arguments
		const firstCallArgs = mockPayload.update.mock.calls[0]?.[0];
		const secondCallArgs = mockPayload.update.mock.calls[1]?.[0];

		// Ensure the calls were made
		expect(firstCallArgs).toBeDefined();
		expect(secondCallArgs).toBeDefined();

		// Verify first article update
		expect(firstCallArgs.collection).toBe('articles');
		expect(firstCallArgs.id).toBe('article-1');
		expect(firstCallArgs.context).toEqual({ isSystemUpdate: true });
		expect(firstCallArgs.req).toBe(mockReq);
		expect(firstCallArgs.data.coreMetadata).toEqual({
			entitlements: ['free'],
			createdBy: 'user1',
			updatedBy: 'user2',
			adMetric: 'admetric-456',
			adMetricLock: true,
		});

		// Verify second article update
		expect(secondCallArgs.collection).toBe('articles');
		expect(secondCallArgs.id).toBe('article-2');
		expect(secondCallArgs.context).toEqual({ isSystemUpdate: true });
		expect(secondCallArgs.req).toBe(mockReq);
		expect(secondCallArgs.data.coreMetadata).toEqual({
			entitlements: ['premium'],
			createdBy: 'user3',
			updatedBy: 'user4',
			adMetric: 'admetric-456',
			adMetricLock: true,
		});

		expect(result).toEqual({
			updatedCount: 2,
			hasMore: false,
			message: expect.stringContaining('Successfully updated 2 articles'),
		});
	});

	test('should queue another job if hasMore is true', async () => {
		// Setup
		const input = {
			categoryId: 'category-123',
			adMetricId: 'admetric-456',
			articleIds: ['article-1', 'article-2'],
			hasMore: true, // More articles to process
			skipIds: [],
			totalCount: 10, // Total of 10 articles
		};

		// Mock article data
		const mockArticle1 = {
			id: 'article-1',
			coreMetadata: {
				entitlements: ['free'],
				createdBy: 'user1',
				updatedBy: 'user2',
			},
		};

		const mockArticle2 = {
			id: 'article-2',
			coreMetadata: {
				entitlements: ['premium'],
				createdBy: 'user3',
				updatedBy: 'user4',
			},
		};

		// Reset mocks
		mockPayload.findByID.mockReset();
		mockPayload.update.mockReset();
		mockPayload.jobs.queue.mockReset();

		// Setup findByID to return different articles
		mockPayload.findByID.mockImplementation((options) => {
			if (options.id === 'article-1') return mockArticle1;
			if (options.id === 'article-2') return mockArticle2;
			return null;
		});

		// Execute
		await updateArticleAdMetric({
			input,
			// @ts-expect-error - Mocking req object for testing
			req: mockReq,
			// @ts-expect-error - Mocking payload object for testing
			payload: mockPayload,
		});

		// Verify
		expect(mockPayload.jobs.queue).toHaveBeenCalledWith({
			task: 'updateAdMetrics',
			req: mockReq,
			input: expect.objectContaining({
				categoryId: 'category-123',
				adMetricId: 'admetric-456',
				skipIds: expect.arrayContaining([
					{ value: 'article-1' },
					{ value: 'article-2' },
				]),
			}),
			queue: 'ad-metrics',
		});
	});
});
