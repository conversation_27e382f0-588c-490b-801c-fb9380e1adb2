import type { PayloadRequest } from 'payload';
import type { TaskUpdateAdMetrics } from '@repo/payload/payload-types';
import { hasLength } from '@repo/utils/hasLength';
import {
	getLocale,
	validLocales,
} from '@repo/payload/utils/getSupportedLocale';

type WorkflowStepInput = {
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	input: TaskUpdateAdMetrics['input'] & { [key: string]: any };
	req: PayloadRequest;
	payload: PayloadRequest['payload'];
	next: (input: unknown) => WorkflowStepOutput;
};

type WorkflowStepOutput = TaskUpdateAdMetrics['input'] & {
	articleIds: string[];
	hasMore: boolean;
	totalCount: number;
};

export const findArticlesByCategory = async ({
	input,
	req,
	payload,
	next,
}: WorkflowStepInput): Promise<WorkflowStepOutput> => {
	// Extract and provide defaults for input properties
	const categoryId = input.categoryId;
	const batchSize = input.batchSize || 100;
	const skipIds = input.skipIds || ([] as { value: string }[]);
	// The locale might be undefined, but getLocale handles that case
	const locale = getLocale(input.locale || undefined);

	payload.logger.debug(
		`Finding articles with category ${categoryId} (batch size: ${batchSize}, skipping ${skipIds.length} articles, locale: ${locale})`,
	);

	try {
		// Build a query that checks for the category in all locales
		const orConditions = validLocales.map((localeCode) => ({
			[`category.${localeCode}.value`]: {
				equals: categoryId,
			},
		}));

		// Find articles with this category that haven't been processed yet
		const articles = await payload.find({
			collection: 'articles',
			where: {
				and: [
					{
						or: orConditions,
					},
					{
						id: {
							not_in: Array.isArray(skipIds)
								? skipIds.map((id) => (typeof id === 'string' ? id : id.value))
								: [],
						},
					},
				],
			},
			limit: batchSize,
			depth: 0, // We don't need related data
			req,
		});

		if (!hasLength(articles.docs)) {
			payload.logger.debug(
				`No more articles found with category ${categoryId} to update`,
			);

			return {
				...input,
				articleIds: [],
				hasMore: false,
				totalCount: 0,
			};
		}

		// Check if there are more articles to process
		const safeSkipIds = Array.isArray(skipIds) ? skipIds : [];
		const hasMore =
			articles.totalDocs > safeSkipIds.length + articles.docs.length;

		// Extract article IDs
		const articleIds = articles.docs.map((article) => article.id);

		payload.logger.debug(
			`Found ${articleIds.length} articles with category ${categoryId} to update`,
		);

		// Pass the article IDs to the next step
		return next({
			...input,
			articleIds,
			hasMore,
			totalCount: articles.totalDocs,
		});
	} catch (error) {
		payload.logger.error(
			`Error finding articles with category ${categoryId}: ${error}`,
		);
		throw error;
	}
};
