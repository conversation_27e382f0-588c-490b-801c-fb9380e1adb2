import { PayloadRequest } from 'payload';
import { hasLength } from '@repo/utils/hasLength';
import type { TaskUpdateAdMetrics } from '@repo/payload/payload-types';

type WorkflowStepInput = {
	input: TaskUpdateAdMetrics['input'] & {
		articleIds: string[];
		hasMore: boolean;
		totalCount: number;
	};
	req: PayloadRequest;
	payload: PayloadRequest['payload'];
};

type WorkflowStepOutput = {
	updatedCount: number;
	hasMore: boolean;
	message: string;
};

export const updateArticleAdMetric = async ({
	input,
	req,
	payload,
}: WorkflowStepInput): Promise<WorkflowStepOutput> => {
	const { categoryId, adMetricId, articleIds, hasMore, totalCount = 0 } = input;
	const skipIds = input.skipIds || ([] as { value: string }[]);

	if (!hasLength(articleIds)) {
		return {
			updatedCount: 0,
			hasMore: false,
			message: `No articles found with category ${categoryId} to update`,
		};
	}

	payload.logger.debug(
		`Updating ${articleIds.length} articles with ad metric ${adMetricId || 'null'}`,
	);

	let updatedCount = 0;
	// Create a new array with the existing skipIds
	const processedIds = Array.isArray(skipIds)
		? [...skipIds]
		: ([] as { value: string }[]);

	// Process each article
	for (const articleId of articleIds) {
		processedIds.push({ value: articleId });

		try {
			// First, get the current article data
			const article = await payload.findByID({
				collection: 'articles',
				id: articleId,
				depth: 0,
			});

			// Check if the article has adMetricLock set to false (manual mode)
			// If it's in manual mode, we should not update the ad metric
			const isManualMode = article.coreMetadata?.adMetricLock === false;

			if (!isManualMode) {
				// Only update if the article is in auto mode (adMetricLock is true or undefined)
				await payload.update({
					collection: 'articles',
					id: articleId,
					data: {
						coreMetadata: {
							...(article.coreMetadata || {}),
							adMetric: adMetricId || null,
							adMetricLock: true, // Keep it in auto mode
						},
					},
					context: {
						isSystemUpdate: true, // Skip hooks to prevent infinite loops
					},
					req,
				});

				updatedCount++;
			} else {
				// Log that we're skipping this article because it's in manual mode
				payload.logger.debug(
					`Skipping article ${articleId} because it's in manual mode (adMetricLock is false)`,
				);
			}
		} catch (articleError) {
			payload.logger.error(
				`Error updating article ${articleId}: ${articleError}`,
			);
			// Continue with other articles even if one fails
		}
	}

	// If there are more articles to process, queue another job
	if (hasMore) {
		await payload.jobs.queue({
			task: 'updateAdMetrics',
			req,
			input: {
				categoryId,
				adMetricId,
				batchSize: articleIds.length, // Use the same batch size
				skipIds: processedIds,
			},
			queue: 'ad-metrics',
		});
	}

	return {
		updatedCount,
		hasMore,
		message: `Successfully updated ${updatedCount} articles with category ${categoryId} (${processedIds.length}/${totalCount} processed)`,
	};
};
