import { TaskConfig } from 'payload';
import { updateAdMetricsHandler } from './task';

export const updateAdMetrics: TaskConfig = {
	retries: 2,
	slug: 'updateAdMetrics',
	inputSchema: [
		{ name: 'categoryId', type: 'text', required: true },
		{ name: 'adMetricId', type: 'text', required: false }, // Can be null if removing ad metric
		{ name: 'batchSize', type: 'number', required: false, defaultValue: 100 },
		{
			name: 'skipIds',
			type: 'array',
			required: false,
			defaultValue: [],
			fields: [{ name: 'value', type: 'text', required: true }],
		},
		{ name: 'locale', type: 'text', required: false },
	],
	outputSchema: [
		{ name: 'updatedCount', type: 'number' },
		{ name: 'hasMore', type: 'checkbox' }, // Use checkbox instead of boolean
		{ name: 'message', type: 'text' },
	],
	onSuccess: async () => {
		console.log('updateAdMetrics task completed');
	},
	handler: updateAdMetricsHandler,
};
