import { TaskHandlerArgs, TaskHandlerResult } from 'payload';
import { findArticlesByCategory } from './findArticlesByCategory';
import { updateArticleAdMetric } from './updateArticleAdMetric';
import type { TaskUpdateAdMetrics } from '@repo/payload/payload-types';

// Define the WorkflowStepOutput type to match the one in findArticlesByCategory.ts
type WorkflowStepOutput = TaskUpdateAdMetrics['input'] & {
	articleIds: string[];
	hasMore: boolean;
	totalCount: number;
};

export const updateAdMetricsHandler = async ({
	input,
	job,
	req,
}: TaskHandlerArgs<'updateAdMetrics'>): Promise<
	TaskHandlerResult<'updateAdMetrics'>
> => {
	const { categoryId, adMetricId } = input;
	const payload = req.payload;

	if (!payload) {
		return {
			state: 'failed',
			errorMessage: 'Payload not found',
		};
	}

	try {
		payload.logger.debug(
			`Starting updateAdMetricsHandler for categoryId: ${categoryId}, adMetricId: ${adMetricId || 'null'}`,
		);

		// Find articles with the specified category
		const findResult = await findArticlesByCategory({
			input,
			req,
			payload,
			next: (nextInput) => nextInput as WorkflowStepOutput,
		});

		// If no articles found, return early
		if (!findResult.articleIds || findResult.articleIds.length === 0) {
			return {
				state: 'succeeded',
				output: {
					updatedCount: 0,
					hasMore: false,
					message: `No articles found with category ${categoryId} to update`,
				},
			};
		}

		// Update articles with the new ad metric
		const updateResult = await updateArticleAdMetric({
			input: findResult,
			req,
			payload,
		});

		return {
			state: 'succeeded',
			output: {
				updatedCount: updateResult.updatedCount,
				hasMore: Boolean(updateResult.hasMore), // Ensure it's a boolean value
				message: updateResult.message,
			},
		};
	} catch (e) {
		const error = e as Error;

		payload.logger.error(`Error in updateAdMetricsHandler: ${error.message}`);

		return {
			state: 'failed',
			errorMessage: `Job ID:${job.id} failed: ${error.message}`,
		};
	}
};
