import { describe, test, expect, vi } from 'vitest';
import { updateAdMetricsHandler } from './task';

describe('updateAdMetricsHandler', () => {
	// Mock payload with all required methods
	const mockPayload = {
		find: vi.fn().mockResolvedValue({
			docs: [{ id: 'article-1' }, { id: 'article-2' }],
			totalDocs: 2,
		}),
		findByID: vi.fn().mockImplementation((options) => {
			if (options.id === 'article-1') {
				return {
					id: 'article-1',
					coreMetadata: {
						entitlements: ['free'],
						createdBy: 'user1',
						updatedBy: 'user2',
					},
				};
			}
			if (options.id === 'article-2') {
				return {
					id: 'article-2',
					coreMetadata: {
						entitlements: ['premium'],
						createdBy: 'user3',
						updatedBy: 'user4',
					},
				};
			}
			return null;
		}),
		update: vi.fn().mockImplementation(() => {
			return Promise.resolve({ id: 'updated-article' });
		}),
		jobs: {
			queue: vi.fn(),
		},
		logger: {
			debug: vi.fn(),
			error: vi.fn(),
		},
	};

	// Mock request object
	const mockReq = {
		payload: mockPayload,
	};

	// Mock job
	const mockJob = {
		id: 'job-123',
	};

	test('should return succeeded state with result from input', async () => {
		// Setup
		const input = {
			categoryId: 'category-123',
			adMetricId: 'admetric-456',
		};

		// Execute
		const result = await updateAdMetricsHandler({
			input,
			// @ts-expect-error - Mocking job object for testing
			job: mockJob,
			// @ts-expect-error - Mocking req object for testing
			req: mockReq,
		});

		// Verify
		expect(result).toEqual({
			state: 'succeeded',
			output: {
				updatedCount: 2,
				hasMore: false,
				message: expect.stringContaining('Successfully updated 2 articles'),
			},
		});
	});

	test('should return failed state if payload is not found', async () => {
		// Setup
		const input = {
			categoryId: 'category-123',
			adMetricId: 'admetric-456',
		};

		// Execute
		const result = await updateAdMetricsHandler({
			input,
			// @ts-expect-error - Mocking job object for testing
			job: mockJob,
			// @ts-expect-error - Mocking req object for testing
			req: { payload: null },
		});

		// Verify
		expect(result).toEqual({
			state: 'failed',
			errorMessage: 'Payload not found',
		});
	});
});
