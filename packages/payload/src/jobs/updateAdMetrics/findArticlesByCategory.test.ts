import { describe, test, expect, vi } from 'vitest';
import { findArticlesByCategory } from './findArticlesByCategory';

// Mock the getSupportedLocale module
vi.mock('@repo/payload/utils/getSupportedLocale', () => ({
	getLocale: vi.fn().mockReturnValue('en-US'),
	validLocales: ['en-US', 'es-US', 'fr-FR', 'de-DE', 'gls'],
}));

describe('findArticlesByCategory', () => {
	// Mock payload
	const mockPayload = {
		find: vi.fn(),
		logger: {
			debug: vi.fn(),
			error: vi.fn(),
		},
	};

	// Mock next function
	const mockNext = vi.fn();

	test('should find articles with the given category', async () => {
		// Setup
		const mockArticles = {
			docs: [
				{ id: 'article-1', title: 'Article 1' },
				{ id: 'article-2', title: 'Article 2' },
			],
			totalDocs: 2,
		};

		mockPayload.find.mockResolvedValue(mockArticles);

		const input = {
			categoryId: 'category-123',
			batchSize: 100,
			skipIds: [],
			locale: 'en-US',
		};

		// Execute
		await findArticlesByCategory({
			input,
			// @ts-expect-error - Mocking payload object for testing
			payload: mockPayload,
			// @ts-expect-error - Mocking req object for testing
			req: { payload: mockPayload },
			next: mockNext,
		});

		// Verify
		expect(mockPayload.find).toHaveBeenCalledWith(
			expect.objectContaining({
				collection: 'articles',
				limit: 100,
				depth: 0,
				where: {
					and: expect.arrayContaining([
						{
							or: expect.arrayContaining([
								{ 'category.en-US.value': { equals: 'category-123' } },
							]),
						},
					]),
				},
			}),
		);

		expect(mockNext).toHaveBeenCalledWith({
			...input,
			articleIds: ['article-1', 'article-2'],
			hasMore: false,
			totalCount: 2,
		});
	});

	test('should return empty result if no articles found', async () => {
		// Setup
		const mockArticles = {
			docs: [],
			totalDocs: 0,
		};

		// Reset the mocks before this test
		mockPayload.find.mockReset();
		mockNext.mockReset();

		mockPayload.find.mockResolvedValue(mockArticles);

		const input = {
			categoryId: 'category-123',
			batchSize: 100,
			skipIds: [],
		};

		// Execute
		const result = await findArticlesByCategory({
			input,
			// @ts-expect-error - Mocking payload object for testing
			payload: mockPayload,
			// @ts-expect-error - Mocking req object for testing
			req: { payload: mockPayload },
			next: mockNext,
		});

		// Verify
		expect(result).toEqual(
			expect.objectContaining({
				articleIds: [],
				hasMore: false,
				totalCount: 0,
			}),
		);

		// Next should not be called
		expect(mockNext).not.toHaveBeenCalled();
	});
});
