# Ad Metrics Update Job

## Overview

The Ad Metrics Update Job is responsible for updating articles when a category's ad metric changes. This job ensures that all articles associated with a specific category have their ad metrics updated to match the category's ad metric, maintaining consistency across the content.

## Job Flow

```mermaid
flowchart TD
    Start[Category Ad Metric Changed] --> Queue[Queue updateAdMetrics Job]
    Queue --> FindArticles[Find Articles by Category]
    FindArticles --> Articles{Articles Found?}
    Articles -->|No| End[Complete Job]
    Articles -->|Yes| UpdateArticles[Update Articles with New Ad Metric]
    UpdateArticles --> More{More Articles?}
    More -->|Yes| QueueNext[Queue Next Batch]
    More -->|No| End
    QueueNext --> End
```

## Components

### 1. Job Queueing

The job is queued by the `queueAdMetricUpdateJobs` hook in the Tags collection when a category tag's ad metric changes. The hook:

1. Detects when a category tag's ad metric has changed
2. Checks if a job already exists for this category
3. Queues a new job with the category ID and new ad metric ID

See: [queueAdMetricUpdateJobs.ts](../collections/Tags/hooks/queueAdMetricUpdateJobs.ts)

### 2. Finding Articles by Category

The `findArticlesByCategory.ts` workflow step:

1. Takes a category ID as input
2. Handles localization by checking for the category across all supported locales
3. Finds articles with that category that haven't been processed yet
4. Returns article IDs for processing
5. Tracks whether there are more articles to process

Key features:

- Batch processing to handle large numbers of articles efficiently
- Skip list to track already processed articles
- Localization support for multilingual content

### 3. Updating Article Ad Metrics

The `updateArticleAdMetric.ts` workflow step:

1. Takes a list of article IDs and an ad metric ID
2. For each article:
   - Retrieves the current article data
   - Updates the article's `coreMetadata.adMetric` field with the new ad metric ID
   - Sets `coreMetadata.adMetricLock` to `true` to indicate auto-generation from category
   - Uses `isSystemUpdate: true` to prevent hook recursion
3. Tracks processed articles and success/failure counts
4. Queues another job if there are more articles to process

## Configuration

The job is configured in `config.ts` with:

- Input schema defining required and optional parameters
- Output schema for job results
- Retry configuration
- Success handler

Input parameters:

- `categoryId` (required): ID of the category whose articles need updating
- `adMetricId` (optional): ID of the new ad metric (can be null to remove)
- `batchSize` (optional): Number of articles to process per batch
- `skipIds` (optional): Array of article IDs to skip
- `locale` (optional): Locale to use for processing

## Error Handling

The job includes robust error handling:

1. Individual article update failures are logged but don't stop the job
2. The job continues processing other articles even if some fail
3. Overall job failures are properly reported with detailed error messages

## Relationship with Tags Collection

This job works in conjunction with the [Tags collection](../collections/Tags/README.md) to maintain the relationship between categories, ad metrics, and articles:

1. Categories (type: `categoryTags`) can have an associated ad metric
2. Ad metrics (type: `adMetrics`) belong to ad zones (type: `adZones`)
3. Articles inherit ad metrics from their category
4. When a category's ad metric changes, this job updates all articles with that category

## Usage

The job is automatically triggered when a category's ad metric changes. It can also be manually triggered through the Payload admin interface or API:

```typescript
await payload.jobs.queue({
	task: "updateAdMetrics",
	input: {
		categoryId: "category-123",
		adMetricId: "admetric-456", // or null to remove
		batchSize: 100,
	},
	queue: "ad-metrics",
});
```

## Testing

The job includes comprehensive tests:

- `findArticlesByCategory.test.ts`: Tests the article finding logic
- `updateArticleAdMetric.test.ts`: Tests the article updating logic
- `task.test.ts`: Tests the overall job handler

Run tests with:

```bash
pnpm --filter web test jobs/updateAdMetrics
```
