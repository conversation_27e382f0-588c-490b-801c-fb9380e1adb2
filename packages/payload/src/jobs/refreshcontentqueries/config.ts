import { TaskConfig } from 'payload';
import { refreshContentQueriesHandler } from './task';

export const refreshContentQueries: TaskConfig = {
	retries: 2,
	slug: 'refreshContentQueries',
	inputSchema: [
		{ name: 'lastSyncedBefore', type: 'text', required: false },
		{ name: 'limit', type: 'number', required: false },
	],
	outputSchema: [],
	onSuccess: async () => {
		console.log('refreshContentQueries task completed');
	},
	handler: refreshContentQueriesHandler,
};
