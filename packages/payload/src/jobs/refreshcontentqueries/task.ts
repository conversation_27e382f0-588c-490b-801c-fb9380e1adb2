import type { TaskH<PERSON>lerArgs, TaskHandlerResult, Where } from 'payload';
import { hasLength } from '@repo/utils/hasLength';

interface RefreshContentQueriesInput {
	lastSyncedBefore?: string;
	limit?: number;
}

export const refreshContentQueriesHandler = async ({
	input,
	req,
}: TaskHandlerArgs<'refreshContentQueries'>): Promise<
	TaskHandlerResult<'refreshContentQueries'>
> => {
	const { lastSyncedBefore, limit = 20 } = input as RefreshContentQueriesInput;
	const payload = req.payload;

	if (!payload) {
		return {
			state: 'failed',
			errorMessage: 'Payload not found',
		};
	}

	try {
		// Default to 5 minutes ago if no lastSyncedBefore is provided
		const syncedBefore =
			lastSyncedBefore || new Date(Date.now() - 5 * 60 * 1000).toISOString();

		const where: Where = {
			queries: {
				not_equals: [],
			},
		};

		// Find content queries that haven't been synced recently
		const contentQueries = await payload.find({
			collection: 'content-queries',
			depth: 0,
			limit,
			where,
			req,
		});

		if (!hasLength(contentQueries.docs)) {
			payload.logger.info('No content queries found to refresh');
			return { state: 'succeeded', output: [] };
		}

		let jobsQueued = 0;

		// Process each content query
		for (const queryDoc of contentQueries.docs) {
			if (!hasLength(queryDoc.queries)) continue;

			// Find queries that need to be refreshed
			const queriesToRefresh = queryDoc.queries.filter((query) => {
				// Skip locked queries
				if (query._locked) return false;

				// Skip queries with no ID
				if (!query.id) return false;

				// If no lastSynced, it needs to be refreshed
				if (!query.lastSynced) return true;

				// If lastSynced is before our cutoff, it needs to be refreshed
				try {
					const lastSyncedDate = new Date(query.lastSynced);
					const cutoffDate = new Date(syncedBefore);
					return lastSyncedDate < cutoffDate;
				} catch (_error) {
					// If we can't parse the date, assume it needs to be refreshed
					return true;
				}
			});

			if (!hasLength(queriesToRefresh)) continue;

			// Queue jobs for each query that needs to be refreshed
			for (const query of queriesToRefresh) {
				try {
					if (query.queryType === 'kalliope' && query.kalliopeCMQSID) {
						await payload.jobs.queue({
							task: 'getCMAssetByID',
							input: {
								kalliopeCMQID: query.kalliopeCMQSID,
								queryDocID: queryDoc.id,
								lastSynced: query.lastSynced || '',
							},
							queue: 'content-queries',
						});
						jobsQueued++;
					} else if (query.queryType === 'data') {
						// Skip if queryId is missing
						if (!query.id) continue;

						// Process the relationship fields to extract IDs
						const processedTags =
							query.tags?.map((tag) =>
								typeof tag === 'string' ? tag : tag.id,
							) || [];
						const processedCategories =
							query.category?.map((cat) =>
								typeof cat === 'string' ? cat : cat.id,
							) || [];
						const processedTopics =
							query.topic?.map((top) =>
								typeof top === 'string' ? top : top.id,
							) || [];

						await payload.jobs.queue({
							task: 'getContentByTags',
							input: {
								queryId: query.id,
								queryDocID: queryDoc.id,
								tags: processedTags.map((id) => ({ id })),
								tagsOperator: query.tagsOperator || 'OR',
								category: processedCategories.map((id) => ({ id })),
								categoryOperator: query.categoryOperator || 'OR',
								topic: processedTopics.map((id) => ({ id })),
								topicOperator: query.topicOperator || 'OR',
								groupsOperator: query.groupsOperator || 'AND',
								lastSynced: query.lastSynced || '',
							},
							queue: 'content-queries',
						});
						jobsQueued++;
					} else if (query.queryType === 'id' && hasLength(query.contentId)) {
						// Skip if queryId is missing
						if (!query.id) continue;

						// Process the relationship fields to extract IDs
						const processedContentIds =
							query.contentId?.map((cid) => {
								return {
									value: typeof cid === 'string' ? cid : cid.id,
								};
							}) || [];

						await payload.jobs.queue({
							task: 'getContentByIds',
							input: {
								queryId: query.id,
								queryDocID: queryDoc.id,
								contentIds: processedContentIds,
								lastSynced: query.lastSynced || '',
							},
							queue: 'content-queries',
						});
						jobsQueued++;
					}
				} catch (error) {
					payload.logger.error(
						`Error queueing refresh job for query ${query.id}:`,
						error,
					);
				}
			}
		}

		payload.logger.info(`Queued ${jobsQueued} content query refresh jobs`);
		return { state: 'succeeded', output: [] };
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		return {
			state: 'failed',
			errorMessage: `Failed to refresh content queries: ${errorMessage}`,
		};
	}
};
