import { TaskConfig } from 'payload';
import { generateAltTextHandler } from './task';

export const generateAltText: TaskConfig = {
	retries: 2,
	slug: 'aiGenerateAltText',
	inputSchema: [
		{ name: 'imageUrl', type: 'text', required: true },
		{ name: 'context', type: 'text', required: false },
	],
	outputSchema: [{ name: 'altText', type: 'text', required: true }],
	onSuccess: async () => {
		console.log('AI generate alt text task completed');
	},
	handler: generateAltTextHandler,
};
