import { TaskHandlerArgs, TaskHandlerResult } from 'payload';
import { createLogger } from '@repo/logger';
import { LiteLLMProvider } from '../../plugins/ai/providers/LiteLLMProvider';

const logger = createLogger('ai-generate-alt-text-job');

export const generateAltTextHandler = async ({
	input,
	job,
	req,
}: TaskHandlerArgs<'aiGenerateAltText'>): Promise<
	TaskHandlerResult<'aiGenerateAltText'>
> => {
	const { imageUrl, context } = input;

	logger.info(`Running generate alt text job for job ${job.id}`);

	try {
		// Use the appropriate provider
		const aiProvider = new LiteLLMProvider(req.payload);

		// Call the provider's generate alt text method directly
		const result = await aiProvider.generateAltText({
			imageUrl,
			context: context || undefined,
		});

		return {
			state: 'succeeded',
			output: {
				altText: result.altText,
			},
		};
	} catch (error) {
		logger.error(`Generate alt text job failed: ${error}`);
		return {
			state: 'failed',
			errorMessage:
				error instanceof Error ? error.message : 'Unknown error occurred',
		};
	}
};
