import path from 'path';

/**
 * Constructs a file path relative to the payload package root directory.
 * Moves up two directories to payload root and resolves the provided subPaths.
 *
 * @param {...string} subPaths - The path segments to be joined.
 * @returns {string} - The resolved absolute path.
 */
export default function getMonoRepoFilePath(...subPaths: string[]): string {
	return path.join(__dirname, '..', '..', ...subPaths);
}
