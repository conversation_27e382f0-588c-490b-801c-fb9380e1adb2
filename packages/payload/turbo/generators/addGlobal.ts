import { PlopTypes } from '@turbo/gen';
import getMonorepoFilePath from './getMonorepoFilePath';
import type {
	ModifyActionWithFormat,
	PrettifyAction,
	ExtendedActionType,
} from './types';

export const addGlobal: PlopTypes.PlopGeneratorConfig = {
	description: 'Creates a new Global Config',
	prompts: [
		{
			type: 'input',
			name: 'globalName',
			message: 'What is the name of the Global? (PascalCase please)',
			default: 'MyGlobal',
			validate: (input: string) =>
				/^[A-Z][a-zA-Z0-9]*$/.test(input) ||
				'Global name must be in PascalCase (e.g., MyGlobal)',
		},
		{
			type: 'input',
			name: 'description',
			message: 'What is the description of the Global?',
		},
	],
	actions: (data): ExtendedActionType[] => {
		const globalName = (data?.globalName as string) || 'MyGlobal';

		const collectionPath = `src/configs/global`;

		const addTemplateFiles: Array<{
			path: string;
			templateFile: string;
		}> = [
			{
				path: `${globalName}.ts`,
				templateFile: './templates/addGlobal/config.ts.hbs',
			},
		];

		/**
		 * These actions modify existing files to insert the new global
		 * Each pattern targets a specific insertion point in the file
		 * */
		const modifyTemplateFiles: Array<{
			path: string;
			pattern: RegExp;
			template: string;
			format: boolean;
		}> = [
			{
				path: 'src/payload-config.ts',
				pattern: /\/\*\* {2}End of global imports \*\//g,
				template: `import { {{globalName}} } from './configs/global/{{globalName}}';\n/**  End of global imports */`,
				format: true,
			},
			{
				path: 'src/payload-config.ts',
				pattern: /(\s*\/\*\* {2}global inclusion \*\/\s*)/g,
				template: `\t\t/**  global inclusion */\n\t\t{{globalName}},\n\t\t`,
				format: true,
			},
		];

		const addActions: PlopTypes.AddActionConfig[] = addTemplateFiles.map(
			({ path, templateFile }) => ({
				type: 'add',
				path: getMonorepoFilePath(collectionPath, path),
				templateFile: templateFile,
			}),
		);

		const modifyActions: ModifyActionWithFormat[] = modifyTemplateFiles.map(
			({ path, pattern, template, format }) => ({
				type: 'modify' as const,
				path: getMonorepoFilePath(path),
				pattern,
				template,
				format,
			}),
		);

		// Flatten all actions into a single array
		const actions = [...addActions, ...modifyActions];

		// Create a Set to track unique paths for formatting and remove dups
		const formattedPaths = new Set<string>();

		/**
		 * Because of the modify we are doing, we could let the user run prettier on the files
		 * Or we could do it for them. This is a good example of how we can add custom actions
		 * */
		const prettifyActions: PrettifyAction[] = actions
			.filter(
				(action): action is ModifyActionWithFormat =>
					'format' in action &&
					action.format === true &&
					!formattedPaths.has(action.path),
			)
			.map(({ path }) => {
				formattedPaths.add(path);
				return {
					type: 'prettify' as const,
					path,
				};
			});

		return [
			...actions,
			...prettifyActions,
			{ type: 'generateTypes' as const },
		] as ExtendedActionType[];
	},
};
