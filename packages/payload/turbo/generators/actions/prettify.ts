import { PlopTypes } from '@turbo/gen';

import { execSync } from 'child_process';

// Define a custom type for prettify actions
export interface CustomPrettifyActionConfig extends PlopTypes.ActionConfig {
	type: 'prettify';
	path: string;
}

export const prettify: PlopTypes.CustomActionFunction = (
	_answers,
	config,
	_plop,
): string => {
	if (!config) {
		throw 'No configuration provided for Prettier.';
	}
	const filePath = (config as CustomPrettifyActionConfig)?.path;

	if (!filePath) {
		throw 'No file path specified for Prettier.';
	}

	try {
		// Run Prettier on the specified file
		execSync(`prettier --write "${filePath}"`, { stdio: 'inherit' });
		return `Formatted ${filePath} with Prettier.`;
	} catch (_error) {
		throw `Failed to format ${filePath} with Prettier.`;
	}
};
