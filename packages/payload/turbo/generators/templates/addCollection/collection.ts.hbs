import type { CollectionConfig } from "payload";
import { name, slug } from "./fields";

export const {{collectionName}}: CollectionConfig = {
	slug: "{{kebabCase collectionName}}",
	labels: {
		singular: "{{collectionName}}",
		plural: "{{collectionName}}",
	},
	admin: {
		useAsTitle: "name",
		defaultColumns: ["name", "id"]
	},
	defaultSort: "-name",
	versions: false,
  access: {
		read: () => true,
	},
  fields: [name, slug],
};
