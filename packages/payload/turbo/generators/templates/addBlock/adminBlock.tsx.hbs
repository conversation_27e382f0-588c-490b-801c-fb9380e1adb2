"use client";

import React, { FC, useEffect, useState } from "react";
import { useAllFormFields } from "@payloadcms/ui";
import { reduceFieldsToValues } from "payload/shared";

import AdminBlock from "@repo/payload/components/AdminBlock";


export const {{blockName}}AdminBlock: FC = () => {
	const [fields, _setFields] = useAllFormFields();
	const formData = reduceFieldsToValues(fields, true);

	const [_exampleFieldData, setExampleFieldData] = useState({
		isCool: formData?.exampleField
	});

	useEffect(() => {
		setExampleFieldData({
			isCool: formData?.exampleField,
		});
	}, [formData?.exampleField]);

	const renderExampleField = () => {
		switch (formData?.exampleField) {
			case "true":
				return "is cool";
			case "false":
				return "is not cool";
			default:
				return "is undecided";
		}
	};

	return (
		<AdminBlock name="{{blockName}}">
			<span>Hello from Block</span>
			<span>You think this: {renderExampleField()}</span>
		</AdminBlock>
	);
};

export default {{blockName}}AdminBlock;
