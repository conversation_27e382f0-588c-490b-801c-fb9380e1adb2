import { exampleField } from "./field";
import type { Block } from "payload";

export const {{blockName}}BlockConfig: Block = {
	slug: "{{blockName}}",
	interfaceName: "{{blockName}}BlockConfig",
	labels: {
		singular: "{{blockName}} Block",
		plural: "{{blockName}} Blocks",
	},
	fields: [exampleField],
	// Component to render in the admin interface
	admin: {
		components: {
			Block: {
				path: "@repo/payload/blocks/{{blockName}}/{{blockName}}AdminBlock#default",
			},
		},
	},
};

export default {{blockName}}BlockConfig;
