import type { PlopTypes } from '@turbo/gen';
import { prettify } from './actions/prettify';
import { generateTypes } from './actions/generateTypes';
import { addBlock } from './addBlock';
import { addCollection } from './addCollection';
import { addGlobal } from './addGlobal';

// Learn more about Turborepo Generators at https://turbo.build/repo/docs/core-concepts/monorepos/code-generation

export default function generator(plop: PlopTypes.NodePlopAPI): void {
	plop.setActionType('generateTypes', generateTypes);
	plop.setActionType('prettify', prettify);
	plop.setGenerator('Add Payload Block', addBlock);
	plop.setGenerator('Add Payload Collection', addCollection);
	plop.setGenerator('Add Global Config', addGlobal);
}
