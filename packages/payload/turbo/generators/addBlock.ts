import { PlopTypes } from '@turbo/gen';
import getMonorepoFilePath from './getMonorepoFilePath';
import type {
	ModifyActionWithFormat,
	PrettifyAction,
	ExtendedActionType,
} from './types';

export const addBlock: PlopTypes.PlopGeneratorConfig = {
	description: 'Creates a new Payload block',
	prompts: [
		{
			type: 'input',
			name: 'blockName',
			message: 'What is the name of the block? (PascalCase please)',
			default: 'HelloWorld',
			validate: (input: string) =>
				/^[A-Z][a-zA-Z0-9]*$/.test(input) ||
				'Block name must be in Pascal<PERSON>ase (e.g., HelloWorld)',
		},
	],
	actions: (data): ExtendedActionType[] => {
		const blockName = (data?.blockName as string) || 'HelloWorld';

		const blockPath = `src/blocks/${blockName}`;

		const addTemplateFiles: Array<{
			path: string;
			templateFile: string;
		}> = [
			{
				path: 'config.ts',
				templateFile: './templates/addBlock/config.ts.hbs',
			},
			{
				path: `${blockName}Block.tsx`,
				templateFile: './templates/addBlock/block.tsx.hbs',
			},
			{
				path: `${blockName}AdminBlock.tsx`,
				templateFile: './templates/addBlock/adminBlock.tsx.hbs',
			},
			{
				path: 'field.ts',
				templateFile: './templates/addBlock/fields.ts.hbs',
			},
			{
				path: 'index.ts',
				templateFile: './templates/addBlock/index.ts.hbs',
			},
		];

		/**
		 * These actions modify existing files to insert the new block into arrays
		 * Each pattern targets a specific insertion point in the file
		 * */
		const modifyTemplateFiles: Array<{
			path: string;
			pattern: RegExp;
			template: string;
			format: boolean;
		}> = [
			/**
			 * Each of these locate the blocks array opening and insert the block at the start
			 */
			{
				path: 'src/collections/Pages/fields/blocks.ts',
				pattern: /export const allowedBlocks: Block\[\] = \[\s*/g,
				template: `export const allowedBlocks: Block[] = [\n\t{{blockName}}BlockConfig,\n\t`,
				format: true,
			},
			{
				path: 'src/collections/Articles/fields/blocks.ts',
				pattern: /export const allowedBlocks: Block\[\] = \[\s*/g,
				template: `export const allowedBlocks: Block[] = [\n\t{{blockName}}BlockConfig,\n\t`,
				format: true,
			},
			{
				path: 'src/blocks/index.ts',
				pattern: /export const blockRegistrations: BlockRegistration\[] = \[/g,
				template: `export const blockRegistrations: BlockRegistration[] = [\n\t{\n\t\tslug: {{blockName}}BlockConfig.slug,\n\t\tcomponent: {{blockName}}Block,\n\t},`,
				format: true,
			},
			{
				path: 'src/blocks/index.ts',
				pattern: /(;\s*\/\/ generator-union-type-insertion-point)/g,
				template: `\n\t| I{{blockName}};\n// generator-union-type-insertion-point`,
				format: true,
			},
			{
				path: 'src/blocks/index.ts',
				pattern: /\/\/ generator-exports-insertion-point/g,
				template: `export * from './{{blockName}}/{{blockName}}Block';\n// generator-exports-insertion-point`,
				format: true,
			},
			{
				path: 'src/blocks/index.admin.ts',
				pattern: /\/\/ generator-exports-insertion-point/g,
				template: `export * from './{{blockName}}/{{blockName}}AdminBlock';\n// generator-exports-insertion-point`,
				format: true,
			},
		];

		/**
		 * These actions add import statements to the beginning of files
		 * Each pattern targets a specific location after existing imports
		 * Using modify instead of append to avoid duplicate imports
		 * */
		const modifyImportFiles: Array<{
			path: string;
			pattern: RegExp;
			template: string;
			format: boolean;
		}> = [
			{
				path: 'src/collections/Pages/fields/blocks.ts',
				pattern: /import { Block } from 'payload';/g,
				template: `import { Block } from 'payload';\nimport {{blockName}}BlockConfig from '../../../blocks/{{blockName}}/config';`,
				format: true,
			},
			{
				path: 'src/collections/Articles/fields/blocks.ts',
				pattern: /import { Block } from 'payload';/g,
				template: `import { Block } from 'payload';\nimport {{blockName}}BlockConfig from '../../../blocks/{{blockName}}/config';`,
				format: true,
			},
			{
				path: 'src/blocks/index.ts',
				pattern: /import { FC } from 'react';/g,
				template: `import { FC } from 'react';\nimport {{blockName}}Block from './{{blockName}}/{{blockName}}Block';\nimport {{blockName}}BlockConfig from './{{blockName}}/config';`,
				format: true,
			},
			{
				path: 'src/blocks/index.ts',
				pattern: /\/\/ generator-type-imports-insertion-point/g,
				template: `{{blockName}}BlockConfig as I{{blockName}},\n\t// generator-type-imports-insertion-point`,
				format: true,
			},
		];

		const addActions: PlopTypes.AddActionConfig[] = addTemplateFiles.map(
			({ path, templateFile }) => ({
				type: 'add',
				path: getMonorepoFilePath(blockPath, path),
				templateFile: templateFile,
			}),
		);

		const modifyActions: ModifyActionWithFormat[] = [
			...modifyTemplateFiles,
			...modifyImportFiles,
		].map(({ path, pattern, template, format }) => ({
			type: 'modify' as const,
			path: getMonorepoFilePath(path),
			pattern,
			template,
			format,
		}));

		// Flatten all actions into a single array
		const actions = [...addActions, ...modifyActions];

		// Create a Set to track unique paths for formatting and remove dups
		const formattedPaths = new Set<string>();

		/**
		 * Because of the modify we are doing, we could let the user run prettier on the files
		 * Or we could do it for them. This is a good example of how we can add custom actions
		 * */
		const prettifyActions: PrettifyAction[] = actions
			.filter(
				(action): action is ModifyActionWithFormat =>
					'format' in action &&
					action.format === true &&
					!formattedPaths.has(action.path),
			)
			.map(({ path }) => {
				formattedPaths.add(path);
				return {
					type: 'prettify' as const,
					path,
				};
			});

		return [
			...actions,
			...prettifyActions,
			{ type: 'generateTypes' as const },
		] as ExtendedActionType[];
	},
};
