import { PlopTypes } from '@turbo/gen';

// Simplified types that only extend what's needed
export type ModifyActionWithFormat = PlopTypes.ModifyActionConfig & {
	format: boolean;
};

export type AppendActionWithFormat = PlopTypes.AppendActionConfig & {
	format: boolean;
};

// Custom action types for non-standard Plop actions
export type PrettifyAction = {
	type: 'prettify';
	path: string;
};

export type GenerateTypesAction = {
	type: 'generateTypes';
};

// Union type for all possible actions including custom ones
export type ExtendedActionType =
	| PlopTypes.ActionType
	| PrettifyAction
	| GenerateTypesAction;
