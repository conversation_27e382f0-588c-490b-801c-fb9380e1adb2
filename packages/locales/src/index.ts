/**
 * Locale data for language selection
 * Organized by regions and countries
 */
export const LOCALE_DATA = [
	{
		title: 'Americas',
		countries: [
			{ name: 'Antigua and Barbuda', locale: 'en-AG', language: 'English' },
			{ name: 'Argentina', locale: 'es-AR', language: 'Español' },
			{ name: 'Bahamas', locale: 'en-BS', language: 'English' },
			{ name: 'Barbados', locale: 'en-BB', language: 'English' },
			{ name: 'Belize', locale: 'en-BZ', language: 'English' },
			{ name: 'Bolivia', locale: 'es-BO', language: 'Español' },
			{ name: 'Brazil', locale: 'pt-BR', language: 'Português' },
			{ name: 'Canada', locale: 'en-CA', language: 'English' },
			{ name: 'Canada', locale: 'fr-CA', language: 'Français' },
			{ name: 'Chile', locale: 'es-CL', language: 'Español' },
			{ name: 'Colombia', locale: 'es-CO', language: 'Español' },
			{ name: 'Costa Rica', locale: 'es-CR', language: 'Español' },
			{ name: 'Dominica', locale: 'en-DM', language: 'English' },
			{ name: 'Dominican Republic', locale: 'es-DO', language: 'Español' },
			{ name: 'Ecuador', locale: 'es-EC', language: 'Español' },
			{ name: 'El Salvador', locale: 'es-SV', language: 'Español' },
			{ name: 'Grenada', locale: 'en-GD', language: 'English' },
			{ name: 'Guatemala', locale: 'es-GT', language: 'Español' },
			{ name: 'Guyana', locale: 'en-GY', language: 'English' },
			{ name: 'Haiti', locale: 'fr-HT', language: 'Français' },
			{ name: 'Honduras', locale: 'es-HN', language: 'Español' },
			{ name: 'Jamaica', locale: 'en-JM', language: 'English' },
			{ name: 'Mexico', locale: 'es-MX', language: 'Español' },
			{ name: 'Nicaragua', locale: 'es-NI', language: 'Español' },
			{ name: 'Panama', locale: 'es-PA', language: 'Español' },
			{ name: 'Panama', locale: 'en-PA', language: 'English' },
			{ name: 'Paraguay', locale: 'es-PY', language: 'Español' },
			{ name: 'Peru', locale: 'es-PE', language: 'Español' },
			{ name: 'St. Kitts and Nevis', locale: 'en-KN', language: 'English' },
			{ name: 'St. Lucia', locale: 'en-LC', language: 'English' },
			{
				name: 'St. Vincent and the Grenadines',
				locale: 'en-VC',
				language: 'English',
			},
			{ name: 'Suriname', locale: 'nl-SR', language: 'Nederlands' },
			{ name: 'Trinidad and Tobago', locale: 'en-TT', language: 'English' },
			{ name: 'Uruguay', locale: 'es-UY', language: 'Español' },
			{ name: 'United States', locale: 'en-US', language: 'English' },
			{ name: 'United States', locale: 'es-US', language: 'Español' },
			{ name: 'Venezuela', locale: 'es-VE', language: 'Español' },
		],
	},
	{
		title: 'Africa',
		countries: [
			{ name: 'Algeria', locale: 'ar-DZ', language: 'العربية' },
			{ name: 'Algeria', locale: 'fr-DZ', language: 'Français' },
			{ name: 'Angola', locale: 'pt-AO', language: 'Português' },
			{ name: 'Benin', locale: 'fr-BJ', language: 'Français' },
			{ name: 'Burkina Faso', locale: 'fr-BF', language: 'Français' },
			{ name: 'Burundi', locale: 'fr-BI', language: 'Français' },
			{ name: 'Cameroon', locale: 'fr-CM', language: 'Français' },
			{ name: 'Cameroon', locale: 'en-CM', language: 'English' },
			{ name: 'Cape Verde', locale: 'pt-CV', language: 'Português' },
			{
				name: 'Central African Republic',
				locale: 'fr-CF',
				language: 'Français',
			},
			{ name: 'Chad', locale: 'fr-TD', language: 'Français' },
			{ name: 'Chad', locale: 'ar-TD', language: 'العربية' },
			{ name: 'Comoros', locale: 'fr-KM', language: 'Français' },
			{ name: 'Comoros', locale: 'ar-KM', language: 'العربية' },
			{
				name: 'Democratic Republic of the Congo',
				locale: 'fr-CD',
				language: 'Français',
			},
			{ name: 'Republic of Congo', locale: 'fr-CG', language: 'Français' },
			{ name: "Côte d'Ivoire", locale: 'fr-CI', language: 'Français' },
			{ name: 'Djibouti', locale: 'fr-DJ', language: 'Français' },
			{ name: 'Djibouti', locale: 'ar-DJ', language: 'العربية' },
			{ name: 'Egypt', locale: 'ar-EG', language: 'العربية' },
			{ name: 'Equatorial Guinea', locale: 'es-GQ', language: 'Español' },
			{ name: 'Eritrea', locale: 'ar-ER', language: 'العربية' },
			{ name: 'Gabon', locale: 'fr-GA', language: 'Français' },
			{ name: 'Gambia', locale: 'en-GM', language: 'English' },
			{ name: 'Ghana', locale: 'en-GH', language: 'English' },
			{ name: 'Guinea', locale: 'fr-GN', language: 'Français' },
			{ name: 'Guinea-Bissau', locale: 'pt-GW', language: 'Português' },
			{ name: 'Kenya', locale: 'en-KE', language: 'English' },
			{ name: 'Lesotho', locale: 'en-LS', language: 'English' },
			{ name: 'Liberia', locale: 'en-LR', language: 'English' },
			{ name: 'Libya', locale: 'ar-LY', language: 'العربية' },
			{ name: 'Madagascar', locale: 'fr-MG', language: 'Français' },
			{ name: 'Mali', locale: 'fr-ML', language: 'Français' },
			{ name: 'Mauritania', locale: 'ar-MR', language: 'العربية' },
			{ name: 'Mauritius', locale: 'en-MU', language: 'English' },
			{ name: 'Mauritius', locale: 'fr-MU', language: 'Français' },
			{ name: 'Morocco', locale: 'ar-MA', language: 'العربية' },
			{ name: 'Morocco', locale: 'fr-MA', language: 'Français' },
			{ name: 'Mozambique', locale: 'pt-MZ', language: 'Português' },
			{ name: 'Namibia', locale: 'en-NA', language: 'English' },
			{ name: 'Niger', locale: 'fr-NE', language: 'Français' },
			{ name: 'Nigeria', locale: 'en-NG', language: 'English' },
			{ name: 'Rwanda', locale: 'fr-RW', language: 'Français' },
			{ name: 'Rwanda', locale: 'en-RW', language: 'English' },
			{ name: 'Sao Tome and Principe', locale: 'pt-ST', language: 'Português' },
			{ name: 'Senegal', locale: 'fr-SN', language: 'Français' },
			{ name: 'Sierra Leone', locale: 'en-SL', language: 'English' },
			{ name: 'Somalia', locale: 'ar-SO', language: 'العربية' },
			{ name: 'South Africa', locale: 'en-ZA', language: 'English' },
			{ name: 'South Sudan', locale: 'en-SS', language: 'English' },
			{ name: 'Sudan', locale: 'ar-SD', language: 'العربية' },
			{ name: 'Swaziland', locale: 'en-SZ', language: 'English' },
			{ name: 'Tanzania', locale: 'en-TZ', language: 'English' },
			{ name: 'Togo', locale: 'fr-TG', language: 'Français' },
			{ name: 'Tunisia', locale: 'ar-TN', language: 'العربية' },
			{ name: 'Uganda', locale: 'en-UG', language: 'English' },
		],
	},
	{
		title: 'Asia Pacific',
		countries: [
			{ name: 'Australia', locale: 'en-AU', language: 'English' },
			{ name: 'Bangladesh', locale: 'bn-BD', language: 'বাংলা' },
			{ name: 'Brunei', locale: 'ms-BN', language: 'Bahasa Melayu' },
			{ name: 'China', locale: 'zh-CN', language: '中文' },
			{ name: 'Hong Kong SAR', locale: 'zh-HK', language: '中文' },
			{ name: 'East Timor', locale: 'pt-TP', language: 'Português' },
			{ name: 'Fiji', locale: 'en-FJ', language: 'English' },
			{ name: 'India (English)', locale: 'en-IN', language: 'English' },
			{ name: 'India (Hindi)', locale: 'hi-IN', language: 'हिन्दी' },
			{ name: 'Indonesia', locale: 'id-ID', language: 'Bahasa Indonesia' },
			{ name: 'Japan', locale: 'ja-JP', language: '日本語' },
			{ name: 'Kiribati', locale: 'en-KI', language: 'English' },
			{ name: 'South Korea', locale: 'ko-KR', language: '한국어' },
			{ name: 'Kyrgyzstan', locale: 'ru-KG', language: 'Русский' },
			{ name: 'Malaysia', locale: 'ms-MY', language: 'Bahasa Melayu' },
			{ name: 'Marshall Islands', locale: 'en-MH', language: 'English' },
			{ name: 'Micronesia', locale: 'en-FM', language: 'English' },
			{ name: 'New Zealand', locale: 'en-NZ', language: 'English' },
			{ name: 'Palau', locale: 'en-PW', language: 'English' },
			{ name: 'Philippines', locale: 'en-PH', language: 'English' },
			{ name: 'Philippines', locale: 'tl-PH', language: 'Tagalog' },
			{ name: 'Samoa', locale: 'en-AS', language: 'English' },
			{ name: 'Singapore', locale: 'en-SG', language: 'English' },
			{ name: 'Singapore', locale: 'zh-SG', language: '中文' },
			{ name: 'Solomon Islands', locale: 'en-SB', language: 'English' },
			{ name: 'Taiwan', locale: 'zh-TW', language: '中文' },
			{ name: 'Thailand', locale: 'th-TH', language: 'ไทย' },
			{ name: 'Tonga', locale: 'en-TO', language: 'English' },
			{ name: 'Tuvalu', locale: 'en-TV', language: 'English' },
			{ name: 'Vanuatu', locale: 'en-VU', language: 'English' },
			{ name: 'Vanuatu', locale: 'fr-VU', language: 'Français' },
			{ name: 'Vietnam', locale: 'vi-VN', language: 'Tiếng Việt' },
		],
	},
	{
		title: 'Europe',
		countries: [
			{ name: 'Andorra', locale: 'ca-AD', language: 'Català' },
			{ name: 'Andorra', locale: 'fr-AD', language: 'Français' },
			{ name: 'Austria', locale: 'de-AT', language: 'Deutsch' },
			{ name: 'Belarus', locale: 'ru-BY', language: 'Русский' },
			{ name: 'Belgium', locale: 'nl-BE', language: 'Dutch' },
			{ name: 'Belgium', locale: 'fr-BE', language: 'Français' },
			{ name: 'Bosnia and Herzegovina', locale: 'hr-BA', language: 'Hrvatski' },
			{ name: 'Croatia', locale: 'hr-HR', language: 'Hrvatski' },
			{ name: 'Cyprus', locale: 'el-CY', language: 'Ελληνικά' },
			{ name: 'Czech Republic', locale: 'cs-CZ', language: 'Čeština' },
			{ name: 'Denmark', locale: 'da-DK', language: 'Dansk' },
			{ name: 'Estonia', locale: 'ru-EE', language: 'Русский' },
			{ name: 'Estonia', locale: 'et-EE', language: 'Eesti' },
			{ name: 'Finland', locale: 'fi-FI', language: 'Suomi' },
			{ name: 'France', locale: 'fr-FR', language: 'Français' },
			{ name: 'Germany', locale: 'de-DE', language: 'Deutsch' },
			{ name: 'Greece', locale: 'el-GR', language: 'Ελληνικά' },
			{ name: 'Hungary', locale: 'hu-HU', language: 'Magyar' },
			{ name: 'Ireland', locale: 'en-IE', language: 'English' },
			{ name: 'Italy', locale: 'it-IT', language: 'Italiano' },
			{ name: 'Liechtenstein', locale: 'de-LI', language: 'Deutsch' },
			{ name: 'Luxembourg', locale: 'fr-LU', language: 'Français' },
			{ name: 'Malta', locale: 'en-MT', language: 'English' },
			{ name: 'Monaco', locale: 'fr-MC', language: 'Français' },
			{ name: 'Netherlands', locale: 'nl-NL', language: 'Nederlands' },
			{ name: 'Norway', locale: 'no-NO', language: 'Norsk' },
			{ name: 'Poland', locale: 'pl-PL', language: 'Polski' },
			{ name: 'Portugal', locale: 'pt-PT', language: 'Português' },
			{ name: 'Romania', locale: 'ro-RO', language: 'Română' },
			{ name: 'Russia', locale: 'ru-RU', language: 'Русский' },
			{ name: 'San Marino', locale: 'it-SM', language: 'Italiano' },
			{ name: 'Slovakia', locale: 'sk-SK', language: 'Slovenčina' },
			{ name: 'Spain', locale: 'es-ES', language: 'Español' },
			{ name: 'Spain', locale: 'ca-ES', language: 'Català' },
			{ name: 'Sweden', locale: 'sv-SE', language: 'Svenska' },
			{ name: 'Switzerland', locale: 'de-CH', language: 'Deutsch' },
			{ name: 'Turkey', locale: 'tr-TR', language: 'Turkçe' },
			{ name: 'Ukraine', locale: 'uk-UA', language: 'Українська' },
			{ name: 'United Kingdom', locale: 'en-GB', language: 'English' },
			{
				name: 'State of Vatican City (Holy See)',
				locale: 'it-VA',
				language: 'Italiano',
			},
		],
	},
	{
		title: 'Middle East',
		countries: [
			{ name: 'Bahrain', locale: 'ar-BH', language: 'العربية' },
			{ name: 'Iran', locale: 'fa-IR', language: ' فارسى' },
			{ name: 'Iraq', locale: 'ar-IQ', language: 'العربية' },
			{ name: 'Israel', locale: 'he-IL', language: 'עִבְרִית' },
			{ name: 'Jordan', locale: 'ar-JO', language: 'العربية' },
			{ name: 'Kuwait', locale: 'ar-KW', language: 'العربية' },
			{ name: 'Lebanon', locale: 'ar-LB', language: 'العربية' },
			{ name: 'Oman', locale: 'ar-OM', language: 'العربية' },
			{ name: 'Pakistan', locale: 'ur-PK', language: ' اردو' },
			{ name: 'Pakistan', locale: 'en-PK', language: 'English' },
			{ name: 'Qatar', locale: 'ar-QA', language: 'العربية' },
			{ name: 'Saudi Arabia', locale: 'ar-SA', language: 'العربية' },
			{ name: 'Syria', locale: 'ar-SY', language: 'العربية' },
			{ name: 'United Arab Emirates', locale: 'ar-AE', language: 'العربية' },
		],
	},
];

export const LOCALES = [
	'ar-AE',
	'ar-BH',
	'ar-DJ',
	'ar-DZ',
	'ar-EG',
	'ar-ER',
	'ar-IL',
	'ar-IQ',
	'ar-JO',
	'ar-KM',
	'ar-KW',
	'ar-LB',
	'ar-LY',
	'ar-MA',
	'ar-MR',
	'ar-OM',
	'ar-QA',
	'ar-SA',
	'ar-SD',
	'ar-SO',
	'ar-SY',
	'ar-TD',
	'ar-TN',
	'ar-YE',
	'az-AZ',
	'bg-BG',
	'bn-BD',
	'bn-IN',
	'bs-BA',
	'ca-AD',
	'ca-ES',
	'cs-CZ',
	'da-DK',
	'de-AT',
	'de-CH',
	'de-DE',
	'de-LI',
	'el-CY',
	'el-GR',
	'en-AG',
	'en-AS',
	'en-AU',
	'en-BB',
	'en-BS',
	'en-BZ',
	'en-CA',
	'en-CM',
	'en-DM',
	'en-FJ',
	'en-FM',
	'en-GB',
	'en-GD',
	'en-GH',
	'en-GM',
	'en-GY',
	'en-IE',
	'en-IN',
	'en-JM',
	'en-KE',
	'en-KI',
	'en-KN',
	'en-LC',
	'en-LR',
	'en-LS',
	'en-MH',
	'en-MT',
	'en-MU',
	'en-NA',
	'en-NG',
	'en-NZ',
	'en-PA',
	'en-PH',
	'en-PK',
	'en-PW',
	'en-RW',
	'en-SB',
	'en-SG',
	'en-SL',
	'en-SS',
	'en-SZ',
	'en-TO',
	'en-TT',
	'en-TV',
	'en-TZ',
	'en-UG',
	'en-US',
	'en-VC',
	'en-VU',
	'en-ZA',
	'en-ZM',
	'en-ZW',
	'es-AR',
	'es-BO',
	'es-CL',
	'es-CO',
	'es-CR',
	'es-DO',
	'es-EC',
	'es-ES',
	'es-GQ',
	'es-GT',
	'es-HN',
	'es-MX',
	'es-NI',
	'es-PA',
	'es-PE',
	'es-PY',
	'es-SV',
	'es-US',
	'es-UY',
	'es-VE',
	'et-EE',
	'fa-IR',
	'fi-FI',
	'fr-AD',
	'fr-BE',
	'fr-BF',
	'fr-BI',
	'fr-BJ',
	'fr-CA',
	'fr-CD',
	'fr-CF',
	'fr-CG',
	'fr-CI',
	'fr-CM',
	'fr-DJ',
	'fr-DZ',
	'fr-FR',
	'fr-GA',
	'fr-GN',
	'fr-HT',
	'fr-KM',
	'fr-LU',
	'fr-MA',
	'fr-MC',
	'fr-MG',
	'fr-ML',
	'fr-MU',
	'fr-NE',
	'fr-RW',
	'fr-SN',
	'fr-TD',
	'fr-TG',
	'fr-VU',
	'gu-IN',
	'he-IL',
	'hi-IN',
	'hr-BA',
	'hr-HR',
	'hu-HU',
	'id-ID',
	'is-IS',
	'it-IT',
	'it-SM',
	'it-VA',
	'ja-JP',
	'jv-ID',
	'ka-GE',
	'kk-KZ',
	'kn-IN',
	'ko-KP',
	'ko-KR',
	'lt-LT',
	'lv-LV',
	'mk-MK',
	'mn-MN',
	'ms-BN',
	'ms-MY',
	'nl-BE',
	'nl-NL',
	'nl-SR',
	'no-NO',
	'pl-PL',
	'pt-AO',
	'pt-BR',
	'pt-CV',
	'pt-GW',
	'pt-MZ',
	'pt-PT',
	'pt-ST',
	'pt-TP',
	'ro-RO',
	'ru-BY',
	'ru-EE',
	'ru-KG',
	'ru-RU',
	'si-LK',
	'sk-SK',
	'sl-SI',
	'sq-AL',
	'sr-BA',
	'sr-ME',
	'sr-RS',
	'sv-SE',
	'sw-CD',
	'sw-KE',
	'sw-TZ',
	'sw-UG',
	'ta-IN',
	'ta-LK',
	'te-IN',
	'tg-TJ',
	'th-TH',
	'tk-TM',
	'tl-PH',
	'tr-TR',
	'uk-UA',
	'ur-PK',
	'uz-UZ',
	'vi-VN',
	'xc-MN',
	'zh-CN',
	'zh-HK',
	'zh-SG',
	'zh-TW',
] as const;

/**
 * Helper function to convert locale format from 'en-US' to 'en_US'
 * @param locale Locale in format 'en-US'
 * @returns Locale in format 'en_US'
 */
export function convertLocaleFormat(locale: string): string {
	return locale.replace('-', '_');
}

/**
 * Helper function to convert locale format from 'en_US' to 'en-US'
 * @param locale Locale in format 'en_US'
 * @returns Locale in format 'en-US'
 */
export function convertLocaleFormatReverse(locale: string): string {
	return locale.replace('_', '-');
}

/**
 * Find country and language info by locale
 * @param locale Locale in either format 'en-US' or 'en_US'
 * @returns Country and language info or undefined if not found
 */
export function findCountryByLocale(
	locale: string,
): { name: string; locale: string; language: string } | undefined {
	// Normalize locale to 'en-US' format for comparison
	const normalizedLocale = locale.includes('_')
		? convertLocaleFormatReverse(locale)
		: locale;

	for (const region of LOCALE_DATA) {
		for (const country of region.countries) {
			if (country.locale === normalizedLocale) {
				return country;
			}
		}
	}

	return undefined;
}

export const isRtlLang = (lang: string) =>
	['ar', 'fa', 'he', 'ur', 'yi'].includes(lang);
