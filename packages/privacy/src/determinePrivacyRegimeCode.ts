import { Regime } from './constants';

/**
 * Determines the privacy regime code based on country code and region code
 * @param countryCode The country code from GeoIP
 * @param regionCode The region code from GeoIP
 * @returns The appropriate privacy regime code
 */
export function determinePrivacyRegimeCode(
	countryCode: string | null,
	regionCode: string | null,
): string {
	// Default to EXEMPT if no country code
	if (!countryCode) return Regime.EXEMPT;

	// Check for country+region specific regimes first (US states)
	if (countryCode === 'US') {
		if (regionCode === 'CA') return Regime.USA_CCPA;
		if (regionCode === 'VA') return Regime.USA_VA;
		if (regionCode === 'CO') return Regime.USA_CO;
		if (regionCode === 'CT') return Regime.USA_CT;
		if (regionCode === 'UT') return Regime.USA_UT;

		// Default for US territories
		return Regime.USA;
	}

	// Check for Canada regions
	if (countryCode === 'CA') {
		if (regionCode === 'QC') return Regime.CA_QC;
		return Regime.CA;
	}

	// Check for GDPR countries
	const gdprCountries = [
		'AT',
		'BE',
		'BG',
		'HR',
		'CY',
		'CZ',
		'DK',
		'EE',
		'FI',
		'FR',
		'DE',
		'GR',
		'HU',
		'IS',
		'IE',
		'IT',
		'LV',
		'LI',
		'LT',
		'LU',
		'MT',
		'NL',
		'NO',
		'PL',
		'PT',
		'RO',
		'SK',
		'SI',
		'ES',
		'SE',
		'GB',
		'RS',
		'CH',
	];
	if (gdprCountries.includes(countryCode)) return Regime.GDPR;

	// Check for other country-specific regimes
	if (countryCode === 'BR') return Regime.LGPD;
	if (countryCode === 'PE') return Regime.LATAM_PE;
	if (countryCode === 'CO') return Regime.LATAM_CO;
	if (countryCode === 'DO') return Regime.LATAM_DO;
	if (countryCode === 'TR') return Regime.TR_KVKK;
	if (countryCode === 'KR') return Regime.KR;
	if (countryCode === 'JP') return Regime.JP;

	// Special case for China (excluding Taiwan, Hong Kong, and Macau)
	if (countryCode === 'CN') return Regime.PIPL;

	// Default fallback
	return Regime.EXEMPT;
}
