import { describe, it, expect } from 'vitest';
import { determinePrivacyRegimeCode } from './determinePrivacyRegimeCode';

describe('determinePrivacyRegimeCode', () => {
	// Test for null/undefined country code
	it('should return exempt when country code is null', () => {
		expect(determinePrivacyRegimeCode(null, 'CA')).toMatchSnapshot();
	});

	// Tests for US states with different privacy regimes
	it('should return usa-ccpa for US-CA', () => {
		expect(determinePrivacyRegimeCode('US', 'CA')).toMatchSnapshot();
	});

	it('should return usa-va for US-VA', () => {
		expect(determinePrivacyRegimeCode('US', 'VA')).toMatchSnapshot();
	});

	it('should return usa-co for US-CO', () => {
		expect(determinePrivacyRegimeCode('US', 'CO')).toMatchSnapshot();
	});

	it('should return usa-ct for US-CT', () => {
		expect(determinePrivacyRegimeCode('US', 'CT')).toMatchSnapshot();
	});

	it('should return usa-ut for US-UT', () => {
		expect(determinePrivacyRegimeCode('US', 'UT')).toMatchSnapshot();
	});

	it('should return usa for other US states', () => {
		expect(determinePrivacyRegimeCode('US', 'NY')).toMatchSnapshot();
	});

	// Tests for Canadian provinces
	it('should return ca-qc for Canada-QC', () => {
		expect(determinePrivacyRegimeCode('CA', 'QC')).toMatchSnapshot();
	});

	it('should return ca for other Canadian provinces', () => {
		expect(determinePrivacyRegimeCode('CA', 'ON')).toMatchSnapshot();
	});

	// Tests for GDPR countries
	it('should return gdpr for countries in the GDPR list', () => {
		// Test a few representative GDPR countries
		expect(determinePrivacyRegimeCode('DE', null)).toMatchSnapshot(); // Germany
		expect(determinePrivacyRegimeCode('FR', null)).toMatchSnapshot(); // France
		expect(determinePrivacyRegimeCode('IT', null)).toMatchSnapshot(); // Italy
		expect(determinePrivacyRegimeCode('ES', null)).toMatchSnapshot(); // Spain
		expect(determinePrivacyRegimeCode('GB', null)).toMatchSnapshot(); // United Kingdom
	});

	// Tests for other country-specific regimes
	it('should return lgpd for Brazil', () => {
		expect(determinePrivacyRegimeCode('BR', null)).toMatchSnapshot();
	});

	it('should return latam-pe for Peru', () => {
		expect(determinePrivacyRegimeCode('PE', null)).toMatchSnapshot();
	});

	it('should return latam-co for Colombia', () => {
		expect(determinePrivacyRegimeCode('CO', null)).toMatchSnapshot();
	});

	it('should return latam-do for Dominican Republic', () => {
		expect(determinePrivacyRegimeCode('DO', null)).toMatchSnapshot();
	});

	it('should return tr-kvkk for Turkey', () => {
		expect(determinePrivacyRegimeCode('TR', null)).toMatchSnapshot();
	});

	it('should return kr for South Korea', () => {
		expect(determinePrivacyRegimeCode('KR', null)).toMatchSnapshot();
	});

	it('should return jp for Japan', () => {
		expect(determinePrivacyRegimeCode('JP', null)).toMatchSnapshot();
	});

	// Test for China (special case)
	it('should return pipl for China (excluding Taiwan, Hong Kong, and Macau)', () => {
		expect(determinePrivacyRegimeCode('CN', null)).toMatchSnapshot();
	});

	// Test for default fallback
	it('should return exempt for countries not in any specific regime', () => {
		expect(determinePrivacyRegimeCode('ZZ', null)).toMatchSnapshot(); // Non-existent country code
		expect(determinePrivacyRegimeCode('AU', null)).toMatchSnapshot(); // Australia
		expect(determinePrivacyRegimeCode('NZ', null)).toMatchSnapshot(); // New Zealand
	});

	// Test with both parameters null
	it('should return exempt when both country code and region code are null', () => {
		expect(determinePrivacyRegimeCode(null, null)).toMatchSnapshot();
	});

	// Test for edge cases
	it('should handle edge cases correctly', () => {
		// Empty strings
		expect(determinePrivacyRegimeCode('', '')).toMatchSnapshot();

		// Case sensitivity
		expect(determinePrivacyRegimeCode('us', 'ca')).toMatchSnapshot(); // Should not match 'US', 'CA'

		// Whitespace
		expect(determinePrivacyRegimeCode(' US ', ' CA ')).toMatchSnapshot(); // Should not match 'US', 'CA'
	});
});
