export enum Regime {
	CA = 'ca',
	CA_QC = 'ca-qc',
	EXEMPT = 'exempt',
	GDPR = 'gdpr',
	JP = 'jp',
	KR = 'kr',
	LATAM_CO = 'latam-co',
	LATAM_DO = 'latam-do',
	LATAM_PE = 'latam-pe',
	LGPD = 'lgpd',
	PIPL = 'pipl',
	TR_KVKK = 'tr-kvkk',
	USA = 'usa',
	USA_CCPA = 'usa-ccpa',
	USA_CO = 'usa-co',
	USA_CT = 'usa-ct',
	USA_VA = 'usa-va',
	USA_UT = 'usa-ut',
}

export default Regime;

export const RESTRICTIVE_REGIMES: Regime[] = [
	Regime.CA_QC,
	Regime.GDPR,
	Regime.LATAM_CO,
	Regime.LATAM_DO,
	Regime.LATAM_PE,
	Regime.LGPD,
	Regime.PIPL,
	Regime.TR_KVKK,
];

// For WEB US regimes
export const US_REGIMES: Regime[] = [
	Regime.USA,
	Regime.USA_CCPA,
	Regime.USA_CO,
	Regime.USA_CT,
	Regime.USA_VA,
];

export const PRIVACY_REGIMES = [
	Regime.CA,
	...US_REGIMES,
	...RESTRICTIVE_REGIMES,
];
