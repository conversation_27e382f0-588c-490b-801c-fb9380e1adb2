// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`determinePrivacyRegimeCode > should handle edge cases correctly 1`] = `"exempt"`;

exports[`determinePrivacyRegimeCode > should handle edge cases correctly 2`] = `"exempt"`;

exports[`determinePrivacyRegimeCode > should handle edge cases correctly 3`] = `"exempt"`;

exports[`determinePrivacyRegimeCode > should return ca for other Canadian provinces 1`] = `"ca"`;

exports[`determinePrivacyRegimeCode > should return ca-qc for Canada-QC 1`] = `"ca-qc"`;

exports[`determinePrivacyRegimeCode > should return exempt for countries not in any specific regime 1`] = `"exempt"`;

exports[`determinePrivacyRegimeCode > should return exempt for countries not in any specific regime 2`] = `"exempt"`;

exports[`determinePrivacyRegimeCode > should return exempt for countries not in any specific regime 3`] = `"exempt"`;

exports[`determinePrivacyRegimeCode > should return exempt when both country code and region code are null 1`] = `"exempt"`;

exports[`determinePrivacyRegimeCode > should return exempt when country code is null 1`] = `"exempt"`;

exports[`determinePrivacyRegimeCode > should return gdpr for countries in the GDPR list 1`] = `"gdpr"`;

exports[`determinePrivacyRegimeCode > should return gdpr for countries in the GDPR list 2`] = `"gdpr"`;

exports[`determinePrivacyRegimeCode > should return gdpr for countries in the GDPR list 3`] = `"gdpr"`;

exports[`determinePrivacyRegimeCode > should return gdpr for countries in the GDPR list 4`] = `"gdpr"`;

exports[`determinePrivacyRegimeCode > should return gdpr for countries in the GDPR list 5`] = `"gdpr"`;

exports[`determinePrivacyRegimeCode > should return jp for Japan 1`] = `"jp"`;

exports[`determinePrivacyRegimeCode > should return kr for South Korea 1`] = `"kr"`;

exports[`determinePrivacyRegimeCode > should return latam-co for Colombia 1`] = `"latam-co"`;

exports[`determinePrivacyRegimeCode > should return latam-do for Dominican Republic 1`] = `"latam-do"`;

exports[`determinePrivacyRegimeCode > should return latam-pe for Peru 1`] = `"latam-pe"`;

exports[`determinePrivacyRegimeCode > should return lgpd for Brazil 1`] = `"lgpd"`;

exports[`determinePrivacyRegimeCode > should return pipl for China (excluding Taiwan, Hong Kong, and Macau) 1`] = `"pipl"`;

exports[`determinePrivacyRegimeCode > should return tr-kvkk for Turkey 1`] = `"tr-kvkk"`;

exports[`determinePrivacyRegimeCode > should return usa for other US states 1`] = `"usa"`;

exports[`determinePrivacyRegimeCode > should return usa-ccpa for US-CA 1`] = `"usa-ccpa"`;

exports[`determinePrivacyRegimeCode > should return usa-co for US-CO 1`] = `"usa-co"`;

exports[`determinePrivacyRegimeCode > should return usa-ct for US-CT 1`] = `"usa-ct"`;

exports[`determinePrivacyRegimeCode > should return usa-ut for US-UT 1`] = `"usa-ut"`;

exports[`determinePrivacyRegimeCode > should return usa-va for US-VA 1`] = `"usa-va"`;
