{"name": "@repo/navigation", "version": "0.0.1", "type": "module", "private": true, "scripts": {"lint": "eslint --max-warnings=0 .", "check-types": "tsc --noEmit"}, "exports": {"./hooks/usePageNavigation": "./src/hooks/usePageNavigation.ts", "./components/Link": "./src/components/Link.tsx"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@repo/vitest-config": "workspace:*", "@types/node": "catalog:node", "@types/react": "catalog:web", "@types/react-dom": "catalog:web", "eslint": "catalog:dev", "typescript": "catalog:dev", "vitest": "catalog:dev"}, "peerDependencies": {"class-variance-authority": "catalog:web", "next": "catalog:web", "next-intl": "catalog:web", "react": "catalog:web", "react-dom": "catalog:web"}, "dependencies": {}}