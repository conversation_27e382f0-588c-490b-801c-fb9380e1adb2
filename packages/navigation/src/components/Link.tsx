'use client';

import NextLink from 'next/link';
import React from 'react';

/**
 * Custom Link component that can render either a Next.js Link or a regular anchor tag.
 *
 * @param mpa - When true (default), renders as <a> tag. When false, uses Next.js Link.
 * @example
 * // MPA mode (default)
 * <Link href="/about">About</Link>
 *
 * // SPA mode
 * <Link href="/about" mpa={false}>About</Link>
 */
export type LinkProps = React.ComponentProps<typeof NextLink> & {
	mpa?: boolean;
};

const Link = ({ mpa = true, ...props }: LinkProps) => {
	// If mpa is true, render an anchor tag (default behavior)
	if (mpa) {
		// Extract href and any other anchor-specific props
		const { href, children, ...restProps } = props;
		return (
			<a href={href.toString()} {...restProps}>
				{children}
			</a>
		);
	}

	// Otherwise, render the Next.js Link component
	return <NextLink {...props} />;
};

export default Link;
