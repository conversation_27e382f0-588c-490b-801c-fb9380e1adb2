import type { LocationPointResponse } from '@repo/dal/locations/types';
import type { LocationData } from '../types';
import { formatGeocode } from './formatGeocode';
import { formatPresentationName } from '@repo/location/utils/formatPresentationName';

export function fromLocationPoint(
	locationPoint: LocationPointResponse,
): Required<LocationData> {
	return {
		displayName: locationPoint.location.displayName,
		adminDistrict: locationPoint.location.adminDistrict,
		geocode: formatGeocode(
			`${locationPoint.location.latitude},${locationPoint.location.longitude}`,
		),
		placeId: locationPoint.location.placeId,
		city: locationPoint.location.city,
		countryCode: locationPoint.location.countryCode,
		adminDistrictCode: locationPoint.location.adminDistrictCode || '',
		presentationName: formatPresentationName(locationPoint.location),
	};
}
