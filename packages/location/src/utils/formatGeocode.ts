/**
 * Formats a geocode string by rounding latitude and longitude to 2 decimal places
 *
 * @param geocode - The geocode string in format "latitude,longitude"
 * @returns A formatted geocode string with values rounded to 2 decimal places
 *
 * @example
 * // Returns "33.86,-84.34"
 * formatGeocode("33.861,-84.339")
 */
export function formatGeocode(geocode: string): string {
	// <PERSON>le empty or invalid geocode
	if (!geocode || !geocode.includes(',')) {
		return geocode;
	}

	const parts = geocode.split(',');

	// Ensure we have both latitude and longitude parts
	if (parts.length !== 2) {
		return geocode;
	}

	const [latitude, longitude] = parts;

	// Parse strings to numbers and round to 2 decimal places
	const roundedLat = parseFloat(latitude || '0').toFixed(2);
	const roundedLng = parseFloat(longitude || '0').toFixed(2);

	// Create geocode string with rounded values
	return `${roundedLat},${roundedLng}`;
}
