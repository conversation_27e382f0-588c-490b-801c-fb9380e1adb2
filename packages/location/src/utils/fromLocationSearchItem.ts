import type { LocationSearchItem } from '@repo/dal/locations/types';
import type { LocationData } from '@repo/location/types';
import { formatGeocode } from './formatGeocode';
import { formatPresentationName } from '@repo/location/utils/formatPresentationName';

export function fromLocationSearchItem(
	locationSearchItem: LocationSearchItem,
): Required<LocationData> {
	return {
		displayName: locationSearchItem.displayName,
		adminDistrict: locationSearchItem.adminDistrict,
		geocode: formatGeocode(
			`${locationSearchItem.latitude},${locationSearchItem.longitude}`,
		),
		placeId: locationSearchItem.placeId,
		city: locationSearchItem.city,
		countryCode: locationSearchItem.countryCode,
		adminDistrictCode: locationSearchItem.adminDistrictCode || '',
		presentationName: formatPresentationName(locationSearchItem),
	};
}
