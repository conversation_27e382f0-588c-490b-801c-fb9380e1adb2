export function formatPresentationName({
	displayName,
	adminDistrict,
	adminDistrictCode,
	countryCode,
	country,
	disputedArea = true, // treat as disputed area by default
	// todo: start passing actual localeCountryCode through when we have it and remove this default
	localeCountryCode = 'US',
	separator = ', ',
}: {
	displayName: string;
	adminDistrict?: string;
	adminDistrictCode?: string;
	countryCode?: string;
	country?: string;
	disputedArea?: boolean;
	localeCountryCode?: string;
	separator?: string;
}) {
	const parts = [];

	if (displayName) parts.push(displayName);

	// if country is Israel
	if (localeCountryCode === 'IL') {
		// dont add district
	} else if (adminDistrictCode) {
		parts.push(adminDistrictCode);
	} else if (adminDistrict) {
		parts.push(adminDistrict);
	}

	if (countryCode !== localeCountryCode && !disputedArea && country) {
		parts.push(country);
	}

	return parts.join(separator);
}
