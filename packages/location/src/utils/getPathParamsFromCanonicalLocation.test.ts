import { describe, it, expect } from 'vitest';
import { getPathParamsFromCanonicalLocation } from './getPathParamsFromCanonicalLocation';

describe('getPathParamsFromCanonicalLocation', () => {
	describe('Happy Path Scenarios', () => {
		it('should extract all parameters from a complete canonical location array', () => {
			const canonicalLocation = ['US', 'NY', 'city', 'New-York'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toEqual({
				country: 'US',
				adminDistrictCode: 'NY',
				locationType: 'city',
				displayName: 'New-York',
			});
		});

		it('should extract only country from a single-segment array', () => {
			const canonicalLocation = ['US'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toEqual({
				country: 'US',
			});
		});

		it('should extract country and adminDistrictCode from a two-segment array', () => {
			const canonicalLocation = ['CA', 'ON'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toEqual({
				country: 'CA',
				adminDistrictCode: 'ON',
			});
		});

		it('should extract country, adminDistrictCode, and locationType from a three-segment array', () => {
			const canonicalLocation = ['GB', 'ENG', 'city'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toEqual({
				country: 'GB',
				adminDistrictCode: 'ENG',
				locationType: 'city',
			});
		});

		it('should return empty object for empty array', () => {
			const canonicalLocation: string[] = [];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toEqual({});
		});
	});

	describe('Real-World Examples', () => {
		it('should handle typical US city location', () => {
			const canonicalLocation = ['US', 'CA', 'city', 'Los-Angeles'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toEqual({
				country: 'US',
				adminDistrictCode: 'CA',
				locationType: 'city',
				displayName: 'Los-Angeles',
			});
		});

		it('should handle international location with special characters', () => {
			const canonicalLocation = ['FR', 'IDF', 'city', 'Paris'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toEqual({
				country: 'FR',
				adminDistrictCode: 'IDF',
				locationType: 'city',
				displayName: 'Paris',
			});
		});

		it('should handle location with URL-encoded display name', () => {
			const canonicalLocation = ['US', 'NY', 'city', 'New%20York'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toEqual({
				country: 'US',
				adminDistrictCode: 'NY',
				locationType: 'city',
				displayName: 'New%20York',
			});
		});

		it('should handle location with hyphens and numbers', () => {
			const canonicalLocation = ['US', 'FL', 'city', 'Miami-Dade-123'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toEqual({
				country: 'US',
				adminDistrictCode: 'FL',
				locationType: 'city',
				displayName: 'Miami-Dade-123',
			});
		});
	});

	describe('Edge Cases', () => {
		it('should handle array with more than 4 segments by ignoring extra segments', () => {
			const canonicalLocation = [
				'US',
				'TX',
				'city',
				'Houston',
				'extra',
				'segments',
				'ignored',
			];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toEqual({
				country: 'US',
				adminDistrictCode: 'TX',
				locationType: 'city',
				displayName: 'Houston',
			});
		});

		it('should handle array with empty string segments', () => {
			const canonicalLocation = ['', 'NY', '', 'Buffalo'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toEqual({
				country: '',
				adminDistrictCode: 'NY',
				locationType: '',
				displayName: 'Buffalo',
			});
		});

		it('should handle array with all empty strings', () => {
			const canonicalLocation = ['', '', '', ''];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toEqual({
				country: '',
				adminDistrictCode: '',
				locationType: '',
				displayName: '',
			});
		});

		it('should handle array with whitespace-only segments', () => {
			const canonicalLocation = [' ', '  ', '\t', '\n'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toEqual({
				country: ' ',
				adminDistrictCode: '  ',
				locationType: '\t',
				displayName: '\n',
			});
		});

		it('should handle array with special characters and symbols', () => {
			const canonicalLocation = ['US@', 'NY#', 'city$', 'New-York%'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toEqual({
				country: 'US@',
				adminDistrictCode: 'NY#',
				locationType: 'city$',
				displayName: 'New-York%',
			});
		});

		it('should handle array with unicode characters', () => {
			const canonicalLocation = ['中国', '北京', '城市', '北京市'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toEqual({
				country: '中国',
				adminDistrictCode: '北京',
				locationType: '城市',
				displayName: '北京市',
			});
		});
	});

	describe('Type Safety and Return Structure', () => {
		it('should return an object with correct property names', () => {
			const canonicalLocation = ['US', 'NY', 'city', 'New-York'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toHaveProperty('country');
			expect(result).toHaveProperty('adminDistrictCode');
			expect(result).toHaveProperty('locationType');
			expect(result).toHaveProperty('displayName');
		});

		it('should not include undefined properties for shorter arrays', () => {
			const canonicalLocation = ['US', 'NY'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toHaveProperty('country');
			expect(result).toHaveProperty('adminDistrictCode');
			expect(result).not.toHaveProperty('locationType');
			expect(result).not.toHaveProperty('displayName');
		});

		it('should return object with string values for all defined properties', () => {
			const canonicalLocation = ['US', 'NY', 'city', 'New-York'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(typeof result.country).toBe('string');
			expect(typeof result.adminDistrictCode).toBe('string');
			expect(typeof result.locationType).toBe('string');
			expect(typeof result.displayName).toBe('string');
		});

		it('should handle numeric strings correctly', () => {
			const canonicalLocation = ['123', '456', '789', '000'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toEqual({
				country: '123',
				adminDistrictCode: '456',
				locationType: '789',
				displayName: '000',
			});
		});
	});

	describe('Boundary Conditions', () => {
		it('should handle exactly 4 segments without issues', () => {
			const canonicalLocation = ['A', 'B', 'C', 'D'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toEqual({
				country: 'A',
				adminDistrictCode: 'B',
				locationType: 'C',
				displayName: 'D',
			});
		});

		it('should handle exactly 5 segments by ignoring the 5th', () => {
			const canonicalLocation = ['A', 'B', 'C', 'D', 'E'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toEqual({
				country: 'A',
				adminDistrictCode: 'B',
				locationType: 'C',
				displayName: 'D',
			});
		});

		it('should handle very long display names', () => {
			const longDisplayName = 'A'.repeat(1000);
			const canonicalLocation = ['US', 'NY', 'city', longDisplayName];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result).toEqual({
				country: 'US',
				adminDistrictCode: 'NY',
				locationType: 'city',
				displayName: longDisplayName,
			});
		});
	});

	describe('Common URL Patterns', () => {
		it('should handle URL-encoded spaces', () => {
			const canonicalLocation = ['US', 'NY', 'city', 'New%20York%20City'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result.displayName).toBe('New%20York%20City');
		});

		it('should handle URL-encoded special characters', () => {
			const canonicalLocation = ['US', 'CA', 'city', 'San%20Jos%C3%A9'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result.displayName).toBe('San%20Jos%C3%A9');
		});

		it('should handle hyphenated location names', () => {
			const canonicalLocation = ['US', 'NC', 'city', 'Winston-Salem'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result.displayName).toBe('Winston-Salem');
		});

		it('should handle location names with periods', () => {
			const canonicalLocation = ['US', 'MO', 'city', 'St.Louis'];
			const result = getPathParamsFromCanonicalLocation(canonicalLocation);

			expect(result.displayName).toBe('St.Louis');
		});
	});
});
