/**
 * Extracts query parameters from canonicalLocation segments
 * Maps each position in the array to the corresponding parameter according to the pattern:
 * /:country/:adminDistrictCode/:locationType/:displayName
 *
 * @param canonicalLocation - Array of URL segments
 * @returns Object with extracted path parameters
 */
export function getPathParamsFromCanonicalLocation(
	canonicalLocation: string[],
): {
	country?: string;
	adminDistrictCode?: string;
	locationType?: string;
	displayName?: string;
} {
	const pathParams: {
		country?: string;
		adminDistrictCode?: string;
		locationType?: string;
		displayName?: string;
	} = {};

	// Map each position in the array to the corresponding parameter
	if (canonicalLocation.length >= 1) pathParams.country = canonicalLocation[0];
	if (canonicalLocation.length >= 2)
		pathParams.adminDistrictCode = canonicalLocation[1];
	if (canonicalLocation.length >= 3)
		pathParams.locationType = canonicalLocation[2];
	if (canonicalLocation.length >= 4)
		pathParams.displayName = canonicalLocation[3];

	return pathParams;
}
