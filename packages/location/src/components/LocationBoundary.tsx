'use client';

import React from 'react';
import { pageLocationDataAtom } from '@repo/location/atoms/pagelocation';
import type { LocationData } from '@repo/location/types';
import { useRehydrateAtoms } from '@repo/utils/hooks/useRehydrateAtoms';

interface LocationBoundaryProps {
	location?: LocationData;
}

export const LocationBoundary: React.FC<LocationBoundaryProps> = (
	props: LocationBoundaryProps,
) => {
	useRehydrateAtoms([[pageLocationDataAtom, props.location]]);

	return null;
};
