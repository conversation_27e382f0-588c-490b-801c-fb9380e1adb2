'use client';

import { useAtomValue } from 'jotai';
import useS<PERSON> from 'swr';
import { userLocationsAtom } from '@repo/user/atoms/preferences/location';
import { wxuUserRecentLocationsAtom } from '@repo/user/atoms/wxu/user';
import { geoipGeocode } from '../atoms/geolocation';
import {
	getLocationPointByGeocode,
	getLocationPointByPlaceId,
} from '@repo/dal/locations/point';
import { pageLocationDataAtom } from '@repo/location/atoms/pagelocation';
import { type LocationData } from '@repo/location/types';
import { fromLocationPoint } from '@repo/location/utils/fromLocationPoint';
import { useParams } from 'next/navigation';

// Add an enum for fallback order options
export enum LocationFallbackOrder {
	PREFER_FAVORITES = 'preferFavorites',
	PREFER_RECENT = 'preferRecent',
}

interface UseLocationSourceProps {
	location?: LocationData;
	fallbackOrder?: LocationFallbackOrder;
}

interface UseLocationSourceResult {
	effectiveLocation: LocationData | undefined;
	isLocationLoading: boolean;
	locationError: Error | null;
}

/**
 * Custom hook that handles location source priority and fetching
 *
 * Priority order:
 * 1a. Direct location prop - for when a component using this hook has a specific location
 *     (regardless if its on a location based page) (highest priority)
 * 1b. Page location from pageLocation atom - for when this atom is populated from page.tsx (from path params and
 *     dal location call), it should use this over 2, 3, or 4.
 * 2.  Favorite location from favorites atom - when specific location or page location is not present
 *     (AKA non location-based pages), use fallBackOrder or defaults to determine if this hook needs to resolve
 *     location from localStorage preferences like favorites or recent searches
 * 3.  Recent location from wxuUserRecentLocationsAtom (only if fallbackOrder is PREFER_RECENT)
 * 4.  GeoIP location from geoipGeocode atom (lowest priority)
 */
export function useLocationSource({
	location,
	fallbackOrder = LocationFallbackOrder.PREFER_FAVORITES,
}: UseLocationSourceProps): UseLocationSourceResult {
	const params = useParams();
	const locale = params?.locale as string;

	const locationFromPage = useAtomValue(pageLocationDataAtom);

	// Get favorite locations from atom
	const userLocations = useAtomValue(userLocationsAtom);

	// Get recent locations from atom
	const recentLocations = useAtomValue(wxuUserRecentLocationsAtom);

	// Get geoip geocode from atom
	const geoipGeocodeValue = useAtomValue(geoipGeocode);

	// Get favorite location geocode if available
	const favoriteGeocode =
		Array.isArray(userLocations) && userLocations.length > 0
			? userLocations[0]?.coordinate
			: null;

	// Get first recent location ID if available
	const recentLocationId =
		Array.isArray(recentLocations) && recentLocations.length > 0
			? recentLocations[0]
			: null;

	const preferRecentFallbackOrder =
		fallbackOrder === LocationFallbackOrder.PREFER_RECENT;

	// SWR fetcher function for location data by geocode
	const locationFetcher = (geocode: string) => {
		return getLocationPointByGeocode(geocode, locale);
	};

	// SWR fetcher function for location data by place ID
	const placeIdFetcher = (placeId: string) => {
		return getLocationPointByPlaceId(placeId, locale);
	};

	// Use SWR to fetch location data for favorite location
	const {
		data: favoriteLocationData,
		error: favoriteLocationError,
		isLoading: isFavoriteLocationLoading,
	} = useSWR(
		// Only fetch if we have a favorite and no direct location prop
		!location && favoriteGeocode ? ['location-data', favoriteGeocode] : null,
		() => (favoriteGeocode ? locationFetcher(favoriteGeocode) : null),
		{
			revalidateOnFocus: false,
			dedupingInterval: 300000, // 5 minutes
		},
	);

	// Fetch location data for recent location if needed
	const {
		data: recentLocationData,
		error: recentLocationError,
		isLoading: isRecentLocationLoading,
	} = useSWR(
		// Only fetch if we have a recent location ID, no direct location prop, no favorite location,
		// and fallbackOrder is PREFER_RECENT
		!location &&
			!favoriteGeocode &&
			recentLocationId &&
			preferRecentFallbackOrder
			? ['recent-location-data', recentLocationId]
			: null,
		() => (recentLocationId ? placeIdFetcher(recentLocationId) : null),
		{
			revalidateOnFocus: false,
			dedupingInterval: 300000, // 5 minutes
		},
	);

	// Fetch location data for geoip geocode if needed
	const {
		data: geoipLocationData,
		error: geoipLocationError,
		isLoading: isGeoipLocationLoading,
	} = useSWR(
		// Only fetch if we have a geoip geocode, no direct location prop, no favorite location,
		// and either fallbackOrder is PREFER_FAVORITES or (fallbackOrder is PREFER_RECENT and no recent location)
		!location &&
			!favoriteGeocode &&
			(fallbackOrder === LocationFallbackOrder.PREFER_FAVORITES ||
				(preferRecentFallbackOrder && !recentLocationId)) &&
			geoipGeocodeValue
			? ['geoip-location-data', geoipGeocodeValue]
			: null,
		() => (geoipGeocodeValue ? locationFetcher(geoipGeocodeValue) : null),
		{
			revalidateOnFocus: false,
			dedupingInterval: 300000, // 5 minutes
		},
	);

	// Directly compute effectiveLocation based on priority order
	let effectiveLocation: LocationData | undefined = undefined;

	// 1a. Location from props (specified location) - highest priority
	if (location) {
		effectiveLocation = location;
	}
	// 1b. Location from page
	else if (locationFromPage) {
		effectiveLocation = locationFromPage;
	}
	// 2. First favorite location (if available and data loaded)
	else if (favoriteLocationData && favoriteGeocode) {
		effectiveLocation = fromLocationPoint(favoriteLocationData);
	}
	// Fallback to basic info if fetch fails but we have geocode
	else if (favoriteLocationError && favoriteGeocode) {
		console.error(
			'Error fetching favorite location data:',
			favoriteLocationError,
		);
		effectiveLocation = {
			displayName: 'Favorite Location',
			adminDistrict: '',
			geocode: favoriteGeocode,
			placeId: 'unknown', // Fallback placeId
		};
	}
	// 3. Recent location from wxuUserRecentLocationsAtom (only if fallbackOrder is PREFER_RECENT)
	else if (
		preferRecentFallbackOrder &&
		recentLocationData &&
		recentLocationId
	) {
		effectiveLocation = fromLocationPoint(recentLocationData);
	}
	// Fallback to basic info if fetch fails but we have recent location ID (only if fallbackOrder is PREFER_RECENT)
	else if (
		preferRecentFallbackOrder &&
		recentLocationError &&
		recentLocationId
	) {
		console.error('Error fetching recent location data:', recentLocationError);
		effectiveLocation = {
			displayName: 'Recent Location',
			adminDistrict: '',
			geocode: '',
			placeId: recentLocationId,
		};
	}
	// 4. GeoIP location from atom and fetched data
	else if (geoipLocationData && geoipGeocodeValue) {
		effectiveLocation = fromLocationPoint(geoipLocationData);
	}
	// Fallback to basic info if fetch fails but we have geocode
	else if (geoipLocationError && geoipGeocodeValue) {
		console.error('Error fetching geoip location data:', geoipLocationError);
		effectiveLocation = {
			displayName: 'Unknown Location',
			adminDistrict: '',
			geocode: geoipGeocodeValue,
			placeId: 'unknown', // Fallback placeId
			city: '',
			countryCode: '',
			adminDistrictCode: '',
		};
	}

	// Combine loading and error states
	const isLocationLoading =
		isFavoriteLocationLoading ||
		(preferRecentFallbackOrder ? isRecentLocationLoading : false) ||
		isGeoipLocationLoading;
	const locationError =
		favoriteLocationError ||
		(preferRecentFallbackOrder ? recentLocationError : null) ||
		geoipLocationError;

	return {
		effectiveLocation,
		isLocationLoading,
		locationError: locationError as Error | null,
	};
}
