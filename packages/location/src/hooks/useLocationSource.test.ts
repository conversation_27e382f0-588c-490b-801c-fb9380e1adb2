import { describe, it, expect, vi, beforeEach, afterAll } from 'vitest';
import { renderHook } from '@testing-library/react';
import { useLocationSource, LocationFallbackOrder } from './useLocationSource';
import { useAtomValue } from 'jotai';
import useSWR from 'swr';

// Define types for better type safety in tests
interface LocationPoint {
	location: {
		displayName: string;
		adminDistrict: string;
		latitude: number;
		longitude: number;
		placeId: string;
	};
}

// Mock dependencies
vi.mock('jotai', async () => {
	const actual = await vi.importActual('jotai');
	return {
		...actual,
		useAtomValue: vi.fn(),
	};
});

vi.mock('@repo/location/atoms/pagelocation', () => ({
	pageLocationDataAtom: {},
}));

vi.mock('swr', async () => {
	const actual = await vi.importActual('swr');
	return {
		...actual,
		default: vi.fn().mockImplementation(() => ({
			data: null,
			error: null,
			isLoading: false,
			mutate: vi.fn(),
			isValidating: false,
		})),
	};
});

vi.mock('@repo/dal/locations/point', () => ({
	getLocationPointByGeocode: vi.fn(),
	getLocationPointByPlaceId: vi.fn(),
}));

vi.mock('@repo/location/utils/fromLocationPoint', () => ({
	fromLocationPoint: vi.fn((data) => {
		// For error handling test cases
		if (data === 'favorite-error') {
			return {
				displayName: 'Favorite Location',
				adminDistrict: '',
				geocode: '35.6895,139.6917',
				placeId: 'unknown',
			};
		}

		if (data === 'recent-error') {
			return {
				displayName: 'Recent Location',
				adminDistrict: '',
				geocode: '',
				placeId: 'ny-456',
			};
		}

		if (data === 'geoip-error') {
			return {
				displayName: 'Unknown Location',
				adminDistrict: '',
				geocode: '34.0522,-118.2437',
				placeId: 'unknown',
				city: '',
				countryCode: '',
				adminDistrictCode: '',
			};
		}

		// For normal test cases, return the expected test values
		if (!data || !data.location) {
			return {
				displayName: 'Converted Location',
				adminDistrict: '',
				geocode: '0,0',
				placeId: 'converted-id',
				city: '',
				countryCode: '',
				adminDistrictCode: '',
				presentationName: 'Converted Location',
			};
		}

		// For test cases that expect the actual location data
		return {
			displayName: 'Converted Location',
			adminDistrict: '',
			geocode: `${data.location.latitude},${data.location.longitude}`,
			placeId: 'converted-id',
			city: '',
			countryCode: '',
			adminDistrictCode: '',
			presentationName: 'Converted Location',
		};
	}),
}));

// Mock console.error to avoid cluttering test output
const originalConsoleError = console.error;
beforeEach(() => {
	console.error = vi.fn();

	// Reset all mocks before each test
	vi.mocked(useAtomValue).mockReset();
	vi.mocked(useSWR).mockReset();
});

afterAll(() => {
	console.error = originalConsoleError;
});

describe('useLocationSource', () => {
	// Helper function to set up atom values
	function setupAtoms({
		pageLocationData = null as any,
		userLocations = [] as Array<{ coordinate: string }>,
		recentLocations = [] as string[],
		geoipGeocode = null as string | null,
	}) {
		vi.mocked(useAtomValue).mockImplementation((atom: any) => {
			// Identify which atom is being accessed based on the mock call count
			const callCount = vi.mocked(useAtomValue).mock.calls.length;

			if (callCount === 1) return pageLocationData; // pageLocationDataAtom
			if (callCount === 2) return userLocations; // userLocationsAtom
			if (callCount === 3) return recentLocations; // wxuUserRecentLocationsAtom
			if (callCount === 4) return geoipGeocode; // geoipGeocode

			return null;
		});
	}

	// Helper function to set up SWR responses
	function setupSWR({
		favoriteLocationData = null as LocationPoint | null,
		favoriteLocationError = null as Error | null,
		isFavoriteLocationLoading = false,
		recentLocationData = null as LocationPoint | null,
		recentLocationError = null as Error | null,
		isRecentLocationLoading = false,
		geoipLocationData = null as LocationPoint | null,
		geoipLocationError = null as Error | null,
		isGeoipLocationLoading = false,
	}) {
		vi.mocked(useSWR).mockImplementation((key: any) => {
			if (key && Array.isArray(key) && key[0] === 'location-data') {
				return {
					data: favoriteLocationData,
					error: favoriteLocationError,
					isLoading: isFavoriteLocationLoading,
					mutate: vi.fn(),
					isValidating: false,
				};
			}

			if (key && Array.isArray(key) && key[0] === 'recent-location-data') {
				return {
					data: recentLocationData,
					error: recentLocationError,
					isLoading: isRecentLocationLoading,
					mutate: vi.fn(),
					isValidating: false,
				};
			}

			if (key && Array.isArray(key) && key[0] === 'geoip-location-data') {
				return {
					data: geoipLocationData,
					error: geoipLocationError,
					isLoading: isGeoipLocationLoading,
					mutate: vi.fn(),
					isValidating: false,
				};
			}

			return {
				data: null,
				error: null,
				isLoading: false,
				mutate: vi.fn(),
				isValidating: false,
			};
		});
	}

	// Test Scenario 1: Direct location prop (highest priority)
	it('should use direct location prop when provided', () => {
		// Setup atoms with all possible fallback data
		setupAtoms({
			pageLocationData: null,
			userLocations: [{ coordinate: '35.6895,139.6917' }],
			recentLocations: ['tokyo-123'],
			geoipGeocode: '40.7128,-74.0060',
		});

		// Setup SWR with all possible fallback data
		setupSWR({
			favoriteLocationData: {
				location: {
					displayName: 'Tokyo',
					adminDistrict: 'Tokyo',
					latitude: 35.6895,
					longitude: 139.6917,
					placeId: 'tokyo-123',
				},
			},
			recentLocationData: {
				location: {
					displayName: 'New York',
					adminDistrict: 'NY',
					latitude: 40.7128,
					longitude: -74.006,
					placeId: 'ny-456',
				},
			},
			geoipLocationData: {
				location: {
					displayName: 'Los Angeles',
					adminDistrict: 'CA',
					latitude: 34.0522,
					longitude: -118.2437,
					placeId: 'la-789',
				},
			},
		});

		// Direct location prop should be used regardless of other data
		const directLocation = {
			displayName: 'San Francisco',
			adminDistrict: 'CA',
			geocode: '37.7749,-122.4194',
			placeId: 'sf-101',
		};

		// Test with both fallback order options to ensure direct location always wins
		const { result: resultPreferFavorites } = renderHook(() =>
			useLocationSource({
				location: directLocation,
				fallbackOrder: LocationFallbackOrder.PREFER_FAVORITES,
			}),
		);

		const { result: resultPreferRecent } = renderHook(() =>
			useLocationSource({
				location: directLocation,
				fallbackOrder: LocationFallbackOrder.PREFER_RECENT,
			}),
		);

		// Verify direct location is used in both cases
		expect(resultPreferFavorites.current.effectiveLocation).toEqual(
			directLocation,
		);
		expect(resultPreferRecent.current.effectiveLocation).toEqual(
			directLocation,
		);

		// Verify no loading or error states
		expect(resultPreferFavorites.current.isLocationLoading).toBe(false);
		expect(resultPreferFavorites.current.locationError).toBe(null);
		expect(resultPreferRecent.current.isLocationLoading).toBe(false);
		expect(resultPreferRecent.current.locationError).toBe(null);
	});

	// Test Scenario 1b: Page location data (when no direct location prop)
	it('should use page location data when no direct location prop is provided but pageLocationData is populated', () => {
		// Page location data that should be used
		const pageLocationData = {
			displayName: 'Chicago',
			adminDistrict: 'IL',
			geocode: '41.8781,-87.6298',
			placeId: 'chicago-123',
			city: 'Chicago',
			countryCode: 'US',
			adminDistrictCode: 'IL',
			presentationName: 'Chicago, IL',
		};

		// Setup atoms with page location data and all possible fallback data
		setupAtoms({
			pageLocationData, // This should take priority
			userLocations: [{ coordinate: '35.6895,139.6917' }], // Has favorites
			recentLocations: ['tokyo-123'], // Has recent
			geoipGeocode: '40.7128,-74.0060', // Has geoIP
		});

		// Setup SWR with all possible fallback data (should not be used)
		setupSWR({
			favoriteLocationData: {
				location: {
					displayName: 'Tokyo',
					adminDistrict: 'Tokyo',
					latitude: 35.6895,
					longitude: 139.6917,
					placeId: 'tokyo-123',
				},
			},
			recentLocationData: {
				location: {
					displayName: 'New York',
					adminDistrict: 'NY',
					latitude: 40.7128,
					longitude: -74.006,
					placeId: 'ny-456',
				},
			},
			geoipLocationData: {
				location: {
					displayName: 'Los Angeles',
					adminDistrict: 'CA',
					latitude: 34.0522,
					longitude: -118.2437,
					placeId: 'la-789',
				},
			},
		});

		// Test with PREFER_FAVORITES - page location should still win
		const { result: resultPreferFavorites } = renderHook(() =>
			useLocationSource({
				fallbackOrder: LocationFallbackOrder.PREFER_FAVORITES,
			}),
		);

		// Verify page location is used exactly as provided
		expect(resultPreferFavorites.current.effectiveLocation).toEqual(
			pageLocationData,
		);

		// Verify no loading or error states (no SWR calls should be made)
		expect(resultPreferFavorites.current.isLocationLoading).toBe(false);
		expect(resultPreferFavorites.current.locationError).toBe(null);

		// Reset mocks for the second test
		vi.mocked(useAtomValue).mockReset();
		vi.mocked(useSWR).mockReset();

		// Setup atoms again with the same data
		setupAtoms({
			pageLocationData, // This should take priority
			userLocations: [{ coordinate: '35.6895,139.6917' }], // Has favorites
			recentLocations: ['tokyo-123'], // Has recent
			geoipGeocode: '40.7128,-74.0060', // Has geoIP
		});

		// Setup SWR with the same fallback data (should not be used)
		setupSWR({
			favoriteLocationData: {
				location: {
					displayName: 'Tokyo',
					adminDistrict: 'Tokyo',
					latitude: 35.6895,
					longitude: 139.6917,
					placeId: 'tokyo-123',
				},
			},
			recentLocationData: {
				location: {
					displayName: 'New York',
					adminDistrict: 'NY',
					latitude: 40.7128,
					longitude: -74.006,
					placeId: 'ny-456',
				},
			},
			geoipLocationData: {
				location: {
					displayName: 'Los Angeles',
					adminDistrict: 'CA',
					latitude: 34.0522,
					longitude: -118.2437,
					placeId: 'la-789',
				},
			},
		});

		// Test with PREFER_RECENT - page location should still win
		const { result: resultPreferRecent } = renderHook(() =>
			useLocationSource({
				fallbackOrder: LocationFallbackOrder.PREFER_RECENT,
			}),
		);

		// Verify page location is used exactly as provided
		expect(resultPreferRecent.current.effectiveLocation).toEqual(
			pageLocationData,
		);

		// Verify no loading or error states (no SWR calls should be made)
		expect(resultPreferRecent.current.isLocationLoading).toBe(false);
		expect(resultPreferRecent.current.locationError).toBe(null);
	});

	// Test Scenario 2: Favorite location (when no location prop or page location)
	it('should use favorite location when no direct location prop or page location is provided', () => {
		// Setup atoms with favorite and other fallback data
		setupAtoms({
			pageLocationData: null,
			userLocations: [{ coordinate: '35.6895,139.6917' }],
			recentLocations: ['tokyo-123'],
			geoipGeocode: '40.7128,-74.0060',
		});

		// Setup SWR with favorite location data
		const favoriteLocationData = {
			location: {
				displayName: 'Tokyo',
				adminDistrict: 'Tokyo',
				latitude: 35.6895,
				longitude: 139.6917,
				placeId: 'tokyo-123',
			},
		};

		setupSWR({
			favoriteLocationData,
			recentLocationData: {
				location: {
					displayName: 'New York',
					adminDistrict: 'NY',
					latitude: 40.7128,
					longitude: -74.006,
					placeId: 'ny-456',
				},
			},
			geoipLocationData: {
				location: {
					displayName: 'Los Angeles',
					adminDistrict: 'CA',
					latitude: 34.0522,
					longitude: -118.2437,
					placeId: 'la-789',
				},
			},
		});

		// Test with PREFER_FAVORITES
		const { result: resultPreferFavorites } = renderHook(() =>
			useLocationSource({
				fallbackOrder: LocationFallbackOrder.PREFER_FAVORITES,
			}),
		);

		// Verify favorite location is used
		expect(resultPreferFavorites.current.effectiveLocation).toEqual({
			displayName: 'Converted Location',
			adminDistrict: '',
			geocode: '35.6895,139.6917',
			placeId: 'converted-id',
			city: '',
			countryCode: '',
			adminDistrictCode: '',
			presentationName: 'Converted Location',
		});

		// Reset mocks for the second test
		vi.mocked(useAtomValue).mockReset();
		vi.mocked(useSWR).mockReset();

		// Setup atoms again with the same data
		setupAtoms({
			pageLocationData: null,
			userLocations: [{ coordinate: '35.6895,139.6917' }],
			recentLocations: ['tokyo-123'],
			geoipGeocode: '40.7128,-74.0060',
		});

		// Setup SWR with the same data
		setupSWR({
			favoriteLocationData,
			recentLocationData: {
				location: {
					displayName: 'New York',
					adminDistrict: 'NY',
					latitude: 40.7128,
					longitude: -74.006,
					placeId: 'ny-456',
				},
			},
			geoipLocationData: {
				location: {
					displayName: 'Los Angeles',
					adminDistrict: 'CA',
					latitude: 34.0522,
					longitude: -118.2437,
					placeId: 'la-789',
				},
			},
		});

		// Test with PREFER_RECENT
		const { result: resultPreferRecent } = renderHook(() =>
			useLocationSource({
				fallbackOrder: LocationFallbackOrder.PREFER_RECENT,
			}),
		);

		// Verify favorite location is used with PREFER_RECENT as well
		expect(resultPreferRecent.current.effectiveLocation).toEqual({
			displayName: 'Converted Location',
			adminDistrict: '',
			geocode: '35.6895,139.6917',
			placeId: 'converted-id',
			city: '',
			countryCode: '',
			adminDistrictCode: '',
			presentationName: 'Converted Location',
		});
	});

	// Test Scenario 3: Recent location with PREFER_RECENT (when no location prop, page location, or favorite location)
	it('should use recent location when no direct location, page location, or favorite location and fallbackOrder is PREFER_RECENT', () => {
		// Setup atoms with recent location but no favorite
		setupAtoms({
			pageLocationData: null,
			userLocations: [], // No favorite locations
			recentLocations: ['ny-456'], // Has recent location
			geoipGeocode: '34.0522,-118.2437', // Has geoIP
		});

		// Setup SWR with recent location data
		const recentLocationData = {
			location: {
				displayName: 'New York',
				adminDistrict: 'NY',
				latitude: 40.7128,
				longitude: -74.006,
				placeId: 'ny-456',
			},
		};

		setupSWR({
			favoriteLocationData: null, // No favorite data
			recentLocationData,
			geoipLocationData: {
				location: {
					displayName: 'Los Angeles',
					adminDistrict: 'CA',
					latitude: 34.0522,
					longitude: -118.2437,
					placeId: 'la-789',
				},
			},
		});

		// Test with PREFER_RECENT
		const { result: resultPreferRecent } = renderHook(() =>
			useLocationSource({
				fallbackOrder: LocationFallbackOrder.PREFER_RECENT,
			}),
		);

		// Verify recent location is used
		expect(resultPreferRecent.current.effectiveLocation).toEqual({
			displayName: 'Converted Location',
			adminDistrict: '',
			geocode: '40.7128,-74.006', // Fixed to match the actual value
			placeId: 'converted-id',
			city: '',
			countryCode: '',
			adminDistrictCode: '',
			presentationName: 'Converted Location',
		});
	});

	// Test Scenario 4: Skip recent location with PREFER_FAVORITES
	it('should skip recent location and use geoIP when fallbackOrder is PREFER_FAVORITES', () => {
		// Setup atoms with recent location but no favorite
		setupAtoms({
			pageLocationData: null,
			userLocations: [], // No favorite locations
			recentLocations: ['ny-456'], // Has recent location
			geoipGeocode: '34.0522,-118.2437', // Has geoIP
		});

		// Setup SWR with both recent and geoIP data
		setupSWR({
			favoriteLocationData: null, // No favorite data
			recentLocationData: {
				location: {
					displayName: 'New York',
					adminDistrict: 'NY',
					latitude: 40.7128,
					longitude: -74.006,
					placeId: 'ny-456',
				},
			},
			geoipLocationData: {
				location: {
					displayName: 'Los Angeles',
					adminDistrict: 'CA',
					latitude: 34.0522,
					longitude: -118.2437,
					placeId: 'la-789',
				},
			},
		});

		// Test with PREFER_FAVORITES
		const { result: resultPreferFavorites } = renderHook(() =>
			useLocationSource({
				fallbackOrder: LocationFallbackOrder.PREFER_FAVORITES,
			}),
		);

		// Verify geoIP location is used, skipping recent
		expect(resultPreferFavorites.current.effectiveLocation).toEqual({
			displayName: 'Converted Location',
			adminDistrict: '',
			geocode: '34.0522,-118.2437',
			placeId: 'converted-id',
			city: '',
			countryCode: '',
			adminDistrictCode: '',
			presentationName: 'Converted Location',
		});
	});

	// Test Scenario 5: GeoIP location (lowest priority)
	it('should use geoIP location when no other sources are available', () => {
		// Setup atoms with only geoIP
		setupAtoms({
			pageLocationData: null,
			userLocations: [], // No favorite locations
			recentLocations: [], // No recent locations
			geoipGeocode: '34.0522,-118.2437', // Has geoIP
		});

		// Setup SWR with only geoIP data
		setupSWR({
			favoriteLocationData: null,
			recentLocationData: null,
			geoipLocationData: {
				location: {
					displayName: 'Los Angeles',
					adminDistrict: 'CA',
					latitude: 34.0522,
					longitude: -118.2437,
					placeId: 'la-789',
				},
			},
		});

		// Test with PREFER_FAVORITES
		const { result: resultPreferFavorites } = renderHook(() =>
			useLocationSource({
				fallbackOrder: LocationFallbackOrder.PREFER_FAVORITES,
			}),
		);

		// Verify geoIP location is used
		expect(resultPreferFavorites.current.effectiveLocation).toEqual({
			displayName: 'Converted Location',
			adminDistrict: '',
			geocode: '34.0522,-118.2437',
			placeId: 'converted-id',
			city: '',
			countryCode: '',
			adminDistrictCode: '',
			presentationName: 'Converted Location',
		});

		// Reset mocks for the second test
		vi.mocked(useAtomValue).mockReset();
		vi.mocked(useSWR).mockReset();

		// Setup atoms again with the same data
		setupAtoms({
			pageLocationData: null,
			userLocations: [], // No favorite locations
			recentLocations: [], // No recent locations
			geoipGeocode: '34.0522,-118.2437', // Has geoIP
		});

		// Setup SWR with the same data
		setupSWR({
			favoriteLocationData: null,
			recentLocationData: null,
			geoipLocationData: {
				location: {
					displayName: 'Los Angeles',
					adminDistrict: 'CA',
					latitude: 34.0522,
					longitude: -118.2437,
					placeId: 'la-789',
				},
			},
		});

		// Test with PREFER_RECENT
		const { result: resultPreferRecent } = renderHook(() =>
			useLocationSource({
				fallbackOrder: LocationFallbackOrder.PREFER_RECENT,
			}),
		);

		// Verify geoIP location is used with PREFER_RECENT as well
		expect(resultPreferRecent.current.effectiveLocation).toEqual({
			displayName: 'Converted Location',
			adminDistrict: '',
			geocode: '34.0522,-118.2437',
			placeId: 'converted-id',
			city: '',
			countryCode: '',
			adminDistrictCode: '',
			presentationName: 'Converted Location',
		});
	});

	// Test Scenario 6: Error handling for favorite location
	it('should handle errors when fetching favorite location data', () => {
		// Setup atoms with favorite location
		setupAtoms({
			pageLocationData: null,
			userLocations: [{ coordinate: '35.6895,139.6917' }],
			recentLocations: [],
			geoipGeocode: null,
		});

		// Setup SWR with error for favorite location
		setupSWR({
			favoriteLocationError: new Error('Failed to fetch favorite location'),
			isFavoriteLocationLoading: false,
		});

		// Test with default fallback order
		const { result } = renderHook(() => useLocationSource({}));

		// Verify fallback location is used with the geocode
		expect(result.current.effectiveLocation).toEqual({
			displayName: 'Favorite Location',
			adminDistrict: '',
			geocode: '35.6895,139.6917',
			placeId: 'unknown',
		});

		// Verify error state
		expect(result.current.locationError).toBeInstanceOf(Error);
		expect(console.error).toHaveBeenCalled();
	});

	// Test Scenario 7: Error handling for recent location with PREFER_RECENT
	it('should handle errors when fetching recent location data with PREFER_RECENT', () => {
		// Setup atoms with recent location but no favorite
		setupAtoms({
			pageLocationData: null,
			userLocations: [],
			recentLocations: ['ny-456'],
			geoipGeocode: null,
		});

		// Setup SWR with error for recent location
		setupSWR({
			recentLocationError: new Error('Failed to fetch recent location'),
			isRecentLocationLoading: false,
		});

		// Test with PREFER_RECENT
		const { result } = renderHook(() =>
			useLocationSource({
				fallbackOrder: LocationFallbackOrder.PREFER_RECENT,
			}),
		);

		// Verify fallback location is used with the placeId
		expect(result.current.effectiveLocation).toEqual({
			displayName: 'Recent Location',
			adminDistrict: '',
			geocode: '',
			placeId: 'ny-456',
		});

		// Verify error state
		expect(result.current.locationError).toBeInstanceOf(Error);
		expect(console.error).toHaveBeenCalled();
	});

	// Test Scenario 8: Error handling for geoIP location
	it('should handle errors when fetching geoIP location data', () => {
		// Setup atoms with only geoIP
		setupAtoms({
			pageLocationData: null,
			userLocations: [],
			recentLocations: [],
			geoipGeocode: '34.0522,-118.2437',
		});

		// Setup SWR with error for geoIP location
		setupSWR({
			geoipLocationError: new Error('Failed to fetch geoIP location'),
			isGeoipLocationLoading: false,
		});

		// Test with default fallback order
		const { result } = renderHook(() => useLocationSource({}));

		// Verify fallback location is used with the geocode
		expect(result.current.effectiveLocation).toEqual({
			displayName: 'Unknown Location',
			adminDistrict: '',
			geocode: '34.0522,-118.2437',
			placeId: 'unknown',
			city: '',
			countryCode: '',
			adminDistrictCode: '',
		});

		// Verify error state
		expect(result.current.locationError).toBeInstanceOf(Error);
		expect(console.error).toHaveBeenCalled();
	});

	// Test Scenario 9: Loading states
	it('should report loading state correctly', () => {
		// Setup atoms with all possible data
		setupAtoms({
			pageLocationData: null,
			userLocations: [{ coordinate: '35.6895,139.6917' }],
			recentLocations: ['ny-456'],
			geoipGeocode: '34.0522,-118.2437',
		});

		// Setup SWR with loading states for favorite location
		setupSWR({
			isFavoriteLocationLoading: true,
			isRecentLocationLoading: false,
			isGeoipLocationLoading: false,
		});

		// Test with PREFER_FAVORITES
		const { result: resultPreferFavorites } = renderHook(() =>
			useLocationSource({
				fallbackOrder: LocationFallbackOrder.PREFER_FAVORITES,
			}),
		);

		// Verify loading state for favorite locations
		expect(resultPreferFavorites.current.isLocationLoading).toBe(true);

		// Reset mocks
		vi.mocked(useAtomValue).mockReset();
		vi.mocked(useSWR).mockReset();

		// Setup atoms again
		setupAtoms({
			pageLocationData: null,
			userLocations: [], // No favorites
			recentLocations: ['ny-456'],
			geoipGeocode: '34.0522,-118.2437',
		});

		// Setup SWR with loading states for recent location
		setupSWR({
			favoriteLocationData: null,
			isFavoriteLocationLoading: false,
			isRecentLocationLoading: true,
			isGeoipLocationLoading: false,
		});

		// Test with PREFER_RECENT
		const { result: resultPreferRecent } = renderHook(() =>
			useLocationSource({
				fallbackOrder: LocationFallbackOrder.PREFER_RECENT,
			}),
		);

		// Verify loading state for recent locations
		expect(resultPreferRecent.current.isLocationLoading).toBe(true);

		// Reset mocks
		vi.mocked(useAtomValue).mockReset();
		vi.mocked(useSWR).mockReset();

		// Setup atoms again
		setupAtoms({
			pageLocationData: null,
			userLocations: [{ coordinate: '35.6895,139.6917' }],
			recentLocations: ['ny-456'],
			geoipGeocode: '34.0522,-118.2437',
		});

		// With PREFER_FAVORITES, recent location loading should not affect the loading state
		setupSWR({
			isFavoriteLocationLoading: false,
			isRecentLocationLoading: true,
			isGeoipLocationLoading: false,
		});

		const { result: resultPreferFavoritesWithRecentLoading } = renderHook(() =>
			useLocationSource({
				fallbackOrder: LocationFallbackOrder.PREFER_FAVORITES,
			}),
		);

		expect(
			resultPreferFavoritesWithRecentLoading.current.isLocationLoading,
		).toBe(false);
	});
});
