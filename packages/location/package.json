{"name": "@repo/location", "version": "0.0.1", "type": "module", "private": true, "scripts": {"lint": "eslint --max-warnings=0 .", "check-types": "tsc --noEmit", "test": "vitest run"}, "exports": {"./components/*": "./src/components/*.tsx", "./atoms/*": "./src/atoms/*.ts", "./hooks/*": "./src/hooks/*.ts", "./utils/*": "./src/utils/*.ts", "./types": "./src/types.ts"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@repo/vitest-config": "workspace:*", "@testing-library/react": "catalog:dev", "@types/node": "catalog:node", "@types/react": "catalog:web", "@types/react-dom": "catalog:web", "eslint": "catalog:dev", "typescript": "catalog:dev", "vitest": "catalog:dev"}, "peerDependencies": {"class-variance-authority": "catalog:web", "next": "catalog:web", "react": "catalog:web", "react-dom": "catalog:web"}, "dependencies": {"@repo/dal": "workspace:*", "@repo/user": "workspace:*", "@repo/utils": "workspace:*", "jotai": "catalog:web", "swr": "catalog:web", "cookies-next": "catalog:web"}}