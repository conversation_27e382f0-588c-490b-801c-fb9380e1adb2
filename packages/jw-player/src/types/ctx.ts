// exported for use in video ads data
export interface IabTags {
	v1?: string[];
	v2?: string[];
	v3?: string[];
}

// exported for use in video ads and metrics data
export interface CTX {
	jwplayer?: string;
	pcollid?: string;
	adzone?: string;
	assetName?: string;
	description?: string;
	title?: string;
	teaserTitle?: string;
	playlists?: Record<string, string>[];
	entitlements?: string[];
	premium?: boolean;
	iab?: IabTags;
	tagsGeo?: string[];
	tagsKeyword?: string[];
	tagsStorm?: string[];
	providername?: string;
	publishdate?: Date;
	lastmodifieddate?: Date;
	duration?: string;
	id?: string;
	noAds?: boolean; // TODO: Calculated, so we may want to move this

	// ads data
	videoIndex?: number;
	collectionId?: string;
	videoId?: string;
	collectionAdZone?: string;
}
