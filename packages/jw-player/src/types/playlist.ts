// https://docs.jwplayer.com/players/reference/playlists

import { CTX } from './ctx';

export type Playlist = PlaylistItem[];
export interface PlaylistItem
	extends RequiredPlaylistItem,
		OptionalPlaylistItem {}

export interface OptionalPlaylistItem {
	adschedule?: string | AdSchedule[]; // ad break scheduling
	description?: string; // description
	image?: string; // image displayed before and after playback
	link?: string; // url sent when shared
	mediaid?: string; // uid, used for advertising, analytics, and discovery
	sources?: Source[]; // used for quality toggling and alternative sources
	starttime?: number; // time in seconds to start
	streamtype?: string; // ? FreeWheel only ? set to 'live' if live, incompatible with playlist.freewheel.streamtype
	title?: string; // title
	tracks?: Track[]; // ? The captions, chapters, or thumbnails for a media item
	withCredentials?: boolean; // ? if true, withCredentials will be used to request a media file rather than CORS ?

	// holds custom properties, such as ctx
	custom?: {
		ctx?: CTX;
	};
}

interface RequiredPlaylistItem {
	file: string; // url to media file
}

interface AdSchedule {
	offset: string | number; //
	tag: string | number; //
	type?: string; //
	vastxml?: string; //
}

interface Source {
	file: string; // url to media file
	default?: boolean; //
	// drm?: string; //
	label?: string; // set if more than two qualities of your video
	// onXhrOpen?: () => void; //
	type?: string; // ? used when missing file extension ?
}

export interface Track {
	default?: boolean; // !CAPTIONS ONLY ? sets a specific captions track to display when true ?
	file: string; // url to caption, chapter, or thumbnail
	kind?: string; // [(captions|chapters|thumbnails)]
	label?: string; // label of the text track
}
