// https://docs.jwplayer.com/players/reference/player-initiation

import { OptionalSetupOptions, SetupOptions, SetupParams } from './setup';
import { EventName, VideoEvent } from './events';
import { PlaylistItem } from './playlist';

export interface JWPlayerInstance {
	on: (
		event: EventName,
		callback: (event: Record<string, unknown>) => void,
	) => JWPlayerInstance;
	off: (event: EventName) => JWPlayerInstance;
	remove: () => void;
	setup: (config: SetupOptions) => JWPlayerInstance;
	getConfig: () => OptionalSetupOptions;
	setConfig: (config: OptionalSetupOptions) => JWPlayerInstance;
	play: () => void;
	pause: () => void;
	stop: () => void;
	playAd: (adTag: string) => void;
	getPlaylistItem: (index?: number) => PlaylistItem;
	getPlaylistIndex: () => number;
	getPosition: () => number;
	getDuration: () => number;
	getAdBlock: () => boolean;
	getMute: () => boolean;
}

// props passed to the JWPlayer component
export interface PlayerProps {
	playlist: PlaylistItem[];
	options?: OptionalSetupOptions;
	setupParams?: SetupParams;
	events?: VideoEvent[];
	className?: string;
}
