// https://docs.jwplayer.com/players/reference/advertising-config-ref
// https://docs.jwplayer.com/players/docs/jw8-define-ad-rules
// https://docs.jwplayer.com/players/docs/listening-for-events-web-player

export interface AdvertisingOptions {
	client: Client; // client used for ad serving (TWC uses googima)
	tag?: string | string[]; // adTag url or an array of fallback adTags

	schedule?: AdSchedule[]; // ad break scheduling
	rules?: AdRules[]; // ad rules

	vpaidcontrols?: boolean; // (IMA, VAST) show controls for VPAID ads
	autoplayadsmuted?: boolean; // allows ads to play muted
}

interface AdRules {
	// Short-form content
	startOn?: number; //
	frequency?: number; //

	// Long-form content
	startOnSeek?: string; // pre
	timeBetweenAds?: number; //
	deferAds?: Record<string, unknown>; //
}

interface AdSchedule {
	offset?: string | number; // number, xx:xx:xx:xxx'pre' | 'post', xx%
	tag?: string; // url to xml
}

type Client = 'dai' | 'freewheel' | 'googima' | 'vast';
