// https://docs.jwplayer.com/players/reference/setup-options

import { AdvertisingOptions } from './advertising';
import { PlaylistItem, OptionalPlaylistItem } from './playlist';

export interface SetupOptions
	extends OptionalPlaylistItem,
		RequiredSetupOptions,
		OptionalSetupOptions {
	file?: string; // url to media file, optional, as it may be defined in a playlist
}

export interface RequiredSetupOptions {
	// Behavior
	playlist: string | PlaylistItem[];
}

export interface OptionalSetupOptions {
	// Appearance
	aspectratio?: string; // x:y format
	controls?: boolean; // [true] display controls
	displaydescription?: boolean; // [true] display description
	displayHeading?: boolean; // [false] (Outstream only) display heading above the outstream player
	displayPlaybackLabel?: boolean; // [false] call-to-action text beneath the play button on the player idle screen
	displaytitle?: boolean; // [true] display title
	height?: number; // [360] height in pixels - OMIT WHEN aspectratio is used
	horizontalVolumeSlider?: boolean; // [false] display volume slider horizontally
	nextUpDisplay?: boolean; // [?] display the next up modal
	qualityLabels?: Record<string, string>; //? override auto-generated quality labels (e.g. {"2500":"High","1000":"Medium"})
	renderCaptionsNatively?: boolean; // [false, chrome|true, safari] use browser's native caption rendering
	stretching?: string; // [(uniform)|exactfit|fill|none] how to stretch the video
	width?: number | string; // [640] width in pixels or % (string, e.g. '100%')

	// Behavior
	aboutlink?: string; // url to learn more on right-click
	abouttext?: string; // text to display in the right-click menu
	allowFullscreen?: boolean; // [true] allow fullscreen
	autostart?: boolean | string; // [(false)|true|'viewable'] start playing automatically
	// defaultBandwidthEstimate?: number //! DO NOT USE
	// fullscreenOrientationLock?: string //! ANDROID ONLY
	generateSEOMetadata?: boolean; // [false] enable Google SEO Optimization
	liveSyncDuration?: number; // [(25)|5 to 30] how far behind live to start, //! LEAVE BLANK FOR AUTO - not sure how
	mute?: boolean; // [false] start muted
	nextupoffset?: number | string; // [(-10)|xx|'xx%'] seconds before the end of the video to show the next up modal
	pipIcon?: string; // [(enabled)|disabled] icon for picture-in-picture
	playbackRateControls?: boolean; // [false] display playback rate controls (i.e. playback speed, e.g. 0.5x)
	playbackRates?: number[]; // [0.25, 0.75, 1, 1.25] custom playback rates to display
	playlistIndex?: number; // [0] index of the playlist item to start with, -1 to -5 to start back from the end
	repeat?: boolean; // [false] repeat playlist

	// Rendering and Loading
	base?: string; // [/] alternate base url skins and providers
	// hlsjdefault?: boolean           //! ANDROID ONLY
	liveTimeout?: number; // [(30)|11+|0 never times out] how many seconds before a live stream times out
	// loadAndParseHlsMetadata?: boolean // ??
	preload?: string; // [(metadata)|auto ~30 secs|none] preload the video
	showUIWhen?: string; // [(onReady)|onContent] when to show the player UI

	// Extended Options
	advertising?: AdvertisingOptions;
	autoPause?: AutoPauseOptions;
	captions?: CaptionsOptions;
	floating?: FloatingOptions;
}

// custom TWC params used to configure the player settings
export interface SetupParams {
	playerId?: string;
	ads?: boolean;
	showDescriptions?: boolean; // overrides default for shows title and description
}

// https://docs.jwplayer.com/players/reference/auto-pause

interface AutoPauseOptions {
	pauseAds?: boolean; // [false] ad playback will pause when the player is not viewable
	viewability?: boolean; // [true] video playback will pause when the player is not viewable
}

// https://docs.jwplayer.com/players/reference/captions-config-ref

interface CaptionsOptions {
	backgroundOpacity?: number; // [75] opacity of the caption background
	color?: string; // [#FFFFFF] font color in hex
	fontSize?: number; // [15] font size in pixels
}

interface FloatingOptions {
	dismissible?: boolean;
	mode?: 'notVisible' | 'always' | 'never';
	showTitle?: boolean;
}
