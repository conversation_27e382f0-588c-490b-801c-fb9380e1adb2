.jwplayerContainer {
	:global(.jw-flag-floating .jw-wrapper) {
		left: 0 !important;
		bottom: 0 !important;
		margin: 8px !important;
		border-radius: 16px;
		overflow: hidden;
		box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.25);
		:global(.jw-float-bar) {
			background: none;
			animation: none !important;
			top: 0;
			right: 0;
			margin: 24px 12px;
			padding: 0;
			height: 0;
			flex-direction: row-reverse !important;
			width: calc(100% - 24px);
			:global(.jw-float-bar-icon) {
				&:hover {
					background: none !important;
				}
				:global(.jw-svg-icon-floating-close) {
					background-image: url('../assets/Close.svg');
					background-size: contain;
					background-repeat: no-repeat;
					width: 24px;
					height: 24px;
					path {
						display: none;
					}
					// stroke: none !important;
					// width: 20px !important;
					// height: 20px !important;
				}
			}
		}
	}
	:global(
		.jw-flag-user-inactive.jw-state-playing:not(.jw-flag-media-audio):not(
				.jw-flag-audio-player
			):not(.jw-flag-touch.jw-flag-ads):not(.jw-flag-casting)
			.jw-wrapper
	) {
		:global(.jw-float-bar) {
			display: none !important;
		}
	}
}

@media screen and (max-width: 767px) {
	.jwplayerContainer {
		:global(.jw-flag-floating .jw-wrapper) {
			top: 0 !important;
			right: 0 !important;
			max-width: calc(100% - 16px) !important;
			max-height: unset;
			aspect-ratio: 16 / 9;
			// max-height: 56.25vw !important;
			margin-top: calc(58px + 8px) !important;
		}
	}
}
