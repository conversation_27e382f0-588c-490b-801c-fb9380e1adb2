import { AdvertisingOptions } from '../types/advertising';
import { OptionalSetupOptions } from '../types/setup';
import { SetupParams } from '../types/setup';

export const getDefaultSetupOptions = (
	params: SetupParams,
): OptionalSetupOptions => {
	const { ads = true, showDescriptions = true } = params;

	const options = {
		aspectratio: '16:9',
		autostart: true,
		backgroundLoading: true,
		width: '100%',
		mute: false,

		autoPause: {
			pauseAds: true,
			viewability: true,
		},

		captions: {
			backgroundOpacity: 50,
			fontSize: 14,
		},
	};

	return {
		...options,
		...(ads ? { advertising: defaultAdvertisingOptions } : {}),
		...(showDescriptions
			? { displaytitle: true, displaydescription: true }
			: { displaytitle: false, displaydescription: false }),
	};
};

const defaultAdvertisingOptions: AdvertisingOptions = {
	autoplayadsmuted: false,
	client: 'googima',
	vpaidcontrols: true,
};
