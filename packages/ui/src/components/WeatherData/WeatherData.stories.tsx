import type { Meta, StoryObj } from '@storybook/react';
import { Temperature } from './Temperature/Temperature';
import { Wind } from './Wind/Wind';
import { Pressure } from './Pressure/Pressure';
import { UVIndex } from './UVIndex/UVIndex';
import { Percentage } from './Percentage/Percentage';
import { Visibility } from './Visibility/Visibility';
import { Accumulation } from './Accumulation/Accumulation';

const meta: Meta = {
	title: 'UI/Weather Data',
	tags: ['autodocs'],
	parameters: {
		docs: {
			description: {
				component:
					'A comprehensive collection of reusable React components for displaying weather-related data with proper formatting, units, and styling. These components support English (e), Metric (m), and Hybrid (h) unit systems.',
			},
		},
	},
};

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * This story showcases unit system comparisons across all weather data components,
 * demonstrating how each component handles English, Metric, and Hybrid units.
 */
export const WeatherDataUnitSystems: Story = {
	name: 'Unit System Comparisons',
	render: () => (
		<div className="space-y-12">
			<h2 className="text-2xl font-bold">Weather Data Unit Systems</h2>
			<p className="text-gray-600">
				All weather data components support three unit systems: English
				(&apos;e&apos;), Metric (&apos;m&apos;), and Hybrid (&apos;h&apos;)
			</p>

			{/* Temperature */}
			<div>
				<h3 className="mb-4 text-xl font-semibold">Temperature</h3>
				<div className="grid grid-cols-3 gap-8 text-center">
					<div>
						<h4 className="mb-4 text-lg font-medium">English</h4>
						<div className="text-3xl">
							<Temperature units="e" value={73} />
						</div>
						<p className="mt-2 text-sm text-gray-600">73°F</p>
					</div>
					<div>
						<h4 className="mb-4 text-lg font-medium">Metric</h4>
						<div className="text-3xl">
							<Temperature units="m" value={73} />
						</div>
						<p className="mt-2 text-sm text-gray-600">73°C</p>
					</div>
					<div>
						<h4 className="mb-4 text-lg font-medium">Hybrid</h4>
						<div className="text-3xl">
							<Temperature units="h" value={73} />
						</div>
						<p className="mt-2 text-sm text-gray-600">73°C</p>
					</div>
				</div>
			</div>

			{/* Wind */}
			<div>
				<h3 className="mb-4 text-xl font-semibold">Wind</h3>
				<div className="grid grid-cols-3 gap-8 text-center">
					<div>
						<h4 className="mb-4 text-lg font-medium">English</h4>
						<div className="text-3xl">
							<Wind windSpeed={15} units="e" />
						</div>
						<p className="mt-2 text-sm text-gray-600">15 mph</p>
					</div>
					<div>
						<h4 className="mb-4 text-lg font-medium">Metric</h4>
						<div className="text-3xl">
							<Wind windSpeed={15} units="m" />
						</div>
						<p className="mt-2 text-sm text-gray-600">15 km/h</p>
					</div>
					<div>
						<h4 className="mb-4 text-lg font-medium">Hybrid</h4>
						<div className="text-3xl">
							<Wind windSpeed={15} units="h" />
						</div>
						<p className="mt-2 text-sm text-gray-600">15 mph</p>
					</div>
				</div>
			</div>

			{/* Pressure */}
			<div>
				<h3 className="mb-4 text-xl font-semibold">Pressure</h3>
				<div className="grid grid-cols-3 gap-8 text-center">
					<div>
						<h4 className="mb-4 text-lg font-medium">English</h4>
						<div className="text-3xl">
							<Pressure pressureAltimeter={30.15} units="e" />
						</div>
						<p className="mt-2 text-sm text-gray-600">30.15 in</p>
					</div>
					<div>
						<h4 className="mb-4 text-lg font-medium">Metric</h4>
						<div className="text-3xl">
							<Pressure pressureAltimeter={1020.5} units="m" />
						</div>
						<p className="mt-2 text-sm text-gray-600">1020.5 mb</p>
					</div>
					<div>
						<h4 className="mb-4 text-lg font-medium">Hybrid</h4>
						<div className="text-3xl">
							<Pressure pressureAltimeter={1020.5} units="h" />
						</div>
						<p className="mt-2 text-sm text-gray-600">1020.5 mb</p>
					</div>
				</div>
			</div>

			{/* Visibility */}
			<div>
				<h3 className="mb-4 text-xl font-semibold">Visibility</h3>
				<div className="grid grid-cols-3 gap-8 text-center">
					<div>
						<h4 className="mb-4 text-lg font-medium">English</h4>
						<div className="text-3xl">
							<Visibility visibility={5} units="e" />
						</div>
						<p className="mt-2 text-sm text-gray-600">5 mi</p>
					</div>
					<div>
						<h4 className="mb-4 text-lg font-medium">Metric</h4>
						<div className="text-3xl">
							<Visibility visibility={5} units="m" />
						</div>
						<p className="mt-2 text-sm text-gray-600">5 km</p>
					</div>
					<div>
						<h4 className="mb-4 text-lg font-medium">Hybrid</h4>
						<div className="text-3xl">
							<Visibility visibility={5} units="h" />
						</div>
						<p className="mt-2 text-sm text-gray-600">5 mi</p>
					</div>
				</div>
			</div>

			{/* Accumulation */}
			<div>
				<h3 className="mb-4 text-xl font-semibold">Accumulation</h3>
				<div className="space-y-6">
					<div>
						<h4 className="mb-4 text-lg font-medium">Rain Accumulation</h4>
						<div className="grid grid-cols-3 gap-8 text-center">
							<div>
								<h5 className="text-md mb-2 font-medium">English</h5>
								<div className="text-3xl">
									<Accumulation precipType="rain" rain={0.5} units="e" />
								</div>
								<p className="mt-2 text-sm text-gray-600">0.5 in</p>
							</div>
							<div>
								<h5 className="text-md mb-2 font-medium">Metric</h5>
								<div className="text-3xl">
									<Accumulation precipType="rain" rain={12.7} units="m" />
								</div>
								<p className="mt-2 text-sm text-gray-600">12.7 mm</p>
							</div>
							<div>
								<h5 className="text-md mb-2 font-medium">Hybrid</h5>
								<div className="text-3xl">
									<Accumulation precipType="rain" rain={12.7} units="h" />
								</div>
								<p className="mt-2 text-sm text-gray-600">12.7 mm</p>
							</div>
						</div>
					</div>
					<div>
						<h4 className="mb-4 text-lg font-medium">Snow Accumulation</h4>
						<div className="grid grid-cols-3 gap-8 text-center">
							<div>
								<h5 className="text-md mb-2 font-medium">English</h5>
								<div className="text-3xl">
									<Accumulation precipType="snow" snow={3} units="e" />
								</div>
								<p className="mt-2 text-sm text-gray-600">3 in</p>
							</div>
							<div>
								<h5 className="text-md mb-2 font-medium">Metric</h5>
								<div className="text-3xl">
									<Accumulation precipType="snow" snow={7.6} units="m" />
								</div>
								<p className="mt-2 text-sm text-gray-600">7.6 cm</p>
							</div>
							<div>
								<h5 className="text-md mb-2 font-medium">Hybrid</h5>
								<div className="text-3xl">
									<Accumulation precipType="snow" snow={7.6} units="h" />
								</div>
								<p className="mt-2 text-sm text-gray-600">7.6 cm</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	),
};

/**
 * This story demonstrates component-specific features and variants,
 * showcasing the unique capabilities of each weather data component.
 */
export const WeatherDataVariants: Story = {
	name: 'Component Variants & Features',
	render: () => (
		<div className="space-y-12">
			<h2 className="text-2xl font-bold">Component-Specific Features</h2>

			{/* Temperature Variants */}
			<div>
				<h3 className="mb-6 text-xl font-semibold">Temperature Variants</h3>
				<div className="space-y-6">
					<div>
						<h4 className="mb-4 text-lg font-medium">Standard Values</h4>
						<div className="grid grid-cols-3 gap-4 text-center">
							<div>
								<div className="mb-2 text-2xl">
									<Temperature units="e" value={73} />
								</div>
								<p className="text-sm text-gray-600">Normal</p>
							</div>
							<div>
								<div className="mb-2 text-2xl">
									<Temperature units="e" value={0} />
								</div>
								<p className="text-sm text-gray-600">Zero</p>
							</div>
							<div>
								<div className="mb-2 text-2xl">
									<Temperature units="e" value={-60} />
								</div>
								<p className="text-sm text-gray-600">Negative</p>
							</div>
						</div>
					</div>
					<div>
						<h4 className="mb-4 text-lg font-medium">Special Cases</h4>
						<div className="grid grid-cols-2 gap-4 text-center">
							<div>
								<div className="mb-2 text-2xl">
									<Temperature value={73} />
								</div>
								<p className="text-sm text-gray-600">No Units</p>
							</div>
							<div>
								<div className="mb-2 text-2xl">
									<Temperature units="e" />
								</div>
								<p className="text-sm text-gray-600">Units Only</p>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Wind Direction Features */}
			<div>
				<h3 className="mb-6 text-xl font-semibold">Wind Direction Features</h3>
				<div className="space-y-6">
					<div>
						<h4 className="mb-4 text-lg font-medium">Display Modes</h4>
						<div className="grid grid-cols-3 gap-8 text-center text-2xl">
							<div>
								<div className="mb-2">
									<Wind
										windSpeed={133}
										windDirection={180}
										windDirectionCardinal="S"
										units="e"
										displayMode="directional"
									/>
								</div>
								<p className="text-sm text-gray-600">
									Directional (Arrow Only)
								</p>
							</div>
							<div>
								<div className="mb-2">
									<Wind
										windSpeed={133}
										windDirection={180}
										windDirectionCardinal="S"
										units="e"
										displayMode="cardinal"
									/>
								</div>
								<p className="text-sm text-gray-600">Cardinal (Letters Only)</p>
							</div>
							<div>
								<div className="mb-2">
									<Wind
										windSpeed={133}
										windDirection={180}
										windDirectionCardinal="S"
										units="e"
										displayMode="directionalAndCardinal"
									/>
								</div>
								<p className="text-sm text-gray-600">Both (Arrow + Letters)</p>
							</div>
						</div>
					</div>
					<div>
						<h4 className="mb-4 text-lg font-medium">
							Directional Display Examples
						</h4>
						<div className="space-y-6">
							<div>
								<h5 className="text-md mb-3 font-medium">
									Cardinal Directions
								</h5>
								<div className="grid grid-cols-4 gap-4 text-center text-2xl">
									<div>
										<div className="mb-2">
											<Wind
												windSpeed={15}
												windDirection={0}
												windDirectionCardinal="N"
												units="e"
												displayMode="directional"
											/>
										</div>
										<p className="text-sm text-gray-600">N (0°)</p>
									</div>
									<div>
										<div className="mb-2">
											<Wind
												windSpeed={15}
												windDirection={90}
												windDirectionCardinal="E"
												units="e"
												displayMode="directional"
											/>
										</div>
										<p className="text-sm text-gray-600">E (90°)</p>
									</div>
									<div>
										<div className="mb-2">
											<Wind
												windSpeed={15}
												windDirection={180}
												windDirectionCardinal="S"
												units="e"
												displayMode="directional"
											/>
										</div>
										<p className="text-sm text-gray-600">S (180°)</p>
									</div>
									<div>
										<div className="mb-2">
											<Wind
												windSpeed={15}
												windDirection={270}
												windDirectionCardinal="W"
												units="e"
												displayMode="directional"
											/>
										</div>
										<p className="text-sm text-gray-600">W (270°)</p>
									</div>
								</div>
							</div>
							<div>
								<h5 className="text-md mb-3 font-medium">
									Diagonal Directions
								</h5>
								<div className="grid grid-cols-4 gap-4 text-center text-2xl">
									<div>
										<div className="mb-2">
											<Wind
												windSpeed={15}
												windDirection={45}
												windDirectionCardinal="NE"
												units="e"
												displayMode="directional"
											/>
										</div>
										<p className="text-sm text-gray-600">NE (45°)</p>
									</div>
									<div>
										<div className="mb-2">
											<Wind
												windSpeed={15}
												windDirection={135}
												windDirectionCardinal="SE"
												units="e"
												displayMode="directional"
											/>
										</div>
										<p className="text-sm text-gray-600">SE (135°)</p>
									</div>
									<div>
										<div className="mb-2">
											<Wind
												windSpeed={15}
												windDirection={225}
												windDirectionCardinal="SW"
												units="e"
												displayMode="directional"
											/>
										</div>
										<p className="text-sm text-gray-600">SW (225°)</p>
									</div>
									<div>
										<div className="mb-2">
											<Wind
												windSpeed={15}
												windDirection={315}
												windDirectionCardinal="NW"
												units="e"
												displayMode="directional"
											/>
										</div>
										<p className="text-sm text-gray-600">NW (315°)</p>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div>
						<h4 className="mb-4 text-lg font-medium">
							Cardinal Display Examples
						</h4>
						<div className="grid grid-cols-4 gap-4 text-center text-2xl">
							<div>
								<div className="mb-2">
									<Wind
										windSpeed={15}
										windDirection={45}
										windDirectionCardinal="NE"
										units="e"
										displayMode="cardinal"
									/>
								</div>
								<p className="text-sm text-gray-600">NE (45°)</p>
							</div>
							<div>
								<div className="mb-2">
									<Wind
										windSpeed={15}
										windDirection={135}
										windDirectionCardinal="SE"
										units="e"
										displayMode="cardinal"
									/>
								</div>
								<p className="text-sm text-gray-600">SE (135°)</p>
							</div>
							<div>
								<div className="mb-2">
									<Wind
										windSpeed={15}
										windDirection={225}
										windDirectionCardinal="SW"
										units="e"
										displayMode="cardinal"
									/>
								</div>
								<p className="text-sm text-gray-600">SW (225°)</p>
							</div>
							<div>
								<div className="mb-2">
									<Wind
										windSpeed={15}
										windDirection={315}
										windDirectionCardinal="NW"
										units="e"
										displayMode="cardinal"
									/>
								</div>
								<p className="text-sm text-gray-600">NW (315°)</p>
							</div>
						</div>
					</div>
					<div>
						<h4 className="mb-4 text-lg font-medium">Layout Options</h4>
						<div className="grid grid-cols-2 gap-8 text-2xl">
							<div>
								<div className="mb-2">
									<Wind
										windSpeed={15}
										windDirection={180}
										windDirectionCardinal="S"
										units="e"
										displayMode="directionalAndCardinal"
										renderDirectionAfterSpeed
									/>
								</div>
								<p className="text-sm text-gray-600">Direction After Speed</p>
							</div>
							<div>
								<div className="mb-2">
									<Wind
										windSpeed={15}
										windDirection={180}
										windDirectionCardinal="S"
										units="e"
										displayMode="directional"
										displaySpaceBetweenWindSpeedAndUnits={false}
									/>
								</div>
								<p className="text-sm text-gray-600">No Space Between Units</p>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Pressure Trends */}
			<div>
				<h3 className="mb-6 text-xl font-semibold">Pressure Trends</h3>
				<div>
					<h4 className="mb-4 text-lg font-medium">
						Pressure Tendency Indicators
					</h4>
					<div className="grid grid-cols-3 gap-8 text-center text-2xl">
						<div>
							<div className="mb-2">
								<Pressure
									pressureAltimeter={30.15}
									pressureTendencyCode={1}
									units="e"
								/>
							</div>
							<p className="text-sm text-gray-600">Rising</p>
						</div>
						<div>
							<div className="mb-2">
								<Pressure
									pressureAltimeter={30.15}
									pressureTendencyCode={0}
									units="e"
								/>
							</div>
							<p className="text-sm text-gray-600">Steady</p>
						</div>
						<div>
							<div className="mb-2">
								<Pressure
									pressureAltimeter={30.15}
									pressureTendencyCode={2}
									units="e"
								/>
							</div>
							<p className="text-sm text-gray-600">Falling</p>
						</div>
					</div>
				</div>
			</div>

			{/* UV Index Variants */}
			<div>
				<h3 className="mb-6 text-xl font-semibold">UV Index Variants</h3>
				<div className="grid grid-cols-2 gap-8">
					<div>
						<h4 className="mb-4 text-lg font-medium">Basic Values</h4>
						<div className="space-y-4 text-2xl">
							<div className="flex justify-between">
								<span>Default:</span>
								<UVIndex uvIndex={5} />
							</div>
							<div className="flex justify-between">
								<span>Extreme:</span>
								<UVIndex uvIndex={11} />
							</div>
							<div className="flex justify-between">
								<span>Zero:</span>
								<UVIndex uvIndex={0} />
							</div>
						</div>
					</div>
					<div>
						<h4 className="mb-4 text-lg font-medium">With Description</h4>
						<div className="text-2xl">
							<UVIndex uvIndex={8} uvDescription="Very High" />
						</div>
					</div>
				</div>
			</div>

			{/* Percentage Display */}
			<div>
				<h3 className="mb-6 text-xl font-semibold">Percentage Display</h3>
				<div className="grid grid-cols-2 gap-8">
					<div>
						<h4 className="mb-4 text-lg font-medium">Basic Values</h4>
						<div className="space-y-4 text-2xl">
							<div className="flex justify-between">
								<span>Default:</span>
								<Percentage value={133} />
							</div>
							<div className="flex justify-between">
								<span>Zero:</span>
								<Percentage value={0} />
							</div>
						</div>
					</div>
					<div>
						<h4 className="mb-4 text-lg font-medium">Rounding Feature</h4>
						<div className="space-y-2 text-2xl">
							<div>
								133 rounded: <Percentage value={133} roundValue />
							</div>
							<div>
								132 rounded: <Percentage value={132} roundValue />
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Precipitation Accumulation */}
			<div>
				<h3 className="mb-6 text-xl font-semibold">
					Precipitation Accumulation
				</h3>
				<div className="space-y-6">
					<div>
						<h4 className="mb-4 text-lg font-medium">Precipitation Types</h4>
						<div className="grid grid-cols-3 gap-4 text-center text-2xl">
							<div>
								<div className="mb-2">
									<Accumulation precipType="rain" rain={0.5} units="e" />
								</div>
								<p className="text-sm text-gray-600">Rain</p>
							</div>
							<div>
								<div className="mb-2">
									<Accumulation precipType="snow" snow={2.5} units="e" />
								</div>
								<p className="text-sm text-gray-600">Snow</p>
							</div>
							<div>
								<div className="mb-2">
									<Accumulation
										precipType="precip"
										rain={0.3}
										snow={1.2}
										units="e"
									/>
								</div>
								<p className="text-sm text-gray-600">Mixed Precip</p>
							</div>
						</div>
					</div>
					<div>
						<h4 className="mb-4 text-lg font-medium">Advanced Features</h4>
						<div className="grid grid-cols-2 gap-4 text-center text-2xl">
							<div>
								<div className="mb-2">
									<Accumulation precipType="rain" qpf={0.75} units="e" />
								</div>
								<p className="text-sm text-gray-600">With QPF</p>
							</div>
							<div>
								<div className="mb-2">
									<Accumulation
										precipType="rain"
										rain={12.5}
										units="e"
										unitOverride="mm"
									/>
								</div>
								<p className="text-sm text-gray-600">Unit Override</p>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Visibility Special Cases */}
			<div>
				<h3 className="mb-6 text-xl font-semibold">Visibility Special Cases</h3>
				<div className="space-y-4">
					<div>
						<h4 className="mb-4 text-lg font-medium">Unlimited Visibility</h4>
						<div className="grid grid-cols-2 gap-8 text-2xl">
							<div>
								English (&gt;10): <Visibility visibility={11} units="e" />
							</div>
							<div>
								Metric (&gt;16): <Visibility visibility={17} units="m" />
							</div>
						</div>
					</div>
					<div>
						<h4 className="mb-4 text-lg font-medium">Zero Visibility</h4>
						<div className="text-2xl">
							<Visibility visibility={0} units="e" />
						</div>
					</div>
				</div>
			</div>
		</div>
	),
};

/**
 * This story demonstrates how components handle error states and edge cases,
 * including missing data and negative values.
 */
export const WeatherDataEdgeCases: Story = {
	name: 'Error States & Edge Cases',
	render: () => (
		<div className="space-y-12">
			<h2 className="text-2xl font-bold">Error States & Edge Cases</h2>

			{/* Missing Data Handling */}
			<div>
				<h3 className="mb-6 text-xl font-semibold">Missing Data Handling</h3>
				<p className="mb-4 text-gray-600">
					Components display &quot;--&quot; for missing data
				</p>
				<div className="grid grid-cols-4 gap-8 text-center text-2xl">
					<div>
						<div className="mb-2">
							<Wind windSpeed={NaN} units="e" />
						</div>
						<p className="text-sm text-gray-600">No Wind Data</p>
					</div>
					<div>
						<div className="mb-2">
							<Pressure units="e" />
						</div>
						<p className="text-sm text-gray-600">No Pressure Data</p>
					</div>
					<div>
						<div className="mb-2">
							<Temperature units="e" />
						</div>
						<p className="text-sm text-gray-600">No Temperature Value</p>
					</div>
					<div>
						<div className="mb-2">
							<Visibility visibility={NaN} units="e" />
						</div>
						<p className="text-sm text-gray-600">No Visibility Data</p>
					</div>
				</div>
			</div>

			{/* Negative Values */}
			<div>
				<h3 className="mb-6 text-xl font-semibold">Negative Values</h3>
				<p className="mb-4 text-gray-600">
					Components handle negative values appropriately
				</p>
				<div className="grid grid-cols-4 gap-4 text-center text-2xl">
					<div>
						<div className="mb-2">
							<Temperature units="e" value={-60} />
						</div>
						<p className="text-sm text-gray-600">Temperature</p>
					</div>
					<div>
						<div className="mb-2">
							<UVIndex uvIndex={-1} />
						</div>
						<p className="text-sm text-gray-600">UV Index</p>
					</div>
					<div>
						<div className="mb-2">
							<Percentage value={-10} />
						</div>
						<p className="text-sm text-gray-600">Percentage</p>
					</div>
					<div>
						<div className="mb-2">
							<Pressure pressureAltimeter={-5} units="e" />
						</div>
						<p className="text-sm text-gray-600">Pressure</p>
					</div>
				</div>
			</div>

			{/* Zero Values */}
			<div>
				<h3 className="mb-6 text-xl font-semibold">Zero Values</h3>
				<p className="mb-4 text-gray-600">
					Components properly display zero values
				</p>
				<div className="grid grid-cols-4 gap-4 text-center text-2xl">
					<div>
						<div className="mb-2">
							<Temperature units="e" value={0} />
						</div>
						<p className="text-sm text-gray-600">Temperature</p>
					</div>
					<div>
						<div className="mb-2">
							<Wind windSpeed={0} units="e" />
						</div>
						<p className="text-sm text-gray-600">Wind Speed</p>
					</div>
					<div>
						<div className="mb-2">
							<UVIndex uvIndex={0} />
						</div>
						<p className="text-sm text-gray-600">UV Index</p>
					</div>
					<div>
						<div className="mb-2">
							<Visibility visibility={0} units="e" />
						</div>
						<p className="text-sm text-gray-600">Visibility</p>
					</div>
				</div>
			</div>
		</div>
	),
};

/**
 * This story demonstrates custom styling capabilities and advanced features
 * available across weather data components.
 */
export const WeatherDataCustomization: Story = {
	name: 'Styling & Customization',
	render: () => (
		<div className="space-y-12">
			<h2 className="text-2xl font-bold">Styling & Customization</h2>

			{/* Custom Styling Examples */}
			<div>
				<h3 className="mb-6 text-xl font-semibold">Custom Styling Examples</h3>
				<div className="space-y-6">
					<div>
						<h4 className="mb-4 text-lg font-medium">Wind Custom Styling</h4>
						<div className="text-3xl">
							<Wind
								windSpeed={25}
								windDirection={180}
								units="e"
								className="font-bold text-blue-600"
							/>
						</div>
					</div>
					<div>
						<h4 className="mb-4 text-lg font-medium">
							Pressure Custom Styling
						</h4>
						<div className="text-3xl">
							<Pressure
								pressureAltimeter={29.85}
								pressureTendencyCode={2}
								units="e"
								className="font-semibold text-red-600"
							/>
						</div>
					</div>
					<div>
						<h4 className="mb-4 text-lg font-medium">
							Temperature Custom Styling
						</h4>
						<div className="text-4xl">
							<Temperature
								value={95}
								units="e"
								className="font-black text-orange-500"
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Design Patterns */}
			<div>
				<h3 className="mb-6 text-xl font-semibold">Design Patterns</h3>
				<div className="space-y-4">
					<p className="text-gray-700">
						All WeatherData components follow consistent design patterns:
					</p>
					<ul className="list-inside list-disc space-y-2 text-gray-700">
						<li>
							<strong>Consistent API</strong>: All components accept `className`
							for styling and follow similar prop naming conventions
						</li>
						<li>
							<strong>Unit Awareness</strong>: Components that display
							measurements accept a `units` prop and format accordingly
						</li>
						<li>
							<strong>Graceful Degradation</strong>: Invalid or missing data
							displays as &quot;--&quot; rather than breaking
						</li>
						<li>
							<strong>Type Safety</strong>: Full TypeScript support with proper
							prop types
						</li>
						<li>
							<strong>Memoization</strong>: Components use `React.memo` where
							appropriate for performance
						</li>
					</ul>
				</div>
			</div>

			{/* Usage Example */}
			<div>
				<h3 className="mb-6 text-xl font-semibold">Usage Example</h3>
				<div className="rounded-lg bg-gray-100 p-6">
					<pre className="overflow-x-auto text-sm">
						{`import { Temperature } from '@repo/ui/components/WeatherData/Temperature/Temperature';
import { Wind } from '@repo/ui/components/WeatherData/Wind/Wind';
import { Pressure } from '@repo/ui/components/WeatherData/Pressure/Pressure';

function WeatherDisplay({ data, units }) {
  return (
    <div className="weather-display">
      <Temperature value={data.temp} units={units} className="text-3xl" />
      <Wind 
        windSpeed={data.windSpeed} 
        windDirection={data.windDirection}
        units={units}
        displayWindDirectionIcon
      />
      <Pressure 
        pressureAltimeter={data.pressure}
        pressureTendencyCode={data.pressureTrend}
        units={units}
      />
    </div>
  );
}`}
					</pre>
				</div>
			</div>
		</div>
	),
};
