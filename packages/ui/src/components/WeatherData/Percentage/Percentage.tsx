import React, { memo } from 'react';
import { cn } from '@repo/ui/lib/utils';

interface PercentageProps {
	className?: string;
	value: number;
	roundValue?: boolean;
}

function round(x: number): number {
	return x % 5 >= 2.5
		? parseInt(String(x / 5), 10) * 5 + 5
		: parseInt(String(x / 5), 10) * 5;
}

const PercentageBase: React.FC<PercentageProps> = ({
	value,
	className,
	roundValue,
}) => {
	const transformedValue = roundValue ? round(value) : value;

	return (
		<span data-testid="PercentageValue" className={cn(className)}>
			{Number.isFinite(transformedValue) && transformedValue >= 0
				? `${transformedValue}%`
				: '--'}
		</span>
	);
};

export const Percentage = memo(PercentageBase);
