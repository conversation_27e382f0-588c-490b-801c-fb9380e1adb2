import React from 'react';
import { cn } from '@repo/ui/lib/utils';
import { unitsSystemByCode } from '@repo/units';
import type { UnitsSystemCodeType, UnitsSystem } from '@repo/units';

type PrecipType = 'rain' | 'snow' | 'precip';

interface PrecipitationType {
	precipType: PrecipType;
}

interface AccumulationProps extends PrecipitationType {
	className?: string;
	units?: UnitsSystemCodeType;
	rain?: string | number;
	snow?: string | number;
	qpf?: string | number;
	qpfSnow?: string | number;
	unitOverride?: string;
	unitClass?: string;
}

export const Accumulation: React.FC<AccumulationProps> = ({
	precipType = 'rain',
	rain,
	snow,
	qpf = 0,
	qpfSnow = 0,
	units,
	unitOverride,
	className,
	unitClass,
}) => {
	const rainValue = rain ?? qpf;
	const snowValue = snow ?? qpfSnow;
	const unitsSystem = units ? unitsSystemByCode(units) : null;

	let displayValue: string | number;
	let unitKey: keyof UnitsSystem;

	if (precipType === 'snow') {
		displayValue = snowValue;
		unitKey = 'accumulation';
	} else if (precipType === 'rain') {
		displayValue = rainValue;
		unitKey = 'precip';
	} else if (precipType === 'precip') {
		const rainHigher = Number(rainValue) >= Number(snowValue);
		displayValue = rainHigher ? rainValue : snowValue;
		unitKey = rainHigher ? 'precip' : 'accumulation';
	} else {
		displayValue = rainValue;
		unitKey = 'precip';
	}

	const unit = unitsSystem?.[unitKey];

	return (
		<span data-testid="AccumulationValue" className={cn(className)}>
			<span>{displayValue}</span>{' '}
			<span className={cn(unitClass)}>{unitOverride || unit}</span>
		</span>
	);
};
