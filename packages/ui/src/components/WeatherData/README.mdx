# WeatherData Components

This folder contains a collection of reusable React components for displaying weather-related data with proper formatting, units, and styling. These components are designed to handle various weather data types consistently across the application.

## Components

### Temperature
Displays temperature values with appropriate units and formatting.

```tsx
import { Temperature } from '@repo/ui/components/WeatherData/Temperature/Temperature';

<Temperature 
  value={73} 
  units="e" 
  className="text-2xl font-bold"
  unitsClassName="text-sm"
  degreeSymbolClass="text-lg"
/>
```

**Props:**
- `value?: number` - Temperature value
- `units?: UnitsSystemCode` - Unit system ('e', 'm', 'h')
- `className?: string` - CSS classes for the container
- `unitsClassName?: string` - CSS classes for the unit text
- `degreeSymbolClass?: string` - CSS classes for the degree symbol

### Percentage
Displays percentage values with optional rounding to nearest 5.

```tsx
import { Percentage } from '@repo/ui/components/WeatherData/Percentage/Percentage';

<Percentage 
  value={133} 
  roundValue 
  className="text-lg"
/>
```

**Props:**
- `value: number` - Percentage value
- `roundValue?: boolean` - Whether to round to nearest 5
- `className?: string` - CSS classes for styling

### UVIndex
Displays UV index values with optional description text.

```tsx
import { UVIndex } from '@repo/ui/components/WeatherData/UVIndex/UVIndex';

<UVIndex 
  uvIndex={5} 
  uvDescription="Moderate"
  className="text-lg"
  descriptionClassName="text-sm text-gray-600"
/>
```

**Props:**
- `uvIndex: number` - UV index value
- `uvDescription?: string` - Description text (e.g., "Moderate", "High")
- `className?: string` - CSS classes for the container
- `descriptionClassName?: string` - CSS classes for the description

### Visibility
Displays visibility distance with appropriate units based on the unit system.

```tsx
import { Visibility } from '@repo/ui/components/WeatherData/Visibility/Visibility';

<Visibility 
  visibility={5} 
  units="e"
  className="text-lg"
  unitsClassName="text-sm"
/>
```

**Props:**
- `visibility: number` - Visibility distance
- `units?: UnitsSystemCode` - Unit system ('e', 'm', 'h')
- `className?: string` - CSS classes for the container
- `unitsClassName?: string` - CSS classes for the unit text

### Accumulation
Displays precipitation accumulation (rain/snow) with flexible precipitation type handling.

```tsx
import { Accumulation } from '@repo/ui/components/WeatherData/Accumulation/Accumulation';

<Accumulation 
  precipType="rain"
  rain={0.5}
  units="e"
  className="text-lg"
  unitClass="text-sm"
/>
```

**Props:**
- `precipType?: PrecipType` - Type of precipitation ('rain', 'snow', 'precip')
- `rain?: string | number` - Rain amount
- `snow?: string | number` - Snow amount
- `qpf?: string | number` - Quantitative precipitation forecast
- `qpfSnow?: string | number` - Snow QPF
- `units?: UnitsSystemCode` - Unit system
- `unitOverride?: string` - Override the default unit
- `className?: string` - CSS classes for the container
- `unitClass?: string` - CSS classes for the unit text

### Wind
Displays wind speed and direction with optional directional arrow icon.

```tsx
import { Wind } from '@repo/ui/components/WeatherData/Wind/Wind';

<Wind 
  windSpeed={15}
  windDirection={270}
  units="e"
  displayWindDirectionIcon
  className="text-lg"
/>
```

**Props:**
- `windSpeed: number` - Wind speed value
- `units?: UnitsSystemCode` - Unit system ('e', 'm', 'h')
- `windDirection?: number` - Wind direction in degrees (0-360)
- `windDirectionCardinal?: string` - Cardinal direction ('N', 'NE', 'E', etc.)
- `className?: string` - CSS classes for the container
- `windSpeedClass?: string` - CSS classes for the wind speed text
- `windUnitClass?: string` - CSS classes for the unit text
- `iconClassName?: string` - CSS classes for the direction arrow icon
- `windDirectionWrapperClass?: string` - CSS classes for the direction wrapper
- `renderDirectionAfterSpeed?: boolean` - Render direction after speed (default: false)
- `displaySpaceBetweenWindSpeedAndUnits?: boolean` - Show space between speed and units (default: true)
- `displayWindDirectionIcon?: boolean` - Show directional arrow icon (default: false)

### Pressure
Displays barometric pressure with optional pressure tendency indicators (rising/falling arrows).

```tsx
import { Pressure } from '@repo/ui/components/WeatherData/Pressure/Pressure';

<Pressure 
  pressureAltimeter={30.15}
  pressureTendencyCode={1}
  units="e"
  className="text-lg"
/>
```

**Props:**
- `pressureAltimeter?: number` - Altimeter pressure value
- `pressureMeanSeaLevel?: number` - Mean sea level pressure value
- `pressureTendencyCode?: 0 | 1 | 2` - Pressure trend (0: steady, 1: rising, 2: falling)
- `units?: UnitsSystemCode` - Unit system ('e', 'm', 'h')
- `className?: string` - CSS classes for the container
- `unitsClassName?: string` - CSS classes for the unit text
- `iconClassName?: string` - CSS classes for the trend arrow icon

**Note:** The component accepts either `pressureAltimeter` or `pressureMeanSeaLevel`. English units display with 2 decimal places (inHg), while metric/hybrid units display with 1 decimal place (mb).

## Unit Systems

The components support three unit systems:

- **'e' (English)**: Fahrenheit, inches, miles, mph
- **'m' (Metric)**: Celsius, millimeters, kilometers, km/h
- **'h' (Hybrid)**: Celsius, inches, miles, mph

## Types

### UnitsSystemCode
```tsx
type UnitsSystemCode = 'e' | 'm' | 'h';
```

### PrecipType
```tsx
type PrecipType = 'rain' | 'snow' | 'precip';
```

## Utilities

### unitsSystemByCode
A utility function that returns the appropriate units for a given unit system code.

```tsx
import { unitsSystemByCode } from '@repo/units';

const units = unitsSystemByCode('e');
// Returns: { temp: 'F', precip: 'in', accumulation: 'in', speed: 'mph', distance: 'mi', pressure: 'in' }
```

## Storybook

All components have comprehensive Storybook stories that demonstrate:
- Different unit systems
- Various data states (normal, zero, negative, undefined)
- Edge cases and special formatting
- Styling variations

To view the stories:
```bash
pnpm --filter storybook dev
```

Then navigate to the "Components/Weather Data" section.

## Testing

Each component includes:
- `data-testid` attributes for testing
- Proper handling of edge cases (undefined, null, negative values)
- Consistent error states (displays '--' for invalid data)

## Design Patterns

These components follow several key patterns:

1. **Consistent API**: All components accept `className` for styling and follow similar prop naming conventions
2. **Unit Awareness**: Components that display measurements accept a `units` prop and format accordingly
3. **Graceful Degradation**: Invalid or missing data displays as '--' rather than breaking
4. **Memoization**: Components use `React.memo` where appropriate for performance
5. **Type Safety**: Full TypeScript support with proper prop types

## Usage Examples

### Basic Weather Display
```tsx
import { Temperature } from '@repo/ui/components/WeatherData/Temperature/Temperature';
import { Percentage } from '@repo/ui/components/WeatherData/Percentage/Percentage';
import { UVIndex } from '@repo/ui/components/WeatherData/UVIndex/UVIndex';

function WeatherSummary({ data, units }) {
  return (
    <div className="weather-summary">
      <Temperature value={data.temp} units={units} className="text-3xl" />
      <Percentage value={data.humidity} className="text-lg" />
      <UVIndex uvIndex={data.uvIndex} uvDescription={data.uvDesc} />
    </div>
  );
}
```

### Precipitation Display
```tsx
import { Accumulation } from '@repo/ui/components/WeatherData/Accumulation/Accumulation';

function PrecipitationForecast({ forecast, units }) {
  return (
    <div>
      <Accumulation 
        precipType="rain"
        qpf={forecast.rainAmount}
        units={units}
      />
      <Accumulation 
        precipType="snow"
        qpfSnow={forecast.snowAmount}
        units={units}
      />
    </div>
  );
}
