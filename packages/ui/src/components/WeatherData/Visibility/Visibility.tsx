import React from 'react';
import { cn } from '@repo/ui/lib/utils';
import { unitsSystemByCode } from '@repo/units';
import type { UnitsSystemCodeType } from '@repo/units';

interface VisibilityProps {
	className?: string;
	units?: UnitsSystemCodeType;
	visibility?: number;
	unitsClassName?: string;
}

export const Visibility: React.FC<VisibilityProps> = ({
	className,
	units,
	visibility,
	unitsClassName,
}) => {
	const unitsSystem = units ? unitsSystemByCode(units) : null;
	const unit = unitsSystem?.distance;
	let formatted: React.ReactElement | string = '';

	if (visibility === 0) {
		formatted = (
			<>
				<span>0</span>
				<span className={cn(unitsClassName)}>&nbsp;&nbsp;{unit}&nbsp;</span>
			</>
		);
	} else if (!visibility || visibility < 0) {
		formatted = '--';
	} else if (
		((units === 'e' || units === 'h') && visibility > 10) ||
		(units === 'm' && visibility > 16)
	) {
		formatted = 'Unlimited';
	} else {
		formatted = (
			<>
				<span>{visibility}</span>
				<span className={cn(unitsClassName)}>&nbsp;&nbsp;{unit}&nbsp;</span>
			</>
		);
	}

	return (
		<span data-testid="VisibilityValue" className={cn(className)}>
			{formatted}
		</span>
	);
};
