import React from 'react';
import { cn } from '@repo/ui/lib/utils';
import { ArrowUp } from '@repo/icons/Navigation';
import { unitsSystemByCode } from '@repo/units';
import { getComputedWindDirection } from '@repo/units/utils';
import type { UnitsSystemCodeType } from '@repo/units';

interface WindProps {
	className?: string;
	units?: UnitsSystemCodeType;
	windSpeed: number;
	windDirection?: number;
	windDirectionCardinal?: string;
	windSpeedClass?: string;
	windUnitClass?: string;
	iconClassName?: string;
	windDirectionWrapperClass?: string;
	renderDirectionAfterSpeed?: boolean;
	displaySpaceBetweenWindSpeedAndUnits?: boolean;
	displayWindDirectionIcon?: boolean;
	displayMode?: 'directional' | 'cardinal' | 'directionalAndCardinal';
}

const WindDirection: React.FC<{
	windDirection?: number;
	windDirectionCardinal?: string;
	windDirectionWrapperClass?: string;
	iconClassName?: string;
	displayWindDirectionIcon?: boolean;
	displayMode?: 'directional' | 'cardinal' | 'directionalAndCardinal';
}> = ({
	windDirection,
	windDirectionCardinal,
	windDirectionWrapperClass,
	iconClassName,
	displayWindDirectionIcon,
	displayMode,
}) => {
	const computedWindDirection = getComputedWindDirection(
		windDirection,
		windDirectionCardinal,
	);

	// Determine what to show based on display mode
	const shouldShowIcon =
		displayMode === 'directional' ||
		displayMode === 'directionalAndCardinal' ||
		(displayMode === undefined && displayWindDirectionIcon);
	const shouldShowCardinal =
		displayMode === 'cardinal' ||
		displayMode === 'directionalAndCardinal' ||
		(displayMode === undefined && !displayWindDirectionIcon);

	return (
		<span className={cn(windDirectionWrapperClass)}>
			{shouldShowCardinal && windDirectionCardinal && (
				<span
					className={cn(
						displayMode === 'cardinal' && !shouldShowIcon ? 'mr-1' : '',
					)}
				>
					{windDirectionCardinal}
				</span>
			)}
			{shouldShowIcon && Number.isFinite(computedWindDirection) ? (
				<ArrowUp
					data-testid="WindDirectionIcon"
					className={cn('inline-block h-4 w-4', iconClassName)}
					style={{ transform: `rotate(${computedWindDirection}deg)` }}
					aria-label="Wind direction arrow"
				/>
			) : null}
		</span>
	);
};

const Unit: React.FC<{
	units?: 'e' | 'm' | 'h';
	windUnitClass?: string;
}> = ({ units, windUnitClass }) => {
	const unitsSystem = units ? unitsSystemByCode(units) : null;
	const unit = unitsSystem?.speed;
	return <span className={cn(windUnitClass)}>{unit}</span>;
};

export const Wind: React.FC<WindProps> = ({
	windSpeed,
	units,
	className,
	windDirection,
	windDirectionCardinal,
	windSpeedClass,
	windUnitClass,
	iconClassName,
	windDirectionWrapperClass,
	renderDirectionAfterSpeed = false,
	displaySpaceBetweenWindSpeedAndUnits = true,
	displayWindDirectionIcon = false,
	displayMode,
}) => {
	return (
		<span
			data-testid="Wind"
			className={cn('inline-flex items-center', className)}
		>
			{!renderDirectionAfterSpeed && (
				<WindDirection
					windDirection={windDirection}
					windDirectionCardinal={windDirectionCardinal}
					windDirectionWrapperClass={windDirectionWrapperClass}
					iconClassName={iconClassName}
					displayWindDirectionIcon={displayWindDirectionIcon}
					displayMode={displayMode}
				/>
			)}
			{Number.isFinite(windSpeed) && units ? (
				<>
					<span className={cn(windSpeedClass)}>{windSpeed}</span>
					{displaySpaceBetweenWindSpeedAndUnits && <>&nbsp;</>}
					<Unit units={units} windUnitClass={windUnitClass} />
					{renderDirectionAfterSpeed && <>&nbsp;</>}
				</>
			) : (
				'--'
			)}
			{renderDirectionAfterSpeed && (
				<WindDirection
					windDirection={windDirection}
					windDirectionCardinal={windDirectionCardinal}
					windDirectionWrapperClass={windDirectionWrapperClass}
					iconClassName={iconClassName}
					displayWindDirectionIcon={displayWindDirectionIcon}
					displayMode={displayMode}
				/>
			)}
		</span>
	);
};
