import React from 'react';
import { cn } from '@repo/ui/lib/utils';
import { ArrowUp, ArrowDown } from '@repo/icons/Navigation';
import { unitsSystemByCode } from '@repo/units';
import type { UnitsSystemCodeType } from '@repo/units';

interface PressureProps {
	className?: string;
	units?: UnitsSystemCodeType;
	unitsClassName?: string;
	pressureAltimeter?: number;
	pressureMeanSeaLevel?: number;
	pressureTendencyCode?: 0 | 1 | 2;
	iconClassName?: string;
}

const UP_CODE = 1;
const DOWN_CODE = 2;

const PressureIcon: React.FC<{
	pressureTendencyCode?: 0 | 1 | 2;
	iconClassName?: string;
}> = ({ pressureTendencyCode, iconClassName }) => {
	if (pressureTendencyCode === UP_CODE) {
		return (
			<ArrowUp
				data-testid="PressureRisingIcon"
				className={cn('mr-1 inline-block h-4 w-4', iconClassName)}
				aria-label="Pressure rising"
			/>
		);
	}

	if (pressureTendencyCode === DOWN_CODE) {
		return (
			<ArrowDown
				data-testid="PressureFallingIcon"
				className={cn('mr-1 inline-block h-4 w-4', iconClassName)}
				aria-label="Pressure falling"
			/>
		);
	}

	return null;
};

export const Pressure: React.FC<PressureProps> = ({
	className,
	unitsClassName,
	pressureAltimeter,
	pressureMeanSeaLevel,
	pressureTendencyCode,
	units,
	iconClassName,
}) => {
	const getFormattedPressure = (): string | null => {
		const pressureValue =
			(pressureAltimeter && pressureAltimeter > 0) ||
			(pressureMeanSeaLevel && pressureMeanSeaLevel > 0)
				? pressureAltimeter || pressureMeanSeaLevel
				: null;

		if (!pressureValue) return null;

		const useEnglish = units === 'e';
		return useEnglish ? pressureValue.toFixed(2) : pressureValue.toFixed(1);
	};

	const unitsSystem = units ? unitsSystemByCode(units) : null;
	const pressureUnits = unitsSystem?.pressure;

	const formattedPressure = getFormattedPressure();

	return (
		<span
			data-testid="PressureValue"
			className={cn('inline-flex items-center', className)}
		>
			{formattedPressure ? (
				<>
					<PressureIcon
						pressureTendencyCode={pressureTendencyCode}
						iconClassName={iconClassName}
					/>
					<span>{formattedPressure}</span>
					&nbsp;
					<span className={cn(unitsClassName)}>{pressureUnits}</span>
				</>
			) : (
				'--'
			)}
		</span>
	);
};
