import React from 'react';
import { cn } from '@repo/ui/lib/utils';

interface UVIndexProps {
	className?: string;
	uvIndex: number;
	uvDescription?: string;
	descriptionClassName?: string;
}

export const UVIndex: React.FC<UVIndexProps> = ({
	uvIndex,
	uvDescription,
	className,
	descriptionClassName,
}) => {
	const extremeNumber = 10;
	let value: string | number;

	if (!Number.isFinite(uvIndex) || uvIndex < 0) {
		value = '--';
	} else if (uvIndex > extremeNumber) {
		value = 'Extreme';
	} else {
		value = uvDescription ? uvIndex : uvIndex;
	}

	return (
		<span data-testid="UVIndexValue" className={cn(className)}>
			{value}
			{uvDescription && uvIndex <= extremeNumber && (
				<span className={cn(descriptionClassName)}>
					&nbsp; ({uvDescription})
				</span>
			)}
		</span>
	);
};
