import React, { memo } from 'react';
import { cn } from '@repo/ui/lib/utils';
import { unitsSystemByCode } from '@repo/units';
import type { UnitsSystemCodeType } from '@repo/units';

interface TemperatureProps {
	className?: string;
	units?: UnitsSystemCodeType;
	unitsClassName?: string;
	degreeSymbolClass?: string;
	value?: number;
}

const TemperatureBase: React.FC<TemperatureProps> = ({
	className,
	unitsClassName,
	units,
	value,
	degreeSymbolClass,
}) => {
	const unitsSystem = units ? unitsSystemByCode(units) : null;
	const unit = unitsSystem?.temp;
	let temp: number | undefined;

	// Round to nearest integer.
	if (Number.isFinite(value)) {
		temp = Math.round(value!);
	}

	return (
		<span data-testid="TemperatureValue" className={cn(className)} dir="ltr">
			{(temp !== undefined && temp !== null) || unit ? (
				<>
					{temp}
					<span className={cn(degreeSymbolClass)}>°</span>
					<span className={cn(unitsClassName)}>{unit}</span>
				</>
			) : (
				'--'
			)}
		</span>
	);
};

export const Temperature = memo(TemperatureBase);
