import type { <PERSON>a, StoryObj } from '@storybook/react';
import { UserAvatar } from './UserAvatar';
import { UserSubscriptionTiers } from '@repo/user/utils/consts';

// Note: TypeScript errors about missing modules are expected and can be ignored.
// These stories are compiled by Storybook, not by the Next.js TypeScript compiler.

const meta: Meta<typeof UserAvatar> = {
	title: 'UI/UserAvatar',
	component: UserAvatar,
	tags: ['autodocs'],
	argTypes: {
		firstName: {
			control: 'text',
			description:
				"User's first name, first initial will be displayed if provided",
		},
		isLoggedIn: {
			control: 'boolean',
			description: 'Whether the user is logged in',
		},
		isPremium: {
			control: 'boolean',
			description: 'Whether the user has a premium subscription',
		},
		subscriptionTier: {
			control: 'select',
			options: Object.values(UserSubscriptionTiers).filter(
				(v) => typeof v === 'number',
			),
			description: "User's subscription tier",
		},
		stroke: {
			control: 'boolean',
			description: 'Whether to show the avatar with a stroke',
		},
		variant: {
			control: 'select',
			options: ['default', 'brand'],
			description: 'The visual style of the avatar',
		},
		size: {
			control: 'select',
			options: ['default', 'sm', 'lg'],
			description: 'The size of the avatar',
		},
	},
	parameters: {
		docs: {
			description: {
				component:
					"A user avatar component that displays different icons based on the user's state and subscription tier.",
			},
		},
	},
};

export default meta;
type Story = StoryObj<typeof UserAvatar>;

/**
 * This story showcases all combinations of user states and sizes in a grid layout,
 * providing a comprehensive view of the component's variations.
 */
export const Combinations: Story = {
	name: 'State and Size Combinations',
	render: () => (
		<div className="space-y-8 p-6">
			<div className="mb-4 rounded-md bg-slate-800 p-6">
				<h2 className="text-xl font-bold text-white">
					State and Size Combinations
				</h2>
				<table className="w-full border-collapse">
					<thead>
						<tr>
							<th className="border border-gray-600 p-2 text-left text-white">
								User State
							</th>
							<th className="border border-gray-600 p-2 text-center text-white">
								Small
							</th>
							<th className="border border-gray-600 p-2 text-center text-white">
								Default
							</th>
							<th className="border border-gray-600 p-2 text-center text-white">
								Large
							</th>
							<th className="border border-gray-600 p-2 text-center text-white">
								Brand/No Stroke
							</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td className="border border-gray-600 p-2 text-white">
								Anonymous
							</td>
							<td className="border border-gray-600 p-2 text-center">
								<div className="flex justify-center">
									<UserAvatar isLoggedIn={false} size="sm" />
								</div>
							</td>
							<td className="border border-gray-600 p-2 text-center">
								<div className="flex justify-center">
									<UserAvatar isLoggedIn={false} size="default" />
								</div>
							</td>
							<td className="border border-gray-600 p-2 text-center">
								<div className="flex justify-center">
									<UserAvatar isLoggedIn={false} size="lg" />
								</div>
							</td>
							<td className="border border-gray-600 p-2 text-center">
								<div className="flex justify-center">
									<UserAvatar
										isLoggedIn={false}
										size="lg"
										variant="brand"
										stroke={false}
									/>
								</div>
							</td>
						</tr>
						<tr>
							<td className="border border-gray-600 p-2 text-white">
								Registered
							</td>
							<td className="border border-gray-600 p-2 text-center">
								<div className="flex justify-center">
									<UserAvatar isLoggedIn={true} firstName="R" size="sm" />
								</div>
							</td>
							<td className="border border-gray-600 p-2 text-center">
								<div className="flex justify-center">
									<UserAvatar isLoggedIn={true} firstName="R" size="default" />
								</div>
							</td>
							<td className="border border-gray-600 p-2 text-center">
								<div className="flex justify-center">
									<UserAvatar isLoggedIn={true} firstName="R" size="lg" />
								</div>
							</td>
							<td className="border border-gray-600 p-2 text-center">
								<div className="flex justify-center">
									<UserAvatar
										isLoggedIn={true}
										firstName="R"
										size="lg"
										variant="brand"
										stroke={false}
									/>
								</div>
							</td>
						</tr>
						<tr>
							<td className="border border-gray-600 p-2 text-white">Premium</td>
							<td className="border border-gray-600 p-2 text-center">
								<div className="flex justify-center">
									<UserAvatar
										isLoggedIn={true}
										isPremium={true}
										subscriptionTier={UserSubscriptionTiers.premium}
										firstName="P"
										size="sm"
									/>
								</div>
							</td>
							<td className="border border-gray-600 p-2 text-center">
								<div className="flex justify-center">
									<UserAvatar
										isLoggedIn={true}
										isPremium={true}
										subscriptionTier={UserSubscriptionTiers.premium}
										firstName="P"
										size="default"
									/>
								</div>
							</td>
							<td className="border border-gray-600 p-2 text-center">
								<div className="flex justify-center">
									<UserAvatar
										isLoggedIn={true}
										isPremium={true}
										subscriptionTier={UserSubscriptionTiers.premium}
										firstName="P"
										size="lg"
									/>
								</div>
							</td>
							<td className="border border-gray-600 p-2 text-center">
								<div className="flex justify-center">
									<UserAvatar
										isLoggedIn={true}
										isPremium={true}
										subscriptionTier={UserSubscriptionTiers.premium}
										firstName="P"
										size="lg"
										variant="brand"
										stroke={false}
									/>
								</div>
							</td>
						</tr>
						<tr>
							<td className="border border-gray-600 p-2 text-white">Ad-Free</td>
							<td className="border border-gray-600 p-2 text-center">
								<div className="flex justify-center">
									<UserAvatar
										isLoggedIn={true}
										isPremium={true}
										subscriptionTier={UserSubscriptionTiers.adFree}
										firstName="A"
										size="sm"
									/>
								</div>
							</td>
							<td className="border border-gray-600 p-2 text-center">
								<div className="flex justify-center">
									<UserAvatar
										isLoggedIn={true}
										isPremium={true}
										subscriptionTier={UserSubscriptionTiers.adFree}
										firstName="A"
										size="default"
									/>
								</div>
							</td>
							<td className="border border-gray-600 p-2 text-center">
								<div className="flex justify-center">
									<UserAvatar
										isLoggedIn={true}
										isPremium={true}
										subscriptionTier={UserSubscriptionTiers.adFree}
										firstName="A"
										size="lg"
									/>
								</div>
							</td>
							<td className="border border-gray-600 p-2 text-center">
								<div className="flex justify-center">
									<UserAvatar
										isLoggedIn={true}
										isPremium={true}
										subscriptionTier={UserSubscriptionTiers.adFree}
										firstName="A"
										size="lg"
										variant="brand"
										stroke={false}
									/>
								</div>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	),
};

/**
 * For reference, this is the Default UserAvatar with standard settings.
 * This can be useful as a simple example for basic usage.
 */
export const Default: Story = {
	args: {
		isLoggedIn: true,
		firstName: 'John',
		stroke: true,
		variant: 'default',
		size: 'default',
	},
	parameters: {
		backgrounds: { default: 'dark' },
	},
};
