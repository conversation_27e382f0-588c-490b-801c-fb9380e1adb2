'use client';

import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { Switch } from './Switch';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form, FormField, FormMessage } from '@repo/ui/components/Form/Form';
import { Button } from '@repo/ui/components/Button/Button';

const meta: Meta<typeof Switch> = {
	title: 'UI/Switch',
	component: Switch,
	parameters: {
		layout: 'centered',
	},
	tags: ['autodocs'],
	decorators: [
		(Story) => (
			<div className="md:w-120 w-full">
				<Story />
			</div>
		),
	],
};

export default meta;
type Story = StoryObj<typeof Switch>;

export const SwitchVariants: Story = {
	render: () => (
		<div className="space-y-12">
			{/* Enabled State Variants */}
			<div className="space-y-8">
				<h2 className="text-xl font-bold">Enabled State</h2>

				<div className="flex items-center gap-12">
					<div className="grow">
						<Switch label="Toggle Off" />
					</div>

					<div className="grow">
						<Switch defaultChecked label="Toggle On" />
					</div>
				</div>
			</div>

			{/* Disabled State Variants */}
			<div className="space-y-8">
				<h2 className="text-xl font-bold">Disabled State</h2>

				<div className="flex w-full items-center gap-12">
					<div className="grow">
						<Switch label="Toggle Off" disabled />
					</div>

					<div className="grow">
						<Switch defaultChecked label="Toggle On" disabled />
					</div>
				</div>
			</div>
		</div>
	),
};

const formSchema = z.object({
	notifications: z.boolean(),
	mfa: z.boolean().default(false).optional(),
});

export const FormExample: Story = {
	render: () => {
		const form = useForm<z.infer<typeof formSchema>>({
			resolver: zodResolver(formSchema),
			defaultValues: {},
		});
		const [submitted, setSubmitted] = useState(false);
		const [formData, setFormData] = useState<z.infer<typeof formSchema>>(
			form.getValues(),
		);

		return (
			<div>
				<div>
					<Form {...form}>
						<form
							className="flex w-full flex-col gap-4"
							onSubmit={form.handleSubmit((data) => {
								setFormData(data);
								setSubmitted(true);
							})}
						>
							<h1 className="text-lg font-bold">User Preference</h1>

							<FormField
								control={form.control}
								name="notifications"
								render={({ field }) => (
									<div>
										<Switch
											label="Notifications"
											description="Get the latest weather updates"
											checked={field.value}
											onCheckedChange={field.onChange}
										/>
										<FormMessage />
									</div>
								)}
							/>

							<FormField
								control={form.control}
								name="mfa"
								render={({ field }) => (
									<Switch
										label="Enable MFA"
										checked={field.value}
										onCheckedChange={field.onChange}
									/>
								)}
							/>

							<Button type="submit">Save</Button>
						</form>
					</Form>

					{submitted && (
						<div className="p-3">
							<h2 className="font-bold">Form Data</h2>
							<pre className="whitespace-pre-wrap break-words text-sm">
								{JSON.stringify(formData, null, 2)}
							</pre>
						</div>
					)}
				</div>
			</div>
		);
	},
};
