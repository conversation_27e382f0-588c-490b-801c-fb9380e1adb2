'use client';

import { ComponentProps, FC, useEffect, useState } from 'react';
import * as SwitchPrimitives from '@radix-ui/react-switch';
import { cn } from '@repo/ui/lib/utils';
import { Checkmark } from '@repo/icons/Status';
import {
	FormControl,
	FormDescription,
	FormItem,
	FormLabel,
} from '@repo/ui/components/Form/Form';

type ToggleSwitchProps = ComponentProps<typeof SwitchPrimitives.Root>;

export interface SwitchProps extends ToggleSwitchProps {
	/** Label text for the switch */
	label: string;
	/** Optional description text for the switch */
	description?: string;
}

const SwitchToggle: FC<ToggleSwitchProps> = ({
	className,
	checked,
	defaultChecked,
	onCheckedChange,
	...props
}) => {
	// Maintain internal state for the toggle
	const [isChecked, setIsChecked] = useState<boolean>(
		checked !== undefined ? checked : defaultChecked || false,
	);

	// Sync with external checked prop when it changes
	useEffect(() => {
		if (checked !== undefined) {
			setIsChecked(checked);
		}
	}, [checked]);

	// Handle toggle
	const handleCheckedChange = (newChecked: boolean) => {
		if (checked === undefined) {
			// Only update internal state if uncontrolled
			setIsChecked(newChecked);
		}
		// Call the callback if provided
		onCheckedChange?.(newChecked);
	};

	return (
		<SwitchPrimitives.Root
			{...props}
			className={cn(
				'w-15 peer relative h-7 shrink-0 cursor-pointer rounded-[8px] transition-colors',
				'focus-visible:ring-brand-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
				'disabled:cursor-not-allowed',
				'data-[state=checked]:bg-brand-300 data-[state=unchecked]:bg-gray-300',
				'disabled:!bg-[rgba(37,36,34,0.20)]',
				'disabled:opacity-70',
				className,
			)}
			checked={isChecked}
			onCheckedChange={handleCheckedChange}
			data-slot="switch"
		>
			<SwitchPrimitives.Thumb
				className={cn(
					'pointer-events-none block h-7 w-7 rounded-[6px] bg-white shadow-lg ring-0',
					'transition-transform data-[state=checked]:translate-x-8 data-[state=unchecked]:translate-x-0',
					'flex items-center justify-center',
				)}
				data-slot="switch-thumb"
			>
				<div className="relative flex h-full w-full items-center justify-center">
					<Checkmark
						className={cn(
							'text-brand-400 h-4 w-4',
							'absolute transition-opacity duration-200',
							isChecked ? 'opacity-100' : 'opacity-0',
						)}
					/>
				</div>
			</SwitchPrimitives.Thumb>
		</SwitchPrimitives.Root>
	);
};

/**
 * Switch component
 *
 * A switch component that can be used to switch between two states.
 */
export const Switch: FC<SwitchProps> = ({
	id,
	label,
	description,
	className,
	...props
}) => {
	return (
		<FormItem
			id={id}
			className="flex w-full flex-row items-start justify-between gap-3"
		>
			<div className="space-y-0.5">
				<FormLabel className="text-base font-bold">{label}</FormLabel>
				<FormDescription
					className={cn('mt-1 text-base', { hidden: !description })}
				>
					{description}
				</FormDescription>
			</div>
			<FormControl>
				<SwitchToggle {...props} />
			</FormControl>
		</FormItem>
	);
};
