# Switch Component

A toggle switch component that follows the WAI-ARIA switch pattern for accessibility, with built-in label and description support.

## Features

- Built-in label and description support
- Visual feedback with checkmark icon when toggled on
- Support for both controlled and uncontrolled usage
- Form integration with React Hook Form
- Accessible by default
- Consistent styling with design system
- Support for disabled state

## Props

| Prop | Type | Description |
|------|------|-------------|
| `label` | `string` | Label text for the switch (required) |
| `description` | `string` | Optional description text displayed below the label |
| `checked` | `boolean` | Controlled state of the switch |
| `defaultChecked` | `boolean` | Initial state for uncontrolled usage |
| `onCheckedChange` | `(checked: boolean) => void` | Callback when the switch state changes |
| `disabled` | `boolean` | Whether the switch is disabled |
| `id` | `string` | Optional ID for the switch |
| `className` | `string` | Optional additional CSS classes |

## Accessibility Features

The Switch component implements the [WAI-ARIA Switch Pattern](https://www.w3.org/WAI/ARIA/apg/patterns/switch/) with the following accessibility features:

1. **Role**: The switch has `role="switch"` to identify it as a switch control.

2. **Accessible Label**: The label is properly associated with the switch.

3. **State**: The switch uses `aria-checked` to indicate its state:
   - `aria-checked="true"` when the switch is on
   - `aria-checked="false"` when the switch is off

4. **Description**: When provided, the description is properly associated with the switch.

5. **Focus Management**: The switch has proper focus styles and can be operated with keyboard.

6. **Visual Feedback**: The switch includes a checkmark icon when toggled on for additional visual feedback.

## Usage Examples

### Basic Usage

```tsx
<Switch label="Enable notifications" />
```

### With Description

```tsx
<Switch 
  label="Weather Alerts" 
  description="Receive notifications about severe weather conditions in your area"
/>
```

### Controlled Component

```tsx
const [checked, setChecked] = useState(false);

<Switch 
  label="Dark Mode"
  description={`Current state: ${checked ? 'On' : 'Off'}`}
  checked={checked}
  onCheckedChange={setChecked}
/>
```

### Disabled State

```tsx
<Switch label="Premium Feature" disabled />
<Switch label="Premium Feature" checked disabled />
```

### Form Integration

```tsx
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form, FormField } from '@repo/ui/components/form';

const formSchema = z.object({
  notifications: z.boolean(),
});

const MyForm = () => {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      notifications: false,
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          control={form.control}
          name="notifications"
          render={({ field }) => (
            <Switch
              label="Notifications"
              description="Get the latest weather updates"
              checked={field.value}
              onCheckedChange={field.onChange}
            />
          )}
        />
        <Button type="submit">Save</Button>
      </form>
    </Form>
  );
};
```

## Visual States

The Switch component has the following visual states:

- **Off**: Default state, gray background
- **On**: Active state, brand color background with checkmark icon
- **Disabled Off**: Dimmed gray background, reduced opacity
- **Disabled On**: Dimmed brand color background, reduced opacity
- **Focus**: Focus ring appears around the switch when focused

## Implementation Notes

This component is built on Radix UI's Switch primitive, which provides the core accessibility features. Our implementation adds:

1. Built-in label and description support
2. Visual feedback with checkmark icon
3. Integration with our form components
4. Consistent styling with our design system
5. Support for both controlled and uncontrolled usage

## Accessibility Testing Checklist

- [ ] Ensure the switch has a proper label
- [ ] Verify that `aria-checked` correctly reflects the current state
- [ ] Test keyboard navigation and operation (Tab to focus, Space to toggle)
- [ ] Ensure the switch is properly grouped when part of a related set
- [ ] Verify that any additional descriptive text is properly associated
- [ ] Test with screen readers to confirm proper announcements
- [ ] Ensure sufficient color contrast for both on and off states
