# Form Component

The Form component provides a flexible and type-safe way to build forms in your React applications.
It is composed by ShardCn using [Radix UI](https://www.radix-ui.com/primitives/docs/components/form) and integrates seamlessly with
[react-hook-form](https://react-hook-form.com/) and [Zod](https://github.com/colinhacks/zod) for validation.

## Import

```tsx
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage
} from '@repo/ui/components/Form/Form';
```

## Basic Usage

```tsx
'use client';

import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from '@repo/ui/components/Form/Form';
import { Input } from '@repo/ui/components/Input/Input';
import { Button } from '@repo/ui/components/Button/Button';

// Define your form schema with Zod
const formSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

export function BasicForm() {
  // Initialize the form with react-hook-form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
  });

  // Handle form submission
  function onSubmit(data: z.infer<typeof formSchema>) {
    console.log(data);
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input label="Email" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit">Submit</Button>
      </form>
    </Form>
  );
}
```

## Form Field Types

### Input Field

```tsx
<FormField
  control={form.control}
  name="username"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Username</FormLabel>
      <FormControl>
        <Input label="Username" {...field} />
      </FormControl>
      <FormDescription>This is your public display name.</FormDescription>
      <FormMessage />
    </FormItem>
  )}
/>
```

### Checkbox Field

```tsx
<FormField
  control={form.control}
  name="acceptTerms"
  render={({ field }) => (
    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
      <FormControl>
        <Checkbox
          checked={field.value}
          onCheckedChange={field.onChange}
        />
      </FormControl>
      <div className="space-y-1 leading-none">
        <FormLabel>Accept terms and conditions</FormLabel>
        <FormDescription>
          You agree to our Terms of Service and Privacy Policy.
        </FormDescription>
        <FormMessage />
      </div>
    </FormItem>
  )}
/>
```

### Switch Field

```tsx
<FormField
  control={form.control}
  name="notifications"
  render={({ field }) => (
    <div>
      <Switch
        label="Notifications"
        description="Get the latest weather updates"
        checked={field.value}
        onCheckedChange={field.onChange}
      />
      <FormMessage />
    </div>
  )}
/>
```

### Textarea Field

```tsx
<FormField
  control={form.control}
  name="bio"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Bio</FormLabel>
      <FormControl>
        <Textarea
          placeholder="Tell us about yourself"
          className="resize-none"
          {...field}
        />
      </FormControl>
      <FormDescription>
        Brief description for your profile.
      </FormDescription>
      <FormMessage />
    </FormItem>
  )}
/>
```

## Complete Form Example

Here's a complete example of a user profile form with multiple field types and validation:

```tsx
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form, FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from '@repo/ui/components/Form/Form';
import { Input } from '@repo/ui/components/Input/Input';
import { Textarea } from '@repo/ui/components/textarea';
import { Switch } from '@repo/ui/components/Switch/Switch';
import { Checkbox } from '@repo/ui/components/checkbox';
import { Button } from '@repo/ui/components/Button/Button';

// Define the form schema with validation
const profileFormSchema = z.object({
  username: z
    .string()
    .min(2, { message: 'Username must be at least 2 characters.' })
    .max(30, { message: 'Username must not be longer than 30 characters.' }),
  email: z
    .string()
    .email({ message: 'Please enter a valid email address.' }),
  bio: z
    .string()
    .max(160, { message: 'Bio must not be longer than 160 characters.' })
    .optional(),
  notifications: z
    .boolean()
    .default(false),
  marketingEmails: z
    .boolean()
    .default(false),
  termsAccepted: z
    .boolean()
    .refine(val => val === true, { message: 'You must accept the terms and conditions.' })
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

export function ProfileForm() {
  const [formData, setFormData] = useState<ProfileFormValues | null>(null);

  // Initialize the form
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      username: '',
      email: '',
      bio: '',
      notifications: false,
      marketingEmails: false,
      termsAccepted: false,
    },
  });

  // Handle form submission
  function onSubmit(data: ProfileFormValues) {
    setFormData(data);
    console.log(data);
  }

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold">Profile Settings</h2>
        <p className="text-gray-500">
          Update your profile information and preferences.
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Username</FormLabel>
                <FormControl>
                  <Input label="Username" {...field} />
                </FormControl>
                <FormDescription>
                  This is your public display name.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input label="Email" type="email" {...field} />
                </FormControl>
                <FormDescription>
                  We'll never share your email with anyone else.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="bio"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Bio</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Tell us about yourself"
                    className="resize-none"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Brief description for your profile. URLs are hyperlinked.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="notifications"
            render={({ field }) => (
              <div>
                <Switch
                  label="Enable notifications"
                  description="Receive notifications about weather alerts and updates"
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
                <FormMessage />
              </div>
            )}
          />

          <FormField
            control={form.control}
            name="marketingEmails"
            render={({ field }) => (
              <div>
                <Switch
                  label="Marketing emails"
                  description="Receive emails about new features and offers"
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
                <FormMessage />
              </div>
            )}
          />

          <FormField
            control={form.control}
            name="termsAccepted"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Accept terms and conditions</FormLabel>
                  <FormDescription>
                    You agree to our Terms of Service and Privacy Policy.
                  </FormDescription>
                  <FormMessage />
                </div>
              </FormItem>
            )}
          />

          <Button type="submit">Update profile</Button>
        </form>
      </Form>

      {formData && (
        <div className="mt-8 p-4 border rounded-md bg-gray-50">
          <h3 className="font-bold mb-2">Form Data:</h3>
          <pre className="whitespace-pre-wrap break-words text-sm">
            {JSON.stringify(formData, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
```

## Form Field Components

The Form component provides several sub-components to help structure your forms:

- `Form`: The main form provider that integrates with react-hook-form
- `FormField`: Connects form controls to react-hook-form
- `FormItem`: Container for form field elements
- `FormLabel`: Label for form fields
- `FormControl`: Wrapper for form controls with proper accessibility attributes
- `FormDescription`: Description text for form fields
- `FormMessage`: Displays validation error messages

## Best Practices

1. **Use Zod for validation**: Define your form schema with Zod for type-safe validation
2. **Organize complex forms into sections**: Group related fields together for better UX
3. **Provide helpful descriptions**: Use FormDescription to give users context about fields
4. **Handle errors gracefully**: Always include FormMessage components to display validation errors
5. **Use appropriate field types**: Choose the right input component for each data type
6. **Provide feedback on submission**: Show loading states and success/error messages
