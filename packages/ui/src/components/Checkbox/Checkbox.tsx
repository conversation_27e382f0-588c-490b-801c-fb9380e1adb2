'use client';

import { ComponentProps, FC, useEffect, useState } from 'react';
import * as CheckboxPrimitive from '@radix-ui/react-checkbox';

import { cn } from '@repo/ui/lib/utils';
import {
	FormControl,
	FormItem,
	FormLabel,
} from '@repo/ui/components/Form/Form';
import {
	CheckboxCheckedFilled,
	CheckboxIndeterminate,
	Checkbox as CheckboxIcon,
} from '@repo/icons/Toggle';

type BaseCheckboxProps = ComponentProps<typeof CheckboxPrimitive.Root>;

interface CheckboxProps extends BaseCheckboxProps {
	/** Label text for the checkbox */
	label: string;
	/** Size of the checkbox label */
	size?: 'standard' | 'large';
}

function BaseCheckbox({
	className,
	checked,
	defaultChecked,
	onCheckedChange,
	...props
}: BaseCheckboxProps) {
	// Maintain internal checked state
	const [checkState, setCheckState] = useState<CheckboxPrimitive.CheckedState>(
		checked !== undefined ? checked : defaultChecked || false,
	);

	// Sync with external checked prop when it changes
	useEffect(() => {
		if (checked !== undefined) {
			setCheckState(checked);
		}
	}, [checked]);

	// Handle toggle
	const handleCheckedChange = (newChecked: boolean) => {
		if (checked === undefined) {
			// Only update internal state if uncontrolled
			setCheckState(newChecked);
		}
		// Call the callback if provided
		onCheckedChange?.(newChecked);
	};

	const CheckStateIcon =
		checkState === 'indeterminate'
			? CheckboxIndeterminate
			: checkState
				? CheckboxCheckedFilled
				: CheckboxIcon;

	return (
		<CheckboxPrimitive.Root
			data-slot="checkbox"
			className={cn(
				'peer disabled:cursor-not-allowed disabled:opacity-50',
				className,
			)}
			{...props}
			checked={checkState}
			onCheckedChange={handleCheckedChange}
		>
			<CheckboxPrimitive.Indicator
				className="flex items-center justify-center text-current transition-none"
				data-slot="checkbox-indicator"
				forceMount
			>
				<CheckStateIcon className="size-6" />
			</CheckboxPrimitive.Indicator>
		</CheckboxPrimitive.Root>
	);
}

export const Checkbox: FC<CheckboxProps> = ({
	id,
	label,
	size = 'standard',
	...props
}) => {
	const isLarge = size === 'large';
	return (
		<FormItem
			id={id}
			className="flex w-full flex-row items-center justify-between gap-3"
		>
			<div className="">
				<FormLabel className={cn(isLarge && 'text-lg')}>{label}</FormLabel>
			</div>
			<FormControl>
				<BaseCheckbox {...props} />
			</FormControl>
		</FormItem>
	);
};
