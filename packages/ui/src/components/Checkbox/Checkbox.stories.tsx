'use client';

import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { Checkbox } from './Checkbox';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import {
	Form,
	FormField,
	FormLabel,
	FormDescription,
	FormMessage,
} from '@repo/ui/components/Form/Form';
import { Button } from '@repo/ui/components/Button/Button';

const meta: Meta<typeof Checkbox> = {
	title: 'UI/Checkbox',
	component: Checkbox,
	parameters: {
		layout: 'centered',
	},
	tags: ['autodocs'],
	decorators: [
		(Story) => (
			<div className="md:w-120 w-full">
				<Story />
			</div>
		),
	],
};

export default meta;
type Story = StoryObj<typeof Checkbox>;

export const CheckboxVariants: Story = {
	render: () => {
		const [checkedItems, setCheckedItems] = useState({
			apple: false,
			orange: false,
			banana: false,
		});

		const allChecked = Object.values(checkedItems).every(Boolean);
		const indeterminate =
			!allChecked && Object.values(checkedItems).some(Boolean);

		return (
			<div className="space-y-12">
				{/* Basic Variants */}
				<div className="space-y-8">
					<h2 className="text-xl font-bold">Basic Variants</h2>

					<div className="flex flex-col gap-4">
						<div className="flex items-center gap-2">
							<Checkbox label="Unchecked" />
						</div>

						<div className="flex items-center gap-2">
							<Checkbox label="Checked" defaultChecked />
						</div>
					</div>
				</div>

				{/* Disabled State Variants */}
				<div className="space-y-8">
					<h2 className="text-xl font-bold">Disabled State</h2>

					<div className="flex flex-col gap-4">
						<div className="flex items-center gap-2">
							<Checkbox label="Disabled Unchecked" disabled />
						</div>

						<div className="flex items-center gap-2">
							<Checkbox label="Disabled Checked" disabled defaultChecked />
						</div>
					</div>
				</div>

				<div className="space-y-8">
					<h2 className="text-xl font-bold">Large</h2>

					<div className="flex flex-col gap-4">
						<div className="flex items-center gap-2">
							<Checkbox label="Large Checkbox" size="large" />
						</div>
					</div>
				</div>

				{/* Indeterminate State Variants */}
				<div className="space-y-8">
					<h2 className="text-xl font-bold">Indeterminate State</h2>

					<div className="space-y-4">
						<div className="flex items-center gap-2">
							<Checkbox
								label="Select All Fruits"
								id="select-all-check"
								checked={indeterminate ? 'indeterminate' : allChecked}
								onCheckedChange={(checked) => {
									setCheckedItems({
										apple: !!checked,
										orange: !!checked,
										banana: !!checked,
									});
								}}
							/>
						</div>

						<div className="ml-6 space-y-2">
							{Object.entries(checkedItems).map(([fruit, checked]) => (
								<div key={fruit} className="flex items-center gap-2">
									<Checkbox
										id={`${fruit}-check`}
										label={fruit}
										checked={checked}
										onCheckedChange={(value) => {
											setCheckedItems((prev) => ({
												...prev,
												[fruit]: !!value,
											}));
										}}
									/>
								</div>
							))}
						</div>
					</div>
				</div>
			</div>
		);
	},
};

const formSchema = z.object({
	terms: z.boolean().refine((value) => value === true, {
		message: `You must agree to the terms and conditions`,
	}),
	marketing: z.boolean().optional(),
	notifications: z
		.object({
			email: z.boolean().optional(),
			push: z.boolean().optional(),
			sms: z.boolean().optional(),
		})
		.refine((data) => data.email || data.push || data.sms, {
			message:
				'At least one notification method must be specified (email, push, or sms)',
			path: [], // You can point to a specific key if needed
		}),
});

export const FormExample: Story = {
	render: () => {
		const form = useForm<z.infer<typeof formSchema>>({
			resolver: zodResolver(formSchema),
			defaultValues: {
				terms: false,
				marketing: false,
				notifications: {
					email: false,
					push: false,
					sms: false,
				},
			},
		});
		const [submitted, setSubmitted] = useState(false);
		const [formData, setFormData] = useState<z.infer<typeof formSchema>>(
			form.getValues(),
		);

		return (
			<div>
				<div>
					<Form {...form}>
						<form
							className="flex w-full flex-col gap-6"
							onSubmit={form.handleSubmit((data) => {
								setFormData(data);
								setSubmitted(true);
							})}
						>
							<h1 className="text-lg font-bold">Sign Up Form</h1>

							<FormField
								control={form.control}
								name="terms"
								render={({ field }) => (
									<div>
										<Checkbox
											label="Accept terms and conditions"
											checked={field.value}
											onCheckedChange={field.onChange}
										/>
										<FormMessage />
									</div>
								)}
							/>

							<FormField
								control={form.control}
								name="marketing"
								render={({ field }) => (
									<div>
										<Checkbox
											label="Receive marketing emails"
											checked={field.value}
											onCheckedChange={field.onChange}
										/>
										<FormDescription>
											Get notified about new products and special offers.
										</FormDescription>
									</div>
								)}
							/>

							<div className="space-y-4">
								<FormField
									control={form.control}
									name="notifications"
									render={() => (
										<div>
											<FormLabel className="text-base">
												Notification Preferences
											</FormLabel>
											<FormDescription>
												Select how you&apos;d like to receive notifications.
											</FormDescription>
											<FormMessage />
										</div>
									)}
								/>

								<div className="space-y-2">
									<FormField
										control={form.control}
										name="notifications.email"
										render={({ field }) => (
											<Checkbox
												label="Email"
												checked={field.value}
												onCheckedChange={field.onChange}
											/>
										)}
									/>

									<FormField
										control={form.control}
										name="notifications.push"
										render={({ field }) => (
											<Checkbox
												label="Push Notifications"
												checked={field.value}
												onCheckedChange={field.onChange}
											/>
										)}
									/>

									<FormField
										control={form.control}
										name="notifications.sms"
										render={({ field }) => (
											<Checkbox
												label="SMS"
												checked={field.value}
												onCheckedChange={field.onChange}
											/>
										)}
									/>
								</div>
							</div>

							<Button type="submit">Submit</Button>
						</form>
					</Form>

					{submitted && (
						<div className="mt-6 rounded-md bg-gray-50 p-4">
							<h2 className="font-bold">Form Data</h2>
							<pre className="mt-2 whitespace-pre-wrap break-words text-sm">
								{JSON.stringify(formData, null, 2)}
							</pre>
						</div>
					)}
				</div>
			</div>
		);
	},
};
