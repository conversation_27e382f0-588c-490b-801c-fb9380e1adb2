import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@repo/ui/lib/utils';

const loaderVariantsConfig = {
	size: {
		sm: 'w-4 h-4 border-[2px]',
		md: 'w-6 h-6 border-[2px]',
		lg: 'w-8 h-8 border-[3px]',
	},
	variant: {
		white: 'border-white border-b-transparent',
		primary: 'border-brand-400 border-b-transparent',
		black: 'border-[#252422] border-b-transparent',
	},
};

const loaderVariants = cva(
	'inline-block rounded-full border-solid animate-spin box-border',
	{
		variants: loaderVariantsConfig,
		defaultVariants: {
			size: 'sm',
			variant: 'black',
		},
	},
);

export interface LoaderProps extends VariantProps<typeof loaderVariants> {
	/**
	 * Additional class names to apply to the loader
	 */
	className?: string;
}

/**
 * Loader component for indicating loading state
 */
export const Loader: React.FC<LoaderProps> = ({ className, size, variant }) => (
	<div className={cn(loaderVariants({ size, variant }), className)} />
);
