import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Loader } from './Loader';

const meta: Meta<typeof Loader> = {
	title: 'UI/Loader',
	component: Loader,
	tags: ['autodocs'],
	parameters: {
		docs: {
			description: {
				component:
					'Loaders are used to indicate a loading state when fetching data or performing operations.',
			},
		},
		status: {
			type: 'stable',
			badge: 'Live on Product',
		},
	},
};

export default meta;
type Story = StoryObj<typeof Loader>;

/**
 * Loader variants showing all available sizes and colors
 */
export const Variants: Story = {
	render: () => (
		<div className="space-y-12">
			{/* Main Loader Example */}
			<div className="bg-gray-100 p-8">
				<Loader />
			</div>

			{/* Properties */}
			<div>
				<h3 className="mb-2 text-sm font-medium">Properties</h3>

				{/* Variant */}
				<div className="mb-8">
					<h4 className="mb-1 text-xs font-medium">Variant</h4>
					<p className="mb-2 text-xs text-gray-600">
						Changes the color of the Loader.
					</p>
					<div className="flex gap-8 bg-gray-100 p-8">
						<div className="flex flex-col items-center">
							<Loader variant="black" />
							<p className="mt-2 text-xs">Black (Default)</p>
						</div>
						<div className="flex flex-col items-center">
							<Loader variant="white" />
							<p className="mt-2 text-xs">White</p>
						</div>
						<div className="flex flex-col items-center">
							<Loader variant="primary" />
							<p className="mt-2 text-xs">Primary</p>
						</div>
					</div>
				</div>

				{/* Size */}
				<div className="mb-8">
					<h4 className="mb-1 text-xs font-medium">Size</h4>
					<p className="mb-2 text-xs text-gray-600">
						Change the size of a Loader. The default size is small (16x16px).
					</p>
					<div className="flex items-center gap-8 bg-gray-100 p-8">
						<div className="flex flex-col items-center">
							<Loader size="sm" />
							<p className="mt-2 text-xs">Small (16x16px)</p>
						</div>
						<div className="flex flex-col items-center">
							<Loader size="md" />
							<p className="mt-2 text-xs">Medium (24x24px)</p>
						</div>
						<div className="flex flex-col items-center">
							<Loader size="lg" />
							<p className="mt-2 text-xs">Large (32x32px)</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	),
};
