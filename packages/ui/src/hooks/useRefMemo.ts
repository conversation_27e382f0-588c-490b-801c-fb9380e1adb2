import { RefObject, useRef } from 'react';

/**
 * useRefMemo is a custom hook that returns a ref object that holds the latest value.
 * It updates the ref's current value whenever the value changes, allowing you to
 * keep a reference to the latest value without causing re-renders.
 * @param value
 */
export const useRefMemo = <T>(value: T): RefObject<T> => {
	const ref = useRef(value);
	ref.current = value;
	return ref;
};
