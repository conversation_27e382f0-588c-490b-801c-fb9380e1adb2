'use client';

import { useState, useEffect } from 'react';
import { debounce } from 'es-toolkit';

/**
 * A hook that returns a debounced version of the provided value.
 * The debounced value will only update after the specified delay has passed
 * without any new updates to the original value.
 *
 * @param value The value to debounce
 * @param delay The delay in milliseconds
 * @returns The debounced value
 */
export function useDebounce<T>(value: T, delay: number): T {
	const [debouncedValue, setDebouncedValue] = useState<T>(value);

	useEffect(() => {
		// Create a debounced function that updates the state
		const debouncedSetValue = debounce((newValue: T) => {
			setDebouncedValue(newValue);
		}, delay);

		// Call the debounced function with the current value
		debouncedSetValue(value);

		// Clean up the debounced function when the component unmounts
		// or when the value or delay changes
		return () => {
			debouncedSetValue.cancel();
		};
	}, [value, delay]);

	return debouncedValue;
}

export default useDebounce;
