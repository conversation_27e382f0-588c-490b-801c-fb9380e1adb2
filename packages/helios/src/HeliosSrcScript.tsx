'use client';

import Script from 'next/script';
import { JSX } from 'react';
import { useIsClient } from '@repo/ui/hooks/useIsClient';

const defaultHeliosScriptUrl = process.env.NEXT_PUBLIC_HELIOS_SCRIPT_URL;

type ScriptConfig =
	| { src: string; code?: never }
	| { src?: never; code: string };

// temporary hardcoded configs
const admiralConfig = {
	enabled: true,
	script:
		'IShmdW5jdGlvbihyLF9uYW1lKXtyW19uYW1lXT1yW19uYW1lXXx8ZnVuY3Rpb24gdCgpeyh0LnE9dC5xfHxbXSkucHVzaChhcmd1bWVudHMpfSxyW19uYW1lXS52PXJbX25hbWVdLnZ8fDIscltfbmFtZV0ucz0iMyI7IShmdW5jdGlvbihyLHQsSCx6KXtmdW5jdGlvbiB4KEgseil7dHJ5e3g9ci5sb2NhbFN0b3JhZ2UsKEg9SlNPTi5wYXJzZSh4W2RlY29kZVVSSShkZWNvZGVVUkkoJyU2NyUyNTY1JTI1JTM3JTM0JTQ5JTI1NzQlNjUlMjU2ZCcpKV0oIl9hUVMwMk5UUXpSVVZET0VNNVJVRkJRall4TmtaRlJUbEZPVFl0TVEiKSkubGdrfHxbXSkmJih6PXJbdF0ucHViYWRzKCkpJiZILmZvckVhY2goKGZ1bmN0aW9uKHIpe3ImJnJbMF0mJnouc2V0VGFyZ2V0aW5nKHJbMF0sclsxXXx8IiIpfSkpfWNhdGNoKFEpe312YXIgeH10cnl7KHo9clt0XT1yW3RdfHx7fSkuY21kPXouY21kfHxbXSx0eXBlb2Ygei5wdWJhZHM9PT1IP3goKTp0eXBlb2Ygei5jbWQudW5zaGlmdD09PUg/ei5jbWQudW5zaGlmdCh4KTp6LmNtZC5wdXNoKHgpfWNhdGNoKFEpe319KSh3aW5kb3csJyUyNSUzNiUzNyUyNTZmJTI1NmYlMjU2JTM3bCUyNTY1dCUyNSUzNiUzMWcnLCJmdW5jdGlvbiIpOzt9KSh3aW5kb3csZGVjb2RlVVJJKGRlY29kZVVSSSgnJTYxZCU2ZCUyNSUzNiUzOSUyNSUzNyUzMmElMjUlMzZjJykpKTshKGZ1bmN0aW9uKHIsdCxILHope0g9ci5jcmVhdGVFbGVtZW50KHQpLHI9ci5nZXRFbGVtZW50c0J5VGFnTmFtZSh0KVswXSxILmFzeW5jPTEsSC5zcmM9Imh0dHBzOi8vbHV4Zm9ybXVsYS5jb20vY2h1bmtzL2JhNTEyM2YxY2MwZjIwYWYvYzI0MzJiYTcubWFpbi5qcyIsKHo9MCkmJnooSCksci5wYXJlbnROb2RlLmluc2VydEJlZm9yZShILHIpfSkoZG9jdW1lbnQsInNjcmlwdCIpOw==',
};

function getScriptsToLoad(): JSX.Element[] {
	const scripts = [] as ScriptConfig[];
	try {
		const decodedScript = Buffer.from(admiralConfig.script, 'base64').toString(
			'utf-8',
		);
		scripts.push({ code: decodedScript });
	} catch (err) {
		console.error(err);
	}

	return scripts.map((script, index) =>
		script.src ? (
			<Script key={`helios-script-${index}`} src={script.src} async />
		) : (
			<Script key={`helios-script-${index}`} id={`helios-script-${index}`}>
				{script.code}
			</Script>
		),
	);
}

interface HeliosSrcScriptProps {
	heliosScriptUrl?: string;
	isNoAds?: boolean;
}

export default function HeliosSrcScript({
	isNoAds,
	heliosScriptUrl = defaultHeliosScriptUrl,
}: HeliosSrcScriptProps) {
	const isClient = useIsClient();
	if (isNoAds) return null;
	if (!heliosScriptUrl) return null;

	return (
		isClient && (
			<>
				<Script src={heliosScriptUrl} />
				{...getScriptsToLoad()}
			</>
		)
	);
}
