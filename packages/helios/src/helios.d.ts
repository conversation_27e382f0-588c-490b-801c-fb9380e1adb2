// Define a type for the Helios object
export type HeliosType = {
	// Add known properties of the Helios object
	// This can be expanded as more properties are discovered
	init?: () => void;
	config?: Record<string, unknown>;
	version?: string;
	on: (event: string, callback: (data: unknown) => void) => void;
	emit: (event: string, data: unknown) => void;
	video: {
		requestVideoBids: () => Promise<unknown[]>;
	};
	modules: {
		iabc: {
			update: (params: Record<string, unknown>) => void;
		};
		videoTag: {
			createAdTag: (bids: unknown[]) => Promise<string>;
		};
	};
};

declare global {
	interface Window {
		__Helios: HeliosType;
	}
}
