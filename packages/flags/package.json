{"name": "@repo/flags", "version": "0.0.1", "type": "module", "private": true, "scripts": {"lint": "eslint --max-warnings=0 .", "check-types": "tsc --noEmit", "test": "vitest run"}, "exports": {".": "./src/index.ts", "./flags": "./src/flags.ts"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@repo/vitest-config": "workspace:*", "@types/node": "catalog:node", "@types/react": "catalog:web", "@types/react-dom": "catalog:web", "eslint": "catalog:dev", "typescript": "catalog:dev", "vitest": "catalog:dev"}, "peerDependencies": {"class-variance-authority": "catalog:web", "next": "catalog:web", "react": "catalog:web", "react-dom": "catalog:web"}, "dependencies": {"flags": "^4.0.1"}}