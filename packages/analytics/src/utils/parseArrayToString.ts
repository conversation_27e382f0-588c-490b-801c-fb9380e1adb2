/**
 * parse array to string according to mParticle format
 * @returns {string}
 */
export const parseArrayToString = (array: string[] | string[][]) => {
	// Sometimes an array may be undefined (e.g. author array)
	if (!array) return '';

	let result = '[';

	if (Array.isArray(array)) {
		array.forEach((item, index) => {
			result += `'${item}'${index !== array.length - 1 ? ',' : ''}`;
		});
	}

	result += ']';

	return result;
};
