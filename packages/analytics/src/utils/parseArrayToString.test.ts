import { describe, expect, it, vi } from 'vitest';
import { parseArrayToString } from './parseArrayToString';

describe('parseArrayToString', () => {
	it('should convert an array to a string representation', () => {
		// Execute & Verify
		expect(parseArrayToString(['item1', 'item2'])).toBe("['item1','item2']");
	});

	it('should return an empty array representation for an empty array', () => {
		// Execute & Verify
		expect(parseArrayToString([])).toBe('[]');
	});

	it('should return an empty string for undefined input', () => {
		// Execute & Verify
		expect(parseArrayToString(undefined as unknown as string[])).toBe('');
	});
});
