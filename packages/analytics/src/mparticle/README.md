# mParticle SDK Integration

This directory contains a React-based implementation of the mParticle SDK for analytics tracking. The implementation is compatible with Next.js, including React Server Components.

## Table of Contents

- [Usage](#usage)
  - [Component Setup](#component-setup)
  - [Tracking Page Views](#tracking-page-views)
  - [Tracking Article Views](#tracking-article-views)
  - [Tracking Session Start](#tracking-session-start)
  - [Tracking Location Views](#tracking-location-views)
  - [Tracking Page View End](#tracking-page-view-end)
  - [Tracking Experiment Enrollment](#tracking-experiment-enrollment)
  - [Managing User Identity](#managing-user-identity)
  - [Managing User Attributes](#managing-user-attributes)
  - [Using CMS Attributes](#using-cms-attributes)
  - [Using Effective Location](#using-effective-location)
- [Available Hooks](#available-hooks)
- [Initialization Process](#initialization-process)
- [Utility Functions](#utility-functions)
- [Types](#types)
- [Error Handling](#error-handling)
- [Next.js Compatibility](#nextjs-compatibility)
- [Environment Configuration](#environment-configuration)
- [Quick Start](#quick-start)
- [Testing and Debugging](#testing-and-debugging)

## Usage

### Component Setup

To ensure analytics tracking across your entire application, add the `MParticle` component to the layout file in your Next.js application:

```tsx
"use client"; // Not needed if the layout is a Server Component

import { MParticle } from "@repo/analytics/mparticle";

export default function RootLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	return (
		<html lang="en">
			<head>
				<MParticle />
			</head>
			<body>{children}</body>
		</html>
	);
}
```

For Next.js applications with multiple route groups and layouts, make sure to add the `MParticle` component to each layout file to ensure consistent tracking across all pages.

The MParticle component consumes configuration directly from environment variables:

- `NEXT_PUBLIC_MPARTICLE_API_KEY`: Your mParticle API key
- `NEXT_PUBLIC_MPARTICLE_ENV`: Set to 'production' for production environment, any other value for development

Make sure to set these environment variables in your `.env.local` file or in your deployment environment.

### Tracking Page Views

#### Using the `usePageView` Hook

The `usePageView` hook is used to track page views:

```tsx
"use client";

import { usePageView } from "@repo/analytics/mparticle";
import { useEffect } from "react";

function HomePage() {
	const { trackPageView } = usePageView();

	useEffect(() => {
		trackPageView();
	}, [trackPageView]);

	return <div>Home Page Content</div>;
}
```

The `usePageView` hook automatically includes the following attributes:

- `cookiesEnabled`: Whether cookies are enabled in the browser
- `pageId`: The current page ID from the pageIdAtom
- `url`: The anonymized URL path

### Tracking Article Views

Use the `useArticleView` hook to track article views:

```tsx
"use client";

import { useArticleView } from "@repo/analytics/mparticle";
import { useEffect } from "react";

function ArticlePage() {
	const trackArticleView = useArticleView();

	useEffect(() => {
		trackArticleView();
	}, [trackArticleView]);

	return <div>Article Content</div>;
}
```

The `useArticleView` hook automatically includes similar attributes to `usePageView` but is specifically designed for article pages.

### Tracking Session Start

Use the `useSessionStart` hook to track session start events:

```tsx
"use client";

import { useSessionStart } from "@repo/analytics/mparticle";
import { useEffect } from "react";

function SessionAwarePage() {
	const trackSessionStart = useSessionStart();

	useEffect(() => {
		// This will track a session start event if this is a new session
		trackSessionStart();
	}, [trackSessionStart]);

	return <div>Session-aware content</div>;
}
```

The `useSessionStart` hook automatically checks if this is a new metrics session and only sends the event once per session. It includes similar attributes to `usePageView` but is specifically designed for tracking new user sessions.

### Tracking Location Views

Use the `useLocationViewed` hook to track location view events:

```tsx
"use client";

import { useLocationViewed } from "@repo/analytics/mparticle";
import { useEffectiveLocation } from "@repo/analytics/mparticle";
import { useEffect } from "react";

function LocationPage() {
	const effectiveLocation = useEffectiveLocation();
	const trackLocationViewed = useLocationViewed({ effectiveLocation });

	useEffect(() => {
		trackLocationViewed();
	}, [trackLocationViewed]);

	return <div>Location-specific content</div>;
}
```

The `useLocationViewed` hook tracks when a user views a specific location. It automatically includes location attributes like latitude and longitude and only sends the event once per session or when the location changes.

### Tracking Page View End

Use the `usePageViewEnd` hook to track page view end events:

```tsx
"use client";

import { usePageViewEnd } from "@repo/analytics/mparticle";
import { useEffectiveLocation } from "@repo/analytics/mparticle";

function PageWithExitTracking() {
	const effectiveLocation = useEffectiveLocation();

	// This hook automatically sets up event listeners for page exit
	usePageViewEnd({ effectiveLocation });

	return <div>Page content with exit tracking</div>;
}
```

The `usePageViewEnd` hook automatically tracks when a user leaves a page, including metrics like time spent on the page. It sets up event listeners for the `beforeunload` event.

### Tracking Experiment Enrollment

Use the `useEnrolledInExperimentEvent` hook to track experiment enrollment events:

```tsx
"use client";

import { useEnrolledInExperimentEvent } from "@repo/analytics/mparticle";
import { useEffect } from "react";
import type { Exposure } from "@amplitude/experiment-js-client";

function ExperimentComponent() {
	const trackEnrolledInExperiment = useEnrolledInExperimentEvent();

	useEffect(() => {
		// When a user is enrolled in an experiment
		const exposure: Exposure = {
			flag_key: "my_experiment",
			variant: "treatment",
			experiment_key: "experiment_123",
		};

		trackEnrolledInExperiment(exposure);
	}, [trackEnrolledInExperiment]);

	return <div>Experiment component</div>;
}
```

The `useEnrolledInExperimentEvent` hook tracks when a user is enrolled in an experiment, including the experiment key and variant.

### Managing User Identity

Use the `useUserIdentity` hook to manage user identity:

```tsx
"use client";

import { useUserIdentity } from "@repo/analytics/mparticle";
import { UserIdentities } from "@mparticle/web-sdk";

function UserProfile() {
	const { login, logout, identify } = useUserIdentity();

	const handleLogin = async (user) => {
		try {
			// Create user identities object
			const identities: UserIdentities = {
				customerid: user.id,
				email: user.email,
				// Other identity types can be added here
			};

			await login(identities);

			// Continue with your login logic
		} catch (error) {
			console.error("Failed to identify user in mParticle", error);
		}
	};

	const handleLogout = async () => {
		try {
			await logout();
			// Continue with your logout logic
		} catch (error) {
			console.error("Failed to logout user in mParticle", error);
		}
	};

	// Identify a user without logging them in
	const handleIdentify = async (user) => {
		try {
			const identities: UserIdentities = {
				customerid: user.id,
				email: user.email,
			};

			await identify(identities);
		} catch (error) {
			console.error("Failed to identify user in mParticle", error);
		}
	};

	return (
		<div>
			<button onClick={() => handleLogin(currentUser)}>Login</button>
			<button onClick={handleLogout}>Logout</button>
		</div>
	);
}
```

### Managing User Attributes

Use the `useUserAttributes` hook to manage user attributes:

```tsx
"use client";

import { useUserAttributes } from "@repo/analytics/mparticle";

function UserPreferences() {
	const { setUserAttributes, setUserAttribute, removeUserAttribute } =
		useUserAttributes();

	const updateAllUserPreferences = () => {
		setUserAttributes(); // This will set all default user attributes
	};

	// Set a single user attribute
	const updateUserPreference = (preference, value) => {
		setUserAttribute(preference, value);
	};

	// Remove a user attribute
	const clearUserPreference = (preference) => {
		removeUserAttribute(preference);
	};

	return (
		<div>
			<button onClick={updateAllUserPreferences}>Update All Preferences</button>
			<button onClick={() => updateUserPreference("theme", "dark")}>
				Set Dark Theme
			</button>
			<button onClick={() => clearUserPreference("theme")}>
				Clear Theme Preference
			</button>
		</div>
	);
}
```

The `useUserAttributes` hook provides functions to set and remove user attributes. It automatically includes attributes like premium status, subscription information, and user preferences.

### Using CMS Attributes

Use the `useCMSAttributes` hook to access CMS-related attributes:

```tsx
"use client";

import { useCMSAttributes } from "@repo/analytics/mparticle";
import { useEffect } from "react";

function ContentPage() {
	const cmsAttributes = useCMSAttributes();

	useEffect(() => {
		console.log("Content ID:", cmsAttributes.contentId);
		console.log("Author:", cmsAttributes.author);
		console.log("Collection:", cmsAttributes.collection);
	}, [cmsAttributes]);

	return <div>Content page</div>;
}
```

The `useCMSAttributes` hook provides access to CMS-related attributes like content ID, author, collection, and entitlements.

### Using Effective Location

Use the `useEffectiveLocation` hook to determine the effective location for analytics events:

```tsx
"use client";

import { useEffectiveLocation } from "@repo/analytics/mparticle";

function WeatherPage() {
	const effectiveLocation = useEffectiveLocation();

	return (
		<div>
			<h1>
				Weather for {effectiveLocation?.displayName || "Unknown Location"}
			</h1>
			{/* Display weather data */}
		</div>
	);
}
```

The `useEffectiveLocation` hook provides access to the effective location data, which is used by other hooks for location-based tracking.

## Available Hooks

- `useMParticle`: Access the raw mParticle instance and initialization status
- `useIsInitialized`: Check if the mParticle SDK is initialized
- `useInitializeSdk`: Initialize the mParticle SDK with proper configuration
- `usePageView`: Track page views with customizable attributes
- `usePageViewEnd`: Track page view end events with time on page metrics
- `useArticleView`: Track article views with customizable attributes
- `useUserIdentity`: Manage user identity (login, logout, identify) with TypeScript support
- `useUserAttributes`: Manage user attributes with TypeScript support
- `useEffectiveLocation`: Determine the effective location for analytics events
- `useLocationViewed`: Track location viewed events
- `useSessionStart`: Track session start events with customizable attributes
- `useEventAttributes`: Access common attributes for analytics events
- `useCMSAttributes`: Access CMS-related attributes for content pages
- `useEnrolledInExperimentEvent`: Track experiment enrollment events

## Initialization Process

The mParticle SDK is initialized as follows:

1. The `useInitializeSdk` hook is called when the `MParticle` component is mounted
2. The SDK is configured with the API key, custom endpoints, data plan, and other settings from environment variables
3. User identities are set during initialization based on login status
4. The `MParticle` component directly uses individual hooks to initialize tracking:
   - Sets basic user attributes using `useUserAttributes`
   - Tracks session start events if this is a new session using `useSessionStart`
   - Tracks article views if on an article page using `useArticleView`
   - Tracks page views using `usePageView`
   - Tracks location viewed events using `useLocationViewed`

## Utility Functions

The implementation includes several utility functions for handling paths and cookies:

- `areCookiesEnabled`: Check if cookies are enabled in the browser
- `trimQueryAndFragment`: Remove query parameters and fragments from a URL path
- `trimLocId`: Remove location IDs from a URL path
- `getAnonymizedPath`: Anonymize a URL path by removing identifiable information
- `stripLastPartOfSlug`: Remove the last part of a URL slug
- `areObjectValuesDefined`: Check if all values in an object are defined
- `getAuthorString`: Format author information for tracking
- `parseArrayToString`: Convert an array to a comma-separated string
- `jwtDecoder`: Decode JWT tokens for subscription information
- `getSubscriptionTier`: Get the subscription tier from subscription data

These functions are used internally by the hooks to normalize paths and ensure consistent tracking.

## Types

The implementation includes TypeScript types for all hooks and functions, providing type safety and autocompletion. Key types include:

- `PageViewOptions`: Options for tracking page views
- `ArticleViewOptions`: Options for tracking article views
- `SessionStartOptions`: Options for tracking session start events
- `LocationViewedOptions`: Options for tracking location viewed events
- `EnrolledInExperimentEventOptions`: Options for tracking experiment enrollment events
- `UserIdentities`: User identity data for login/identify operations
- `UserAttributes`: User attribute data
- `CMSAttributes`: CMS-related attributes for content pages
- `LocationData`: Location data structure

## Error Handling

All hooks include error handling to prevent crashes if mParticle is not initialized or encounters errors. Errors are logged to the console in development mode. Each hook includes proper error handling and returns appropriate error messages or promises that can be caught and handled in your application code.

### Common Error Scenarios

1. **SDK Not Initialized**: If you attempt to use any hook before the SDK is initialized, the hook will log a warning and return a no-op function.

2. **API Key Missing**: If the `NEXT_PUBLIC_MPARTICLE_API_KEY` environment variable is not set, the SDK will not initialize and will log an error.

3. **Network Issues**: If the SDK cannot connect to mParticle servers, events will be queued locally and retried when connectivity is restored.

4. **Invalid Event Data**: If you pass invalid data to any tracking function, the hook will log an error and prevent the event from being sent.

### Debugging Tips

- Set `NEXT_PUBLIC_MPARTICLE_ENV` to any value other than 'production' to enable verbose logging
- Check the browser console for mParticle-related messages
- Use the mParticle Live Stream feature in the mParticle dashboard to see events in real-time
- Implement error callbacks in your tracking functions to handle specific error scenarios

## Next.js Compatibility

All hooks and components include the `'use client'` directive, making them compatible with Next.js React Server Components. They can be safely imported and used in client components without issues.

### Adding to Multiple Layouts

In Next.js applications with route groups and multiple layouts, you should add the `MParticle` component to each layout file. For example:

```tsx
// app/(home)/layout.tsx
import { MParticle } from "@repo/analytics/mparticle";

export default function HomeLayout({ children }) {
	return (
		<html lang="en">
			<body>
				<MParticle />
				<Header />
				{children}
			</body>
		</html>
	);
}

// app/content/layout.tsx
import { MParticle } from "@repo/analytics/mparticle";

export default function ContentLayout({ children }) {
	return (
		<html lang="en">
			<body>
				<MParticle />
				<main>{children}</main>
			</body>
		</html>
	);
}
```

This ensures that analytics tracking is available across all pages in your application, regardless of which layout they use.

### Server Components vs. Client Components

- The `MParticle` component and all hooks must be used within client components (marked with `"use client"`)
- You can import these components and hooks in server components, but they must be passed to client components for actual usage
- For server-rendered pages, analytics events will only be sent after hydration is complete on the client side

## Environment Configuration

For local development, create a `.env.local` file with the following variables:

```env
NEXT_PUBLIC_MPARTICLE_API_KEY=your_api_key_here
NEXT_PUBLIC_MPARTICLE_ENV=development
```

For production environments, set these variables in your deployment platform's environment configuration:

```env
NEXT_PUBLIC_MPARTICLE_API_KEY=your_production_api_key_here
NEXT_PUBLIC_MPARTICLE_ENV=production
```

### Environment Variables

- `NEXT_PUBLIC_MPARTICLE_API_KEY`: Your mParticle API key (required)
- `NEXT_PUBLIC_MPARTICLE_ENV`: Set to 'production' for production environment, any other value for development
  - In development mode, mParticle will log verbose messages to the console
  - In production mode, only warnings will be logged

## Quick Start

The fastest way to implement analytics tracking in your Next.js application:

1. Add the `MParticle` component to your layout files:

```tsx
// app/layout.tsx
import { MParticle } from "@repo/analytics/mparticle";

export default function RootLayout({ children }) {
	return (
		<html>
			<body>
				<MParticle />
				{children}
			</body>
		</html>
	);
}
```

2. Set up environment variables in your `.env.local` file:

```env
NEXT_PUBLIC_MPARTICLE_API_KEY=your_api_key_here
NEXT_PUBLIC_MPARTICLE_ENV=development
```

3. Track page views in your page components:

```tsx
"use client";

import { usePageView, useEffectiveLocation } from "@repo/analytics/mparticle";
import { useEffect } from "react";

export default function Page() {
	const effectiveLocation = useEffectiveLocation();
	const { trackPageView } = usePageView({ effectiveLocation });

	useEffect(() => {
		trackPageView();
	}, [trackPageView]);

	return <div>Page content</div>;
}
```

## Testing and Debugging

### Verifying Implementation

To verify that your mParticle implementation is working correctly:

1. Set `NEXT_PUBLIC_MPARTICLE_ENV` to any value other than 'production'
2. Open your browser's developer console
3. Navigate through your application and check for mParticle log messages
4. Verify that events are being sent with the expected attributes

### Using the mParticle Live Stream

The mParticle dashboard provides a Live Stream feature that shows events as they occur:

1. Log in to your mParticle account
2. Navigate to the Live Stream section
3. Filter by your application's API key
4. Interact with your application and observe events in real-time

### Testing in Development

For testing in development environments:

1. Use a separate mParticle API key for development
2. Set up a development workspace in mParticle
3. Configure outputs to test destinations
4. Validate that events are properly formatted and contain all required attributes

### Common Issues and Solutions

| Issue                             | Solution                                                     |
| --------------------------------- | ------------------------------------------------------------ |
| Events not appearing in mParticle | Check browser console for errors, verify API key is correct  |
| Missing user attributes           | Ensure `useUserAttributes` hooks are properly implemented    |
| Duplicate events                  | Check for multiple instances of tracking hooks or components |
| Incorrect event attributes        | Verify attribute formatting and naming conventions           |
| SDK not initializing              | Check environment variables and network connectivity         |
| Location data missing             | Verify that `useEffectiveLocation` is being used correctly   |
