'use client';

import { useEffect } from 'react';
import { useInitializeSdk } from './hooks/useInitializeSdk';
import { useUserAttributes } from './hooks/useUserAttributes';
import { useSessionStart } from './hooks/useSessionStart';
import { usePageView } from './hooks/usePageView';
import { useLocationViewed } from './hooks/useLocationViewed';
import { useArticleView } from './hooks/useArticleView';
import { usePageViewEnd } from './hooks/usePageViewEnd';

export const MParticle = () => {
	useInitializeSdk();

	const { setUserAttributes } = useUserAttributes();
	const trackSessionStart = useSessionStart();
	const trackArticleView = useArticleView();
	const trackPageView = usePageView();
	const trackLocationViewed = useLocationViewed();

	usePageViewEnd();

	useEffect(() => {
		setUserAttributes();
	}, [setUserAttributes]);

	useEffect(() => {
		trackSessionStart();
	}, [trackSessionStart]);

	useEffect(() => {
		trackArticleView();
	}, [trackArticleView]);

	useEffect(() => {
		trackPageView();
	}, [trackPageView]);

	useEffect(() => {
		trackLocationViewed();
	}, [trackLocationViewed]);

	return null;
};
