'use client';
// @ts-nocheck - Disable TypeScript checking for this test file

import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import { useInitializeSdk } from './useInitializeSdk';
import mParticle, { IdentityResult } from '@mparticle/web-sdk';
import { useIsInitialized } from './useIsInitialized';
import { wxuUserAnonymousIdAtom } from '@repo/user/atoms/wxu/user';
import { userEmailAtom } from '@repo/user/atoms/profile';
import { useUser } from '@repo/user/hooks/useUser';

// Mock dependencies
vi.mock('@mparticle/web-sdk', () => ({
	default: {
		init: vi.fn(),
	},
}));

vi.mock('./useIsInitialized', () => ({
	useIsInitialized: vi.fn(),
}));

vi.mock('jotai', () => ({
	useAtomValue: vi.fn(),
	atom: vi.fn((initialValue) => ({ init: initialValue })),
}));

vi.mock('@repo/user/hooks/useUser', () => ({
	useUser: vi.fn(),
}));

// Import the mocked useAtomValue after mocking jotai
const { useAtomValue } = await import('jotai');

describe('useInitializeSdk', () => {
	const mockUserID = 'test-user-id';

	beforeEach(() => {
		vi.resetAllMocks();

		// Mock environment variables
		vi.stubEnv('NEXT_PUBLIC_MPARTICLE_API_KEY', 'test-api-key');
		vi.stubEnv('NEXT_PUBLIC_MPARTICLE_ENV', 'development');

		// Mock localStorage
		vi.stubGlobal('localStorage', {
			setItem: vi.fn(),
		});

		// Mock console
		vi.stubGlobal('console', {
			debug: vi.fn(),
			error: vi.fn(),
		});
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should initialize mParticle when all conditions are met for a logged-in user', () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(false);
		vi.mocked(useUser).mockReturnValue({
			user: {
				isUserLoggedIn: true,
				userID: mockUserID,
				subscriptionTier: 0,
				isUserPremium: false,
			},
			error: null,
			logout: async () => true,
			userProfile: null,
		});
		vi.mocked(useAtomValue).mockImplementation((atom) => {
			if (atom === userEmailAtom) return '<EMAIL>';
			if (atom === wxuUserAnonymousIdAtom) return 'anonymous-id';
			return undefined;
		});

		// Execute
		renderHook(() => useInitializeSdk());

		// Verify
		expect(mParticle.init).toHaveBeenCalledWith(
			'test-api-key',
			expect.objectContaining({
				isDevelopmentMode: true,
				logLevel: 'verbose',
				identifyRequest: {
					userIdentities: {
						email: '<EMAIL>',
						customerid: 'test-user-id',
						other: 'anonymous-id',
					},
				},
				dataPlan: {
					planId: 'weather_web',
					planVersion: 1,
				},
			}),
		);
	});

	it('should initialize mParticle when all conditions are met for a non-logged-in user', () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(false);
		vi.mocked(useUser).mockReturnValue({
			user: {
				isUserLoggedIn: false,
				userID: '',
				subscriptionTier: 0,
				isUserPremium: false,
			},
			error: null,
			logout: async () => true,
			userProfile: null,
		});
		vi.mocked(useAtomValue).mockImplementation((atom) => {
			if (atom === userEmailAtom) return '';
			if (atom === wxuUserAnonymousIdAtom) return 'anonymous-id';
			return undefined;
		});

		// Execute
		renderHook(() => useInitializeSdk());

		// Verify
		expect(mParticle.init).toHaveBeenCalledWith(
			'test-api-key',
			expect.objectContaining({
				identifyRequest: {
					userIdentities: {
						email: '',
						customerid: null,
						other: 'anonymous-id',
					},
				},
			}),
		);
	});

	it('should not initialize mParticle when API key is missing', () => {
		// Setup
		vi.stubEnv('NEXT_PUBLIC_MPARTICLE_API_KEY', '');
		vi.mocked(useIsInitialized).mockReturnValue(false);
		vi.mocked(useUser).mockReturnValue({
			user: {
				isUserLoggedIn: true,
				userID: mockUserID,
				subscriptionTier: 0,
				isUserPremium: false,
			},
			error: null,
			logout: async () => true,
			userProfile: null,
		});
		vi.mocked(useAtomValue).mockImplementation((atom) => {
			if (atom === userEmailAtom) return '<EMAIL>';
			if (atom === wxuUserAnonymousIdAtom) return 'anonymous-id';
			return undefined;
		});

		// Execute
		renderHook(() => useInitializeSdk());

		// Verify
		expect(mParticle.init).not.toHaveBeenCalled();
		expect(console.error).toHaveBeenCalledWith('MParticle API key is required');
	});

	it('should not initialize mParticle when user identities are not set', () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(false);
		vi.mocked(useUser).mockReturnValue({
			user: {
				isUserLoggedIn: true,
				userID: mockUserID,
				subscriptionTier: 0,
				isUserPremium: false,
			},
			error: null,
			logout: async () => true,
			userProfile: null,
		});
		vi.mocked(useAtomValue).mockImplementation((atom) => {
			if (atom === userEmailAtom) return '';
			if (atom === wxuUserAnonymousIdAtom) return undefined;
			return undefined;
		});

		// Execute
		renderHook(() => useInitializeSdk());

		// Verify
		expect(mParticle.init).not.toHaveBeenCalled();
	});

	it('should not initialize mParticle when already initialized', () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(true);
		vi.mocked(useUser).mockReturnValue({
			user: {
				isUserLoggedIn: true,
				userID: mockUserID,
				subscriptionTier: 0,
				isUserPremium: false,
			},
			error: null,
			logout: async () => true,
			userProfile: null,
		});
		vi.mocked(useAtomValue).mockImplementation((atom) => {
			if (atom === userEmailAtom) return '<EMAIL>';
			if (atom === wxuUserAnonymousIdAtom) return 'anonymous-id';
			return undefined;
		});

		// Execute
		renderHook(() => useInitializeSdk());

		// Verify
		expect(mParticle.init).not.toHaveBeenCalled();
	});

	it('should only initialize mParticle once even when re-rendered', () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(false);
		vi.mocked(useUser).mockReturnValue({
			user: {
				isUserLoggedIn: false,
				userID: mockUserID,
				subscriptionTier: 0,
				isUserPremium: false,
			},
			error: null,
			logout: async () => true,
			userProfile: null,
		});
		vi.mocked(useAtomValue).mockImplementation((atom) => {
			if (atom === userEmailAtom) return '';
			if (atom === wxuUserAnonymousIdAtom) return 'anonymous-id';
			return undefined;
		});

		// Execute
		const { rerender } = renderHook(() => useInitializeSdk());
		rerender();

		// Verify
		expect(mParticle.init).toHaveBeenCalledTimes(1);
	});

	it('should set localStorage when identity callback is triggered', () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(false);
		vi.mocked(useUser).mockReturnValue({
			user: {
				isUserLoggedIn: true,
				userID: mockUserID,
				subscriptionTier: 0,
				isUserPremium: false,
			},
			error: null,
			logout: async () => true,
			userProfile: null,
		});
		vi.mocked(useAtomValue).mockImplementation((atom) => {
			if (atom === userEmailAtom) return '<EMAIL>';
			if (atom === wxuUserAnonymousIdAtom) return 'anonymous-id';
			return undefined;
		});

		// Execute
		renderHook(() => useInitializeSdk());

		// Get the identity callback
		const initMocks = vi.mocked(mParticle.init);

		const identityCallback = initMocks.mock.calls?.[0]?.[1]?.identityCallback;

		if (identityCallback) {
			const result = {
				getUser: () => ({
					getMPID: () => 'test-mpid',
				}),
			};

			// Simulate the identity callback being triggered
			identityCallback(result as IdentityResult);
		}

		// Verify
		expect(localStorage.setItem).toHaveBeenCalledWith(
			'wxu-web/keyval:mparticleUser',
			expect.stringContaining('test-mpid'),
		);
	});

	it('should handle production environment correctly', () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(false);
		vi.mocked(useUser).mockReturnValue({
			user: {
				isUserLoggedIn: true,
				userID: mockUserID,
				subscriptionTier: 0,
				isUserPremium: false,
			},
			error: null,
			logout: async () => true,
			userProfile: null,
		});
		vi.mocked(useAtomValue).mockImplementation((atom) => {
			if (atom === userEmailAtom) return '<EMAIL>';
			if (atom === wxuUserAnonymousIdAtom) return 'anonymous-id';
			return undefined;
		});
		vi.stubEnv('NEXT_PUBLIC_MPARTICLE_ENV', 'production');

		// Execute
		renderHook(() => useInitializeSdk());

		// Verify
		expect(mParticle.init).toHaveBeenCalledWith(
			'test-api-key',
			expect.objectContaining({
				isDevelopmentMode: false,
				logLevel: 'warning',
			}),
		);
	});
});
