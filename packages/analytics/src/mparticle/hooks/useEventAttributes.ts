'use client';

import { useAtomValue } from 'jotai';
import { getMetricsSession } from '../../session/metricsSession';
import {
	areCookiesEnabled,
	getAnonymizedPath,
	getCampaign,
	getPartner,
	getLinkReferral,
	getTpcc,
	stripLastPartOfSlug,
	WEEK_DAYS,
} from './utils';
import { pageIdAtom } from '../../atoms/pageId';
import { useMemo } from 'react';
import { pageLocaleAtom } from '../../atoms/pageLocale';
import { deviceClassAtom } from '../../atoms/deviceClass';
import { getCookie } from 'cookies-next/client';
import { usePathname } from 'next/navigation';
import canUseDOM from '@repo/utils/canUseDOM';
import { useLocationSource } from '@repo/location/hooks/useLocationSource';

// export interface AttributesProps {
// }

export interface EventAttributes {
	[key: string]: unknown;
	campaign: string | null;
	cookiesEnabled: boolean;
	dayOfWeek: string | null;
	deviceClass: string;
	functionalTechnologies: boolean;
	geographicallyRelevantAds: boolean;
	gpcSignalOptOut: boolean;
	hourOfDay: number;
	linkReferral: string | null;
	locale: string;
	pageId: string;
	partner: string | null;
	physicalCountry: string;
	physicalState: string;
	priv: string;
	privProducts: string;
	referrer: string | null;
	regime: string;
	schemaVersion: string;
	pageCount: number;
	sessionId: string;
	sessionStartTime: number;
	// siteMode: string;
	// sourcePage: string;
	tpcc: string | null;
	url: string;
	wlocCity?: string;
	wlocCountry?: string;
	wlocState?: string;
}

// Event Attributes
export const useEventAttributes = () => {
	// Cookies Attributes
	const campaign = getCampaign();
	const tpcc = getTpcc();
	const partner = getPartner();
	const cookiesEnabled = areCookiesEnabled();

	// GeoIp Attributes
	const physicalCountry = getCookie('twc-location-country') as string;
	const physicalState = getCookie('twc-location-region') as string;

	// Session Attributes
	const session = getMetricsSession();
	const pageCount = session?.pageCount ? session?.pageCount + 1 : 1;
	const sessionId = session?.sessionId || '';
	const sessionStartTime = session?.sessionStartTime || 0;

	// Page Attributes
	const pageId = useAtomValue(pageIdAtom) || '';
	const locale = useAtomValue(pageLocaleAtom);
	const deviceClass = useAtomValue(deviceClassAtom);
	const pathname = usePathname();
	const url =
		pageId === 'article'
			? stripLastPartOfSlug(pathname)
			: getAnonymizedPath(pathname);
	const linkReferral = getLinkReferral();
	const referrer = typeof document !== 'undefined' ? document.referrer : null;
	const locationSource = useLocationSource({});

	const wlocCity = locationSource?.effectiveLocation?.displayName;
	const wlocCountry = locationSource?.effectiveLocation?.adminDistrict;
	const wlocState = locationSource?.effectiveLocation?.adminDistrictCode;

	// Date Attributes
	const date = new Date();
	const hourOfDay = date.getHours();
	const day = date.getDay();
	const dayOfWeek = WEEK_DAYS[day] || null;

	// Privacy Attributes
	const regime = getCookie('twc-privacy') as string;
	const privProducts = canUseDOM
		? (window.DprSdk?.getCcpaSaleOptInString() ?? '')
		: '';
	const priv = canUseDOM
		? (window.DprSdk?.getGdprConsentOptInsString() ?? '')
		: '';
	const gpcSignalOptOut = canUseDOM
		? (window.navigator?.globalPrivacyControl ?? false)
		: false;
	const functionalTechnologies =
		priv?.includes('functional-technology') ?? false;
	const geographicallyRelevantAds =
		priv?.includes('geographically-relevant-advertising') ?? false;

	const generalAttributes: EventAttributes = useMemo(
		() => ({
			campaign,
			tpcc,
			cookiesEnabled,
			physicalCountry,
			physicalState,
			pageId,
			partner,
			locale,
			deviceClass,
			url,
			linkReferral,
			referrer,
			pageCount,
			sessionId,
			sessionStartTime,
			schemaVersion: 'nextgen',
			hourOfDay,
			dayOfWeek,
			wlocCity,
			wlocCountry,
			wlocState,
			privProducts,
			priv,
			gpcSignalOptOut,
			functionalTechnologies,
			geographicallyRelevantAds,
			regime,
		}),
		[
			campaign,
			tpcc,
			cookiesEnabled,
			physicalCountry,
			physicalState,
			pageId,
			partner,
			locale,
			url,
			linkReferral,
			referrer,
			pageCount,
			sessionId,
			sessionStartTime,
			hourOfDay,
			dayOfWeek,
			wlocCity,
			wlocCountry,
			wlocState,
			priv,
			privProducts,
			gpcSignalOptOut,
			functionalTechnologies,
			geographicallyRelevantAds,
			regime,
		],
	);

	return generalAttributes;
};
