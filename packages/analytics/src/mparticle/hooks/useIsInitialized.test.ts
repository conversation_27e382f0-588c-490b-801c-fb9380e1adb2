'use client';

import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import { useIsInitialized } from './useIsInitialized';
import mParticle from '@mparticle/web-sdk';

// Mock mParticle SDK
vi.mock('@mparticle/web-sdk', () => ({
	default: {
		isInitialized: vi.fn(),
		ready: vi.fn(),
	},
}));

describe('useIsInitialized', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should return true when mParticle is initialized', () => {
		// Setup
		vi.mocked(mParticle.isInitialized).mockReturnValue(true);

		// Execute
		const { result } = renderHook(() => useIsInitialized());

		// Verify
		expect(result.current).toBe(true);
	});

	it('should return false when mParticle is not initialized', () => {
		// Setup
		vi.mocked(mParticle.isInitialized).mockReturnValue(false);

		// Execute
		const { result } = renderHook(() => useIsInitialized());

		// Verify
		expect(result.current).toBe(false);
	});

	it('should subscribe to mParticle ready event', () => {
		// Setup
		vi.mocked(mParticle.isInitialized).mockReturnValue(false);

		// Execute
		renderHook(() => useIsInitialized());

		// Verify
		expect(mParticle.ready).toHaveBeenCalled();
	});
});
