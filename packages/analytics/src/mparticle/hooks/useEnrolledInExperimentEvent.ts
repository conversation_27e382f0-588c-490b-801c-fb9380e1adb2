'use client';

import { useCallback } from 'react';
import mParticle from '@mparticle/web-sdk';
import type { SDKEventAttrs, SDKEventCustomFlags } from '@mparticle/web-sdk';
import { EventAttributes, useEventAttributes } from './useEventAttributes';
import { useIsInitialized } from './useIsInitialized';

interface EnrolledInExperimentAttributes extends EventAttributes {
	newVariant: string;
	newExperiment: string;
	newExperimentReceivedAt: number;
}

export type Exposure = {
	/**
	 * (Required) The key for the flag the user was exposed to.
	 */
	flag_key: string;
	/**
	 * (Optional) The variant the user was exposed to. If null or missing, the
	 * event will not be persisted, and will unset the user property.
	 */
	variant?: string;
	/**
	 * (Optional) The experiment key used to differentiate between multiple
	 * experiments associated with the same flag.
	 */
	experiment_key?: string;
	/**
	 * (Optional) Flag, segment, and variant metadata produced as a result of
	 * evaluation for the user. Used for system purposes.
	 */
	metadata?: Record<string, unknown>;
};

/**
 * Hook for tracking experiment enrollment events in mParticle
 * @returns A function to track experiment enrollment events
 */
export const useEnrolledInExperimentEvent = () => {
	const generalAttributes = useEventAttributes();
	const isInitialized = useIsInitialized();

	const trackEnrolledInExperiment = useCallback(
		(exposure: Exposure, customFlags: SDKEventCustomFlags = {}) => {
			if (!isInitialized) {
				console.warn('MParticle is not initialized');
				return;
			}

			if (!exposure) {
				console.warn('No exposure data provided to trackEnrolledInExperiment');
				return;
			}

			try {
				const eventInfoAttributes: EnrolledInExperimentAttributes = {
					...generalAttributes,
					newVariant: exposure.variant || '',
					newExperiment: exposure.experiment_key || '',
					newExperimentReceivedAt: Date.now(),
				};

				mParticle.logEvent(
					'enrolled-in-experiment',
					mParticle.EventType.Other,
					eventInfoAttributes as SDKEventAttrs,
					customFlags,
				);

				console.debug(
					`MParticle: Event tracked - enrolled-in-experiment for ${exposure.flag_key || 'unknown'}:${exposure.variant}`,
				);
			} catch (error) {
				console.error('MParticle: Failed to track event', error);
			}
		},
		[isInitialized, generalAttributes],
	);

	return trackEnrolledInExperiment;
};
