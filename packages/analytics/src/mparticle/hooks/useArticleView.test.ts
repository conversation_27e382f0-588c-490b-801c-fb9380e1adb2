'use client';

import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useArticleView } from './useArticleView';
import mParticle from '@mparticle/web-sdk';
import { useIsInitialized } from './useIsInitialized';
import { useEventAttributes, EventAttributes } from './useEventAttributes';
import { useCMSAttributes, CMSAttributes } from './useCMSAttributes';
import type { LocationData } from '@repo/location/types';
import { usePathname } from 'next/navigation';

// Mock dependencies
vi.mock('@mparticle/web-sdk', () => ({
	default: {
		logEvent: vi.fn(),
		EventType: {
			Other: 'other',
		},
	},
}));

vi.mock('./useIsInitialized', () => ({
	useIsInitialized: vi.fn(),
}));

vi.mock('./useEventAttributes', () => ({
	useEventAttributes: vi.fn(),
}));

vi.mock('./useCMSAttributes', () => ({
	useCMSAttributes: vi.fn(),
}));

vi.mock('next/navigation', () => ({
	usePathname: vi.fn(),
}));

describe('useArticleView', () => {
	const mockEventAttributes: EventAttributes = {
		pageId: 'article', // Important: must be 'article' for the hook to work
		url: '/test',
		cookiesEnabled: true,
		campaign: null,
		dayOfWeek: 'MON',
		deviceClass: 'desktop',
		functionalTechnologies: false,
		geographicallyRelevantAds: false,
		gpcSignalOptOut: false,
		hourOfDay: 12,
		linkReferral: null,
		locale: 'en-US',
		partner: '',
		physicalCountry: 'US',
		physicalState: 'NY',
		priv: '',
		privProducts: '',
		referrer: null,
		regime: '',
		schemaVersion: 'nextgen',
		pageCount: 1,
		sessionId: 'test-session',
		sessionStartTime: Date.now(),
		tpcc: null,
		wlocCity: 'New York, NY',
		wlocCountry: 'New York',
		wlocState: 'NY',
	};

	const mockCMSAttributes: CMSAttributes = {
		author: 'John Doe',
		contentId: 'article-123',
		publishDate: '2025-05-06',
		collection: 'weather',
		entitlements: "['premium']",
		createdDate: '2025-05-01',
	};

	beforeEach(() => {
		vi.resetAllMocks();
		vi.mocked(useIsInitialized).mockReturnValue(true);
		vi.mocked(useEventAttributes).mockReturnValue(mockEventAttributes);
		vi.mocked(useCMSAttributes).mockReturnValue(mockCMSAttributes);
		vi.mocked(usePathname).mockReturnValue('/news/weather/article-123');
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should track article view when initialized and attributes are ready', async () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(true);

		// Execute
		const { result } = renderHook(() => useArticleView());

		await act(async () => {
			await result.current();
		});

		// Verify
		expect(mParticle.logEvent).toHaveBeenCalledWith(
			'article-viewed',
			'other',
			expect.objectContaining({
				...mockEventAttributes,
				...mockCMSAttributes,
				url: '/news/weather',
			}),
		);
	});

	it('should not track article view when not initialized', async () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(false);

		// Execute
		const { result } = renderHook(() => useArticleView());

		await act(async () => {
			await result.current();
		});

		// Verify
		expect(mParticle.logEvent).not.toHaveBeenCalled();
	});

	it('should not track article view when page is not an article', async () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(true);
		vi.mocked(useEventAttributes).mockReturnValue({
			...mockEventAttributes,
			pageId: 'home', // Not an article page
		} as EventAttributes);

		// Execute
		const { result } = renderHook(() => useArticleView());

		await act(async () => {
			await result.current();
		});

		// Verify
		expect(mParticle.logEvent).not.toHaveBeenCalled();
	});

	it('should only track article view once even when called multiple times', async () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(true);

		// Execute
		const { result } = renderHook(() => useArticleView());

		await act(async () => {
			await result.current();
			await result.current(); // Call a second time
		});

		// Verify
		expect(mParticle.logEvent).toHaveBeenCalledTimes(1);
	});
});
