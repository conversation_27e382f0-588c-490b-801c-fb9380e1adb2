'use client';

import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useUserAttributes } from './useUserAttributes';
import mParticle from '@mparticle/web-sdk';
import { useIsInitialized } from './useIsInitialized';
import { useUser } from '@repo/user/hooks/useUser';
import { useAtomValue } from 'jotai';
import { getCookie } from 'cookies-next/client';
import * as utils from './utils';

// Mock mParticle SDK
vi.mock('@mparticle/web-sdk', () => ({
	default: {
		Identity: {
			getCurrentUser: vi.fn(),
		},
	},
}));

// Import types from mParticle for better type checking
import type { User } from '@mparticle/web-sdk';
import { userFirstNameAtom, userGenderAtom } from '@repo/user/atoms/profile';
import {
	userHasLoggedInBefore<PERSON>tom,
	wxuUserAnonymousIdAtom,
} from '@repo/user/atoms/wxu/user';

// Mock useIsInitialized hook
vi.mock('./useIsInitialized', () => ({
	useIsInitialized: vi.fn(),
}));

// Mock useUser hook
vi.mock('@repo/user/hooks/useUser', () => ({
	useUser: vi.fn(),
}));

// Mock jotai and atoms
vi.mock('jotai', () => ({
	useAtomValue: vi.fn(),
}));

// Mock specific atoms
vi.mock('@repo/user/atoms/profile', () => ({
	userFirstNameAtom: 'userFirstNameAtom',
	userGenderAtom: 'userGenderAtom',
}));

vi.mock('@repo/user/atoms/wxu/user', () => ({
	wxuUserAnonymousIdAtom: 'wxuUserAnonymousIdAtom',
	userHasLoggedInBeforeAtom: 'userHasLoggedInBeforeAtom',
}));

// Mock cookies-next
vi.mock('cookies-next/client', () => ({
	getCookie: vi.fn(),
}));

// Mock utils
vi.mock('./utils', () => ({
	areObjectValuesDefined: vi.fn(),
	getSubscriptionTier: vi.fn(),
	jwtDecoder: vi.fn(),
}));

// Mock canUseDOM
vi.mock('@repo/utils/canUseDOM', () => ({
	default: true,
}));

describe('useUserAttributes', () => {
	// Create a mock of mParticle User object with just the methods we need
	const mockCurrentUser = {
		setUserAttributes: vi.fn(),
		setUserAttribute: vi.fn(),
		removeUserAttribute: vi.fn(),
	} as unknown as User; // Type assertion to User

	// Create typed versions of the mock functions for better TypeScript support
	const mockSetUserAttributes = mockCurrentUser.setUserAttributes as ReturnType<
		typeof vi.fn
	>;
	const mockSetUserAttribute = mockCurrentUser.setUserAttribute as ReturnType<
		typeof vi.fn
	>;
	const mockRemoveUserAttribute =
		mockCurrentUser.removeUserAttribute as ReturnType<typeof vi.fn>;

	// Mock user data
	const mockUserData = {
		userID: '', // We don't use this in our tests
		isUserLoggedIn: true,
		isUserPremium: true,
		subscriptionTier: 1,
	};

	const mockSubscriptionJWT = {
		expirationDateTime: '2023-12-31',
		transactionDateTime: '2023-01-01',
		firstTransactionDateTime: '2023-01-01',
		autoRenew: true,
		productID: 'premium-123',
		price: '9.99',
		productName: 'Premium Subscription',
	};

	beforeEach(() => {
		vi.resetAllMocks();

		// Default mock implementations
		vi.mocked(useIsInitialized).mockReturnValue(true);
		vi.mocked(useUser).mockReturnValue({
			user: mockUserData,
			userProfile: null,
			error: null,
			logout: vi.fn(),
		});
		vi.mocked(useAtomValue).mockImplementation((atom: any) => {
			// Mock different atom values based on the atom reference
			if (atom === userFirstNameAtom) return 'John';
			if (atom === userGenderAtom) return 'Male';
			if (atom === wxuUserAnonymousIdAtom) return 'anon-123';
			if (atom === userHasLoggedInBeforeAtom) return true;
			return undefined;
		});
		vi.mocked(getCookie).mockReturnValue('subscription-jwt');
		vi.mocked(utils.jwtDecoder).mockReturnValue(mockSubscriptionJWT);
		vi.mocked(utils.getSubscriptionTier).mockReturnValue('premium');
		vi.mocked(utils.areObjectValuesDefined).mockReturnValue(true);
		vi.mocked(mParticle.Identity.getCurrentUser).mockReturnValue(
			mockCurrentUser as User,
		);

		// Mock window.DprSdk
		vi.stubGlobal('window', {
			DprSdk: {
				getUserConsent: vi.fn().mockReturnValue(true),
			},
		});
	});

	afterEach(() => {
		vi.clearAllMocks();
		vi.unstubAllGlobals();
	});

	it('should return the expected functions', () => {
		// Execute
		const { result } = renderHook(() => useUserAttributes());

		// Verify
		expect(result.current).toHaveProperty('setUserAttributes');
		expect(result.current).toHaveProperty('setUserAttribute');
		expect(result.current).toHaveProperty('removeUserAttribute');
		expect(typeof result.current.setUserAttributes).toBe('function');
		expect(typeof result.current.setUserAttribute).toBe('function');
		expect(typeof result.current.removeUserAttribute).toBe('function');
	});

	it('should collect user attributes correctly', () => {
		// Execute
		renderHook(() => useUserAttributes());

		// Verify
		expect(getCookie).toHaveBeenCalledWith('subscription');
		expect(utils.jwtDecoder).toHaveBeenCalledWith('subscription-jwt');
		expect(utils.getSubscriptionTier).toHaveBeenCalledWith(1);
	});

	it('should not set user attributes when mParticle is not initialized', () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(false);

		// Execute
		const { result } = renderHook(() => useUserAttributes());
		act(() => {
			result.current.setUserAttributes();
		});

		// Verify
		expect(mockSetUserAttributes).not.toHaveBeenCalled();
	});

	it('should not set user attributes when attributes are not ready', () => {
		// Setup
		vi.mocked(utils.areObjectValuesDefined).mockReturnValue(false);

		// Execute
		const { result } = renderHook(() => useUserAttributes());
		act(() => {
			result.current.setUserAttributes();
		});

		// Verify
		expect(mockSetUserAttributes).not.toHaveBeenCalled();
	});

	it('should not set user attributes when there is no current user', () => {
		// Setup
		vi.mocked(mParticle.Identity.getCurrentUser).mockReturnValue(
			null as unknown as User,
		);

		// Execute
		const { result } = renderHook(() => useUserAttributes());
		act(() => {
			result.current.setUserAttributes();
		});

		// Verify
		expect(mockSetUserAttributes).not.toHaveBeenCalled();
	});

	it('should set user attributes when initialized and attributes are ready', () => {
		// Execute
		const { result } = renderHook(() => useUserAttributes());
		act(() => {
			result.current.setUserAttributes();
		});

		// Verify
		expect(mockSetUserAttributes).toHaveBeenCalled();
		// Use non-null assertion since we've verified the function was called
		const userAttributesArg = mockSetUserAttributes.mock.calls?.[0]?.[0] ?? {};
		expect(userAttributesArg).toHaveProperty('premium', true);
		expect(userAttributesArg).toHaveProperty('premiumProductTier', 'premium');
		expect(userAttributesArg).toHaveProperty('isLoggedIn', true);
		expect(userAttributesArg).toHaveProperty('isRegisteredUser', true);
		expect(userAttributesArg).toHaveProperty('$FirstName', 'John');
		expect(userAttributesArg).toHaveProperty('$Gender', 'Male');
		expect(userAttributesArg).toHaveProperty('Amplitude.device_id', 'anon-123');
	});

	it('should not set user attribute when mParticle is not initialized', () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(false);
		const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

		// Execute
		const { result } = renderHook(() => useUserAttributes());
		act(() => {
			result.current.setUserAttribute('testKey', 'testValue');
		});

		// Verify
		expect(mockSetUserAttribute).not.toHaveBeenCalled();
		expect(consoleSpy).toHaveBeenCalledWith('MParticle is not initialized');
	});

	it('should not set user attribute when there is no current user', () => {
		// Setup
		vi.mocked(mParticle.Identity.getCurrentUser).mockReturnValue(
			null as unknown as User,
		);
		const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

		// Execute
		const { result } = renderHook(() => useUserAttributes());
		act(() => {
			result.current.setUserAttribute('testKey', 'testValue');
		});

		// Verify
		expect(mockSetUserAttribute).not.toHaveBeenCalled();
		expect(consoleSpy).toHaveBeenCalledWith('MParticle: No current user');
	});

	it('should set user attribute when mParticle is initialized with current user', () => {
		// Setup
		const consoleSpy = vi.spyOn(console, 'debug').mockImplementation(() => {});

		// Execute
		const { result } = renderHook(() => useUserAttributes());
		act(() => {
			result.current.setUserAttribute('testKey', 'testValue');
		});

		// Verify
		expect(mockSetUserAttribute).toHaveBeenCalledWith('testKey', 'testValue');
		expect(consoleSpy).toHaveBeenCalledWith(
			'MParticle: User attribute set - testKey',
		);
	});

	it('should not remove user attribute when mParticle is not initialized', () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(false);
		const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

		// Execute
		const { result } = renderHook(() => useUserAttributes());
		act(() => {
			result.current.removeUserAttribute('testKey');
		});

		// Verify
		expect(mockRemoveUserAttribute).not.toHaveBeenCalled();
		expect(consoleSpy).toHaveBeenCalledWith('MParticle is not initialized');
	});

	it('should not remove user attribute when there is no current user', () => {
		// Setup
		vi.mocked(mParticle.Identity.getCurrentUser).mockReturnValue(
			null as unknown as User,
		);
		const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

		// Execute
		const { result } = renderHook(() => useUserAttributes());
		act(() => {
			result.current.removeUserAttribute('testKey');
		});

		// Verify
		expect(mockRemoveUserAttribute).not.toHaveBeenCalled();
		expect(consoleSpy).toHaveBeenCalledWith('MParticle: No current user');
	});

	it('should remove user attribute when mParticle is initialized with current user', () => {
		// Setup
		const consoleSpy = vi.spyOn(console, 'debug').mockImplementation(() => {});

		// Execute
		const { result } = renderHook(() => useUserAttributes());
		act(() => {
			result.current.removeUserAttribute('testKey');
		});

		// Verify
		expect(mockRemoveUserAttribute).toHaveBeenCalledWith('testKey');
		expect(consoleSpy).toHaveBeenCalledWith(
			'MParticle: User attribute removed - testKey',
		);
	});

	it('should handle user consent settings correctly', () => {
		// Setup
		vi.stubGlobal('window', {
			DprSdk: {
				getUserConsent: vi.fn().mockImplementation((type) => {
					if (type === 'sale-of-data') return true;
					if (type === 'sensitive-data') return false;
					return null;
				}),
			},
		});

		// Execute
		const { result } = renderHook(() => useUserAttributes());
		act(() => {
			result.current.setUserAttributes();
		});

		// Verify
		expect(mockSetUserAttributes).toHaveBeenCalled();
		// Use non-null assertion since we've verified the function was called
		const userAttributesArg = mockSetUserAttributes.mock.calls?.[0]?.[0] ?? {};
		expect(userAttributesArg).toHaveProperty('saleOfDataAuthorization', true);
		expect(userAttributesArg).toHaveProperty(
			'sensitiveDataAuthorization',
			false,
		);
	});

	it('should remove undefined properties from user attributes', () => {
		// Setup
		vi.mocked(useAtomValue).mockImplementation((atom: any) => {
			// Only return defined values for some atoms
			if (atom === 'wxuUserAnonymousIdAtom') return 'anon-123';
			return undefined;
		});

		// Execute
		const { result } = renderHook(() => useUserAttributes());
		act(() => {
			result.current.setUserAttributes();
		});

		// Verify
		expect(mockSetUserAttributes).toHaveBeenCalled();
		// Use non-null assertion since we've verified the function was called
		const userAttributesArg = mockSetUserAttributes.mock.calls?.[0]?.[0] ?? {};
		expect(userAttributesArg).not.toHaveProperty('$FirstName');
		expect(userAttributesArg).not.toHaveProperty('$Gender');
		expect(userAttributesArg).toHaveProperty('Amplitude.device_id', 'anon-123');
	});
});
