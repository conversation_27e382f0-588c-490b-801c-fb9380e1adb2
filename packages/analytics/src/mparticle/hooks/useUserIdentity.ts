'use client';

import { useCallback } from 'react';
import mParticle, { IdentityApiData, UserIdentities } from '@mparticle/web-sdk';
import { useIsInitialized } from './useIsInitialized';

/**
 * Hook for managing user identity in mParticle
 * @returns Functions to identify, login, and logout
 */
export const useUserIdentity = () => {
	const isInitialized = useIsInitialized();

	const identify = useCallback(
		(identities: UserIdentities) => {
			if (!isInitialized) {
				console.warn('MParticle is not initialized');
				return Promise.reject(new Error('MParticle is not initialized'));
			}

			// TODO: Add all user identities when UPSx logic is added in
			const identityRequest: IdentityApiData = {
				userIdentities: {
					customerid: identities.customerid,
					email: identities.email,
					other: identities.other,
				},
			};

			return mParticle.Identity.identify(identityRequest);
		},
		[isInitialized],
	);

	const login = useCallback(
		(identities: UserIdentities) => {
			if (!isInitialized) {
				console.warn('MParticle is not initialized');
				return Promise.reject(new Error('MParticle is not initialized'));
			}

			const identityRequest: IdentityApiData = {
				userIdentities: {
					customerid: identities.customerid,
					email: identities.email,
					facebook: identities.facebook,
					facebookcustomaudienceid: identities.facebookcustomaudienceid,
					google: identities.google,
					microsoft: identities.microsoft,
					other: identities.other,
					other2: identities.other2,
					other3: identities.other3,
					other4: identities.other4,
					twitter: identities.twitter,
					yahoo: identities.yahoo,
				},
			};

			return mParticle.Identity.login(identityRequest);
		},
		[isInitialized],
	);

	const logout = useCallback(() => {
		if (!isInitialized) {
			console.warn('MParticle is not initialized');
			return Promise.reject(new Error('MParticle is not initialized'));
		}

		const identityRequest: IdentityApiData = { userIdentities: {} };
		return mParticle.Identity.logout(identityRequest);
	}, [isInitialized]);

	return {
		identify,
		login,
		logout,
	};
};
