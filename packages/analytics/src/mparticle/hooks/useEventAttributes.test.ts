'use client';

import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import { useEventAttributes } from './useEventAttributes';
import { getMetricsSession } from '../../session/metricsSession';
import * as utils from './utils';
import { pageIdAtom } from '../../atoms/pageId';
import { pageLocaleAtom } from '../../atoms/pageLocale';
import { deviceClassAtom } from '../../atoms/deviceClass';
import { getCookie } from 'cookies-next/client';
import { usePathname } from 'next/navigation';
import type { LocationData } from '@repo/location/types';
import { renderHook } from '@testing-library/react';
import { useAtomValue } from 'jotai';
import { pageLocationDataAtom } from '@repo/location/atoms/pagelocation';

// Mock dependencies
vi.mock('../../session/metricsSession', () => ({
	getMetricsSession: vi.fn(),
}));

vi.mock('./utils', () => ({
	areCookiesEnabled: vi.fn(),
	getCampaign: vi.fn(),
	getPartner: vi.fn(),
	getTpcc: vi.fn(),
	getLinkReferral: vi.fn(),
	stripLastPartOfSlug: vi.fn(),
	getAnonymizedPath: vi.fn(),
	WEEK_DAYS: ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'],
}));

// Mock jotai
vi.mock('jotai', () => ({
	useAtomValue: vi.fn(),
	atom: vi.fn((initialValue) => ({ init: initialValue })),
}));

vi.mock('cookies-next/client', () => ({
	getCookie: vi.fn(),
}));

vi.mock('next/navigation', () => ({
	usePathname: vi.fn(),
	useParams: vi.fn(),
}));

vi.mock('@repo/utils/canUseDOM', () => ({
	default: true,
}));

describe('useEventAttributes', () => {
	const mockSession = {
		pageCount: 5,
		sessionId: 'test-session-id',
		sessionStartTime: 1620000000000,
	};

	const mockLocationData: LocationData = {
		displayName: 'New York, NY',
		adminDistrict: 'New York',
		adminDistrictCode: 'NY',
		geocode: '40.7128,-74.006',
		placeId: 'ny-123',
		city: 'New York',
		countryCode: 'US',
	};

	beforeEach(() => {
		vi.resetAllMocks();

		// Mock session
		vi.mocked(getMetricsSession).mockReturnValue(mockSession);

		// Mock utils
		vi.mocked(utils.areCookiesEnabled).mockReturnValue(true);
		vi.mocked(utils.getCampaign).mockReturnValue('test-campaign');
		vi.mocked(utils.getPartner).mockReturnValue('test-partner');
		vi.mocked(utils.getTpcc).mockReturnValue('test-tpcc');
		vi.mocked(utils.getLinkReferral).mockReturnValue('test-referral');
		vi.mocked(utils.getAnonymizedPath).mockImplementation((path) => path);
		vi.mocked(utils.stripLastPartOfSlug).mockImplementation(
			(path = '') => path,
		);

		// Mock atoms
		vi.mocked(useAtomValue).mockImplementation((atom) => {
			if (atom === pageIdAtom) return 'test-page';
			if (atom === pageLocaleAtom) return 'en-US';
			if (atom === deviceClassAtom) return 'mobile';
			if (atom === pageLocationDataAtom) return mockLocationData;
			return '';
		});

		// Mock cookies - use type assertion to bypass TypeScript errors
		const mockedGetCookie = getCookie as unknown as ReturnType<typeof vi.fn>;
		mockedGetCookie.mockImplementation((name: string): string => {
			if (name === 'partner') return 'mockedPartner';
			if (name === 'twc-location-country') return 'US';
			if (name === 'twc-location-region') return 'NY';
			if (name === 'twc-privacy') return 'ccpa';
			return '';
		});

		// Mock pathname
		vi.mocked(usePathname).mockReturnValue('/weather');

		vi.spyOn(document, 'referrer', 'get').mockImplementationOnce(
			() => 'https://example.com',
		);

		// Mock window objects
		vi.stubGlobal('window', {
			DprSdk: {
				getUserConsentWithDefaultValue: vi.fn().mockReturnValue('true'),
				getGdprConsentOptInsString: vi
					.fn()
					.mockReturnValue(
						'functional-technology,geographically-relevant-advertising',
					),
				getCcpaSaleOptInString: vi.fn().mockReturnValue('CCPAoptin'),
			},
			navigator: {
				globalPrivacyControl: false,
			},
		});

		// Mock date
		const mockDate = new Date(2023, 0, 2, 15); // Monday, Jan 2, 2023, 3:00 PM
		vi.setSystemTime(mockDate);
	});

	afterEach(() => {
		vi.clearAllMocks();
		vi.useRealTimers();
		vi.unstubAllGlobals();
	});

	it('should return the correct attributes with default values', async () => {
		// Execute
		const { result } = renderHook(() => useEventAttributes());

		// Verify
		const attributes = result.current;
		expect(attributes.campaign).toBe('test-campaign');
		expect(attributes.tpcc).toBe('test-tpcc');
		expect(attributes.cookiesEnabled).toBe(true);
		expect(attributes.physicalCountry).toBe('US');
		expect(attributes.physicalState).toBe('NY');
		expect(attributes.pageId).toBe('test-page');
		expect(attributes.partner).toBe('test-partner');
		expect(attributes.locale).toBe('en-US');
		expect(attributes.deviceClass).toBe('mobile');
		expect(attributes.url).toBe('/weather');
		expect(attributes.linkReferral).toBe('test-referral');
		expect(attributes.referrer).toBe('https://example.com');
		expect(attributes.pageCount).toBe(6);
		expect(attributes.sessionId).toBe('test-session-id');
		expect(attributes.sessionStartTime).toBe(1620000000000);
		expect(attributes.schemaVersion).toBe('nextgen');
		expect(attributes.hourOfDay).toBe(15);
		expect(attributes.dayOfWeek).toBe('MON');
		expect(attributes.wlocCity).toBe('New York, NY');
		expect(attributes.wlocCountry).toBe('New York');
		expect(attributes.wlocState).toBe('NY');
		expect(attributes.privProducts).toBe('CCPAoptin');
		expect(attributes.priv).toBe(
			'functional-technology,geographically-relevant-advertising',
		);
		expect(attributes.gpcSignalOptOut).toBe(false);
		expect(attributes.functionalTechnologies).toBe(true);
		expect(attributes.geographicallyRelevantAds).toBe(true);
		expect(attributes.regime).toBe('ccpa');
	});

	it('should handle article page URLs correctly', () => {
		// Setup
		vi.mocked(useAtomValue).mockImplementation((atom) => {
			if (atom === pageIdAtom) return 'article';
			if (atom === pageLocaleAtom) return 'en-US';
			if (atom === deviceClassAtom) return 'mobile';
			return '';
		});
		vi.mocked(usePathname).mockReturnValue('/news/weather/article-123');
		vi.mocked(utils.stripLastPartOfSlug).mockReturnValue('/news/weather');

		// Execute
		const { result } = renderHook(() => useEventAttributes());

		// Verify
		expect(result.current.url).toBe('/news/weather');
		expect(result.current.pageId).toBe('article');
	});

	it('should handle non-article page URLs correctly', () => {
		// Setup
		vi.mocked(usePathname).mockReturnValue('/weather/forecast');
		vi.mocked(utils.getAnonymizedPath).mockReturnValue('/weather/forecast');

		// Execute
		const { result } = renderHook(() => useEventAttributes());

		// Verify
		expect(result.current.url).toBe('/weather/forecast');
	});

	it('should handle missing session data', () => {
		// Setup
		vi.mocked(getMetricsSession).mockReturnValue(null);

		// Execute
		const { result } = renderHook(() => useEventAttributes());

		// Verify
		expect(result.current.pageCount).toBe(1);
		expect(result.current.sessionId).toBe('');
		expect(result.current.sessionStartTime).toBe(0);
	});

	it('should handle privacy settings correctly', () => {
		// Setup
		vi.stubGlobal('window', {
			DprSdk: {
				getUserConsentWithDefaultValue: vi.fn().mockReturnValue('false'),
				getGdprConsentOptInsString: vi
					.fn()
					.mockReturnValue('geographically-relevant-advertising'),
				getCcpaSaleOptInString: vi.fn().mockReturnValue('CCPAoptout'),
			},
			navigator: {
				globalPrivacyControl: true,
			},
		});

		// Execute
		const { result } = renderHook(() => useEventAttributes());

		// Verify
		expect(result.current.privProducts).toBe('CCPAoptout');
		expect(result.current.priv).toBe('geographically-relevant-advertising');
		expect(result.current.gpcSignalOptOut).toBe(true);
		expect(result.current.functionalTechnologies).toBe(false);
		expect(result.current.geographicallyRelevantAds).toBe(true);
	});
});
