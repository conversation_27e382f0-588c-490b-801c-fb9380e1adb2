'use client';

import { UserSubscriptionTiers } from '@repo/user/utils/consts';
import canUseDOM from '@repo/utils/canUseDOM';
import { getCookie, setCookie } from 'cookies-next/client';
import { parseArrayToString } from '../../utils/parseArrayToString';

export function areCookiesEnabled() {
	if (canUseDOM && navigator?.cookieEnabled) return true;
	return false;
}

export interface IabTags {
	v1?: string[];
	v2?: string[];
	v3?: string[];
}

export const mapIabTagsToIabContent = (iab: IabTags) => {
	const iabContent = [];

	if (iab?.v1) {
		iabContent.push(iab.v1);
	}
	if (iab?.v2) {
		iabContent.push(iab.v2);
	}
	if (iab?.v3) {
		iabContent.push(iab.v3);
	}

	return parseArrayToString(iabContent);
};

/**
 * Remove any query params and/or fragments from a page's path.
 */
export function trimQueryAndFragment(path: string) {
	let trimmedPath = path;
	const queryIndex = path?.indexOf?.('?');
	const fragmentIndex = path?.indexOf?.('#');

	// Because fragments follow query params, check for the existence of query params first.
	// If there are query params, trim everything from '?' and on. Otherwise, check for fragments.
	if (queryIndex > -1) {
		const eol = queryIndex;

		trimmedPath = trimmedPath.substring(0, eol);
	} else if (fragmentIndex > -1) {
		const eol = fragmentIndex;

		trimmedPath = trimmedPath.substring(0, eol);
	}

	return trimmedPath;
}

/**
 * Remove locId from the end of a path (assumes that
 * query and fragment are already removed)
 */
export function trimLocId(path: string) {
	// Remove locid from path. Since query and fragment are already
	// removed, this means removing anything from /l/ (if present)
	// to the next slash (if present) or the end of the string.
	return path?.replace?.(/\/l\/[^/]+/gi, '/l');
}

export function getAnonymizedPath(path: string) {
	let anonymizedPath = trimQueryAndFragment(path);

	anonymizedPath = trimLocId(anonymizedPath);

	if (
		anonymizedPath === '/index.html' ||
		anonymizedPath === '/' ||
		anonymizedPath === ''
	) {
		anonymizedPath = '/';
	} else {
		const pathCategories = anonymizedPath?.split('/') || [];
		const lastIndex = pathCategories.length - 1;
		const omnTemp = pathCategories[lastIndex] ?? '';
		const previousVal = lastIndex > 0 ? pathCategories[lastIndex - 1] : '';

		if (
			previousVal !== 'news' &&
			(omnTemp.match(/[A-Z]/) ||
				omnTemp.match(/[0-9]/) ||
				omnTemp.match(/[@#$%&!*:]/) ||
				omnTemp === '')
		) {
			pathCategories.pop();
		}
		anonymizedPath = pathCategories.join('/');
	}

	return anonymizedPath;
}

export const stripLastPartOfSlug = (url = '') => {
	const splitUrl = url.split('/');

	if (splitUrl?.[splitUrl.length - 1] === '') {
		splitUrl.pop();
	}

	return splitUrl.slice(0, splitUrl.length - 1).join('/');
};

export const getUrlQueryParams = (): { [key: string]: string } => {
	if (canUseDOM) {
		// + is being decoded as ' ', so encode before using
		const urlSearchParams = new URLSearchParams(
			window.location.search.replace('+', '%2B'),
		);
		const params = Object.fromEntries(urlSearchParams.entries());

		return params;
	}

	return {};
};

export const getLinkReferral = () => {
	const linkReferral = getUrlQueryParams()?.traffic_source || null;

	return linkReferral;
};

export const getPartner = () => {
	// The partner cookie is now set by middleware
	const partnerStr = getCookie('twc-partner') || null;
	return partnerStr;
};

export const getCampaign = () => {
	const pageQuery = getUrlQueryParams();
	let campaignStr = null;
	let storeCampaignCookie = true;

	// campaign logic
	const campaign = [];

	if (pageQuery.cm_ven) campaign.push(pageQuery.cm_ven);
	if (pageQuery.cm_date) campaign.push(pageQuery.cm_date);
	if (pageQuery.cm_cat) campaign.push(pageQuery.cm_cat);
	if (pageQuery.cm_pla) campaign.push(pageQuery.cm_pla);
	if (pageQuery.cm_ite) campaign.push(pageQuery.cm_ite);

	if (campaign.length > 0) {
		campaignStr = campaign.join(':');
	} else {
		campaignStr = getCookie('campaign-values') || null;
		storeCampaignCookie = false;
	}

	// If the campaignStr came from the CAMPAIGN_COOKIE, do not reset the cookie.
	if (storeCampaignCookie && campaignStr) {
		setCookie('campaign-values', campaignStr, {
			domain: '.weather.com',
			path: '/',
			maxAge: 1800, // 30 Minutes
		});
	}

	return campaignStr;
};

export const getTpcc = () => {
	const tpcc = getUrlQueryParams()?.tpcc || getCookie('tpcc') || null;

	if (tpcc) {
		setCookie('tpcc', tpcc, {
			path: '/',
			domain: '.weather.com',
			maxAge: 1800, // 30 Minutes
		});
	}

	return tpcc;
};

export const areObjectValuesDefined = (object: Record<string, unknown>) => {
	return Object.keys(object).every((key) => {
		return object[key] !== undefined;
	});
};

export function getSubscriptionTier(
	subscriptionTier: UserSubscriptionTiers | undefined,
): string {
	switch (subscriptionTier) {
		case UserSubscriptionTiers.premium:
			return 'premium';
		case UserSubscriptionTiers.standard:
			return 'standard';
		case UserSubscriptionTiers.premiumWithAd:
			return 'premiumWithAd';
		case UserSubscriptionTiers.adFree:
			return 'adFree';
		case UserSubscriptionTiers.none:
			return 'none';
		default:
			return 'none';
	}
}

export function jwtDecoder(token: string) {
	// If there is no subcription cookie and
	// therefore no token return an empty object
	if (!token) return {};
	const splitToken = token.split('.');
	const base64Url =
		(Array.isArray(splitToken) && splitToken.length >= 2 && splitToken[1]) ||
		'';
	const base64 = base64Url.replace('-', '+').replace('_', '/');

	try {
		const jwtObj = JSON.parse(window.atob(base64));

		return jwtObj || {};
	} catch {
		return {};
	}
}

export const WEEK_DAYS = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];
