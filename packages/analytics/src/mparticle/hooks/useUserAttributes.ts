'use client';

import { useCallback, useEffect, useMemo, useState } from 'react';
import mParticle from '@mparticle/web-sdk';
import { useIsInitialized } from './useIsInitialized';
import { useAtomValue } from 'jotai';
import {
	areObjectValuesDefined,
	getSubscriptionTier,
	jwtDecoder,
} from './utils';
import { userFirstNameAtom, userGenderAtom } from '@repo/user/atoms/profile';
import { getCookie } from 'cookies-next/client';
import { wxuUserAnonymousIdAtom } from '@repo/user/atoms/wxu/user';
import { userHasLoggedInBeforeAtom } from '@repo/user/atoms/wxu/user';
import canUseDOM from '@repo/utils/canUseDOM';
import { useUser } from '@repo/user/hooks/useUser';

interface UserAttributes {
	premium?: boolean;
	premiumProductTier?: string;
	premiumExpiration?: string;
	premiumStart?: string;
	premiumFirstTransactionDate?: string;
	premiumProductAutoRenew?: string;
	isRegisteredUser?: boolean;
	isLoggedIn?: boolean;
	devUser?: boolean;
	'Amplitude.device_id'?: string;
	experiment_ds?: string;
	variant_ds?: string;
	emailSubscriptions?: string;
	subscribedAlerts_ds?: string;
	lotame_id?: string;
	$Gender?: string;
	$FirstName?: string;
	saleOfDataAuthorization?: boolean;
	sensitiveDataAuthorization?: boolean;
}

/**
 * Hook for managing user identity in mParticle
 * @returns Functions to remove and set user attributes
 */
export const useUserAttributes = () => {
	const isInitialized = useIsInitialized();
	const {
		user: { isUserLoggedIn, isUserPremium, subscriptionTier },
	} = useUser();

	const [areUserAttributesReady, setAreUserAttributesReady] = useState(false);
	const [hasSetUserAttributes, setHasSetUserAttributes] = useState(false);

	const premium = isUserPremium ?? false;
	const premiumProductTier = getSubscriptionTier(subscriptionTier);
	const firstName = useAtomValue(userFirstNameAtom);
	const gender = useAtomValue(userGenderAtom);
	const anonId = useAtomValue(wxuUserAnonymousIdAtom) as string;
	const userHasLoggedInBefore = useAtomValue(
		userHasLoggedInBeforeAtom,
	) as boolean;

	const saleOfDataAuthorization = canUseDOM
		? window?.DprSdk?.getUserConsent('sale-of-data') !== false
		: undefined;
	const sensitiveDataAuthorization = canUseDOM
		? window?.DprSdk?.getUserConsent('sensitive-data') !== false
		: undefined;

	const subscriptionJWT = getCookie('subscription') ?? '';
	const {
		// premium = false,
		// product: premiumProductTier = null,
		productID: premiumProductId = null,
		expirationDateTime: premiumExpiration = null,
		transactionDateTime: premiumStart = null,
		firstTransactionDateTime: premiumFirstTransactionDate = null,
		autoRenew: premiumProductAutoRenew = null,
		// term: premiumProductTerm = null,
		price: premiumPrice = null,
		productName: premiumProductName = null,
	} = jwtDecoder(subscriptionJWT) || {};

	const userAttributes: UserAttributes = useMemo(
		() => ({
			// Dynamic Attributes
			premium,
			premiumProductTier,
			premiumExpiration,
			premiumStart,
			premiumFirstTransactionDate,
			premiumProductId,
			premiumProductAutoRenew,
			premiumPrice,
			premiumProductName,
			isRegisteredUser: isUserLoggedIn || userHasLoggedInBefore || false,
			isLoggedIn: isUserLoggedIn,
			// emailSubscriptions,
			'Amplitude.device_id': anonId,
			// experiment_ds: experimentDS,
			// variant_ds: variantDS,
			// emailSubscriptions: parseArrayToString(emailSubscriptions),
			// subscribedAlerts_ds: parseArrayToString(subscribedAlerts),
			// lotame_id: lotameID,
			$Gender: gender,
			$FirstName: firstName,
			saleOfDataAuthorization,
			sensitiveDataAuthorization,
			// Static User attributes
			devUser: process.env.NEXT_PUBLIC_MPARTICLE_ENV !== 'production',
		}),
		[
			premium,
			premiumProductTier,
			premiumExpiration,
			premiumStart,
			premiumFirstTransactionDate,
			premiumProductId,
			premiumProductAutoRenew,
			premiumPrice,
			premiumProductName,
			anonId,
			isUserLoggedIn,
			userHasLoggedInBefore,
			firstName,
			gender,
			saleOfDataAuthorization,
			sensitiveDataAuthorization,
		],
	);

	useEffect(() => {
		if (
			areObjectValuesDefined(userAttributes as Record<string, unknown>) &&
			!areUserAttributesReady
		) {
			setAreUserAttributesReady(true);
		}
	}, [userAttributes, areUserAttributesReady]);

	const setUserAttributes = useCallback(() => {
		// Removes all undefined properties
		for (const k in userAttributes) {
			if (userAttributes[k as keyof UserAttributes] === undefined)
				delete userAttributes[k as keyof UserAttributes];
		}

		if (isInitialized && areUserAttributesReady && !hasSetUserAttributes) {
			const currentUser = mParticle?.Identity?.getCurrentUser?.();

			if (!currentUser) {
				console.warn('MParticle: No current user');
				return;
			}

			currentUser.setUserAttributes(userAttributes as Record<string, unknown>);

			setHasSetUserAttributes(true);
			console.debug('MParticle: User attributes set - ', userAttributes);
		}
	}, [
		isInitialized,
		userAttributes,
		areUserAttributesReady,
		hasSetUserAttributes,
	]);

	const setUserAttribute = useCallback(
		(key: string, value: string | number | boolean | null) => {
			if (!isInitialized) {
				console.warn('MParticle is not initialized');
				return;
			}

			const currentUser = mParticle?.Identity?.getCurrentUser?.();
			if (!currentUser) {
				console.warn('MParticle: No current user');
				return;
			}

			currentUser.setUserAttribute(key, value as string);
			console.debug(`MParticle: User attribute set - ${key}`);
		},
		[isInitialized],
	);

	const removeUserAttribute = useCallback(
		(key: string) => {
			if (!isInitialized) {
				console.warn('MParticle is not initialized');
				return;
			}

			const currentUser = mParticle?.Identity?.getCurrentUser?.();
			if (!currentUser) {
				console.warn('MParticle: No current user');
				return;
			}

			currentUser.removeUserAttribute(key);
			console.debug(`MParticle: User attribute removed - ${key}`);
		},
		[isInitialized],
	);

	return {
		setUserAttribute,
		setUserAttributes,
		removeUserAttribute,
	};
};
