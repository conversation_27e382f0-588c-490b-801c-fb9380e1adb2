'use client';

import { describe, expect, it, vi, beforeEach } from 'vitest';
import {
	areCookiesEnabled,
	trimQueryAndFragment,
	trimLocId,
	getAnonymizedPath,
	stripLastPartOfSlug,
	areObjectValuesDefined,
	getSubscriptionTier,
	getPartner,
} from './utils';

// Mock dependencies
vi.mock('@repo/utils/canUseDOM', () => ({
	default: true,
}));

vi.mock('cookies-next/client', () => ({
	getCookie: vi.fn(),
}));

import { getCookie } from 'cookies-next/client';

// Mock navigator
Object.defineProperty(global, 'navigator', {
	value: {
		cookieEnabled: true,
	},
	writable: true,
});

describe('Utility Functions', () => {
	describe('areCookiesEnabled', () => {
		it('should return true when cookies are enabled', () => {
			// Setup
			vi.stubGlobal('navigator', { cookieEnabled: true });

			// Execute & Verify
			expect(areCookiesEnabled()).toBe(true);
		});

		it('should return false when cookies are disabled', () => {
			// Setup
			vi.stubGlobal('navigator', { cookieEnabled: false });

			// Execute & Verify
			expect(areCookiesEnabled()).toBe(false);
		});

		it('should return false when DOM is not available', () => {
			// Setup
			vi.doMock('@repo/utils/canUseDOM', () => ({
				default: false,
			}));

			// Execute & Verify
			expect(areCookiesEnabled()).toBe(false);
		});
	});

	describe('trimQueryAndFragment', () => {
		it('should remove query parameters from a URL', () => {
			// Execute & Verify
			expect(trimQueryAndFragment('/path?param=value')).toBe('/path');
		});

		it('should remove fragments from a URL', () => {
			// Execute & Verify
			expect(trimQueryAndFragment('/path#section')).toBe('/path');
		});

		it('should remove both query parameters and fragments from a URL', () => {
			// Execute & Verify
			expect(trimQueryAndFragment('/path?param=value#section')).toBe('/path');
		});

		it('should return the original path if no query or fragment exists', () => {
			// Execute & Verify
			expect(trimQueryAndFragment('/path')).toBe('/path');
		});
	});

	describe('trimLocId', () => {
		it('should remove locId from a path', () => {
			// Execute & Verify
			expect(trimLocId('/weather/l/USNY0996:1:US')).toBe('/weather/l');
		});

		it('should return the original path if no locId exists', () => {
			// Execute & Verify
			expect(trimLocId('/weather')).toBe('/weather');
		});
	});

	describe('getAnonymizedPath', () => {
		it('should anonymize a path with locId', () => {
			// Execute & Verify
			expect(getAnonymizedPath('/weather/l/USNY0996:1:US')).toBe('/weather/l');
		});

		it('should handle index.html paths', () => {
			// Execute & Verify
			expect(getAnonymizedPath('/index.html')).toBe('/');
		});

		it('should handle root paths', () => {
			// Execute & Verify
			expect(getAnonymizedPath('/')).toBe('/');
		});

		it('should handle empty paths', () => {
			// Execute & Verify
			expect(getAnonymizedPath('')).toBe('/');
		});
	});

	describe('stripLastPartOfSlug', () => {
		it('should remove the last part of a URL slug', () => {
			// Execute & Verify
			expect(stripLastPartOfSlug('/news/weather/article-123')).toBe(
				'/news/weather',
			);
		});

		it('should handle URLs with trailing slash', () => {
			// Execute & Verify
			expect(stripLastPartOfSlug('/news/weather/')).toBe('/news');
		});

		it('should return an empty string for undefined input', () => {
			// Execute & Verify
			expect(stripLastPartOfSlug(undefined as unknown as string)).toBe('');
		});
	});

	describe('areObjectValuesDefined', () => {
		it('should return true when all object values are defined', () => {
			// Setup
			const obj = { key1: 'value1', key2: 0, key3: false };

			// Execute & Verify
			expect(areObjectValuesDefined(obj)).toBe(true);
		});

		it('should return false when any object value is undefined', () => {
			// Setup
			const obj = { key1: 'value1', key2: undefined, key3: false };

			// Execute & Verify
			expect(areObjectValuesDefined(obj)).toBe(false);
		});
	});

	describe('getSubscriptionTier', () => {
		it('should return the correct subscription tier name', () => {
			// Execute & Verify
			expect(getSubscriptionTier(0)).toBe('standard');
			expect(getSubscriptionTier(1)).toBe('premium');
			expect(getSubscriptionTier(2)).toBe('adFree');
			expect(getSubscriptionTier(3)).toBe('premiumWithAd');
			expect(getSubscriptionTier(-1)).toBe('none');
		});

		it('should return "none" for undefined or invalid tiers', () => {
			// Execute & Verify
			expect(getSubscriptionTier(undefined)).toBe('none');
			expect(getSubscriptionTier(99 as any)).toBe('none');
		});
	});

	describe('getPartner', () => {
		beforeEach(() => {
			// Reset mocks before each test
			vi.clearAllMocks();
		});

		it('should return cookie value when cookie exists', () => {
			// Setup
			vi.mocked(getCookie).mockReturnValue('cookie-partner');

			// Execute & Verify
			expect(getPartner()).toBe('cookie-partner');
			expect(getCookie).toHaveBeenCalledWith('twc-partner');
		});

		it('should return null when cookie does not exist', () => {
			// Setup
			vi.mocked(getCookie).mockReturnValue(undefined);

			// Execute & Verify
			expect(getPartner()).toBe(null);
			expect(getCookie).toHaveBeenCalledWith('twc-partner');
		});

		it('should return null when cookie returns empty string', () => {
			// Setup
			vi.mocked(getCookie).mockReturnValue('');

			// Execute & Verify
			expect(getPartner()).toBe(null);
			expect(getCookie).toHaveBeenCalledWith('twc-partner');
		});
	});
});
