'use client';

import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import { useCMSAttributes } from './useCMSAttributes';
import { useAtomValue } from 'jotai';
import type { CMSAttributes } from '../../atoms/metricsData';

// Mock dependencies
vi.mock('jotai', () => ({
	useAtomValue: vi.fn(),
	atom: vi.fn((initialValue) => ({ init: initialValue })),
}));

describe('useCMSAttributes', () => {
	// Mock CMS attributes data
	const mockCMSAttributes: CMSAttributes = {
		author: "['<PERSON>']",
		createdDate: '2023-05-15',
		contentId: 'article-123',
		collection: 'weather-news',
		teaserTitle: 'Test Teaser',
		publishDate: '2023-05-16',
		entitlements: "['platform-all-free','platform-all-premium']",
		title: 'Test Article Title',
		seoTitle: 'Test SEO Title',
	};

	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should return the atom value directly', () => {
		// Setup
		vi.mocked(useAtomValue).mockReturnValue(mockCMSAttributes);

		// Execute
		const { result } = renderHook(() => useCMSAttributes());

		// Verify
		expect(result.current).toBe(mockCMSAttributes);
		expect(useAtomValue).toHaveBeenCalledTimes(1);
	});

	it('should handle undefined atom value', () => {
		// Setup
		vi.mocked(useAtomValue).mockReturnValue(undefined);

		// Execute
		const { result } = renderHook(() => useCMSAttributes());

		// Verify
		expect(result.current).toBeUndefined();
		expect(useAtomValue).toHaveBeenCalledTimes(1);
	});

	it('should handle null atom value', () => {
		// Setup
		vi.mocked(useAtomValue).mockReturnValue(null);

		// Execute
		const { result } = renderHook(() => useCMSAttributes());

		// Verify
		expect(result.current).toBeNull();
		expect(useAtomValue).toHaveBeenCalledTimes(1);
	});

	it('should return different values when atom changes', () => {
		// Setup - first render
		vi.mocked(useAtomValue).mockReturnValue(mockCMSAttributes);
		const { result, rerender } = renderHook(() => useCMSAttributes());
		const firstResult = result.current;

		// Setup - second render with different data
		const updatedAttributes: CMSAttributes = {
			...mockCMSAttributes,
			contentId: 'updated-article-456',
			title: 'Updated Title',
		};
		vi.mocked(useAtomValue).mockReturnValue(updatedAttributes);

		// Execute
		rerender();

		// Verify
		expect(result.current).toBe(updatedAttributes);
		expect(result.current).not.toBe(firstResult);
		expect((result.current as CMSAttributes).contentId).toBe(
			'updated-article-456',
		);
		expect((result.current as CMSAttributes).title).toBe('Updated Title');
	});
});
