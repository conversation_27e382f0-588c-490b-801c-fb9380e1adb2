'use client';

import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { usePageView } from './usePageView';
import mParticle from '@mparticle/web-sdk';
import { useIsInitialized } from './useIsInitialized';
import { useEventAttributes, EventAttributes } from './useEventAttributes';
import { incrementMetricsSessionCount } from '../../session/metricsSession';
import type { LocationData } from '@repo/location/types';

// Mock dependencies
vi.mock('@mparticle/web-sdk', () => ({
	default: {
		logEvent: vi.fn(),
		EventType: {
			Other: 'other',
		},
	},
}));

vi.mock('./useIsInitialized', () => ({
	useIsInitialized: vi.fn(),
}));

vi.mock('./useEventAttributes', () => ({
	useEventAttributes: vi.fn(),
}));

vi.mock('../../session/metricsSession', () => ({
	incrementMetricsSessionCount: vi.fn(),
}));

describe('usePageView', () => {
	const mockEventAttributes: EventAttributes = {
		pageId: 'test-page',
		url: '/test',
		partner: '',
		cookiesEnabled: true,
		campaign: null,
		dayOfWeek: 'MON',
		deviceClass: 'desktop',
		functionalTechnologies: false,
		geographicallyRelevantAds: false,
		gpcSignalOptOut: false,
		hourOfDay: 12,
		linkReferral: null,
		locale: 'en-US',
		physicalCountry: 'US',
		physicalState: 'NY',
		priv: '',
		privProducts: '',
		referrer: null,
		regime: '',
		schemaVersion: 'nextgen',
		pageCount: 1,
		sessionId: 'test-session',
		sessionStartTime: Date.now(),
		tpcc: null,
		wlocCity: 'New York, NY',
		wlocCountry: 'New York',
		wlocState: 'NY',
	};

	beforeEach(() => {
		vi.resetAllMocks();
		vi.mocked(useIsInitialized).mockReturnValue(true);
		vi.mocked(useEventAttributes).mockReturnValue(mockEventAttributes);
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should track page view when initialized and attributes are ready', async () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(true);

		// Execute
		const { result } = renderHook(() => usePageView());

		await act(async () => {
			await result.current();
		});

		// Verify
		expect(mParticle.logEvent).toHaveBeenCalledWith(
			'page-viewed',
			'other',
			mockEventAttributes,
		);
		expect(incrementMetricsSessionCount).toHaveBeenCalled();
	});

	it('should not track page view when not initialized', async () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(false);

		// Execute
		const { result } = renderHook(() => usePageView());

		await act(async () => {
			await result.current();
		});

		// Verify
		expect(mParticle.logEvent).not.toHaveBeenCalled();
		expect(incrementMetricsSessionCount).not.toHaveBeenCalled();
	});

	it('should not track page view when attributes are not ready', async () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(true);
		vi.mocked(useEventAttributes).mockReturnValue({
			...mockEventAttributes,
			pageId: undefined as unknown as string,
		});

		// Execute
		const { result } = renderHook(() => usePageView());

		await act(async () => {
			await result.current();
		});

		// Verify
		expect(mParticle.logEvent).not.toHaveBeenCalled();
		expect(incrementMetricsSessionCount).not.toHaveBeenCalled();
	});

	it('should only track page view once even when called multiple times', async () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(true);

		// Execute
		const { result } = renderHook(() => usePageView());

		await act(async () => {
			await result.current();
			await result.current(); // Call a second time
		});

		// Verify
		expect(mParticle.logEvent).toHaveBeenCalledTimes(1);
		expect(incrementMetricsSessionCount).toHaveBeenCalledTimes(1);
	});
});
