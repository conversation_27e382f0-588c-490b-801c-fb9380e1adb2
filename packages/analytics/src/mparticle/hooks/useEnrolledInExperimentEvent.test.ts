'use client';

import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import {
	useEnrolledInExperimentEvent,
	type Exposure,
} from './useEnrolledInExperimentEvent';
import mParticle from '@mparticle/web-sdk';
import { useEventAttributes, EventAttributes } from './useEventAttributes';
import { useIsInitialized } from './useIsInitialized';
import { useLocationSource } from '@repo/location/hooks/useLocationSource';

// Mock dependencies
vi.mock('@mparticle/web-sdk', () => ({
	default: {
		logEvent: vi.fn(),
		EventType: {
			Other: 'other',
		},
	},
}));

vi.mock('./useEventAttributes', () => ({
	useEventAttributes: vi.fn(),
}));

vi.mock('./useIsInitialized', () => ({
	useIsInitialized: vi.fn(),
}));

vi.mock('@repo/location/hooks/useLocationSource', () => ({
	useLocationSource: vi.fn(),
}));

describe('useEnrolledInExperimentEvent', () => {
	// Mock data
	const mockLocationSource = {
		effectiveLocation: {
			displayName: 'New York, NY',
			adminDistrict: 'New York',
			adminDistrictCode: 'NY',
			geocode: '40.7128,-74.006',
			placeId: 'ny-123',
			city: 'New York',
			countryCode: 'US',
		},
		isLocationLoading: false,
		locationError: null,
	};

	const mockEventAttributes: EventAttributes = {
		pageId: 'test-page',
		url: '/test',
		cookiesEnabled: true,
		campaign: null,
		dayOfWeek: 'MON',
		deviceClass: 'desktop',
		functionalTechnologies: false,
		geographicallyRelevantAds: false,
		gpcSignalOptOut: false,
		hourOfDay: 12,
		linkReferral: null,
		locale: 'en-US',
		partner: 'test-partner',
		physicalCountry: 'US',
		physicalState: 'NY',
		priv: '',
		privProducts: '',
		referrer: null,
		regime: '',
		schemaVersion: 'nextgen',
		pageCount: 1,
		sessionId: 'test-session',
		sessionStartTime: Date.now(),
		tpcc: null,
		wlocCity: 'New York, NY',
		wlocCountry: 'New York',
		wlocState: 'NY',
	};

	const mockExposure: Exposure = {
		flag_key: 'test-flag',
		variant: 'test-variant',
		experiment_key: 'test-experiment',
	};

	beforeEach(() => {
		vi.resetAllMocks();

		// Mock hooks
		vi.mocked(useLocationSource).mockReturnValue(mockLocationSource);
		vi.mocked(useEventAttributes).mockReturnValue(mockEventAttributes);
		vi.mocked(useIsInitialized).mockReturnValue(true);

		// Mock Date.now
		vi.spyOn(Date, 'now').mockImplementation(() => 1620000000000);

		// Mock console
		vi.spyOn(console, 'debug').mockImplementation(() => {});
		vi.spyOn(console, 'error').mockImplementation(() => {});
		vi.spyOn(console, 'warn').mockImplementation(() => {});
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should track experiment enrollment event when mParticle is initialized', () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(true);

		// Execute
		const { result } = renderHook(() => useEnrolledInExperimentEvent());

		act(() => {
			result.current(mockExposure);
		});

		// Verify
		expect(mParticle.logEvent).toHaveBeenCalled();
		const callArgs = vi.mocked(mParticle.logEvent).mock.calls[0];

		// Make sure we have call arguments
		expect(callArgs).toBeDefined();

		// Check mParticle.logEvent args
		expect(callArgs?.[0]).toBe('enrolled-in-experiment');
		expect(callArgs?.[1]).toBe(mParticle.EventType.Other);

		// Check specific properties we care about
		const attrs = callArgs?.[2] as Record<string, any>;
		expect(attrs.newVariant).toBe('test-variant');
		expect(attrs.newExperiment).toBe('test-experiment');
		expect(attrs.newExperimentReceivedAt).toBe(1620000000000);
		expect(console.debug).toHaveBeenCalledWith(
			`MParticle: Event tracked - enrolled-in-experiment for test-flag:test-variant`,
		);
	});

	it('should not track experiment enrollment event when mParticle is not initialized', () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(false);

		// Execute
		const { result } = renderHook(() => useEnrolledInExperimentEvent());

		act(() => {
			result.current(mockExposure);
		});

		// Verify
		expect(mParticle.logEvent).not.toHaveBeenCalled();
		expect(console.warn).toHaveBeenCalledWith('MParticle is not initialized');
	});

	it('should not track experiment enrollment event when exposure is not provided', () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(true);

		// Execute
		const { result } = renderHook(() => useEnrolledInExperimentEvent());

		act(() => {
			// @ts-expect-error Testing invalid input
			result.current();
		});

		// Verify
		expect(mParticle.logEvent).not.toHaveBeenCalled();
		expect(console.warn).toHaveBeenCalledWith(
			'No exposure data provided to trackEnrolledInExperiment',
		);
	});

	it('should handle missing values in exposure object', () => {
		// Setup
		const incompleteExposure = {
			flag_key: 'test-flag',
			// Missing variant and experiment_key
		} as unknown as Exposure;

		// Execute
		const { result } = renderHook(() => useEnrolledInExperimentEvent());

		act(() => {
			result.current(incompleteExposure);
		});

		// Verify
		expect(mParticle.logEvent).toHaveBeenCalled();
		const callArgs = vi.mocked(mParticle.logEvent).mock.calls[0];
		const attrs = callArgs?.[2] as Record<string, any>;

		// Should use empty string for missing values
		expect(attrs.newVariant).toBe('');
		expect(attrs.newExperiment).toBe('');
	});

	it('should handle error when tracking fails', () => {
		// Setup
		vi.mocked(mParticle.logEvent).mockImplementation(() => {
			throw new Error('Test error');
		});

		// Execute
		const { result } = renderHook(() => useEnrolledInExperimentEvent());

		act(() => {
			result.current(mockExposure);
		});

		// Verify
		expect(console.error).toHaveBeenCalledWith(
			'MParticle: Failed to track event',
			expect.any(Error),
		);
	});

	it('should use the correct dependencies in useCallback', () => {
		// Setup
		const initialAttributes = { ...mockEventAttributes };
		const updatedAttributes = { ...mockEventAttributes, pageId: 'new-page-id' };

		// First render with initial values
		vi.mocked(useEventAttributes).mockReturnValue(initialAttributes);
		const { result, rerender } = renderHook(() =>
			useEnrolledInExperimentEvent(),
		);

		act(() => {
			result.current(mockExposure);
		});

		// Clear mocks to check next call
		vi.clearAllMocks();

		// Update a dependency
		vi.mocked(useEventAttributes).mockReturnValue(updatedAttributes);

		// Rerender the hook
		rerender();

		// Call the function again
		act(() => {
			result.current(mockExposure);
		});

		// Verify the updated value was used
		expect(mParticle.logEvent).toHaveBeenCalled();
		const callArgs = vi.mocked(mParticle.logEvent).mock.calls[0];
		const attrs = callArgs?.[2] as Record<string, any>;
		expect(attrs.pageId).toBe('new-page-id');
	});

	it('should accept custom flags parameter', () => {
		// Setup
		const customFlags = {
			'test-flag': 'test-value',
		};

		// Execute
		const { result } = renderHook(() => useEnrolledInExperimentEvent());

		act(() => {
			result.current(mockExposure, customFlags);
		});

		// Verify
		expect(mParticle.logEvent).toHaveBeenCalled();
		const callArgs = vi.mocked(mParticle.logEvent).mock.calls[0];

		// Check that custom flags were passed
		expect(callArgs?.[3]).toEqual(customFlags);
	});
});
