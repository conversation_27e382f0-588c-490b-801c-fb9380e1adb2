'use client';

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import mParticle, { SDKEventAttrs } from '@mparticle/web-sdk';
import { areObjectValuesDefined } from './utils';
import { useIsInitialized } from './useIsInitialized';
import { incrementMetricsSessionCount } from '../../session/metricsSession';
import { EventAttributes, useEventAttributes } from './useEventAttributes';

// export interface PageViewOptions {
// }

interface PageViewAttributes extends EventAttributes {
	siteMode?: string;
	sourcePage?: string;
}

/**
 * Hook for tracking page views in mParticle
 * @returns A function to track page views
 */
export const usePageView = () => {
	const isInitialized = useIsInitialized();

	const generalAttributes = useEventAttributes();
	const [arePageViewAttributesReady, setArePageViewAttributesReady] =
		useState(false);
	const hasSentEvent = useRef(false);

	const eventInfoAttributes: PageViewAttributes = useMemo(
		() => ({
			...generalAttributes,
		}),
		[generalAttributes],
	);

	useEffect(() => {
		if (
			areObjectValuesDefined(eventInfoAttributes) &&
			!arePageViewAttributesReady
		) {
			setArePageViewAttributesReady(true);
		}
	}, [eventInfoAttributes, arePageViewAttributesReady]);

	const trackPageView = useCallback(async () => {
		if (isInitialized && arePageViewAttributesReady && !hasSentEvent.current) {
			try {
				// Note: .logEvent('page-viewed') instead of .logPageView() to maintain
				// consistency with wxu-web and to have more control over the event attributes
				mParticle?.logEvent?.(
					'page-viewed',
					mParticle.EventType.Other,
					eventInfoAttributes as SDKEventAttrs,
				);

				hasSentEvent.current = true;

				// Taken from legacy repo:
				// If metricsEvent type === 'page-viewed',
				// increment pageCount in session.
				// https://weather.atlassian.net/browse/WEB-22597
				incrementMetricsSessionCount();

				console.debug(
					`MParticle: Event "page-viewed" tracked - ${eventInfoAttributes.pageId}`,
				);
			} catch (error) {
				console.error('MParticle: Event "page-viewed" failed', error);
			}
		}
	}, [isInitialized, eventInfoAttributes, arePageViewAttributesReady]);

	return trackPageView;
};
