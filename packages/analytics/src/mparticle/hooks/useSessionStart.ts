'use client';

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import mParticle, { SDKEventAttrs } from '@mparticle/web-sdk';
import { areObjectValuesDefined } from './utils';
import { useIsInitialized } from './useIsInitialized';
import {
	deleteIsNewMestricsSession,
	isNewMetricsSession,
} from '../../session/metricsSession';
import { EventAttributes, useEventAttributes } from './useEventAttributes';

// export interface SessionStartOptions {
// }

interface SessionStartAttributes extends EventAttributes {
	// Add extra session start attrs if needed
	[key: string]: unknown;
}

/**
 * Hook for tracking page views in mParticle
 * @returns A function to track page views
 */
export const useSessionStart = () => {
	const isInitialized = useIsInitialized();
	const generalAttributes = useEventAttributes();
	const [areSessionStartAttributesReady, setAreSessionStartAttributesReady] =
		useState(false);
	const hasSentEvent = useRef(false);

	const eventInfoAttributes: SessionStartAttributes = useMemo(
		() => ({
			...generalAttributes,
		}),
		[generalAttributes],
	);

	useEffect(() => {
		if (
			areObjectValuesDefined(eventInfoAttributes as Record<string, unknown>) &&
			!areSessionStartAttributesReady
		) {
			setAreSessionStartAttributesReady(true);
		}
	}, [eventInfoAttributes, areSessionStartAttributesReady]);

	const trackSessionStart = useCallback(async () => {
		if (
			isNewMetricsSession() &&
			isInitialized &&
			areSessionStartAttributesReady &&
			!hasSentEvent.current
		) {
			try {
				// Note: .logEvent('session-start') instead of .logSessionStart() to maintain
				// consistency with wxu-web and to have more control over the event attributes
				mParticle?.logEvent?.(
					'session-start',
					mParticle.EventType.Other,
					eventInfoAttributes as SDKEventAttrs,
				);

				hasSentEvent.current = true;
				deleteIsNewMestricsSession();

				console.debug(
					`MParticle: Event "session-start" tracked - sessionId: ${eventInfoAttributes.sessionId}`,
				);
			} catch (error) {
				console.error('MParticle: Event "session-start" failed', error);
			}
		}
	}, [isInitialized, eventInfoAttributes, areSessionStartAttributesReady]);

	return trackSessionStart;
};
