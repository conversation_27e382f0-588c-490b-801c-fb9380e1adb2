'use client';

import { useSyncExternalStore } from 'react';
import mParticle from '@mparticle/web-sdk';

const subscribe = (onStoreChange: () => void) => {
	mParticle.ready(onStoreChange);

	// mParticle doesn't have an unsubscribe method.
	return () => {};
};

export const useIsInitialized = () => {
	// Syncs mParticle external isInitialized value to this custom hook.
	const isInitialized = useSyncExternalStore(
		subscribe,
		() => {
			if (mParticle.isInitialized()) return true;

			return false;
		},
		() => false,
	);

	return isInitialized;
};
