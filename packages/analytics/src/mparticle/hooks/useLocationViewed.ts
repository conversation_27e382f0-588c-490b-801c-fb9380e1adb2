'use client';

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import mParticle, { SDKEventAttrs } from '@mparticle/web-sdk';
import { areObjectValuesDefined } from './utils';
import { useIsInitialized } from './useIsInitialized';
import { EventAttributes, useEventAttributes } from './useEventAttributes';
import { isNewMetricsSession } from '../../session/metricsSession';
import canUseDOM from '@repo/utils/canUseDOM';
import { useLocationSource } from '@repo/location/hooks/useLocationSource';

export interface LocationViewedOptions {
	attributes?: LocationViewedAttributes;
}

interface LocationViewedAttributes extends EventAttributes {
	latitude?: number;
	longitude?: number;
}

/**
 * Hook for tracking location viewed events in mParticle
 * @returns A function to track location viewed events
 */
export const useLocationViewed = () => {
	const { effectiveLocation } = useLocationSource({});
	const isInitialized = useIsInitialized();
	const generalAttributes = useEventAttributes();
	const [
		areLocationViewedAttributesReady,
		setAreLocationViewedAttributesReady,
	] = useState(false);
	const hasSentEvent = useRef(false);

	// Track previous location for change detection
	const prevLocationRef = useRef<string | undefined>(undefined);

	// Check if location has changed set by Search's onClick
	// const [hasChangedLocation, setHasUserChangedLocation] = useAtom(
	// 	hasUserChangedLocationAtom,
	// );

	// Update previous location ref when effective location changes
	useEffect(() => {
		if (effectiveLocation?.geocode) {
			prevLocationRef.current = effectiveLocation.geocode;
		}
	}, [effectiveLocation?.geocode]);

	// Parse latitude and longitude from geocode if available
	const geocodeParts = effectiveLocation?.geocode?.split?.(',');
	const latitude = geocodeParts?.[0] ? parseFloat(geocodeParts[0]) : undefined;
	const longitude = geocodeParts?.[1] ? parseFloat(geocodeParts[1]) : undefined;

	const shouldSendLocationViewed = canUseDOM && isNewMetricsSession();
	// || hasChangedLocation; TODO; Phase 2 in case site switches to SPA

	const eventInfoAttributes: LocationViewedAttributes = useMemo(() => {
		return {
			...generalAttributes,
			latitude,
			longitude,
		};
	}, [generalAttributes, latitude, longitude]);

	useEffect(() => {
		if (
			areObjectValuesDefined(eventInfoAttributes) &&
			!areLocationViewedAttributesReady
		) {
			setAreLocationViewedAttributesReady(true);
		}
	}, [eventInfoAttributes, areLocationViewedAttributesReady]);

	const trackLocationViewed = useCallback(async () => {
		const latitude = eventInfoAttributes.latitude;
		const longitude = eventInfoAttributes.longitude;

		if (
			isInitialized &&
			areLocationViewedAttributesReady &&
			!hasSentEvent.current &&
			latitude &&
			longitude &&
			shouldSendLocationViewed
		) {
			try {
				mParticle?.logEvent?.(
					'location-viewed',
					mParticle.EventType.Other,
					eventInfoAttributes as SDKEventAttrs,
				);

				hasSentEvent.current = true;
				// setHasUserChangedLocation(RESET); TODO; Phase 2 in case site switches to SPA

				console.debug(
					`MParticle: Event "location-viewed" tracked - ${effectiveLocation?.displayName || 'Unknown location'}`,
				);
			} catch (error) {
				console.error('MParticle: Event "location-viewed" failed', error);
			}
		}
	}, [
		isInitialized,
		eventInfoAttributes,
		areLocationViewedAttributesReady,
		effectiveLocation,
		shouldSendLocationViewed,
	]);

	return trackLocationViewed;
};
