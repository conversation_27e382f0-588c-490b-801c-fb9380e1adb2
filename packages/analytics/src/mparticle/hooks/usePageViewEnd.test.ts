'use client';

import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import { usePageViewEnd } from './usePageViewEnd';
import mParticle from '@mparticle/web-sdk';
import { useEventAttributes, EventAttributes } from './useEventAttributes';
import type { LocationData } from '@repo/location/types';

// Mock dependencies
vi.mock('@mparticle/web-sdk', () => ({
	default: {
		logEvent: vi.fn(),
		EventType: {
			Other: 'other',
		},
		isInitialized: vi.fn().mockReturnValue(true),
	},
}));

// We don't need to mock useIsInitialized as it's not directly used in the implementation

vi.mock('./useEventAttributes', () => ({
	useEventAttributes: vi.fn(),
}));

vi.mock('@repo/utils/canUseDOM', () => ({
	default: true,
}));

describe('usePageViewEnd', () => {
	const mockEventAttributes: EventAttributes = {
		pageId: 'test-page',
		partner: '',
		url: '/test',
		cookiesEnabled: true,
		campaign: null,
		dayOfWeek: 'MON',
		deviceClass: 'desktop',
		functionalTechnologies: false,
		geographicallyRelevantAds: false,
		gpcSignalOptOut: false,
		hourOfDay: 12,
		linkReferral: null,
		locale: 'en-US',
		physicalCountry: 'US',
		physicalState: 'NY',
		priv: '',
		privProducts: '',
		referrer: null,
		regime: '',
		schemaVersion: 'nextgen',
		pageCount: 1,
		sessionId: 'test-session',
		sessionStartTime: Date.now(),
		tpcc: null,
		wlocCity: 'New York, NY',
		wlocCountry: 'New York',
		wlocState: 'NY',
	};

	// Spy on window event listeners
	let addEventListenerSpy: ReturnType<typeof vi.spyOn>;
	let removeEventListenerSpy: ReturnType<typeof vi.spyOn>;
	let documentAddEventListenerSpy: ReturnType<typeof vi.spyOn>;
	let documentRemoveEventListenerSpy: ReturnType<typeof vi.spyOn>;

	// Original window and document properties
	const originalScrollY = window.scrollY;
	const originalInnerHeight = window.innerHeight;
	const originalDocumentScrollHeight = document.documentElement.scrollHeight;

	beforeEach(() => {
		vi.resetAllMocks();
		vi.mocked(useEventAttributes).mockReturnValue(mockEventAttributes);
		vi.mocked(mParticle.isInitialized).mockReturnValue(true);

		// Mock window and document properties for scroll depth calculations
		Object.defineProperty(window, 'scrollY', { value: 100, writable: true });
		Object.defineProperty(window, 'innerHeight', {
			value: 800,
			writable: true,
		});
		Object.defineProperty(document.documentElement, 'scrollHeight', {
			value: 2000,
			writable: true,
		});

		// Mock window event listeners
		addEventListenerSpy = vi.spyOn(window, 'addEventListener');
		removeEventListenerSpy = vi.spyOn(window, 'removeEventListener');
		documentAddEventListenerSpy = vi.spyOn(document, 'addEventListener');
		documentRemoveEventListenerSpy = vi.spyOn(document, 'removeEventListener');

		// Mock console methods
		vi.spyOn(console, 'debug').mockImplementation(() => {});
		vi.spyOn(console, 'error').mockImplementation(() => {});
	});

	afterEach(() => {
		vi.clearAllMocks();

		// Restore original window and document properties
		Object.defineProperty(window, 'scrollY', { value: originalScrollY });
		Object.defineProperty(window, 'innerHeight', {
			value: originalInnerHeight,
		});
		Object.defineProperty(document.documentElement, 'scrollHeight', {
			value: originalDocumentScrollHeight,
		});
	});

	it('should add beforeunload and blur event listeners when mounted', () => {
		// Execute
		renderHook(() => usePageViewEnd());

		// Verify
		expect(addEventListenerSpy).toHaveBeenCalledWith(
			'beforeunload',
			expect.any(Function),
		);
		expect(addEventListenerSpy).toHaveBeenCalledWith(
			'blur',
			expect.any(Function),
		);
	});

	it('should remove beforeunload and blur event listeners when unmounted', () => {
		// Execute
		const { unmount } = renderHook(() => usePageViewEnd());
		unmount();

		// Verify
		expect(removeEventListenerSpy).toHaveBeenCalledWith(
			'beforeunload',
			expect.any(Function),
		);
		expect(removeEventListenerSpy).toHaveBeenCalledWith(
			'blur',
			expect.any(Function),
		);
	});

	it('should track page view end when beforeunload event is triggered and mParticle is initialized', () => {
		// Setup
		vi.mocked(mParticle.isInitialized).mockReturnValue(true);

		// Execute
		renderHook(() => usePageViewEnd());

		// Get the callback function passed to addEventListener for beforeunload
		const beforeUnloadCallbackIndex = addEventListenerSpy.mock.calls.findIndex(
			(call) => call[0] === 'beforeunload',
		);
		const beforeUnloadCallback = addEventListenerSpy.mock.calls?.[
			beforeUnloadCallbackIndex
		]?.[1] as () => void;

		// Simulate beforeunload event
		beforeUnloadCallback();

		// Verify
		expect(mParticle.logEvent).toHaveBeenCalledWith(
			'page-view-end',
			'other',
			expect.objectContaining({
				...mockEventAttributes,
				timeOnPage: expect.any(Number),
				scrollDepth: expect.any(Number),
			}),
		);
	});

	it('should track page view end when blur event is triggered and mParticle is initialized', () => {
		// Setup
		vi.mocked(mParticle.isInitialized).mockReturnValue(true);

		// Execute
		renderHook(() => usePageViewEnd());

		// Get the callback function passed to addEventListener for blur
		const blurCallbackIndex = addEventListenerSpy.mock.calls.findIndex(
			(call) => call[0] === 'blur',
		);
		const blurCallback = addEventListenerSpy.mock.calls?.[
			blurCallbackIndex
		]?.[1] as () => void;

		// Simulate blur event
		blurCallback();

		// Verify
		expect(mParticle.logEvent).toHaveBeenCalledWith(
			'page-view-end',
			'other',
			expect.objectContaining({
				...mockEventAttributes,
				timeOnPage: expect.any(Number),
				scrollDepth: expect.any(Number),
			}),
		);
	});

	it('should not track page view end when mParticle is not initialized', () => {
		// Setup
		vi.mocked(mParticle.isInitialized).mockReturnValue(false);

		// Execute
		renderHook(() => usePageViewEnd());

		// Get the callback function passed to addEventListener for beforeunload
		const beforeUnloadCallbackIndex = addEventListenerSpy.mock.calls.findIndex(
			(call) => call[0] === 'beforeunload',
		);
		const beforeUnloadCallback = addEventListenerSpy.mock.calls?.[
			beforeUnloadCallbackIndex
		]?.[1] as () => void;

		// Simulate beforeunload event
		beforeUnloadCallback();

		// Verify
		expect(mParticle.logEvent).not.toHaveBeenCalled();
	});

	it('should include timeOnPage and scrollDepth in event attributes', async () => {
		// Setup
		vi.mocked(mParticle.isInitialized).mockReturnValue(true);

		// Mock Date.prototype.getTime to return consistent values for testing
		const originalGetTime = Date.prototype.getTime;
		const mockStartTime = 1620000000000; // arbitrary start time
		const mockCurrentTime = mockStartTime + 5000; // 5 seconds later

		// Mock Date.prototype.getTime to first return the start time and then the current time
		Date.prototype.getTime = vi
			.fn()
			.mockReturnValueOnce(mockStartTime) // For starttime in the module
			.mockReturnValueOnce(mockCurrentTime); // For timeOnPage calculation in the hook

		// We need to re-import the module to reset the starttime constant
		vi.resetModules();

		// Re-import the hook to get a fresh instance with our mocked time
		const { usePageViewEnd: freshUsePageViewEnd } = await import(
			'./usePageViewEnd'
		);

		// Execute
		renderHook(() => freshUsePageViewEnd());

		// Get the callback function passed to addEventListener
		const beforeUnloadCallbackIndex = addEventListenerSpy.mock.calls.findIndex(
			(call) => call[0] === 'beforeunload',
		);
		const beforeUnloadCallback = addEventListenerSpy.mock.calls?.[
			beforeUnloadCallbackIndex
		]?.[1] as () => void;

		// Simulate beforeunload event
		beforeUnloadCallback();

		// Verify
		expect(mParticle.logEvent).toHaveBeenCalledWith(
			'page-view-end',
			'other',
			expect.objectContaining({
				...mockEventAttributes,
				timeOnPage: 5, // 5 seconds
				scrollDepth: expect.any(Number),
			}),
		);

		// Restore original Date.prototype.getTime
		Date.prototype.getTime = originalGetTime;
	});

	it('should handle errors when tracking page view end', () => {
		// Setup
		vi.mocked(mParticle.isInitialized).mockReturnValue(true);
		vi.mocked(mParticle.logEvent).mockImplementation(() => {
			throw new Error('Test error');
		});

		// Execute
		renderHook(() => usePageViewEnd());

		// Get the callback function passed to addEventListener
		const beforeUnloadCallbackIndex = addEventListenerSpy.mock.calls.findIndex(
			(call) => call[0] === 'beforeunload',
		);
		const beforeUnloadCallback = addEventListenerSpy.mock.calls?.[
			beforeUnloadCallbackIndex
		]?.[1] as () => void;

		// Simulate beforeunload event
		beforeUnloadCallback();

		// Verify
		expect(console.error).toHaveBeenCalledWith(
			'MParticle: Event "page-view-end" failed',
			expect.any(Error),
		);
	});
	describe('calcScrollDepth', () => {
		it('should return 100 when document height equals window height', async () => {
			// Setup
			Object.defineProperty(document.documentElement, 'scrollHeight', {
				value: 800,
			});
			Object.defineProperty(window, 'innerHeight', { value: 800 });

			// We need to re-import the module to reset the scrollHeight variable
			vi.resetModules();
			const { calcScrollDepth: freshCalcScrollDepth } = await import(
				'./usePageViewEnd'
			);

			// Execute & Verify
			expect(freshCalcScrollDepth()).toBe(100);
		});

		it('should calculate scroll depth as a percentage', async () => {
			// Setup
			Object.defineProperty(document.documentElement, 'scrollHeight', {
				value: 2000,
			});
			Object.defineProperty(window, 'innerHeight', { value: 625 });
			Object.defineProperty(window, 'scrollY', { value: 600 });

			// We need to re-import the module to reset the scrollHeight variable
			vi.resetModules();
			const { calcScrollDepth: freshCalcScrollDepth } = await import(
				'./usePageViewEnd'
			);

			// Execute & Verify
			// Calculation: (625 / (2000 - 625)) * 100 = 50%
			expect(freshCalcScrollDepth()).toBe(45);
		});

		it('should cap scroll depth at 100%', async () => {
			// Setup
			Object.defineProperty(document.documentElement, 'scrollHeight', {
				value: 2000,
			});
			Object.defineProperty(window, 'innerHeight', { value: 2000 });
			Object.defineProperty(window, 'scrollY', { value: 2000 });

			// We need to re-import the module to reset the scrollHeight variable
			vi.resetModules();
			const { calcScrollDepth: freshCalcScrollDepth } = await import(
				'./usePageViewEnd'
			);

			// Execute & Verify
			expect(freshCalcScrollDepth()).toBe(100);
		});

		it('should set scroll depth to 0 if calculated value is negative', async () => {
			// Setup
			Object.defineProperty(document.documentElement, 'scrollHeight', {
				value: 2000,
			});
			Object.defineProperty(window, 'innerHeight', { value: -800 });
			Object.defineProperty(window, 'scrollY', { value: -100 });

			// We need to re-import the module to reset the scrollHeight variable
			vi.resetModules();
			const { calcScrollDepth: freshCalcScrollDepth } = await import(
				'./usePageViewEnd'
			);

			// Execute & Verify
			expect(freshCalcScrollDepth()).toBe(0);
		});
	});

	describe('Scroll tracking functions', () => {
		// Since resetScrollheight is not exported, we can't test it directly
		// Instead, we can test the behavior of the module by simulating DOM events
		it('should update scroll tracking on scroll event with debounce', async () => {
			// Setup
			vi.useFakeTimers();

			// We need to re-import the module to reset the scrollHeight variable
			vi.resetModules();

			// Import the module
			await import('./usePageViewEnd');

			// Get the scroll event listener and call it
			const scrollEvent = new Event('scroll');
			window.dispatchEvent(scrollEvent);

			// The debouncedCalcScrollHeight function uses debounce, so we need to advance timers
			vi.advanceTimersByTime(250); // Advance past the debounce delay (250ms)

			// Now import calcScrollDepth to check the result
			const { calcScrollDepth } = await import('./usePageViewEnd');

			// Verify that scrollHeight was updated by checking the result of calcScrollDepth
			Object.defineProperty(window, 'scrollY', { value: 0 });
			Object.defineProperty(window, 'innerHeight', { value: 600 });
			Object.defineProperty(document.documentElement, 'scrollHeight', {
				value: 2000,
			});

			// The scrollHeight should have been set 800 by the scroll event
			// Calculation: (800 / (2000 - 600)) * 100 = 57.14% ≈ 57%
			expect(calcScrollDepth()).toBe(57);

			// Restore real timers
			vi.useRealTimers();
		});
	});
});
