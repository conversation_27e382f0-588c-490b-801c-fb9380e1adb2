'use client';

import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useUserIdentity } from './useUserIdentity';
import mParticle, { IdentityApiData, UserIdentities } from '@mparticle/web-sdk';
import { useIsInitialized } from './useIsInitialized';

// Mock dependencies
vi.mock('@mparticle/web-sdk', () => ({
	default: {
		Identity: {
			identify: vi.fn().mockResolvedValue(undefined),
			login: vi.fn().mockResolvedValue(undefined),
			logout: vi.fn().mockResolvedValue(undefined),
		},
	},
}));

vi.mock('./useIsInitialized', () => ({
	useIsInitialized: vi.fn(),
}));

describe('useUserIdentity', () => {
	// Mock data
	const mockUserIdentities: UserIdentities = {
		customerid: 'test-customer-id',
		email: '<EMAIL>',
		facebook: 'test-facebook',
		facebookcustomaudienceid: 'test-facebook-audience',
		google: 'test-google',
		microsoft: 'test-microsoft',
		other: 'test-other',
		other2: 'test-other2',
		other3: 'test-other3',
		other4: 'test-other4',
		twitter: 'test-twitter',
		yahoo: 'test-yahoo',
	};

	beforeEach(() => {
		vi.resetAllMocks();

		// Mock hooks
		vi.mocked(useIsInitialized).mockReturnValue(true);

		// Mock console
		vi.spyOn(console, 'warn').mockImplementation(() => {});
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	describe('identify', () => {
		it('should identify user when mParticle is initialized', async () => {
			// Setup
			vi.mocked(useIsInitialized).mockReturnValue(true);
			vi.mocked(mParticle.Identity.identify).mockResolvedValue(undefined);

			// Execute
			const { result } = renderHook(() => useUserIdentity());

			// Use act with await for async operations
			await act(async () => {
				await result.current.identify(mockUserIdentities);
			});

			// Verify
			expect(mParticle.Identity.identify).toHaveBeenCalled();
			const callArgs = vi.mocked(mParticle.Identity.identify).mock.calls[0];

			// Make sure we have call arguments
			expect(callArgs).toBeDefined();

			// Check identity request format
			const identityRequest = callArgs?.[0] as IdentityApiData;
			expect(identityRequest.userIdentities).toEqual({
				customerid: 'test-customer-id',
				email: '<EMAIL>',
				other: 'test-other',
			});
		});

		it('should return rejected promise when mParticle is not initialized', async () => {
			// Setup
			vi.mocked(useIsInitialized).mockReturnValue(false);

			// Execute
			const { result } = renderHook(() => useUserIdentity());

			// Verify
			await expect(result.current.identify(mockUserIdentities)).rejects.toThrow(
				'MParticle is not initialized',
			);
			expect(mParticle.Identity.identify).not.toHaveBeenCalled();
			expect(console.warn).toHaveBeenCalledWith('MParticle is not initialized');
		});

		it('should handle error when identify fails', async () => {
			// Setup
			vi.mocked(useIsInitialized).mockReturnValue(true);
			const mockError = new Error('Identify failed');
			vi.mocked(mParticle.Identity.identify).mockRejectedValue(mockError);

			// Execute
			const { result } = renderHook(() => useUserIdentity());

			// Verify
			await expect(result.current.identify(mockUserIdentities)).rejects.toThrow(
				'Identify failed',
			);
		});
	});

	describe('login', () => {
		it('should login user when mParticle is initialized', async () => {
			// Setup
			vi.mocked(useIsInitialized).mockReturnValue(true);
			vi.mocked(mParticle.Identity.login).mockResolvedValue(undefined);

			// Execute
			const { result } = renderHook(() => useUserIdentity());

			// Use act with await for async operations
			await act(async () => {
				await result.current.login(mockUserIdentities);
			});

			// Verify
			expect(mParticle.Identity.login).toHaveBeenCalled();
			const callArgs = vi.mocked(mParticle.Identity.login).mock.calls[0];

			// Make sure we have call arguments
			expect(callArgs).toBeDefined();

			// Check identity request format
			const identityRequest = callArgs?.[0] as IdentityApiData;
			expect(identityRequest.userIdentities).toEqual(mockUserIdentities);
		});

		it('should return rejected promise when mParticle is not initialized', async () => {
			// Setup
			vi.mocked(useIsInitialized).mockReturnValue(false);

			// Execute
			const { result } = renderHook(() => useUserIdentity());

			// Verify
			await expect(result.current.login(mockUserIdentities)).rejects.toThrow(
				'MParticle is not initialized',
			);
			expect(mParticle.Identity.login).not.toHaveBeenCalled();
			expect(console.warn).toHaveBeenCalledWith('MParticle is not initialized');
		});

		it('should handle error when login fails', async () => {
			// Setup
			vi.mocked(useIsInitialized).mockReturnValue(true);
			const mockError = new Error('Login failed');
			vi.mocked(mParticle.Identity.login).mockRejectedValue(mockError);

			// Execute
			const { result } = renderHook(() => useUserIdentity());

			// Verify
			await expect(result.current.login(mockUserIdentities)).rejects.toThrow(
				'Login failed',
			);
		});
	});

	describe('logout', () => {
		it('should logout user when mParticle is initialized', async () => {
			// Setup
			vi.mocked(useIsInitialized).mockReturnValue(true);
			vi.mocked(mParticle.Identity.logout).mockResolvedValue(undefined);

			// Execute
			const { result } = renderHook(() => useUserIdentity());

			// Use act with await for async operations
			await act(async () => {
				await result.current.logout();
			});

			// Verify
			expect(mParticle.Identity.logout).toHaveBeenCalled();
			const callArgs = vi.mocked(mParticle.Identity.logout).mock.calls[0];

			// Make sure we have call arguments
			expect(callArgs).toBeDefined();

			// Check identity request format
			const identityRequest = callArgs?.[0] as IdentityApiData;
			expect(identityRequest.userIdentities).toEqual({});
		});

		it('should return rejected promise when mParticle is not initialized', async () => {
			// Setup
			vi.mocked(useIsInitialized).mockReturnValue(false);

			// Execute
			const { result } = renderHook(() => useUserIdentity());

			// Verify
			await expect(result.current.logout()).rejects.toThrow(
				'MParticle is not initialized',
			);
			expect(mParticle.Identity.logout).not.toHaveBeenCalled();
			expect(console.warn).toHaveBeenCalledWith('MParticle is not initialized');
		});

		it('should handle error when logout fails', async () => {
			// Setup
			vi.mocked(useIsInitialized).mockReturnValue(true);
			const mockError = new Error('Logout failed');
			vi.mocked(mParticle.Identity.logout).mockRejectedValue(mockError);

			// Execute
			const { result } = renderHook(() => useUserIdentity());

			// Verify
			await expect(result.current.logout()).rejects.toThrow('Logout failed');
		});
	});

	it('should use the correct dependencies in useCallback', async () => {
		// Setup - First render with mParticle initialized
		vi.mocked(useIsInitialized).mockReturnValue(true);
		const { result, rerender } = renderHook(() => useUserIdentity());

		// First call should succeed
		await act(async () => {
			await result.current.identify(mockUserIdentities);
		});
		expect(mParticle.Identity.identify).toHaveBeenCalled();

		// Clear mocks to check next call
		vi.clearAllMocks();

		// Update dependency - mParticle is no longer initialized
		vi.mocked(useIsInitialized).mockReturnValue(false);

		// Rerender the hook
		rerender();

		// Second call should fail
		await expect(result.current.identify(mockUserIdentities)).rejects.toThrow(
			'MParticle is not initialized',
		);
		expect(mParticle.Identity.identify).not.toHaveBeenCalled();
	});

	it('should return all three functions', () => {
		// Execute
		const { result } = renderHook(() => useUserIdentity());

		// Verify
		expect(result.current).toHaveProperty('identify');
		expect(result.current).toHaveProperty('login');
		expect(result.current).toHaveProperty('logout');
		expect(typeof result.current.identify).toBe('function');
		expect(typeof result.current.login).toBe('function');
		expect(typeof result.current.logout).toBe('function');
	});
});
