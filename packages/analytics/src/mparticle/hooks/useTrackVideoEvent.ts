'use client';

import { useCallback } from 'react';
import mParticle from '@mparticle/web-sdk';
import type { SDKEventAttrs } from '@mparticle/web-sdk';
import { useEventAttributes } from './useEventAttributes';
import { usePathname } from 'next/navigation';
import { stripLastPartOfSlug } from './utils';
import { useCMSAttributes } from './useCMSAttributes';

interface OptionsEventAttributes {
	adtitle?: string;
	contentWatchedSeconds?: number;
	errorMessage?: string;
	playReason?: string;
	videoView?: number;

	// add extra events attributes from currentVideo, jwPlayer
	assetName?: string;
	adBlock?: boolean;
	contentId?: string;
	entitlements?: string;
	iabContent?: string;
	muted?: boolean;
	adsMetricsUnitAndZone?: string;
	collection?: string;
	collectionId?: string;
	id?: string;
	duration?: string;
	lastmodifieddate?: string;
	premiumContent?: boolean | undefined;
	publishdate?: string;
	providerName?: string;
	tagsGeo?: string;
	tagsKeyword?: string;
	tagsStorm?: string;
}

export interface TrackVideoEventOptions {
	eventName: string;
	eventAttributes?: OptionsEventAttributes;
}

interface TrackVideoAttributes {
	author?: string;
	cookiesEnabled?: boolean;
	contentId?: string;
	locale?: string;
	pageId?: string;
	schemaVersion?: string;
	sessionId?: string;
	sessionStartTime?: number;
	title?: string;
	teaserTitle?: string;
	url?: string;
	wlocCity?: string;
	wlocCountry?: string;
	wlocState?: string;
}

/**
 * Hook for tracking video custom events in mParticle
 * @returns A function to track custom events
 */
export const useTrackVideoEvent = () => {
	const generalAttributes = useEventAttributes();
	const cmsAttributes = useCMSAttributes();
	const pathname = usePathname();

	const trackVideoEvent = useCallback(
		({
			eventName,
			// currentVideo,
			// jwPlayer,
			eventAttributes = {},
		}: TrackVideoEventOptions) => {
			if (!mParticle.isInitialized()) {
				console.warn('MParticle is not initialized');
				return;
			}

			const eventInfoAttributes: TrackVideoAttributes = {
				...eventAttributes,
				author: cmsAttributes?.author || '',
				contentId: cmsAttributes?.contentId || eventAttributes.contentId,
				teaserTitle: '', // Intentially left empty per requirements.
				title: '', // Intentially left empty per requirements.
				...generalAttributes,
				url: pathname ? stripLastPartOfSlug(pathname) : '',
			};

			try {
				mParticle.logEvent(
					eventName,
					mParticle.EventType.Other,
					eventInfoAttributes as SDKEventAttrs,
				);
				console.debug(
					`MParticle: Video Event tracked - "${eventName}"`,
					eventInfoAttributes,
				);
			} catch (error) {
				console.error(
					`MParticle: Failed to track Video Event - "${eventName}"`,
					error,
				);
			}
		},
		[
			cmsAttributes?.author,
			cmsAttributes?.contentId,
			generalAttributes,
			pathname,
		],
	);

	return trackVideoEvent;
};
