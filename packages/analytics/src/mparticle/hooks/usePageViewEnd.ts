'use client';

import { useEffect } from 'react';
import mParticle, { SDKEventAttrs } from '@mparticle/web-sdk';
import { EventAttributes, useEventAttributes } from './useEventAttributes';
import { debounce } from 'es-toolkit';
import canUseDOM from '@repo/utils/canUseDOM';

// export interface PageViewOptions {
// }

interface PageViewEndAttributes extends EventAttributes {
	// TODO: Needs Helios events logic to be hooked in.
	// adLoaded: boolean;
	// adRequested: boolean;
	timeOnPage: number;
	scrollDepth: number;
}

const starttime = new Date().getTime();
const debouncedCalcScrollHeight = debounce(() => {
	calcScrollHeight();
}, 250);

if (canUseDOM) {
	// Need to attach these events as soon as possible.
	// Since useEffect might execute after the events.
	window.addEventListener('scroll', debouncedCalcScrollHeight);
}

/**
 * Hook for tracking page views in mParticle
 * @returns A function to track page views
 */
export const usePageViewEnd = () => {
	const generalAttributes = useEventAttributes();

	useEffect(() => {
		const trackPageViewEnd = () => {
			if (mParticle.isInitialized()) {
				const timeOnPage = (new Date().getTime() - starttime) / 1000;
				const scrollDepth = calcScrollDepth();

				const eventInfoAttributes: PageViewEndAttributes = {
					...generalAttributes,
					timeOnPage,
					scrollDepth,
				};

				try {
					mParticle?.logEvent?.(
						'page-view-end',
						mParticle.EventType.Other,
						eventInfoAttributes as SDKEventAttrs,
					);

					console.debug(
						`MParticle: Event "page-view-end" tracked - ${eventInfoAttributes.timeOnPage}`,
					);
				} catch (error) {
					console.error('MParticle: Event "page-view-end" failed', error);
				}
			}
		};

		window.addEventListener('beforeunload', trackPageViewEnd);
		// Sends the event when the user leaves the tab
		window.addEventListener('blur', trackPageViewEnd);

		return () => {
			window.removeEventListener('beforeunload', trackPageViewEnd);
			window.removeEventListener('blur', trackPageViewEnd);
		};
	}, [generalAttributes]);
};

let scrollHeight = canUseDOM && window.innerHeight > 0 ? window.innerHeight : 0;

export function calcScrollHeight() {
	if (window.scrollY > scrollHeight) {
		scrollHeight = window.scrollY;
	}
}

export function calcScrollDepth() {
	const documentScroll = document.documentElement.scrollHeight;
	const windowHeight = window.innerHeight;

	scrollHeight = scrollHeight || window.scrollY;
	let depth =
		documentScroll === windowHeight
			? 100
			: Math.round((scrollHeight / (documentScroll - windowHeight)) * 100);

	if (depth > 100) {
		depth = 100;
	} else if (depth < 0) {
		depth = 0;
	}

	return depth;
}
