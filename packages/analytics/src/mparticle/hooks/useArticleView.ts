'use client';

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import mParticle, { SDKEventAttrs } from '@mparticle/web-sdk';
import { areObjectValuesDefined, stripLastPartOfSlug } from './utils';
import { useIsInitialized } from './useIsInitialized';
import { EventAttributes, useEventAttributes } from './useEventAttributes';
import { usePathname } from 'next/navigation';
import { CMSAttributes, useCMSAttributes } from './useCMSAttributes';

// export interface ArticleViewOptions {
// }

interface ArticleViewAttributes extends EventAttributes, CMSAttributes {}

/**
 * Hook for tracking article views in mParticle
 * @returns A function to track article views
 */
export const useArticleView = () => {
	const isInitialized = useIsInitialized();
	const pathname = usePathname();
	const generalAttributes = useEventAttributes();
	const cmsAttributes = useCMSAttributes();

	const [areArticleViewAttributesReady, setAreSessionStartAttributesReady] =
		useState(false);
	const hasSentEvent = useRef(false);

	const eventInfoAttributes: ArticleViewAttributes = useMemo(
		() => ({
			// premiumContent: cmsAttributes?.premiumContent, true
			author: cmsAttributes?.author || '',
			contentId: cmsAttributes?.contentId,
			publishDate: cmsAttributes?.publishDate,
			// provider: cmsAttributes?.providerName,
			// tagsGeo: cmsAttributes?.tagsGeo,
			// tagsKeyword: cmsAttributes?.tagsKeyword,
			// tagsStorm: cmsAttributes?.tagsStorm,
			collection: cmsAttributes?.collection as string,
			// pageTemplate: cmsAttributes?.pageTemplate, standard
			// nodeTypes: cmsAttributes?.nodeTypes,
			entitlements: cmsAttributes?.entitlements,
			createdDate: cmsAttributes?.createdDate,
			teaserTitle: '',
			title: '',
			seoTitle: '',
			...generalAttributes,
			url: pathname ? stripLastPartOfSlug(pathname) : '',
		}),
		[generalAttributes, cmsAttributes, pathname],
	);

	useEffect(() => {
		if (
			areObjectValuesDefined(eventInfoAttributes as Record<string, unknown>) &&
			!areArticleViewAttributesReady
		) {
			setAreSessionStartAttributesReady(true);
		}
	}, [eventInfoAttributes, areArticleViewAttributesReady]);

	const trackArticleView = useCallback(async () => {
		if (
			eventInfoAttributes?.pageId === 'article' &&
			isInitialized &&
			areArticleViewAttributesReady &&
			!hasSentEvent.current
		) {
			try {
				mParticle?.logEvent?.(
					'article-viewed',
					mParticle.EventType.Other,
					eventInfoAttributes as SDKEventAttrs,
				);

				console.debug(
					`MParticle: Event "article-viewed" tracked - ${eventInfoAttributes?.pageId}`,
				);

				hasSentEvent.current = true;
			} catch (error) {
				console.error('MParticle: Event "article-viewed" failed', error);
			}
		}
	}, [isInitialized, eventInfoAttributes, areArticleViewAttributesReady]);

	return trackArticleView;
};
