'use client';

import mParticle, { MPConfiguration } from '@mparticle/web-sdk';
import { useAtomValue } from 'jotai';
import { useEffect, useRef } from 'react';
import { useIsInitialized } from './useIsInitialized';
import { wxuUserAnonymousIdAtom } from '@repo/user/atoms/wxu/user';
import { userEmailAtom } from '@repo/user/atoms/profile';
import { useUser } from '@repo/user/hooks/useUser';

interface MPConfigurationExtended extends MPConfiguration {
	v1SecureServiceUrl: 'mparticle.weather.com/webevents/v1/JS/';
	v2SecureServiceUrl: 'mparticle.weather.com/webevents/v2/JS/';
	v3SecureServiceUrl: 'mparticle.weather.com/webevents/v3/JS/';
	configUrl: 'mparticle.weather.com/tags/JS/v2/';
	identityUrl: 'mparticle.weather.com/identity/v1/';
	aliasUrl: 'mparticle.weather.com/webevents/v1/identity/';
}

export const useInitializeSdk = () => {
	const apiKey = process.env.NEXT_PUBLIC_MPARTICLE_API_KEY || '';
	const isDevelopmentMode =
		process.env.NEXT_PUBLIC_MPARTICLE_ENV !== 'production';
	const isInitialized = useIsInitialized();
	const hasInvokedMPInit = useRef(isInitialized);

	// User Profile atom values
	const {
		user: { isUserLoggedIn, userID },
	} = useUser();
	const userEmail = useAtomValue(userEmailAtom);

	// mParticle UserIdentities values
	const customerid = isUserLoggedIn ? userID : null;
	const email = isUserLoggedIn ? userEmail : '';
	const other = useAtomValue(wxuUserAnonymousIdAtom) as string;

	const shouldInvokeMPInit = getShouldInvokeMPInit({
		hasInvokedMPInit: hasInvokedMPInit.current,
		apiKey,
		isUserLoggedIn,
		customerid,
		email,
		other,
	});

	useEffect(() => {
		if (!apiKey) {
			console.error('MParticle API key is required');

			return;
		}

		if (shouldInvokeMPInit) {
			hasInvokedMPInit.current = true;

			// Initialize mParticle
			const mpConfig: MPConfigurationExtended = {
				isDevelopmentMode,
				logLevel: isDevelopmentMode
					? ('verbose' as const)
					: ('warning' as const),
				identifyRequest: {
					userIdentities: {
						email,
						customerid,
						other,
					},
				},
				v1SecureServiceUrl: 'mparticle.weather.com/webevents/v1/JS/',
				v2SecureServiceUrl: 'mparticle.weather.com/webevents/v2/JS/',
				v3SecureServiceUrl: 'mparticle.weather.com/webevents/v3/JS/',
				configUrl: 'mparticle.weather.com/tags/JS/v2/',
				identityUrl: 'mparticle.weather.com/identity/v1/',
				aliasUrl: 'mparticle.weather.com/webevents/v1/identity/',
				dataPlan: {
					planId: 'weather_web',
					planVersion: 1,
				},
				identityCallback: (result) => {
					// Identity callback is triggered after successful mParticle initialization
					// or after user identity changes (login/logout/identify)
					if (result?.getUser()) {
						const mpid = result.getUser().getMPID();

						if (mpid) {
							console.debug('MParticle: Identity callback triggered', mpid);
							// For Helios
							localStorage.setItem(
								// Helios uses the legacy wxu-web key
								'wxu-web/keyval:mparticleUser',
								JSON.stringify({
									value: { mpid },
									updatedAt: Date.now(),
								}),
							);
						}
					}
				},
			};

			console.debug('Mparticle initialized with config:', mpConfig);

			mParticle.init(apiKey, mpConfig);
		}
	}, [shouldInvokeMPInit, apiKey, isDevelopmentMode, customerid, email, other]);
};

const getShouldInvokeMPInit = ({
	hasInvokedMPInit,
	apiKey,
	isUserLoggedIn,
	customerid,
	email,
	other,
}: {
	hasInvokedMPInit: boolean;
	apiKey: string;
	isUserLoggedIn?: boolean;
	customerid?: string | null;
	email?: string | null;
	other?: string;
}) => {
	// If user not logged in, other should be defined.
	// If user is logged in, other, customerid and email should be defined.
	const areUserIdentitiesSet =
		other && ((isUserLoggedIn && customerid && email) || !isUserLoggedIn);

	return !hasInvokedMPInit && apiKey && areUserIdentitiesSet;
};
