'use client';

import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useLocationViewed } from './useLocationViewed';
import mParticle from '@mparticle/web-sdk';
import { useIsInitialized } from './useIsInitialized';
import { useEventAttributes, EventAttributes } from './useEventAttributes';
import { isNewMetricsSession } from '../../session/metricsSession';
import type { LocationData } from '@repo/location/types';
import { useAtomValue } from 'jotai';
import { pageLocationDataAtom } from '@repo/location/atoms/pagelocation';

// Mock dependencies
vi.mock('@mparticle/web-sdk', () => ({
	default: {
		logEvent: vi.fn(),
		EventType: {
			Other: 'other',
		},
	},
}));

vi.mock('./useIsInitialized', () => ({
	useIsInitialized: vi.fn(),
}));

vi.mock('./useEventAttributes', () => ({
	useEventAttributes: vi.fn(),
}));

vi.mock('../../session/metricsSession', () => ({
	isNewMetricsSession: vi.fn(),
}));

vi.mock('@repo/utils/canUseDOM', () => ({
	default: true,
}));

// Mock jotai
vi.mock('jotai', () => ({
	useAtomValue: vi.fn(),
	atom: vi.fn((initialValue) => ({ init: initialValue })),
}));

describe('useLocationViewed', () => {
	const mockLocationData: LocationData = {
		displayName: 'New York, NY',
		adminDistrict: 'New York',
		geocode: '40.7128,-74.006',
		placeId: 'ny-123',
		city: 'New York',
		countryCode: 'US',
		adminDistrictCode: 'NY',
	};

	const mockEventAttributes: EventAttributes = {
		pageId: 'home',
		partner: '',
		url: '/test',
		cookiesEnabled: true,
		campaign: null,
		dayOfWeek: 'MON',
		deviceClass: 'desktop',
		functionalTechnologies: false,
		geographicallyRelevantAds: false,
		gpcSignalOptOut: false,
		hourOfDay: 12,
		linkReferral: null,
		locale: 'en-US',
		physicalCountry: 'US',
		physicalState: 'NY',
		priv: '',
		privProducts: '',
		referrer: null,
		regime: '',
		schemaVersion: 'nextgen',
		pageCount: 1,
		sessionId: 'test-session',
		sessionStartTime: Date.now(),
		tpcc: null,
		wlocCity: 'New York, NY',
		wlocCountry: 'New York',
		wlocState: 'NY',
	};

	beforeEach(() => {
		vi.resetAllMocks();
		vi.mocked(useIsInitialized).mockReturnValue(true);
		vi.mocked(useEventAttributes).mockReturnValue(mockEventAttributes);
		vi.mocked(isNewMetricsSession).mockReturnValue('true');

		vi.mocked(useAtomValue).mockImplementation((atom) => {
			if (atom === pageLocationDataAtom) return mockLocationData;

			return '';
		});
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should track location viewed when initialized, attributes are ready, and it is a new session', async () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(true);
		vi.mocked(isNewMetricsSession).mockReturnValue('true');

		// Execute
		const { result } = renderHook(() => useLocationViewed());

		await act(async () => {
			await result.current();
		});

		// Verify
		expect(mParticle.logEvent).toHaveBeenCalledWith(
			'location-viewed',
			'other',
			expect.objectContaining({
				...mockEventAttributes,
				latitude: 40.7128,
				longitude: -74.006,
			}),
		);
	});

	it('should not track location viewed when not initialized', async () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(false);
		vi.mocked(isNewMetricsSession).mockReturnValue('true');

		// Execute
		const { result } = renderHook(() => useLocationViewed());

		await act(async () => {
			await result.current();
		});

		// Verify
		expect(mParticle.logEvent).not.toHaveBeenCalled();
	});

	it('should not track location viewed when it is not a new session', async () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(true);
		vi.mocked(isNewMetricsSession).mockReturnValue(null);

		// Execute
		const { result } = renderHook(() => useLocationViewed());

		await act(async () => {
			await result.current();
		});

		// Verify
		expect(mParticle.logEvent).not.toHaveBeenCalled();
	});

	it('should not track location viewed when location data is missing', async () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(true);
		vi.mocked(isNewMetricsSession).mockReturnValue('true');

		vi.mocked(useAtomValue).mockImplementation((atom) => {
			if (atom === pageLocationDataAtom) return undefined;

			return '';
		});

		// Execute
		const { result } = renderHook(() => useLocationViewed());

		await act(async () => {
			await result.current();
		});

		// Verify
		expect(mParticle.logEvent).not.toHaveBeenCalled();
	});

	it('should only track location viewed once even when called multiple times', async () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(true);
		vi.mocked(isNewMetricsSession).mockReturnValue('true');

		// Execute
		const { result } = renderHook(() => useLocationViewed());

		await act(async () => {
			await result.current();
			await result.current(); // Call a second time
		});

		// Verify
		expect(mParticle.logEvent).toHaveBeenCalledTimes(1);
	});

	it('should parse latitude and longitude correctly from geocode', async () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(true);
		vi.mocked(isNewMetricsSession).mockReturnValue('true');

		const customLocation: LocationData = {
			...mockLocationData,
			geocode: '35.6762,139.6503', // Tokyo coordinates
		};

		vi.mocked(useAtomValue).mockImplementation((atom) => {
			if (atom === pageLocationDataAtom) return customLocation;

			return '';
		});

		// Execute
		const { result } = renderHook(() => useLocationViewed());

		await act(async () => {
			await result.current();
		});

		// Verify
		expect(mParticle.logEvent).toHaveBeenCalledWith(
			'location-viewed',
			'other',
			expect.objectContaining({
				latitude: 35.6762,
				longitude: 139.6503,
			}),
		);
	});
});
