'use client';

import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useSessionStart } from './useSessionStart';
import mParticle from '@mparticle/web-sdk';
import { useIsInitialized } from './useIsInitialized';
import { useEventAttributes, EventAttributes } from './useEventAttributes';
import {
	isNewMetricsSession,
	deleteIsNewMestricsSession,
} from '../../session/metricsSession';
import type { LocationData } from '@repo/location/types';
import { usePathname } from 'next/navigation';
import { useAtomValue } from 'jotai';

// Mock dependencies
vi.mock('@mparticle/web-sdk', () => ({
	default: {
		logEvent: vi.fn(),
		EventType: {
			Other: 'other',
		},
	},
}));

vi.mock('./useIsInitialized', () => ({
	useIsInitialized: vi.fn(),
}));

vi.mock('./useEventAttributes', () => ({
	useEventAttributes: vi.fn(),
}));

vi.mock('../../session/metricsSession', () => ({
	isNewMetricsSession: vi.fn(),
	deleteIsNewMestricsSession: vi.fn(),
}));

vi.mock('next/navigation', () => ({
	usePathname: vi.fn(),
}));

vi.mock('jotai', () => ({
	useAtomValue: vi.fn(),
	atom: vi.fn(),
}));

describe('useSessionStart', () => {
	const mockEventAttributes: EventAttributes = {
		pageId: 'home',
		partner: '',
		url: '/',
		cookiesEnabled: true,
		campaign: null,
		dayOfWeek: 'MON',
		deviceClass: 'desktop',
		functionalTechnologies: false,
		geographicallyRelevantAds: false,
		gpcSignalOptOut: false,
		hourOfDay: 12,
		linkReferral: null,
		locale: 'en-US',
		physicalCountry: 'US',
		physicalState: 'NY',
		priv: '',
		privProducts: '',
		referrer: null,
		regime: '',
		schemaVersion: 'nextgen',
		pageCount: 1,
		sessionId: 'test-session',
		sessionStartTime: Date.now(),
		tpcc: null,
		wlocCity: 'New York, NY',
		wlocCountry: 'New York',
		wlocState: 'NY',
	};

	beforeEach(() => {
		vi.resetAllMocks();
		vi.mocked(useIsInitialized).mockReturnValue(true);
		vi.mocked(useEventAttributes).mockReturnValue(mockEventAttributes);
		vi.mocked(usePathname).mockReturnValue('/');
		vi.mocked(useAtomValue).mockReturnValue('home'); // pageIdAtom
		vi.mocked(isNewMetricsSession).mockReturnValue('true');
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should track session start when initialized, attributes are ready, and it is a new session', async () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(true);
		vi.mocked(isNewMetricsSession).mockReturnValue('true');

		// Execute
		const { result } = renderHook(() => useSessionStart());

		await act(async () => {
			await result.current();
		});

		// Verify
		expect(mParticle.logEvent).toHaveBeenCalledWith(
			'session-start',
			'other',
			expect.objectContaining(mockEventAttributes),
		);
		expect(deleteIsNewMestricsSession).toHaveBeenCalled();
	});

	it('should not track session start when not initialized', async () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(false);
		vi.mocked(isNewMetricsSession).mockReturnValue('true');

		// Execute
		const { result } = renderHook(() => useSessionStart());

		await act(async () => {
			await result.current();
		});

		// Verify
		expect(mParticle.logEvent).not.toHaveBeenCalled();
		expect(deleteIsNewMestricsSession).not.toHaveBeenCalled();
	});

	it('should not track session start when it is not a new session', async () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(true);
		vi.mocked(isNewMetricsSession).mockReturnValue(null);

		// Execute
		const { result } = renderHook(() => useSessionStart());

		await act(async () => {
			await result.current();
		});

		// Verify
		expect(mParticle.logEvent).not.toHaveBeenCalled();
		expect(deleteIsNewMestricsSession).not.toHaveBeenCalled();
	});

	it('should only track session start once even when called multiple times', async () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(true);
		vi.mocked(isNewMetricsSession).mockReturnValue('true');

		// Execute
		const { result } = renderHook(() => useSessionStart());

		await act(async () => {
			await result.current();
			await result.current(); // Call a second time
		});

		// Verify
		expect(mParticle.logEvent).toHaveBeenCalledTimes(1);
		expect(deleteIsNewMestricsSession).toHaveBeenCalledTimes(1);
	});
});
