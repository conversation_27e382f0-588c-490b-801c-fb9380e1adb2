'use client';

import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useTrackArticleShare } from './useTrackArticleShare';
import mParticle from '@mparticle/web-sdk';
import { useEventAttributes, EventAttributes } from './useEventAttributes';
import { useCMSAttributes } from './useCMSAttributes';
import { useLocationSource } from '@repo/location/hooks/useLocationSource';
import { useIsInitialized } from './useIsInitialized';

// Mock dependencies
vi.mock('@mparticle/web-sdk', () => ({
	default: {
		logEvent: vi.fn(),
		EventType: {
			Other: 'other',
		},
	},
}));

vi.mock('./useIsInitialized', () => ({
	useIsInitialized: vi.fn(),
}));

vi.mock('./useEventAttributes', () => ({
	useEventAttributes: vi.fn(),
}));

vi.mock('./useCMSAttributes', () => ({
	useCMSAttributes: vi.fn(),
}));

vi.mock('@repo/location/hooks/useLocationSource', () => ({
	useLocationSource: vi.fn(),
}));

describe('useTrackArticleShare', () => {
	const mockEventAttributes: EventAttributes = {
		pageId: 'test-page',
		url: '/test',
		cookiesEnabled: true,
		campaign: null,
		dayOfWeek: 'MON',
		deviceClass: 'desktop',
		functionalTechnologies: false,
		geographicallyRelevantAds: false,
		gpcSignalOptOut: false,
		hourOfDay: 12,
		linkReferral: null,
		locale: 'en-US',
		partner: 'test-partner',
		physicalCountry: 'US',
		physicalState: 'NY',
		priv: '',
		privProducts: '',
		referrer: null,
		regime: '',
		schemaVersion: 'nextgen',
		pageCount: 1,
		sessionId: 'test-session',
		sessionStartTime: Date.now(),
		tpcc: null,
		wlocCity: 'New York, NY',
		wlocCountry: 'New York',
		wlocState: 'NY',
	};

	const mockCMSAttributes = {
		author: 'Test Author',
		contentId: 'test-content-id',
		collection: 'test-collection',
		createdDate: '2023-01-01',
		publishDate: '2023-01-02',
		entitlements: 'premium',
	};

	beforeEach(() => {
		vi.resetAllMocks();

		// Mock hooks
		vi.mocked(useIsInitialized).mockReturnValue(true);
		vi.mocked(useEventAttributes).mockReturnValue(mockEventAttributes);
		vi.mocked(useCMSAttributes).mockReturnValue(mockCMSAttributes);

		// Mock console
		vi.spyOn(console, 'debug').mockImplementation(() => {});
		vi.spyOn(console, 'error').mockImplementation(() => {});
		vi.spyOn(console, 'warn').mockImplementation(() => {});
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should track article share event when mParticle is initialized', () => {
		// Setup
		const sharedMethod = 'facebook';

		// Execute
		const { result } = renderHook(() => useTrackArticleShare());

		act(() => {
			result.current({ sharedMethod });
		});

		// Verify
		expect(mParticle.logEvent).toHaveBeenCalled();
		const callArgs = vi.mocked(mParticle.logEvent).mock.calls[0];

		// Make sure we have call arguments
		expect(callArgs).toBeDefined();

		expect(callArgs?.[0]).toBe('content-shared');
		expect(callArgs?.[1]).toBe(mParticle.EventType.Other);

		// Check specific properties we care about
		const attrs = callArgs?.[2] as Record<string, unknown>;
		expect(attrs.author).toBe('Test Author');
		expect(attrs.contentId).toBe('test-content-id');
		expect(attrs.collection).toBe('test-collection');
		expect(attrs.createdDate).toBe('2023-01-01');
		expect(attrs.publishDate).toBe('2023-01-02');
		expect(attrs.entitlements).toBe('premium');
		expect(attrs.sharedMethod).toBe('facebook');

		// Verify general attributes are included
		expect(attrs.pageId).toBe('test-page');
		expect(attrs.url).toBe('/test');

		expect(console.debug).toHaveBeenCalledWith(
			'MParticle: Event "content-shared" tracked',
			expect.any(Object),
		);
	});

	it('should not track article share event when mParticle is not initialized', () => {
		// Setup
		vi.mocked(useIsInitialized).mockReturnValue(false);
		const sharedMethod = 'twitter';

		// Execute
		const { result } = renderHook(() => useTrackArticleShare());

		act(() => {
			result.current({ sharedMethod });
		});

		// Verify
		expect(mParticle.logEvent).not.toHaveBeenCalled();
		expect(console.warn).toHaveBeenCalledWith('MParticle is not initialized');
	});

	it('should handle error when tracking fails', () => {
		// Setup
		vi.mocked(mParticle.logEvent).mockImplementation(() => {
			throw new Error('Test error');
		});
		const sharedMethod = 'email';

		// Execute
		const { result } = renderHook(() => useTrackArticleShare());

		act(() => {
			result.current({ sharedMethod });
		});

		// Verify
		expect(console.error).toHaveBeenCalledWith(
			'MParticle: Event "content-shared" failed',
			expect.any(Error),
		);
	});

	it('should use different shared methods', () => {
		// Setup - First with one method
		const sharedMethod1 = 'facebook';

		// Execute
		const { result } = renderHook(() => useTrackArticleShare());

		act(() => {
			result.current({ sharedMethod: sharedMethod1 });
		});

		// Verify first call
		expect(mParticle.logEvent).toHaveBeenCalled();
		const callArgs1 = vi.mocked(mParticle.logEvent).mock.calls[0];
		expect(callArgs1).toBeDefined();

		const attrs1 = callArgs1?.[2] as Record<string, unknown>;
		expect(attrs1.sharedMethod).toBe('facebook');

		// Clear mocks
		vi.clearAllMocks();

		// Setup - Then with another method
		const sharedMethod2 = 'twitter';

		act(() => {
			result.current({ sharedMethod: sharedMethod2 });
		});

		// Verify second call
		expect(mParticle.logEvent).toHaveBeenCalled();
		const callArgs2 = vi.mocked(mParticle.logEvent).mock.calls[0];
		expect(callArgs2).toBeDefined();
		const attrs2 = callArgs2?.[2] as Record<string, unknown>;
		expect(attrs2.sharedMethod).toBe('twitter');
	});

	it('should use the correct dependencies in useCallback', () => {
		// Setup
		const sharedMethod = 'facebook';
		const updatedCMSAttributes = {
			...mockCMSAttributes,
			author: 'New Author',
		};

		// First render with initial values
		const { result, rerender } = renderHook(() => useTrackArticleShare());

		act(() => {
			result.current({ sharedMethod });
		});

		// Clear mocks to check next call
		vi.clearAllMocks();

		// Update a dependency
		vi.mocked(useCMSAttributes).mockReturnValue(updatedCMSAttributes);

		// Rerender the hook
		rerender();

		// Call the function again
		act(() => {
			result.current({ sharedMethod });
		});

		// Verify the updated value was used
		expect(mParticle.logEvent).toHaveBeenCalled();
		const callArgs = vi.mocked(mParticle.logEvent).mock.calls[0];
		expect(callArgs).toBeDefined();
		const attrs = callArgs?.[2] as Record<string, unknown>;
		expect(attrs.author).toBe('New Author');
	});

	it('should update when general attributes change', () => {
		// Setup
		const sharedMethod = 'facebook';
		const updatedEventAttributes = {
			...mockEventAttributes,
			pageId: 'new-test-page',
		};

		// First render with initial values
		const { result, rerender } = renderHook(() => useTrackArticleShare());

		act(() => {
			result.current({ sharedMethod });
		});

		// Clear mocks to check next call
		vi.clearAllMocks();

		// Update a dependency
		vi.mocked(useEventAttributes).mockReturnValue(updatedEventAttributes);

		// Rerender the hook
		rerender();

		// Call the function again
		act(() => {
			result.current({ sharedMethod });
		});

		// Verify the updated value was used
		expect(mParticle.logEvent).toHaveBeenCalled();
		const callArgs = vi.mocked(mParticle.logEvent).mock.calls[0];
		expect(callArgs).toBeDefined();
		const attrs = callArgs?.[2] as Record<string, unknown>;
		expect(attrs.pageId).toBe('new-test-page');
	});
});
