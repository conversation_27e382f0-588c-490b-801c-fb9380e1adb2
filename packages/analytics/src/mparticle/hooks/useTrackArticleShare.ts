'use client';

import mParticle from '@mparticle/web-sdk';
import type { SDKEventAttrs } from '@mparticle/web-sdk';
import { useIsInitialized } from './useIsInitialized';
import { useEventAttributes, EventAttributes } from './useEventAttributes';
import { useCMSAttributes } from './useCMSAttributes';
import { useCallback } from 'react';

interface ArticleShareEventAttributes extends EventAttributes {
	moduleId?: string;
	launchSource?: string;
	physicalWeatherCondition?: string;
	wlocDMA?: string;
	localSevere?: string;
	observedFeelsLikeTemperature?: string;
	observedTemperature?: string;
	todayForecastDayTemperature?: string;
	todayForecastNightTemperature?: string;
	deviceTimeZone?: string;
	evaStatus?: string;
	premiumContent?: string;
	createdDate?: string;
	contentId?: string;
	publishDate?: string;
	provider?: string;
	tagsGeo?: string;
	tagsKeyword?: string;
	tagsStorm?: string;
	author?: string | null;
	collection?: string;
	pageTemplate?: string;
	nodeTypes?: string;
	entitlements?: string;
	adsMetricsUnitAndZone?: string;
	iabContent?: string;
	sharedMethod?: string;
	experimentJoinList?: string;
	weatherMode?: string;
}

export const useTrackArticleShare = () => {
	const eventName = 'content-shared';
	const isInitialized = useIsInitialized();

	const generalAttributes = useEventAttributes();
	const cmsAttributes = useCMSAttributes();

	const trackArticleShare = useCallback(
		({ sharedMethod }: { sharedMethod: string }) => {
			const articleShareEventAttributes: ArticleShareEventAttributes = {
				createdDate: cmsAttributes?.createdDate,
				contentId: cmsAttributes?.contentId,
				publishDate: cmsAttributes?.publishDate,
				author: cmsAttributes?.author || '',
				collection: cmsAttributes?.collection as string,
				entitlements: cmsAttributes?.entitlements,
				sharedMethod,
				...generalAttributes,
			};

			if (!isInitialized) {
				console.warn('MParticle is not initialized');
				return;
			}

			try {
				mParticle.logEvent(
					eventName,
					mParticle.EventType.Other,
					articleShareEventAttributes as unknown as SDKEventAttrs,
				);
				console.debug(
					`MParticle: Event "${eventName}" tracked`,
					articleShareEventAttributes,
				);
			} catch (error) {
				console.error(`MParticle: Event "${eventName}" failed`, error);
			}

			return trackArticleShare;
		},
		[
			cmsAttributes?.author,
			cmsAttributes?.contentId,
			cmsAttributes?.collection,
			cmsAttributes?.createdDate,
			cmsAttributes?.entitlements,
			cmsAttributes?.publishDate,
			generalAttributes,
			isInitialized,
		],
	);

	return trackArticleShare;
};
