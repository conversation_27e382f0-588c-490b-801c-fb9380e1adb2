import { getCookie, setCookie, deleteCookie } from 'cookies-next/client';
import { v4 } from 'uuid';

const METRICS_SESSION_COOKIE_NAME = 'wxu-metrics-session';
const NEW_METRICS_SESSION = 'new-metrics-session';

interface Session {
	sessionId: string;
	sessionStartTime: number;
	pageCount: number;
}

export const getMetricsSession = (): Session | null => {
	const sessionCookieValue = getCookie(METRICS_SESSION_COOKIE_NAME);

	if (!sessionCookieValue) {
		return null;
	}

	const [sessionId = '', sessionStartTimeMs = '', pageCountStr = ''] =
		sessionCookieValue.split(':');

	const sessionStartTime = parseInt(sessionStartTimeMs, 10);
	const pageCount = parseInt(pageCountStr, 10);
	const session = {
		sessionId,
		sessionStartTime,
		pageCount,
	};

	return session;
};

export const setMetricsSession = (session: Session | null) => {
	const sessionId = session?.sessionId || v4();
	const sessionStartTime = session?.sessionStartTime || new Date().getTime();
	const pageCount = session?.pageCount || 0;

	const value = `${sessionId}:${sessionStartTime}:${pageCount}`;

	setCookie(METRICS_SESSION_COOKIE_NAME, decodeURIComponent(value), {
		domain: '.weather.com',
		path: '/',
		maxAge: 1800, // 30 Minutes
	});
};

export const deleteMetricsSession = (): void => {
	deleteCookie(METRICS_SESSION_COOKIE_NAME, {
		domain: '.weather.com',
	});
};

export const incrementMetricsSessionCount = () => {
	const session = getMetricsSession();

	if (session) {
		setMetricsSession({
			...session,
			pageCount: session?.pageCount + 1,
		});
	}
};

export const setIsNewMestricsSession = () => {
	localStorage.setItem(NEW_METRICS_SESSION, 'true');
};

export const deleteIsNewMestricsSession = () => {
	localStorage.removeItem(NEW_METRICS_SESSION);
};

export const isNewMetricsSession = () => {
	return localStorage.getItem(NEW_METRICS_SESSION);
};
