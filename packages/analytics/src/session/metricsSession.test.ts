'use client';

import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import {
	getMetricsSession,
	setMetricsSession,
	deleteMetricsSession,
	incrementMetricsSessionCount,
	setIsNewMestricsSession,
	deleteIsNewMestricsSession,
	isNewMetricsSession,
} from './metricsSession';
import { getCookie, setCookie, deleteCookie } from 'cookies-next/client';
import { v4 } from 'uuid';

// Mock dependencies
vi.mock('cookies-next/client', () => ({
	getCookie: vi.fn(),
	setCookie: vi.fn(),
	deleteCookie: vi.fn(),
}));

vi.mock('uuid', () => ({
	v4: vi.fn(() => 'mock-uuid-v4'),
}));

describe('metricsSession', () => {
	// Mock data
	const mockSession = {
		sessionId: 'test-session-id',
		sessionStartTime: 1620000000000,
		pageCount: 5,
	};

	const mockCookieValue = 'test-session-id:1620000000000:5';

	beforeEach(() => {
		vi.resetAllMocks();

		// Mock localStorage
		vi.stubGlobal('localStorage', {
			getItem: vi.fn(),
			setItem: vi.fn(),
			removeItem: vi.fn(),
		});
	});

	afterEach(() => {
		vi.clearAllMocks();
		vi.unstubAllGlobals();
	});

	describe('getMetricsSession', () => {
		it('should return null when cookie does not exist', () => {
			// Setup
			vi.mocked(getCookie).mockReturnValue(undefined);

			// Execute
			const result = getMetricsSession();

			// Verify
			expect(result).toBeNull();
			expect(getCookie).toHaveBeenCalledWith('wxu-metrics-session');
		});

		it('should parse and return session data when cookie exists', () => {
			// Setup
			vi.mocked(getCookie).mockReturnValue(mockCookieValue);

			// Execute
			const result = getMetricsSession();

			// Verify
			expect(result).toEqual(mockSession);
			expect(getCookie).toHaveBeenCalledWith('wxu-metrics-session');
		});

		it('should handle malformed cookie values gracefully', () => {
			// Setup
			vi.mocked(getCookie).mockReturnValue('malformed-value');

			// Execute
			const result = getMetricsSession();

			// Verify
			expect(result).toEqual({
				sessionId: 'malformed-value',
				sessionStartTime: NaN,
				pageCount: NaN,
			});
		});
	});

	describe('setMetricsSession', () => {
		it('should create a new session with default values when passed null', () => {
			// Setup
			const now = new Date(2023, 0, 1).getTime();
			vi.spyOn(Date.prototype, 'getTime').mockReturnValue(now);

			// Execute
			setMetricsSession(null);

			// Verify
			expect(v4).toHaveBeenCalled();
			expect(setCookie).toHaveBeenCalledWith(
				'wxu-metrics-session',
				`mock-uuid-v4:${now}:0`,
				{
					domain: '.weather.com',
					path: '/',
					maxAge: 1800,
				},
			);
		});

		it('should use provided session values when passed a session object', () => {
			// Execute
			setMetricsSession(mockSession);

			// Verify
			expect(v4).not.toHaveBeenCalled();
			expect(setCookie).toHaveBeenCalledWith(
				'wxu-metrics-session',
				mockCookieValue,
				{
					domain: '.weather.com',
					path: '/',
					maxAge: 1800,
				},
			);
		});
	});

	describe('deleteMetricsSession', () => {
		it('should call deleteCookie with the correct cookie name and domain', () => {
			// Execute
			deleteMetricsSession();

			// Verify
			expect(deleteCookie).toHaveBeenCalledWith('wxu-metrics-session', {
				domain: '.weather.com',
			});
		});
	});

	describe('incrementMetricsSessionCount', () => {
		it('should increment the page count when a session exists', () => {
			// Setup
			vi.mocked(getCookie).mockReturnValue(mockCookieValue);

			// Execute
			incrementMetricsSessionCount();

			// Verify
			expect(setCookie).toHaveBeenCalledWith(
				'wxu-metrics-session',
				'test-session-id:1620000000000:6',
				{
					domain: '.weather.com',
					path: '/',
					maxAge: 1800,
				},
			);
		});

		it('should not modify anything when no session exists', () => {
			// Setup
			vi.mocked(getCookie).mockReturnValue(undefined);

			// Execute
			incrementMetricsSessionCount();

			// Verify
			expect(setCookie).not.toHaveBeenCalled();
		});
	});

	describe('setIsNewMestricsSession', () => {
		it('should set the correct item in localStorage', () => {
			// Execute
			setIsNewMestricsSession();

			// Verify
			expect(localStorage.setItem).toHaveBeenCalledWith(
				'new-metrics-session',
				'true',
			);
		});
	});

	describe('deleteIsNewMestricsSession', () => {
		it('should remove the correct item from localStorage', () => {
			// Execute
			deleteIsNewMestricsSession();

			// Verify
			expect(localStorage.removeItem).toHaveBeenCalledWith(
				'new-metrics-session',
			);
		});
	});

	describe('isNewMetricsSession', () => {
		it('should return the value from localStorage', () => {
			// Setup
			vi.mocked(localStorage.getItem).mockReturnValue('true');

			// Execute
			const result = isNewMetricsSession();

			// Verify
			expect(result).toBe('true');
			expect(localStorage.getItem).toHaveBeenCalledWith('new-metrics-session');
		});

		it('should return null when localStorage item does not exist', () => {
			// Setup
			vi.mocked(localStorage.getItem).mockReturnValue(null);

			// Execute
			const result = isNewMetricsSession();

			// Verify
			expect(result).toBeNull();
		});
	});
});
