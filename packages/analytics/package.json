{"name": "@repo/analytics", "version": "0.0.1", "type": "module", "private": true, "scripts": {"lint": "eslint --max-warnings=0 .", "check-types": "tsc --noEmit", "test": "vitest run"}, "exports": {"./atoms/*": "./src/atoms/*.ts", "./mparticle/hooks/*": "./src/mparticle/hooks/*.ts", "./mparticle/MParticle": "./src/mparticle/Mparticle.tsx", "./Analytics": "./src/Analytics.tsx", "./utils/*": "./src/utils/*.ts"}, "devDependencies": {"@repo/dpr-sdk": "workspace:*", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@repo/vitest-config": "workspace:*", "@testing-library/react": "catalog:dev", "@types/mparticle__web-sdk": "^2.20.3", "@types/node": "catalog:node", "@types/react": "catalog:web", "@types/react-dom": "catalog:web", "eslint": "catalog:dev", "typescript": "catalog:dev", "vitest": "catalog:dev"}, "peerDependencies": {"class-variance-authority": "catalog:web", "next": "catalog:web", "react": "catalog:web", "react-dom": "catalog:web"}, "dependencies": {"@mparticle/web-sdk": "^2.43.2", "@repo/dpr-sdk": "workspace:*", "@repo/location": "workspace:*", "@repo/units": "workspace:*", "@repo/user": "workspace:*", "@repo/utils": "workspace:*", "cookies-next": "catalog:web", "es-toolkit": "catalog:web", "jotai": "catalog:web", "jotai-optics": "catalog:web", "swr": "catalog:web", "uuid": "^11.1.0"}}