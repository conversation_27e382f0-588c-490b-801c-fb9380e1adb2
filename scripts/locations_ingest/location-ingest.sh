#!/bin/bash

# Location Ingest Script
# Processes a large JSON file and makes API calls to weather.com payload endpoint
# Usage: ./location-ingest.sh <json_file> [start_batch]

set -euo pipefail

# Configuration
API_URL="https://dev.weather.com/api/payload/v1/ingest/locations"
API_KEY="9b9f99ca-afc6-4ff2-8402-abc2ecfc6424"
BATCH_SIZE=50
DELAY_BETWEEN_BATCHES=1  # seconds
PARTNER="sun"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Required fields for validation
REQUIRED_FIELDS=("language" "displayname" "city" "admindistrictcode" "admindistrict" "country" "countrycode" "latitude" "longitude")

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Usage function
usage() {
    echo "Usage: $0 <json_file> [start_batch]"
    echo ""
    echo "Arguments:"
    echo "  json_file    Path to the JSON file to process"
    echo "  start_batch  Optional: Batch number to start from (default: 1)"
    echo ""
    echo "Example:"
    echo "  $0 locations.json"
    echo "  $0 locations.json 5  # Resume from batch 5"
    exit 1
}

# Validate required tools
check_dependencies() {
    local missing_tools=()
    
    if ! command -v jq &> /dev/null; then
        missing_tools+=("jq")
    fi
    
    if ! command -v curl &> /dev/null; then
        missing_tools+=("curl")
    fi
    
    if ! command -v awk &> /dev/null; then
        missing_tools+=("awk")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_info "Please install missing tools:"
        for tool in "${missing_tools[@]}"; do
            case $tool in
                jq)
                    echo "  - jq: brew install jq (macOS) or apt-get install jq (Ubuntu)"
                    ;;
                curl)
                    echo "  - curl: Usually pre-installed, or brew install curl (macOS)"
                    ;;
                awk)
                    echo "  - awk: Usually pre-installed on Unix systems"
                    ;;
            esac
        done
        exit 1
    fi
}

# Validate JSON file
validate_json_file() {
    local json_file="$1"
    
    if [ ! -f "$json_file" ]; then
        log_error "JSON file not found: $json_file"
        exit 1
    fi
    
    if ! jq empty "$json_file" 2>/dev/null; then
        log_error "Invalid JSON file: $json_file"
        exit 1
    fi
    
    # Check if it's an array
    if ! jq -e 'type == "array"' "$json_file" >/dev/null 2>&1; then
        log_error "JSON file must contain an array of locations"
        exit 1
    fi
    
    log_success "JSON file validation passed"
}

# Validate a single location record
validate_location() {
    local location="$1"
    local index="$2"
    local missing_fields=()
    
    # Check required fields
    for field in "${REQUIRED_FIELDS[@]}"; do
        if ! echo "$location" | jq -e "has(\"$field\") and (.${field} != null) and (.${field} != \"\")" >/dev/null 2>&1; then
            missing_fields+=("$field")
        fi
    done
    
    if [ ${#missing_fields[@]} -ne 0 ]; then
        log_warn "Skipping location at index $index - missing required fields: ${missing_fields[*]}"
        return 1
    fi
    
    # Validate latitude and longitude are numbers
    local lat=$(echo "$location" | jq -r '.latitude')
    local lon=$(echo "$location" | jq -r '.longitude')
    
    # Check if latitude and longitude are valid numbers (including negative decimals)
    if ! [[ "$lat" =~ ^-?[0-9]+(\.[0-9]+)?$ ]] || ! [[ "$lon" =~ ^-?[0-9]+(\.[0-9]+)?$ ]]; then
        log_warn "Skipping location at index $index - invalid latitude ($lat) or longitude ($lon) format"
        return 1
    fi
    
    # Validate coordinate ranges using awk for reliable floating-point comparison
    if ! awk -v lat="$lat" 'BEGIN { exit !(lat >= -90 && lat <= 90) }'; then
        log_warn "Skipping location at index $index - latitude out of range: $lat (must be between -90 and 90)"
        return 1
    fi
    
    if ! awk -v lon="$lon" 'BEGIN { exit !(lon >= -180 && lon <= 180) }'; then
        log_warn "Skipping location at index $index - longitude out of range: $lon (must be between -180 and 180)"
        return 1
    fi
    
    return 0
}

# Create API payload for a batch of locations
create_payload() {
    local locations_json="$1"
    
    # Create the payload structure
    jq -n \
        --arg partner "$PARTNER" \
        --argjson locations "$locations_json" \
        '{
            "partner": $partner,
            "locations": $locations
        }'
}

# Make API call
make_api_call() {
    local payload="$1"
    local batch_num="$2"
    local temp_file=$(mktemp)
    
    log_info "Making API call for batch $batch_num..."
    
    # Make the API call
    local http_code
    http_code=$(curl -s -w "%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: users API-Key $API_KEY" \
        -d "$payload" \
        "$API_URL" \
        -o "$temp_file")
    
    local response_body
    response_body=$(cat "$temp_file")
    rm "$temp_file"
    
    # Check response
    if [ "$http_code" -ge 200 ] && [ "$http_code" -lt 300 ]; then
        log_success "Batch $batch_num: API call successful (HTTP $http_code)"
        if [ -n "$response_body" ] && [ "$response_body" != "null" ]; then
            log_info "Response: $response_body"
        fi
        return 0
    else
        log_error "Batch $batch_num: API call failed (HTTP $http_code)"
        if [ -n "$response_body" ]; then
            log_error "Response: $response_body"
        fi
        return 1
    fi
}

# Process locations in batches
process_locations() {
    local json_file="$1"
    local start_batch="${2:-1}"
    
    # Get total count
    local total_locations
    total_locations=$(jq 'length' "$json_file")
    local total_batches
    total_batches=$(( (total_locations + BATCH_SIZE - 1) / BATCH_SIZE ))
    
    log_info "Processing $total_locations locations in $total_batches batches of $BATCH_SIZE"
    log_info "Starting from batch $start_batch"
    
    local processed_count=0
    local skipped_count=0
    local successful_batches=0
    local failed_batches=0
    
    # Process each batch
    for (( batch_num=start_batch; batch_num<=total_batches; batch_num++ )); do
        local start_index=$(( (batch_num - 1) * BATCH_SIZE ))
        local end_index=$(( start_index + BATCH_SIZE - 1 ))
        
        log_info "Processing batch $batch_num/$total_batches (locations $((start_index + 1))-$((end_index + 1)))"
        
        # Extract batch from JSON file
        local batch_locations=()
        local batch_json="[]"
        
        # Process each location in the batch
        for (( i=start_index; i<=end_index && i<total_locations; i++ )); do
            local location
            location=$(jq -c ".[$i]" "$json_file")
            
            if [ "$location" = "null" ]; then
                break
            fi
            
            # Validate location
            if validate_location "$location" "$i"; then
                # Add to batch
                batch_json=$(echo "$batch_json" | jq ". += [$location]")
                ((processed_count++))
            else
                ((skipped_count++))
            fi
        done
        
        # Check if batch has any valid locations
        local batch_size
        batch_size=$(echo "$batch_json" | jq 'length')
        
        if [ "$batch_size" -eq 0 ]; then
            log_warn "Batch $batch_num: No valid locations, skipping API call"
            continue
        fi
        
        # Create payload and make API call
        local payload
        payload=$(create_payload "$batch_json")
        
        if make_api_call "$payload" "$batch_num"; then
            ((successful_batches++))
        else
            ((failed_batches++))
        fi
        
        # Progress update
        local progress_percent
        progress_percent=$(( batch_num * 100 / total_batches ))
        log_info "Progress: $batch_num/$total_batches batches ($progress_percent%)"
        
        # Delay between batches (except for the last one)
        if [ "$batch_num" -lt "$total_batches" ] && [ "$DELAY_BETWEEN_BATCHES" -gt 0 ]; then
            log_info "Waiting ${DELAY_BETWEEN_BATCHES}s before next batch..."
            sleep "$DELAY_BETWEEN_BATCHES"
        fi
    done
    
    # Final summary
    echo ""
    log_success "Processing complete!"
    log_info "Summary:"
    log_info "  Total locations processed: $processed_count"
    log_info "  Locations skipped: $skipped_count"
    log_info "  Successful batches: $successful_batches"
    log_info "  Failed batches: $failed_batches"
    log_info "  Total batches: $total_batches"
    
    if [ "$failed_batches" -gt 0 ]; then
        log_warn "Some batches failed. Check the logs above for details."
        return 1
    fi
    
    return 0
}

# Main function
main() {
    local json_file="${1:-}"
    local start_batch="${2:-1}"
    
    # Validate arguments
    if [ -z "$json_file" ]; then
        log_error "JSON file argument is required"
        usage
    fi
    
    # Validate start_batch is a number
    if ! [[ "$start_batch" =~ ^[0-9]+$ ]] || [ "$start_batch" -lt 1 ]; then
        log_error "Start batch must be a positive integer"
        usage
    fi
    
    log_info "Location Ingest Script Starting..."
    log_info "JSON file: $json_file"
    log_info "API URL: $API_URL"
    log_info "Batch size: $BATCH_SIZE"
    log_info "Start batch: $start_batch"
    
    # Check dependencies
    check_dependencies
    
    # Validate JSON file
    validate_json_file "$json_file"
    
    # Process locations
    if process_locations "$json_file" "$start_batch"; then
        log_success "All batches processed successfully!"
        exit 0
    else
        log_error "Some batches failed during processing"
        exit 1
    fi
}

# Handle script interruption
trap 'log_warn "Script interrupted. You can resume from the last batch using: $0 $1 <batch_number>"' INT TERM

# Run main function with all arguments
main "$@"
