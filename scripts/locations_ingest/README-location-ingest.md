# Location Ingest Script

A bash script that processes large JSON files containing location data and makes batch API calls to the weather.com payload endpoint.

## Features

- **Batch Processing**: Processes locations in configurable batches (default: 50)
- **Field Validation**: Validates all required fields before making API calls
- **Error Resilience**: Continues processing even if individual records fail validation
- **Resume Capability**: Can resume from a specific batch if interrupted
- **Progress Tracking**: Shows detailed progress and statistics
- **Memory Efficient**: Uses `jq` streaming to handle large files without loading everything into memory
- **Detailed Logging**: Color-coded logging with different levels (INFO, WARN, ERROR, SUCCESS)

## Prerequisites

- `jq` - JSON processor
  - macOS: `brew install jq`
  - Ubuntu/Debian: `apt-get install jq`
- `curl` - HTTP client (usually pre-installed)
- `bc` - Basic calculator (usually pre-installed)

## Usage

### Basic Usage

```bash
./scripts/location-ingest.sh <json_file>
```

### Resume from Specific Batch

```bash
./scripts/location-ingest.sh <json_file> <start_batch>
```

### Examples

```bash
# Process entire file
./scripts/location-ingest.sh locations.json

# Resume from batch 5 (if interrupted)
./scripts/location-ingest.sh locations.json 5

# Test with sample data
./scripts/test-location-ingest.sh
```

## JSON File Format

The script expects a JSON array of location objects. Each location must have these required fields:

```json
[
  {
    "language": "en",
    "displayname": "Location Name",
    "city": "City Name",
    "admindistrictcode": "STATE",
    "admindistrict": "State Name",
    "country": "Country Name",
    "countrycode": "US",
    "latitude": 40.7128,
    "longitude": -74.0060,
    "location_data": {
      "poitype": "optional field",
      "other_optional": "fields"
    }
  }
]
```

### Required Fields

- `language` - Language code (e.g., "en")
- `displayname` - Display name of the location
- `city` - City name
- `admindistrictcode` - State/province code
- `admindistrict` - State/province name
- `country` - Country name
- `countrycode` - Country code
- `latitude` - Latitude (-90 to 90)
- `longitude` - Longitude (-180 to 180)

### Optional Fields

- `location_data` - Object containing additional location metadata (all fields within this object are optional)

## Configuration

You can modify these variables at the top of the script:

```bash
API_URL="https://dev.weather.com/api/payload/v1/ingest/locations"
API_KEY="your-api-key-here"
BATCH_SIZE=50
DELAY_BETWEEN_BATCHES=1  # seconds
PARTNER="sun"
```

## API Request Format

The script sends POST requests with this structure:

```json
{
  "partner": "sun",
  "locations": [
    {
      "language": "en",
      "displayname": "Location Name",
      "city": "City Name",
      "admindistrictcode": "STATE",
      "admindistrict": "State Name",
      "country": "Country Name",
      "countrycode": "US",
      "latitude": 40.7128,
      "longitude": -74.0060,
      "location_data": {
        "poitype": "optional field"
      }
    }
  ]
}
```

## Error Handling

The script handles various error conditions:

- **Missing Required Fields**: Logs warning and skips the location
- **Invalid Coordinates**: Validates latitude/longitude ranges
- **API Failures**: Logs error but continues with next batch
- **File Issues**: Validates JSON format before processing
- **Network Issues**: Provides detailed HTTP status codes

## Output

The script provides detailed logging:

```
[INFO] Location Ingest Script Starting...
[INFO] JSON file: locations.json
[INFO] Processing 1000 locations in 20 batches of 50
[INFO] Processing batch 1/20 (locations 1-50)
[SUCCESS] Batch 1: API call successful (HTTP 200)
[INFO] Progress: 1/20 batches (5%)
[WARN] Skipping location at index 75 - missing required fields: city
[SUCCESS] Processing complete!
[INFO] Summary:
[INFO]   Total locations processed: 995
[INFO]   Locations skipped: 5
[INFO]   Successful batches: 20
[INFO]   Failed batches: 0
```

## Testing

Use the test script to validate functionality:

```bash
./scripts/test-location-ingest.sh
```

This creates a small test file with valid and invalid locations to demonstrate the script's validation and error handling.

## Troubleshooting

### Common Issues

1. **Missing jq**: Install with `brew install jq` (macOS) or `apt-get install jq` (Ubuntu)
2. **Permission denied**: Run `chmod +x scripts/location-ingest.sh`
3. **Invalid JSON**: Validate your JSON file with `jq . your-file.json`
4. **API errors**: Check the API key and endpoint URL

### Resume Processing

If the script is interrupted, you can resume from the last successful batch:

```bash
# If interrupted at batch 15, resume from there
./scripts/location-ingest.sh locations.json 15
```

### Large Files

For very large files (>100MB), consider:

- Reducing `BATCH_SIZE` to use less memory
- Increasing `DELAY_BETWEEN_BATCHES` to avoid rate limiting
- Running during off-peak hours

## Script Files

- `scripts/location-ingest.sh` - Main processing script
- `scripts/test-location-ingest.sh` - Test script with sample data
- `scripts/README-location-ingest.md` - This documentation

## Security Notes

- The API key is hardcoded in the script - consider using environment variables for production
- The script makes actual API calls - test with small datasets first
- Review the test data before running to ensure it meets your requirements
