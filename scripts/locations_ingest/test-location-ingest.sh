#!/bin/bash

# Test script for location-ingest.sh
# Creates a small test JSON file and runs the ingest script in dry-run mode

set -euo pipefail

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Create test data
create_test_data() {
    local test_file="$1"
    
    log_info "Creating test data file: $test_file"
    
    cat > "$test_file" << 'EOF'
[
  {
    "language": "en",
    "displayname": "Sugarland Mountain Trail",
    "city": "Gatlinburg",
    "admindistrictcode": "TN",
    "admindistrict": "Tennessee",
    "country": "United States",
    "countrycode": "US",
    "latitude": 35.5906,
    "longitude": -83.4742,
    "location_data": {
      "poitype": "hiking trail"
    }
  },
  {
    "language": "en",
    "displayname": "Southwest Georgia Regional Airport",
    "city": "Albany",
    "admindistrictcode": "GA",
    "admindistrict": "Georgia",
    "country": "United States",
    "countrycode": "US",
    "latitude": 31.532946,
    "longitude": -84.196215,
    "location_data": {
      "poitype": "airport",
      "icaoId": "KABY",
      "iataId": "ABY"
    }
  },
  {
    "language": "en",
    "displayname": "Veterans Memorial Soccer Complex",
    "city": "Huntington",
    "admindistrictcode": "WV",
    "admindistrict": "West Virginia",
    "country": "United States",
    "countrycode": "US",
    "latitude": 38.42610087,
    "longitude": -82.40993434,
    "location_data": {
      "poitype": "sports venue"
    }
  },
  {
    "language": "en",
    "displayname": "Invalid Location - Missing City",
    "admindistrictcode": "CA",
    "admindistrict": "California",
    "country": "United States",
    "countrycode": "US",
    "latitude": 34.0522,
    "longitude": -118.2437,
    "location_data": {
      "poitype": "test location"
    }
  },
  {
    "language": "en",
    "displayname": "Invalid Coordinates",
    "city": "Test City",
    "admindistrictcode": "TX",
    "admindistrict": "Texas",
    "country": "United States",
    "countrycode": "US",
    "latitude": 999,
    "longitude": -999,
    "location_data": {
      "poitype": "test location"
    }
  }
]
EOF
    
    log_success "Test data created with 5 locations (3 valid, 2 invalid)"
}

# Main test function
main() {
    local test_file="test-locations.json"
    local script_path="./location-ingest.sh"
    
    log_info "Location Ingest Test Script"
    log_info "=========================="
    
    # Check if the main script exists
    if [ ! -f "$script_path" ]; then
        log_warn "Main script not found at $script_path"
        log_info "Please run this test from the project root directory"
        exit 1
    fi
    
    # Create test data
    create_test_data "$test_file"
    
    # Show test data
    log_info "Test data contents:"
    echo "---"
    cat "$test_file" | jq '.'
    echo "---"
    
    # Run the script with test data
    log_info "Running location ingest script with test data..."
    log_warn "Note: This will make actual API calls to the weather.com endpoint"
    
    read -p "Do you want to proceed with the API calls? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "Running script..."
        if "$script_path" "$test_file"; then
            log_success "Script completed successfully!"
        else
            log_warn "Script completed with some errors (check output above)"
        fi
    else
        log_info "Skipping API calls. You can run the script manually with:"
        echo "  $script_path $test_file"
    fi
    
    # Cleanup
    log_info "Cleaning up test file..."
    rm -f "$test_file"
    
    log_success "Test complete!"
}

# Run main function
main "$@"
